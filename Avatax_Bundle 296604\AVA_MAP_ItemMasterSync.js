/******************************************************************************************************
	Script Name - AVA_MAP_ItemMasterSync.js
	Company -     Avalara Technologies Pvt Ltd.
******************************************************************************************************/

/**
 *@NApiVersion 2.0
 *@NScriptType MapReduceScript
 */

define(['N/record', 'N/search', 'N/runtime', 'N/https', './utility/AVA_Library',  './utility/AVA_CommonServerFunctions'],
	function (record, search, runtime, https, ava_library, ava_commonFunction){
		function getInputData(context){
			try{
				var imsDetails = runtime.getCurrentScript().getParameter({
					name: 'custscript_ava_imsbatchdetails'
				});
				log.debug('getInputData', 'imsDetails = ' + imsDetails);
				var customerSearchObject = [];
				
				if(imsDetails){
					imsDetails = JSON.parse(imsDetails);
					var newIMSBatchId = imsDetails.newbatchid;
					if(newIMSBatchId){
						record.submitFields({
							type: 'customrecord_avaimsbatch',
							id: newIMSBatchId,
							values: {
								custrecord_ava_imsstatus: 'In Progress'
							},
							options: {
								enableSourcing: false,
								ignoreMandatoryFields: true
							}
						});
					}
					
					var itemNameContains = imsDetails.itemnamecontains;
					var startDate = imsDetails.startdate;
					var endDate = imsDetails.enddate;
					startDate += ' 12:00 am';
					endDate += ' 11:59 pm';
					
					var filtersArray = [];
					filtersArray.push(["isinactive", "is", "F"]);
					filtersArray.push("AND");
					filtersArray.push(["subtype", "noneof", "Purchase"]);
					filtersArray.push("AND");
					filtersArray.push(["type", "anyof", "Assembly", "GiftCert", "InvtPart", "Kit", "NonInvtPart", "OthCharge", "Service"]);
					filtersArray.push("AND");
					filtersArray.push(["name", "contains", itemNameContains]);
					filtersArray.push("AND");
					filtersArray.push(["created", "within", startDate, endDate]);
					
					var columnsArray = [];
					columnsArray.push(search.createColumn({
						name: "itemid",
						sort: search.Sort.ASC,
						label: "Name"
					}));
					if(runtime.isFeatureInEffect('BARCODES') == true){
						columnsArray.push(search.createColumn({
							name: "upccode"
						}));
					}
					columnsArray.push(search.createColumn({
						name: "salesdescription"
					}));
					columnsArray.push(search.createColumn({
						name: "custitem_ava_taxcode"
					}));
					
					customerSearchObject = getAllSearchResults({
						type: 'item',
						filters: filtersArray,
						columns: columnsArray
					});
				}
				
				log.debug('getInputData','customerSearchObject.length = '+customerSearchObject.length);
				return customerSearchObject;
			}
			catch(e){
				log.error('getInputData', 'ERROR NAME = ' + e.name + ', ERROR TYPE = ' + e.type + ', ERROR MESSAGE = ' + e.message);
			}
		}

		function map(context){
			try{
				var parameterDetails = runtime.getCurrentScript().getParameter({
					name: 'custscript_ava_imsbatchdetails'
				});
				var o_data = JSON.parse(context.value);
				itemToSyncCatalogue(o_data, parameterDetails);
			}
			catch(e){
				log.error('map', 'ERROR NAME = ' + e.name + ', ERROR TYPE = ' + e.type + ', ERROR MESSAGE = ' + e.message);
			}
		}
		
		function summarize(context){
			try{
				var parameterDetails = runtime.getCurrentScript().getParameter({
					name: 'custscript_ava_imsbatchdetails'
				});
				parameterDetails = JSON.parse(parameterDetails);
				
				if(parameterDetails.newbatchid){
					var completedBatchId = record.submitFields({
						type: 'customrecord_avaimsbatch',
						id: parameterDetails.newbatchid,
						values: {
							custrecord_ava_imsstatus: 'Completed'
						},
						options: {
							enableSourcing: false,
							ignoreMandatoryFields: true
						}
					});
					log.debug('summarize', 'Status Completed - completedBatchId = ' + completedBatchId);
				}
			}
			catch(e){
				log.error('summarize', 'ERROR NAME = ' + e.name + ', ERROR TYPE = ' + e.type + ', ERROR MESSAGE = ' + e.message);
			}
		}
		
		function getAllSearchResults(options){
			try{
				var stRecordType = options.type;
				var stSavedSearch = options.searchId;
				var arrFilters = options.filters;
				var arrColumns = options.columns;
				var arrResults = [];
				var count = 1000;
				var start = 0;
				var end = 1000;
				var searchObj = search.create({
					type: stRecordType,
					filters: arrFilters,
					columns: arrColumns
				});
				var rs = searchObj.run();
				while(count == 1000){
					var results = rs.getRange(start, end);
					arrResults = arrResults.concat(results);
					start = end;
					end += 1000;
					count = results.length;
				}
				return arrResults;
			}
			catch(e){
				log.error('getAllSearchResults', 'ERROR NAME = ' + e.name + ', ERROR TYPE = ' + e.type + ', ERROR MESSAGE = ' + e.message);
			}
		}
		
		function itemToSyncCatalogue(o_data, parameterDetails){
			try{
				var fieldLookUp = search.lookupFields({
					type: o_data.recordType,
					id: o_data.id,
					columns: ['subsidiary']
				});
				var itemSubsidiary = fieldLookUp.subsidiary[0].value;
				
				var avaConfigObjRec = ava_library.mainFunction('AVA_LoadValuesToGlobals', '');
				var defCompanyId = ava_library.mainFunction('AVA_GetDefaultCompanyCode', itemSubsidiary);
				var imsRecDetails = AVA_SearchIMSRecord(o_data.id);
				var responseItemId = '0';
				
				if(defCompanyId[1]){
					var details;
										
					if(avaConfigObjRec.AVA_AdditionalInfo3 != null && avaConfigObjRec.AVA_AdditionalInfo3.length > 0){
						details = avaConfigObjRec.AVA_AdditionalInfo3;
					}
					else if(avaConfigObjRec.AVA_AdditionalInfo != null && avaConfigObjRec.AVA_AdditionalInfo.length > 0){
						details = ava_commonFunction.mainFunction('AVA_General', (avaConfigObjRec.AVA_AccountValue + '+' + avaConfigObjRec.AVA_AdditionalInfo + '+' + avaConfigObjRec.AVA_AdditionalInfo1 + '+' + avaConfigObjRec.AVA_AdditionalInfo2));
					}
										
					var avaTaxDetails = ava_library.mainFunction('AVA_InitSignatureObject', avaConfigObjRec.AVA_ServiceUrl);
					
					if(imsRecDetails['imsitemid'] == '0'){
						var itemCodeNS = o_data.values.itemid;
						itemCodeNS = (itemCodeNS != null) ? itemCodeNS.substring(0, 50) : '';
						var filterDetails = "?$filter=itemCode eq " + "'" + itemCodeNS + "'";
						var retrieveItemsForCompany = new avaTaxDetails.retrieveItemsForCompany(details, defCompanyId[1], filterDetails, '', avaConfigObjRec.AVA_AdditionalInfo3);
						
						var responseDetails = https.get({
							url: retrieveItemsForCompany.url,
							body: retrieveItemsForCompany.data,
							headers: retrieveItemsForCompany.headers
						});
						
						if(responseDetails.code == 200){
							var responseBodyDetails = JSON.parse(responseDetails.body);
							
							if(responseBodyDetails.value.length > 0){
								imsRecDetails['imsitemid'] = responseBodyDetails.value[0].id;
							}
						}
					}
					
					var itemDetails = AVA_ItemRecordToSyncBody(o_data, avaTaxDetails, imsRecDetails['imsitemid']);
					var itemToSyncCatalogue = itemDetails.itemToSyncCatalogue(details, defCompanyId[1], itemDetails, avaConfigObjRec.AVA_AdditionalInfo3);
					
					var response = https.post({
						url: itemToSyncCatalogue.url,
						body: itemToSyncCatalogue.data,
						headers: itemToSyncCatalogue.headers
					});
					
					var errorMessage = '';
					var responseBody = JSON.parse(response.body);
					if(response.code == 200){
						responseItemId = responseBody.result[0].itemId;
						
						if(responseBody.result[0].itemEvent == 'Error'){
							errorMessage = responseBody.result[0].errors[0];
						}
					}
					else{
						if(response.body){
							errorMessage = responseBody.error.message;
						}
					}
					
					AVA_CreateUpdateIMSRecord(o_data, itemToSyncCatalogue.data, response.code, errorMessage, imsRecDetails['imsrecid'], parameterDetails, responseItemId);
				}
				else{
					AVA_CreateUpdateIMSRecord(o_data, '', '', 'Default company code not mapped on Avalara Configuration for this item subsidiary.', imsRecDetails['imsrecid'], parameterDetails, responseItemId);
				}
			}
			catch(e){
				log.error('itemToSyncCatalogue', 'ERROR NAME = ' + e.name + ', ERROR TYPE = ' + e.type + ', ERROR MESSAGE = ' + e.message);
			}
		}
		
		function AVA_ItemRecordToSyncBody(o_data, avaTaxDetails, imsItemId){
			try{
				var itemDetails = new avaTaxDetails.itemsCatalog();
				
				itemDetails.itemId = imsItemId;
				
				var itemId = o_data.values.itemid;
				itemDetails.itemCode = (itemId != null) ? itemId.substring(0, 50) : '';
				
				var itemDescription = o_data.values.salesdescription;
				itemDetails.description = (itemDescription != null) ? itemDescription.substring(0, 255) : '';
				
				var taxCodeValue = o_data.values.custitem_ava_taxcode;
				itemDetails.taxCode = taxCodeValue != null ? taxCodeValue.substring(0, 50) : '';
				
				if(runtime.isFeatureInEffect('BARCODES') == true){
					var itemUPC = o_data.values.upccode;
					itemDetails.upc = itemUPC != null ? itemUPC.substring(0, 50) : '';
				}
				
				itemDetails.source = 'NetSuite';
				return itemDetails;
			}
			catch(e){
				log.error('AVA_ItemRecordToSyncBody', 'ERROR NAME = ' + e.name + ', ERROR TYPE = ' + e.type + ', ERROR MESSAGE = ' + e.message);
			}
		}
		
		function AVA_CreateUpdateIMSRecord(o_data, requestData, responseCode, errorMessage, imsRecId, parameterDetails, responseItemId){
			try{
				if(imsRecId == 'null' || !imsRecId){
					var imsDetailsRecObj = record.create({
						type: 'customrecord_avaimsdetails'
					});
					imsDetailsRecObj.setValue({
						fieldId: 'custrecord_ava_imsitem',
						value: o_data.id
					});
					imsDetailsRecObj.setValue({
						fieldId: 'custrecord_ava_imsiteminternalid',
						value: o_data.id
					});
					parameterDetails = JSON.parse(parameterDetails);
					imsDetailsRecObj.setValue({
						fieldId: 'custrecord_ava_imsbatchdetails_ref',
						value: parameterDetails.newbatchid
					});
				}
				else{
					var imsDetailsRecObj = record.load({
						type: 'customrecord_avaimsdetails',
						id: imsRecId
					});
					
					var imsBatchDetailsRef = imsDetailsRecObj.getValue({
						fieldId: 'custrecord_ava_imsbatchdetails_ref'
					});
					parameterDetails = JSON.parse(parameterDetails);
					var newBatchId = parameterDetails.newbatchid;
					newBatchId = newBatchId.toString();
					imsBatchDetailsRef = imsBatchDetailsRef + '-' + newBatchId
					imsDetailsRecObj.setValue({
						fieldId: 'custrecord_ava_imsbatchdetails_ref',
						value: imsBatchDetailsRef
					});
				}
				
				var todayDate = new Date();
				var userId = runtime.getCurrentUser().id;
				
				if(responseItemId != '0'){
					imsDetailsRecObj.setValue({
						fieldId: 'custrecord_ava_imsitemid',
						value: responseItemId
					});
				}
				
				imsDetailsRecObj.setValue({
					fieldId: 'custrecord_ava_imsdate',
					value: todayDate
				});
				
				if(userId != null && userId > 0){
					imsDetailsRecObj.setValue({
						fieldId: 'custrecord_ava_imsuser',
						value: userId
					});
				}

				imsDetailsRecObj.setValue({
					fieldId: 'custrecord_ava_imsrequestdata',
					value: requestData
				});
				imsDetailsRecObj.setValue({
					fieldId: 'custrecord_ava_imsresponsecode',
					value: responseCode
				});
				imsDetailsRecObj.setValue({
					fieldId: 'custrecord_ava_imsresponseerror',
					value: errorMessage
				});
				if((responseCode == 200)&&(!errorMessage)){
					imsDetailsRecObj.setValue({
						fieldId: 'custrecord_ava_imsdetailsstatus',
						value: 'Success'
					});
				}
				else{
					imsDetailsRecObj.setValue({
						fieldId: 'custrecord_ava_imsdetailsstatus',
						value: 'Failed'
					});
				}
				
				var imsDetailsRecId = imsDetailsRecObj.save({
					enableSourcing: true,
					ignoreMandatoryFields: true
				});
			}
			catch(e){
				log.error('AVA_CreateUpdateIMSRecord', 'ERROR NAME = ' + e.name + ', ERROR TYPE = ' + e.type + ', ERROR MESSAGE = ' + e.message);
			}
		}
		
		function AVA_SearchIMSRecord(itemInternalId){
			var imsRecDetails = [];
			imsRecDetails['imsrecid'] = 'null';
			imsRecDetails['imsitemid'] = '0';
			
			try{
				var avaimsdetailsSearchObj = search.create({
				   type: "customrecord_avaimsdetails",
				   filters:
				   [
					  ["custrecord_ava_imsiteminternalid", "equalto", itemInternalId]
				   ],
				   columns:
				   [
					  search.createColumn({name: "custrecord_ava_imsitemid"})
				   ]
				});
				avaimsdetailsSearchObj.run().each(function(result){
				   imsRecDetails['imsrecid'] = result.id;
				   imsRecDetails['imsitemid'] = (result.getValue({name: "custrecord_ava_imsitemid"}) != '') ? result.getValue({name: "custrecord_ava_imsitemid"}) : '0';
				   return true;
				});
			}
			catch(e){
				log.error('AVA_SearchIMSRecord', 'ERROR NAME = ' + e.name + ', ERROR TYPE = ' + e.type + ', ERROR MESSAGE = ' + e.message);
			}
			
			return imsRecDetails;
		}
		
		return {
			getInputData: getInputData,
			map: map,
			summarize: summarize
		};
	}
);