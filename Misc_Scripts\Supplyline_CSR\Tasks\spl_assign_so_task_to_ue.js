/**
 *@NApiVersion 2.1
 *@NScriptType UserEventScript
 */

//@ts-ignore
define(["N/log", "N/search", "N/record"], function (log, search, record) {
	function afterSubmit(context) {
		if (context.type == "create" || context.type == "copy") {
			var salesOrder = context.newRecord;

			var salesOrderStatusObj = search.lookupFields({
				type: search.Type.SALES_ORDER,
				id: salesOrder.id,
				columns: ["status"],
			});

			var salesOrderStatus = salesOrderStatusObj.status[0].value;

			if (salesOrderStatus == "pendingApproval") {
				var salesOrderNameObj = search.lookupFields({
					type: search.Type.SALES_ORDER,
					id: salesOrder.id,
					columns: ["tranid"],
				});

				var salesOrderName = salesOrderNameObj.tranid;
				var customer = salesOrder.getValue("entity");
				var csrRep = getCsrRep();
				createTask();

				function getCsrRep() {
					var searchObj = search.create({
						type: "task",
						filters: [
							["assigned.role", "anyof", "1028"],
							"AND",
							["status", "noneof", "COMPLETE"], //UPDATE IF ADD STATUSES
						],
						columns: [
							search.createColumn({
								name: "assigned",
								summary: "GROUP",
								label: "Assigned To",
							}),
							search.createColumn({
								name: "title",
								summary: "COUNT",
								sort: search.Sort.ASC,
								label: "Task Title",
							}),
						],
					});

					var resultSet = searchObj.run();

					var results = resultSet.getRange({
						start: 0,
						end: 10, //PULLS RESULTS OF UP TO 10 REPS, CAN CHANGE
					});

					var resultsArr = [];

					results.forEach((result) => {
						var csrRep = result.getValue(resultSet.columns[0]);
						var taskCount = result.getValue(resultSet.columns[1]);
						resultsArr.push({
							csrRep: csrRep,
							taskCount: taskCount,
						});
					});

					resultsArr.sort((a, b) => a.taskCount - b.taskCount);

					var csrRep = resultsArr[0].csrRep;

					return csrRep;
				}

				function createTask() {
					var task = record.create({
						type: record.Type.TASK,
					});

					task.setValue({
						fieldId: "title",
						value: "Please approve " + salesOrderName,
					});

					task.setValue({
						fieldId: "assigned",
						value: 3145, //csrRep
					});

					task.setValue({
						fieldId: "company",
						value: customer,
					});

					task.setValue({
						fieldId: "transaction",
						value: salesOrder.id,
					});

					task.save();
				}
			}
		}
	}

	return {
		afterSubmit: afterSubmit,
	};
});
