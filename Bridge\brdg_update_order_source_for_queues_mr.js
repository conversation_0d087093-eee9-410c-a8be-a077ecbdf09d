/**
 * @NApiVersion 2.1
 * @NScriptType MapReduceScript
 * @NAmdConfig /SuiteScripts/config.json
 */

//@ts-ignore
define(["N/log", "N/error", "N/search", "N/query", "N/record"], function (
	log,
	error,
	search,
	query,
	record
) {
	var orderSourceDictionary = {
		"004f80c4-0438-4aa7-a2de-5cca899302e8": "WhatsApp Order",
		"0c1ac9c1-f4bd-4a81-9cd6-797d99be55aa": "Retail Order",
		"9140155a-76c2-40b1-9447-97bad91528ee": "Phone Order",
		"788947e4-3e2f-4848-afe4-3233d23cc17b": "Email Order",
		"5cf1594e-2354-4ba2-ac9b-740d9c178fd2": "DoorDash Order",
		"31f506b0-4549-4dd5-a7f4-8680bd6ec836": "WhatsApp Order - <PERSON><PERSON><PERSON>",
		"bdcc430a-02c1-4e90-aeab-5bd505fe15b6": "WhatsApp Order - Simcha Ingber",
		"8ac99481-1d0b-45bf-9f08-9c51e09a1470": "WhatsApp Order - Sruly Iowiniski",
		"7d153078-5770-40bd-bb75-9ce4e336e07f": "WhatsApp Order - Shea Berkowitz",
		"909c01ff-8c6f-4f76-860c-9fe952299782": "WhatsApp Order - Aryeh Kranz",
		"11e7ec50-6c5a-4dd8-bc3f-d76db9da927f": "WhatsApp Order",
		"d2e49788-b9da-4c4d-b005-e3321f6f369c": "Retail Order",
		"caa0fe15-fcf7-451a-8bcd-1fed09b0554c": "Phone Order",
		"058b8fea-dd73-4bec-8fe3-9278ad767bff": "Email Order",
		"c1bb73ea-1d20-4eab-9e84-69898744b052": "DoorDash Order",
		"80b1ac6f-5d7d-467f-8beb-06f7b1e3409d": "WhatsApp Order - Yehuda Stefansky",
		"7bf319a4-a009-4afa-9b72-b377a94cc90c": "WhatsApp Order - Simcha Ingber",
		"62725517-6d98-4c27-aa64-ea99f4e6c9c0": "WhatsApp Order - Naftali Bandman",
		"79fb502e-c56f-456e-be2b-f4c6903e6bf0": "WhatsApp Order",
		"eaf3794e-e539-419e-9fa4-0cf5c79d4868": "Retail Order",
		"7f400917-00b0-432b-881c-e0f08f7ac150": "Phone Order",
		"41cd9d93-837a-4f1a-9a89-278781fe432c": "Email Order",
		"9c6ca88e-1086-4c6d-a7c9-93ae78153fb5": "DoorDash Order",
		"fc6668aa-717c-404c-bded-d8c526801a68": "WhatsApp Order - Yehuda Stefansky",
		"8ff27c43-add2-47e1-ad81-ffc93b109b73": "WhatsApp Order - Simcha Ingber",
		"ec0737bb-9540-4a38-b47d-1d402213952a": "WhatsApp Order - Shuki Waldman",
		"5b7bc88d-966a-4aa6-95e0-6285e6edcebb": "WhatsApp Order",
		"40cac8da-5eef-4b69-a828-3677c600a117": "Retail Order",
		"9bbaf4ed-8a7b-4a59-9185-201e8213d21b": "Phone Order",
		"93ed6774-38b6-4c1e-9de7-e1d4d1d5b76a": "Email Order",
		"26bab28d-d289-435b-b437-72b87e334d95": "DoorDash Order",
		"59405c22-a5f9-469f-9ea1-41787c1fd3fd": "WhatsApp Order - Yehuda Stefansky",
		"ba7cc74e-3470-4e56-b38e-11864bab7959": "WhatsApp Order - Simcha Ingber",
		"7429141b-9785-486a-839e-bf721eac9e14": "WhatsApp Order - Meir Walden",
		"d8a0a5a1-c087-4c99-a36a-41ecb1059935": "WhatsApp Order",
		"522f8b57-8abe-45ad-bd64-da75f935d9d4": "Retail Order",
		"f134f2f5-c6ec-47e4-b03a-5b27ea21f35a": "Phone Order",
		"f6a2070f-4584-43b0-a90e-538ea44bb62a": "Email Order",
		"bb2c5458-2c1a-4f08-8d51-c6496fbef756": "DoorDash Order",
		"39cc04bc-ce4f-4384-b5cd-89606a9b5efc": "WhatsApp Order - Yehuda Stefansky",
		"f4a35eee-128f-4ea6-b870-5fc8591339c2": "WhatsApp Order - Simcha Ingber",
		"e004d093-a5fc-4ff5-94e0-4f184a05ea80": "WhatsApp Order - Dovi Hirsch",
		"bb2c5458-2c1a-4f08-8d51-c6496fbef756": "DoorDash Order",
		"5cdc12ee-dd76-44e5-91c0-156017e46158": "WhatsApp Order",
		"1e0a762a-196f-4435-9653-5a4dc9c5e0d9": "Retail Order",
		"01b1744f-4d0b-4a2d-8493-f06e77dbb61d": "Phone Order",
		"923b0323-a444-413f-9ca7-3c1260961198": "Email Order",
		"1acbaeaa-db10-4b13-836d-280ef0ef6555": "DoorDash Order",
		"6492d76e-f5ac-481b-952d-0b8ef2e1ce68": "WhatsApp Order - Yehuda Stefansky",
		"685b474c-d093-4e58-96a5-ad8705c51586": "WhatsApp Order - Simcha Ingber",
	};

	let orderSourceFieldIdDictionary = {
		thevineyardexpress: "1736796433769721856",
		thevineyardmadison: "1610344091843272704",
		thevineyardwestgate: "1706734809029918720",
		thevineyardsouth: "1735337187240448000",
		thevineyardbergenfield: "1736796250906456064",
	};

	let salesRepFieldIdDictionary = {
		thevineyardexpress: "1747659883390730240",
		thevineyardmadison: "1747659505330962432",
		thevineyardwestgate: "1747659649866678272",
		thevineyardsouth: "1747659791967485952",
		thevineyardbergenfield: "1747659959267782656",
	};

	function getInputData(context) {
		let sqlQuery = /*sql*/ `
        SELECT 
            log.recordid id,
            log.custrecord_in8_vend_raw_error error,
            log.custrecord_in8_vend_log_status status,
            log.custrecord_in8_vend_domain_prefix_ref store, 
            log.custrecord_in8_vend_log_payload payload,
			log.custrecord_in8_vend_log_datetime
        FROM
            customrecord_in8_vend_log log 
        WHERE
			1=1
			AND 
			(
				log.custrecord_in8_vend_log_status LIKE '%error%'
				OR log.custrecord_in8_vend_log_status IS NULL
			)
			AND log.custrecord_in8_vend_raw_error LIKE '%Item not found with Lightspeed X internal id%'
			AND log.custrecordin8_vend_log_tran_num IS NULL
			--AND log.recordid = 3004025
		  `;

		//log.custrecord_in8_vend_order_num IN('VRM1-75624','VRM2-68159','VRM3-32354','VRM3-33619','VRM3-33896','VRM3-33905','VRM3-34384','VRM3-34384','VRM3-34495','VRM3-34616','VRM5-1153','VRS1-57393','VYE2-2961')`;

		log.audit("SQL Query", sqlQuery);
		let results = query
			.runSuiteQL({
				query: sqlQuery,
				params: [],
			})
			.asMappedResults();

		return results;
	}

	function map(context) {
		var parsedJSON = JSON.parse(context.value);

		try {
			let dataObj = { data: [] };

			let orderSourceId;
			let salesRepName;

			let {
				id: recordInternalId,
				error: errorText,
				store,
				payload,
			} = parsedJSON;

			let parsedPayload = JSON.parse(payload);

			//Pull out order source id from error payload
			const regex =
				/(?<=Error: Item not found with Lightspeed X internal id: ).*?(?= SKU: )/;
			orderSourceId =
				errorText && errorText.match(regex) && errorText.match(regex)[0];

			if (!orderSourceId) {
				//Search for order source in list of items instead (information isn't in the error payload)
				let orderSourceItemobj = parsedPayload.line_items.find((item) => {
					return orderSourceDictionary[item.product_id];
				});

				orderSourceId = orderSourceItemobj?.["product_id"];
			}

			if (!orderSourceId || orderSourceDictionary[orderSourceId] == null) {
				log.debug(recordInternalId, "No order source id found in payload");
			}
			//Add order source to custom fields payload
			let orderSourceObj = {
				definition_id: salesRepFieldIdDictionary[store],
				name: "order_source",
				title: "Order Source",
				type: "string",
				string_value: orderSourceId,
			};

			dataObj.data.push(orderSourceObj);

			//Add sales rep to custom fields payload
			if (
				orderSourceDictionary[orderSourceId] &&
				orderSourceDictionary[orderSourceId].includes("WhatsApp Order - ")
			) {
				salesRepName = orderSourceDictionary[orderSourceId].replace(
					"WhatsApp Order - ",
					""
				);

				let salesRepObj = {
					definition_id: orderSourceFieldIdDictionary[store],
					name: "sales_rep",
					title: "Sales Rep",
					type: "string",
					string_value: salesRepName,
				};

				dataObj.data.push(salesRepObj);
			}

			let jsonDataObj = JSON.stringify(dataObj);

			//Remove order source object from main payload
			parsedPayload.line_items = parsedPayload.line_items.filter((item) => {
				return item.product_id !== orderSourceId;
			});

			//Set values on queue record
			const updatedPayload = JSON.stringify(parsedPayload);

			const netSuiteRecord = record.load({
				type: "customrecord_in8_vend_log",
				id: recordInternalId,
			});

			const fieldsToUpdate = [
				{ fieldId: "custrecord_in8_vend_tran_custom", value: jsonDataObj },
				{ fieldId: "custrecord_in8_vend_log_status", value: "" },
				{ fieldId: "custrecord_in8_vend_log_payload", value: updatedPayload },
				{ fieldId: "custrecord_in8_vend_raw_error", value: "" },
			];

			fieldsToUpdate.forEach((field) => {
				netSuiteRecord.setValue(field);
			});

			netSuiteRecord.save();

			context.write(recordInternalId, recordInternalId);
		} catch (e) {
			throw error.create({
				name: "Error Updating Record",
				message: `Error: ${e}`,
			});
		}
	}

	function summarize(context) {
		var recordsProcessedSuccessfullyText =
			getRecordsProcessedSuccessfully(context);
		var errorMessagesText = getErrorMessages(context);
		logResults();
		function getRecordsProcessedSuccessfully(summary) {
			let summaryText = ``;
			summary.output.iterator().each(function (key, value) {
				summaryText += `${key}, `;
				return true;
			});
			return summaryText;
		}
		function getErrorMessages(summary) {
			let errorText = ``;
			summary.mapSummary.errors.iterator().each(function (key, value) {
				var errorMessage = JSON.parse(value).message;
				errorText += `${errorMessage},
		    `;
				return true;
			});
			return errorText;
		}
		function logResults() {
			if (recordsProcessedSuccessfullyText) {
				log.audit({
					title: `Records Processed Successfully`,
					details: recordsProcessedSuccessfullyText,
				});
			}
			if (errorMessagesText) {
				log.error({
					title: "Error Log",
					details: errorMessagesText,
				});
			}
		}
	}

	return {
		getInputData,
		map,
		summarize,
	};
});
