/**
 * @NApiVersion 2.1
 * @NScriptType MapReduceScript
 * @NAmdConfig /SuiteScripts/config.json
 */

//@ts-ignore
define([
	"require",
	"GetEdiIntegrationsLib",
	"Get855InternalIdsLib",
	"GetEdiPartnerValuesLib",
	"ProcessOutgoingEdiFileEmailLib",
	"PushEdiEmailInfoToDBLib",
	"./../../../EDI/Libraries/Process_EDI_File/spl_process_outgoing_855_lib",
	"../../../Classes/vlmd_custom_error_object",
	"../../../Classes/vlmd_mr_summary_handling",
	"N/log",
], function (
	require,
	getEdiIntegrationsLib,
	getPoAckInternalIdsLib,
	getEdiPartnerValuesLib,
	processOutgoingEdiFileEmailLib,
	pushEdiEmailInfoToDBLib
) {
	/**@type {import ('./../../../EDI/Libraries/Process_EDI_File/spl_process_outgoing_855_lib')} */
	const process855Lib = require("./../../../EDI/Libraries/Process_EDI_File/spl_process_outgoing_855_lib");

	/** @type {import("../../../Classes/vlmd_custom_error_object").CustomErrorObject} */
	const CustomErrorObject = require("../../../Classes/vlmd_custom_error_object");

	const dataObj = {
		prodGuidBool: true,
		prodDirectoryBool: true,
		decodeContent: true,
		prodGUID: "fd7c83ddba9f47988d2f897fecfe066b",
		sandboxGUID: "********************************",
		prodDirectory: "/users/Agora/OUT/855",
		testDirectory: "",
		documentType: "Puchase Order Acknowledgement",
		documentTypeId: 7,
		purchasingSoftware: "Agora",
		purchasingSoftwareId: 6,
		pushEmailToDB: true,
	};

	function getInputData(context) {
		const customErrorObject = new CustomErrorObject();

		try {
			const getInputDataResultsArr = [];

			const customerFoldersArr =
				getEdiIntegrationsLib.getInfoForOutgoingDocument(
					dataObj.purchasingSoftwareId,
					dataObj.documentTypeId
				);

			customerFoldersArr.forEach((customer) => {
				dataObj.customerName = customer.customerName;

				const salesOrderArr = getPoAckInternalIdsLib.getInternalIds(
					customer.customerId,
					dataObj.purchasingSoftwareId
				);

				if (salesOrderArr.errorLog.length > 0) {
					throw customErrorObject.updateError({
						errorType: customErrorObject.ErrorTypes.NO_VALUE_RETURNED,
						summary: "ERROR_GETTING_INTERNAL_IDS",
						details: `Error getting sales order internal ids for ${customer.customerName}: ${salesOrderArr.errorLog}`,
					});
				} else {
					if (salesOrderArr.internalIds.length > 0) {
						salesOrderArr.internalIds.forEach((salesOrderInternalId) => {
							getInputDataResultsArr.push({
								customerName: customer.customerName,
								customerId: customer.customerId,
								accountNumber: customer.accountNumber,
								salesOrderInternalId: salesOrderInternalId,
								integrationFolder: customer.integrationFolder,
							});
						});
					}
				}
			});

			return getInputDataResultsArr;
		} catch (err) {
			customErrorObject.throwError({
				summaryText: "GET_INPUT_DATA_ERROR",
				error: err,
			});
		}
	}

	function map(context) {
		const customErrorObject = new CustomErrorObject();

		const parsedResult = JSON.parse(context.value);
		const {
			customerName,
			customerId,
			accountNumber,
			salesOrderInternalId,
			integrationFolder,
		} = parsedResult;

		try {
			const partnerValues =
				getEdiPartnerValuesLib.getAgoraValues(integrationFolder);

			if (!partnerValues) {
				throw customErrorObject.updateError({
					errorType: customErrorObject.ErrorTypes.NO_VALUE_RETURNED,
					summary: "ERROR_GETTING_PARTNER_VALUES",
					details: `EDI partner values not gotten for ${accountNumber} - ${customerName}`,
				});
			}

			const processResultsObj = process855Lib.process855(
				salesOrderInternalId,
				customerName,
				customerId,
				accountNumber,
				partnerValues,
				dataObj,
				integrationFolder,
				customErrorObject
			);

			if (!processResultsObj.processedSuccessfully) {
				throw customErrorObject.updateError({
					errorType: customErrorObject.ErrorTypes.UNHANDLED_ERROR,
					summary: "ERROR_PROCESSING_855",
					details: `Sales Order: ${processResultsObj.salesOrderNumber}, Purchase Order: ${processResultsObj.purchaseOrderNumber}, Error: ${processResultsObj.errorMessage}`,
				});
			}

			context.write(customerName, processResultsObj.salesOrderNumber);
		} catch (err) {
			customErrorObject.throwError({
				summaryText: `MAP_ERROR`,
				error: err,
				recordId: salesOrderInternalId,
				recordType: "SALES_ORDER",
				errorWillBeGrouped: true,
			});
		}
	}

	function reduce(context) {
		context.write({
			key: context.key,
			value: context.values,
		});
	}

	function summarize(context) {
		const customErrorObject = new CustomErrorObject();
		try {
			const StageHandling = require("../../../Classes/vlmd_mr_summary_handling");
			const stageHandling = new StageHandling(context);

			stageHandling.printScriptProcessingSummary();
			stageHandling.printRecordsProcessed();
			stageHandling.printErrors({
				groupErrors: true,
			});

			const sentEmailObj = processOutgoingEdiFileEmailLib.processEmail(
				context,
				[],
				dataObj.purchasingSoftware,
				dataObj.documentType
			);

			if (dataObj.pushEmailToDB) {
				try {
					pushEdiEmailInfoToDBLib.pushEdiEmailInfoToDB(dataObj, sentEmailObj);
				} catch (e) {
					throw `Error pushing EDI email to database: ${e}`;
				}
			}
		} catch (err) {
			customErrorObject.throwError({
				summaryText: `SUMMARIZE_ERROR`,
				error: err,
			});
		}
	}

	return {
		getInputData,
		map,
		reduce,
		summarize,
	};
});
