/**
 * @description Provides the user with updated RIP information after a vendor bill with RIP items is saved
 * Called by the brdg_rip_create_records_ue script.
 * 
 * @NApiVersion 2.1
 * @NScriptType Suitelet
 * 
 * <AUTHOR>
 * @module brdg_redirect_rip_confirmation_sl
 */

//@ts-ignore
define(["N/log", "N/runtime", "N/url", "N/ui/serverWidget"], function (
	log,
	runtime,
	url,
	serverWidgetModule
) {
	function onRequest(context) {
		if(context.request.parameters.newAccrualRecordsCreated){
		const updatedRIP =
			context.request.parameters.newAccrualRecordsCreated.toString();
		const updatedRIPArr = updatedRIP.split(",");
		const response = context.response;
		const environment = (runtime.envType == "SANDBOX")? '5802576-sb2' : '5802576';
		const vendorBillId = context.request.parameters.currentVendorBill;
		const vendorCreditCreated = context.request.parameters.newVendorCredit;
		const form = serverWidgetModule.createForm({
			title: "New RIP Accrual Records Created",
		});
		let num = 1;
		updatedRIPArr.forEach((ripUpdate) => {
			const updatedRipInfoField = form.addField({
				id: "custpage_htmlfield" + num,
				type: serverWidgetModule.FieldType.INLINEHTML,
				label: "None",
			});
			const updatedRipLinkField = form.addField({
				id: "custpage_linkfield" + num,
				type: serverWidgetModule.FieldType.INLINEHTML,
				label: "URL",
			});

			updatedRipInfoField.defaultValue = `<p style="font-size: 20px; color: navy">${num}:<p/>`;
			updatedRipLinkField.defaultValue = `<a href = "https://5802576-sb2.app.netsuite.com/app/common/custom/custrecordentry.nl?rectype=2309&id=${ripUpdate}" target= "_parent" style="font-size: 20px; color: green">View New Accrual Record, id ${ripUpdate}</a>`;
			num = num + 1;
		});


		const vendorBillViewURL = `https://${environment}.app.netsuite.com/app/accounting/transactions/vendbill.nl?id=${vendorBillId}`;
		const vendorBillEditURL = `https://${environment}.app.netsuite.com/app/accounting/transactions/vendbill.nl?id=${vendorBillId}&e=T`;
		const vendorCreditURL = `https://${environment}.app.netsuite.com/app/accounting/transactions/vendcred.nl?id=${vendorCreditCreated}`;

		//#region Add Buttons
		const openIrViewMode =
			"require(['N/https'], function(https) { window.open('" +
			vendorBillViewURL +
			"');});";
		const openIrEditMode =
			"require(['N/https'], function(https) { window.open('" +
			vendorBillEditURL +
			"');});";
		const openVendorCredit =
			"require(['N/https'], function(https) { window.open('" +
			vendorCreditURL +
			"');});";

		form.addButton({
			id: "backToIR",
			label: "View Vendor Bill",
			functionName: openIrViewMode,
		});
		form.addButton({
			id: "editIR",
			label: "Edit Vendor Bill",
			functionName: openIrEditMode,
		});
		form.addButton({
			id: "viewCredit",
			label: "View Vendor Credit",
			functionName: openVendorCredit,
		})

		//#endregion
		
		response.writePage(form);
	}
	else{
		throw 'You cannot access this suitelet alone. It needs to be redirected from an applicable vendor bill!'
	}
	}
	return {
		onRequest: onRequest,
	};
});
