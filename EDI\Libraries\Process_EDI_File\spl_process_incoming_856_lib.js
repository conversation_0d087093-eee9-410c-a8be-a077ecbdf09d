/**
 * @NApiVersion 2.1
 * @description Process incoming 856 (tracking) information sent by vendor
 * Called By: Medline, Drive, and Medacure Process Incoming 856
 */

//@ts-ignore
define([
  "N/log",
  "N/error",
  "N/search",
  "Parse856Lib",
  "RecordModuleHelperLib",
  "CreateItemFulfillmentLib",
  "GetCarrierIdByScacLib",
  "MoveFileLib",
], function (
  log,
  error,
  search,
  parseAsnLib,
  recordHelperLib,
  createItemFulfillmentLib,
  getCarrierIdByScacLib,
  moveFileLib
) {
  function processASN(ediDataObj, partnerValues, ediFile) {
    var helperFunctions = (function () {
      function queryForPurchaseOrderinNS(purchaseOrderNumber) {
        try {
          var searchObj = search.create({
            type: "purchaseorder",
            filters: [
              ["mainline", "is", "T"],
              "AND",
              ["type", "anyof", "PurchOrd"],
              "AND",
              ["numbertext", "is", purchaseOrderNumber],
            ],
          });

          var searchResultsObj = searchObj.run().getRange({
            start: 0,
            end: 1,
          })[0];

          if (!searchResultsObj) {
            throw `No corresponding PO found in NS for ${purchaseOrderNumber}`;
          }

          return searchResultsObj.id;
        } catch (e) {
          throw `Error Getting PO Int ID: ${e}`;
        }
      }

      function loadPurchaseOrder(purchaseOrderInternalId) {
        const poRecord = recordHelperLib.loadRecord(
          purchaseOrderInternalId,
          "PURCHASE_ORDER"
        );

        if (!poRecord) {
          throw `No PO record returned for ${purchaseOrderInternalId}`;
        }

        return poRecord;
      }

      function getCustomerId(salesOrderInternalId) {
        return search.lookupFields({
          type: search.Type.SALES_ORDER,
          id: salesOrderInternalId,
          columns: ["entity"],
        }).entity;
      }

      function getCarrierByScacCode(scac) {
        var getCarrierObj = getCarrierIdByScacLib.getCarrier(scac);

        if (
          !getCarrierObj ||
          getCarrierObj.errorLog.length > 0 ||
          getCarrierObj.id
        ) {
          throw getCarrierObj.errorLog ?? `Error Getting Carrier`;
        }

        return getCarrierObj.carrierId;
      }

      function validateHasItemsToFulfil(asnItems) {
        const itemsInNetSuiteArr = asnItems.filter((item) => item.internalId);
        const itemsNotInNetSuiteArr = asnItems.filter(
          (item) => !item.internalId
        );

        if (itemsNotInNetSuiteArr.length > 0) {
          let itemsNotInNetSuiteStr = itemsNotInNetSuiteArr
            .map((item) => item.itemName)
            .join(", ");

          processingLog.push(
            `These items sent by the vendor don't match any items in NetSuite and weren't mark fulfilled: ${itemsNotInNetSuiteStr}`
          );
        }

        return itemsInNetSuiteArr.length > 0;
      }

      function createItemFulfillment(package, trackingObj) {
        try {
          const { trackingNumber, items } = package;
          const {
            netSuiteCarrierId,
            transactionControlNumber,
            salesOrderInternalId,
          } = trackingObj;

          const {
            errorLog: createIfErrorLog,
            processingLog: createIfProcessingLog,
            itemFulfillmentInternalId: newRecordId,
            itemFulfillmentName: newRecordName,
          } = createItemFulfillmentLib.createItemFulfillment(
            items,
            netSuiteCarrierId,
            trackingNumber,
            transactionControlNumber,
            salesOrderInternalId
          );

          if (createIfErrorLog.length > 0) {
            errorLog = errorLog.concat(errorLog, createIfErrorLog);
          }

          if (createIfProcessingLog.length > 0) {
            processingLog = processingLog.concat(
              processingLog,
              createIfProcessingLog
            );
          }

          if (newRecordId) {
            itemFulfillmentInternalId = newRecordId;
            itemFulfillmentName = search.lookupFields({
              type: search.Type.ITEM_FULFILLMENT,
              id: newRecordId,
              columns: ["tranid"],
            }).tranid;
          }
        } catch (e) {
          errorLog.push(e);
        }
      }

      function moveFile() {
        try {
          var moveErrorLog = moveFileLib.moveFile(
            ediDataObj,
            fileName,
            errorLog.length <= 0
          );

          if (moveErrorLog.length <= 0) {
            return true;
          }

          errorLog.push(moveErrorLog);
        } catch (e) {
          throw error.create({
            name: "Error Moving File",
            message: e,
          });
        }
      }

      return {
        queryForPurchaseOrderinNS,
        loadPurchaseOrder,
        getCustomerId,
        getCarrierByScacCode,
        validateHasItemsToFulfil,
        createItemFulfillment,
        moveFile,
      };
    })();

    let processingLog = [];
    let errorLog = [];
    let itemFulfillmentInternalId = null;
    let itemFulfillmentName = "";
    let isTrackingForDropShipOrder = false;
    let subsidiary;

    const { content: fileContent, fileName } = ediFile;

    try {
      let { asnObj: trackingObj, errorLog: parsingErrorLog } =
        parseAsnLib.parse856(fileContent, partnerValues);

      if (parsingErrorLog.length > 0) {
        throw `File not parsed correctly:  ${parsingErrorLog}`;
      }

      trackingObj.netSuiteCarrierId = helperFunctions.getCarrierByScacCode(
        trackingObj.carrier
      );

      trackingObj.purchaseOrderInternalId =
        helperFunctions.queryForPurchaseOrderinNS(trackingObj.poNumber);

      let purchaseOrderRecord = helperFunctions.loadPurchaseOrder(
        trackingObj.purchaseOrderInternalId
      );

      trackingObj.purchaseOrderName = purchaseOrderRecord.getValue("tranid");

      trackingObj.salesOrderInternalId =
        purchaseOrderRecord.getValue("createdfrom");

      trackingObj.isTrackingForDropShipOrder =
        trackingObj.salesOrderInternalId !== null;

      isTrackingForDropShipOrder = trackingObj.isTrackingForDropShipOrder;
      subsidiary = purchaseOrderRecord.getText("subsidiary");

      if (trackingObj.isTrackingForDropShipOrder) {
        //Create IF record for SO, will mark PO received as well.
        trackingObj.salesOrderName = purchaseOrderRecord.getText("createdfrom");
        trackingObj.customerId = helperFunctions.getCustomerId(
          trackingObj.salesOrderInternalId
        );

        trackingObj.packages.forEach((package) => {
          var validItemsToFulfill = helperFunctions.validateHasItemsToFulfil(
            package.items
          );

          if (!validItemsToFulfill) {
            errorLog.push("No valid items to create fulfillment with.");

            return;
          }
          helperFunctions.createItemFulfillment(package, trackingObj);
        });
      } else {
        //Is tracking for warehouse order -> add tracking to PO
        try {
          const trackingNumbersText = trackingObj.packages
            .map((package) => package.trackingNumber)
            .join(", ");
          purchaseOrderRecord.setValue("trackingnumbers", trackingNumbersText);

          purchaseOrderRecord.save();
        } catch (e) {
          errorLog.push(`Error Setting Tracking on PO: ${e.message}`);
        }
      }

      //NOTE: Medacure files are moved within the MR script file
      let fileMovedSuccessfully =
        ediDataObj.purchasingSoftwareId != 13 && helperFunctions.moveFile();

      return {
        errorLog,
        processingLog,
        itemFulfillmentInternalId,
        itemFulfillmentName,
        isTrackingForDropShipOrder,
        subsidiary,
        fileMovedSuccessfully,
      };
    } catch (e) {
      errorLog.push(`Error Processing Tracking: ${e}`);
    }

    return {
      errorLog,
      processingLog,
      itemFulfillmentInternalId,
      itemFulfillmentName,
      isTrackingForDropShipOrder,
      subsidiary,
      fileMovedSuccessfully: false,
    };
  }

  return {
    processASN,
  };
});
