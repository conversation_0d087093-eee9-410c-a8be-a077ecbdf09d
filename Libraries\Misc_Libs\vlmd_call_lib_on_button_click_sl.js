/**
 * @NApiVersion 2.1
 * @NScriptType Suitelet
 * @NAmdConfig /SuiteScripts/config.json
 * <AUTHOR>
 */

//@ts-ignore
define([
	"require",
	"N/log",
	"N/redirect",
	"N/record",
	"CreateRipBillCreditLib",
	"UpdateGrossProfitValuesOnInvoiceLib",
	"CalculateCogsPerLineLib",
	"../../Classes/vlmd_custom_error_object",
], function (
	require,
	log,
	redirect,
	record,
	createBillCreditLib,
	updateGrossProfitValuesOnInvoicesLib,
	calculateCogsPerLineLib
) {
	/** @type {import("../../Classes/vlmd_custom_error_object").CustomErrorObject} */

	const CustomErrorObject = require("../../Classes/vlmd_custom_error_object");

	return {
		onRequest: function (context) {
			const request = context.request;

			const recordModuleMappingObj = {
				cashsale: "CASH_SALE",
				custinvc: "INVOICE",
				custcred: "CREDIT_MEMO",
				creditmemo: "CREDIT_MEMO",
				invoice: "INVOICE",
				RtnAuth: "RETURN_AUTHORIZATION",
			};

			const customErrorObject = new CustomErrorObject();

			function redirectToRecord(recordType, transactionId) {
				const recordModuleType = recordModuleMappingObj[recordType];

				return redirect.toRecord({
					type: record.Type[recordModuleType],
					id: transactionId,
				});
			}

			if (request.method === "GET") {
				const requestparam = request.parameters;
				const functionName = requestparam.functionName;
				const transactionId = requestparam.transactionId;

				if (functionName == "createBillCredit") {
					createBillCreditLib.createBillCredit(transactionId);

					redirect.toRecord({
						type: record.Type.VENDOR_BILL,
						id: transactionId,
					});
				}

				if (functionName == "calculateGrossProfit") {
					const recordType = requestparam.recordType;
					const createdFromRecordType = requestparam.createdFromRecordType;

					try {
						const errorLogObj =
							updateGrossProfitValuesOnInvoicesLib.updateGrossProfitValues(
								transactionId,
								recordModuleMappingObj[recordType],
								recordModuleMappingObj[createdFromRecordType]
							);

						if (errorLogObj.errorLog.length > 0) {
							throw errorLogObj.errorLog;
						}
					} catch (e) {
						log.error(
							`ERROR_UPDATING_GROSS_PROFIT_VALUES_FOR_${transactionId}`,
							e
						);
					}

					redirectToRecord(recordType, transactionId);
				}

				if (functionName == "calculateCogsOnItems") {
					try {
						const recordType = requestparam.recordType;

						if (!recordType) {
							throw customErrorObject.updateError({
								errorType: customErrorObject.ErrorTypes.MISSING_PARAM,
								summary: "MISSING_RECORD_TYPE",
								details: "No record type was passed in.",
							});
						}

						calculateCogsPerLineLib.setAllCogsPerItem(
							transactionId,
							recordType,
							customErrorObject
						);

						redirectToRecord(recordType, transactionId);
					} catch (err) {
						customErrorObject.throwError({
							summaryText: "ERROR_GETTING_COGS",
							err,
						});
					}
				}
			}
		},
	};
});
