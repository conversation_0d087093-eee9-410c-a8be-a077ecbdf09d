/**
 * @description Library containing function that creates a vendor credit to reflect rebates associated with inventory items
 * 
 * @NApiVersion 2.1
 * 
 * <AUTHOR>
 * @module brdg_create_bill_credit_lib
 * 
 */

define([
	"N/log",
	"N/query",
	"N/format",
	"RecordModuleHelperLib",
	"BridgeHelperFunctionsLib"
],
function (
	log,
	query,
	format,
	recordHelperLib,
	bridgeHelperFunctionsLib
) {
	function createBillCredit(vendorBillId, vendorBill) {
		const helperFunctions = (function () {

			/**
			 * Determine the applicable rebate amount for the item
			 * Vendor bill date should be within the rebate agreement date range
			 *
			 * @typedef ItemObject
			 * @property {string} item Internal ID
			 * @property {string} location Location ID
			 * @property {number} quantity Quantity
			 *
			 * @param {ItemObject} itemObj Inventory item's internalid, quantity & location
			 * @param {Date} billDate Date assigned to the Vendor Bill
			 * @returns {number} Amount of rebate applicable to the item
			 */
			function getApplicableRip(itemObj, billDate) {
				const formattedBillDate = format.format({
					value:billDate,
					type:format.Type.DATE
				});

				const rebateSqlQuery = `
					SELECT custrecord_dollar_off
					FROM customrecord_brdg_rebates_accrual_per_po po
					JOIN customrecord_brdg_rebate_item_accrual ia on po.custrecord_item_accrual_record = ia.id
					JOIN CUSTOMRECORD_REBATE_AGREEMENT_DETAIL ad on ia.custrecord_rebate_agreement_detail = ad.id
					JOIN customrecord_rebate_agreement  ra on ad.custrecord_rebate_parent = ra.id
					JOIN customrecord_rebate_tier_level tl on po.custrecord_tier_level = tl.id
					WHERE custrecord_item = '${itemObj.item}'
						and TO_DATE('${formattedBillDate}','MM/DD/YYYY') between ra.custrecord_rebate_start_date and custrecord_rebate_end_date
				`;

				const resultSet = query.runSuiteQL({
					query: rebateSqlQuery
				});

				const offValue = resultSet
					&& resultSet.results
					&& Array.isArray(resultSet.results)
					&& resultSet.results.length > 0
					&& resultSet.results[0].values
					&& Array.isArray(resultSet.results[0].values)
					&& resultSet.results[0].values.length > 0
					&& resultSet.results[0].values[0];

				return offValue;
			}

			return { getApplicableRip };
		})();

		try {
			if (!vendorBill) {
				vendorBill = recordHelperLib.loadRecord(vendorBillId, "VENDOR_BILL");
			}

			const isForInventoryItems = true;
			const accountsPayableAccountId = 112;
			const approvedStatusId = "2";
			const approvalStatus = vendorBill.getValue("approvalstatus");

			if (approvalStatus !== approvedStatusId) {
				return [];
			}

			const billDate = vendorBill.getValue("trandate");
			const billLocation = vendorBill.getValue("location");

			const itemObjsArr = recordHelperLib.getItemSublistObjsArr(
				vendorBill,
				["item", "quantity", "location"],
				isForInventoryItems
			);

			const itemSublistArr = itemObjsArr.reduce((itemsArr, itemObj) => {
				const {item, quantity, location} = itemObj;

				if (!item || !quantity || !(location || billLocation)) {
					return [];
				}

				const rebateAmount = helperFunctions.getApplicableRip(
					itemObj,
					billDate
				);

				if (rebateAmount) {
					itemsArr.push({
						item,
						quantity,
						location: location || billLocation,
						rate: rebateAmount
					});
				}

				return itemsArr;
			}, []);

			if (itemSublistArr.length > 0) {
				const vendorCreditRecord = recordHelperLib.createRecord("VENDOR_CREDIT");
				const vendorCreditObj = {
					entity: vendorBill.getValue("entity"),
					account: accountsPayableAccountId,
					trandate: billDate,
					subsidiary: vendorBill.getValue("subsidiary")
				};

				recordHelperLib.setBodyValues(vendorCreditObj, vendorCreditRecord);
				recordHelperLib.setSublistValues(
					itemSublistArr,
					"item",
					vendorCreditRecord
				);

				return recordHelperLib.saveRecord(vendorCreditRecord);
			}
		} catch (error) {
			log.error(error.name, error.message);
		}
	}

	/**
	 * Retrieve the SuiteQL string to query the bills that do not have Bill Credits yet
	 *  - Filter for Vendor Bills that were created on the day the MR deployment will run
	 *  - Ensure that the Bill is approved
	 * Referenced by brdg_rip_create_bill_credit_mr
	 *
	 * @returns {string} SuiteQL string
	 */
	function getBillsToProcessQuery() {
		const approvedStatusId = 2;
		const bridgeSubsidiaries = bridgeHelperFunctionsLib.getSuiteqlBridgeSubsidiaryList(
			bridgeHelperFunctionsLib.getBridgeSubsidiaries()
		);

		return `
			SELECT transaction.id
			FROM transaction, transactionLine
			WHERE type = 'VendBill'
				AND custbody_spl_brdg_rip_bill_credit IS NULL
				AND TO_CHAR (SYSDATE, 'MM/DD/YYYY') = TO_CHAR (createddate, 'MM/DD/YYYY')
				AND approvalstatus = ${approvedStatusId}
				AND transaction.id = transactionLine.transaction
				AND transactionLine.mainline = 'T'
				AND transactionLine.subsidiary in ${bridgeSubsidiaries}
			ORDER BY transaction.id DESC
		`;
	}

	return {
		createBillCredit,
		getBillsToProcessQuery
	};
});
