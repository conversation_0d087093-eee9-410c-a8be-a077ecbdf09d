/**
 * @NApiVersion 2.x
 * @NScriptType ClientScript
 * @NAmdConfig /SuiteScripts/config.json
 */

//@ts-ignore
define(["N/log", "N/currentRecord", "N/search", "Numeral"], function (
	log,
	currentRecord,
	search,
	numeral
) {
	function validateLine(context) {
		var salesOrder = context.currentRecord;

		var subsidiary = salesOrder.getValue("subsidiary");
		if (subsidiary != 1) {
			//Supplyline
			return;
		}

		console.log("Confirm Item Rate");
		if (context.sublistId == "item") {
			var itemInternalId = salesOrder.getCurrentSublistValue({
				sublistId: "item",
				fieldId: "item",
			});

			var dontUseItemObj = search.lookupFields({
				type: search.Type.SALES_ORDER,
				id: itemInternalId,
				columns: ["displayname", "item"], //['custitem_spl_dont_use_item_in_new_tran'],
			});

			var item = dontUseItemObj.item;
			var displayName = dontUseItemObj.displayname;
			// var dontUseItem = dontUseItem['custitem_spl_dont_use_item_in_new_tran']
			console.log("new");
			console.log("Item int id " + itemInternalId);
			// console.log('dont use item obj ' + JSON.stringify(dontUseItemObj));
			//console.log('dont use item ' + dontUseItem);
			console.log("display name " /* + displayName*/);
			console.log("item " + item);
		}

		return true;
	}

	//function saveRecord(context) {
	//    var salesOrder = context.currentRecord;
	//    var createdFrom = salesOrder.getValue('createdfrom');
	//    var ediControlNumber = salesOrder.getValue('custbody_spl_edi_trans_cntrl_num');
	//    console.log('Created From ' + createdFrom)
	//    console.log('EDI #' + ediControlNumber);
	//    if (createdFrom || ediControlNumber) {
	//        console.log('true');
	//        var itemsToAlert = validateItems();
	//        console.log('Items missing info ' + itemsToAlert)
	//        if (itemsToAlert.length > 0) {
	//            displayAlert();
	//        }
	//    }

	//    return true;

	//    function validateItems() {
	//        var arr = [];
	//        var salesOrderLineCount = getSalesOrderLineCount();
	//        validateLines();

	//        return arr;

	//        function getSalesOrderLineCount() {
	//            return salesOrder.getLineCount({
	//                sublistId: 'item'
	//            });
	//        };

	//        function validateLines() {
	//            for (var x = 0; x < salesOrderLineCount; x++) {
	//                var rate = salesOrder.getSublistText({
	//                    sublistId: 'item',
	//                    fieldId: 'rate',
	//                    line: x
	//                });

	//                var itemName = salesOrder.getSublistText({
	//                    sublistId: 'item',
	//                    fieldId: 'item',
	//                    line: x
	//                });

	//                var searchObj = search.create({
	//                    type: "item",
	//                    filters:
	//                        [
	//                            ["isinactive", "is", "F"],
	//                            "AND",
	//                            ["name", "is", itemName]
	//                        ],
	//                    columns:
	//                        [
	//                            search.createColumn({ name: "lastpurchaseprice", label: "Last Purchase Price" })
	//                        ]
	//                });
	//                var itemObj = searchObj.run().getRange({
	//                    start: 0,
	//                    end: 1000
	//                })[0];

	//                var lastPurchasePrice = itemObj.getValue('lastpurchaseprice');

	//                console.log('Item Name ' + itemName);
	//                console.log('Rate ' + rate);
	//                console.log('Last Purchase Price ' + lastPurchasePrice);

	//                if (parseInt(rate, 10) < parseInt(lastPurchasePrice, 10)) {
	//                    console.log('Hit Rate Error');
	//                    arr.push(itemName);
	//                }
	//            };
	//        }
	//    }

	//    function displayAlert() {
	//        itemsToAlert = itemsToAlert.join(', ');

	//        alert("This following items have a 'Last Purchase Price' that is lower than their rate.\n\n" +
	//            itemsToAlert +
	//            '\n\nPlease confirm before continuing.');
	//    }
	//}

	return {
		validateLine: validateLine,
		//saveRecord: saveRecord
	};
});
