/**
 * @NApiVersion 2.x
 * @NScriptType ClientScript
 * @NAmdConfig /SuiteScripts/config.json
 */

//@ts-ignore
define(["N/log", "Moment"], function (log, moment) {
	//See notes below on changes when can approve sales orders without approving
	var salesOrderContext;
	var salesOrderDate;

	var vendors = [
		{
			id: 742, //Medline
			startDate: "01-05-2021",
		},
	];

	function pageInit(context) {
		//Pull values
		var salesOrder = context.currentRecord;
		salesOrderDate = moment(salesOrder.getValue("trandate")).format(
			"MM-DD-YYYY"
		);
		salesOrderContext = context.mode;
	}

	function validateLine(context) {
		//When creating new sales orders
		if (salesOrderContext == "edit" || salesOrderContext == "create") {
			//take out edit
			var salesOrder = context.currentRecord;

			var subsidiary = salesOrder.getValue("subsidiary");
			if (subsidiary != 1) {
				//Supplyline
				return true;
			}

			if (context.sublistId === "item") {
				var vendorOnLineItem = salesOrder.getCurrentSublistValue({
					sublistId: "item",
					fieldId: "povendor",
				});

				if (vendorOnLineItem) {
					var checkForThisVendor = vendors.find(function (v) {
						return v.id == vendorOnLineItem;
					});

					if (checkForThisVendor) {
						var shipAccount = salesOrder.getCurrentSublistValue({
							sublistId: "item",
							fieldId: "custcol_spl_ship_account",
						});

						if (!shipAccount) {
							alert("Please choose a ship account for this item.");
							return false;
						}
					}
				}
			}
		}
		return true;
	}

	function saveRecord(context) {
		//When saving a sales order created via integration

		if (salesOrderContext == "copy" || salesOrderContext == "edit") {
			var itemsMissingInfo = [];
			validateItems();

			if (itemsMissingInfo.length > 0) {
				displayAlert();
				return false;
			}

			function validateItems() {
				var salesOrder = context.currentRecord;
				var subsidiary = salesOrder.getValue("subsidiary");
				if (subsidiary != 1) {
					//Supplyline
					return true;
				}
				var salesOrderLineCount = getSalesOrderLineCount();
				validateLines();

				function getSalesOrderLineCount() {
					return salesOrder.getLineCount({
						sublistId: "item",
					});
				}

				function validateLines() {
					for (var x = 0; x < salesOrderLineCount; x++) {
						var vendorOnLineItem = getVendorOnLineItem();
						if (vendorOnLineItem) {
							var checkForThisVendor = getCheckForThisVendor();
							if (checkForThisVendor) {
								var hasShipAccount = checkShipAccount();
								if (!hasShipAccount) {
									var itemName = getItemName();
									itemsMissingInfo.push(itemName);
								}
							}
						}

						/********Validate Lines Helper Functions********/
						function getVendorOnLineItem() {
							return salesOrder.getSublistValue({
								sublistId: "item",
								fieldId: "povendor",
								line: x,
							});
						}

						function getCheckForThisVendor() {
							var vendorToCheck = vendors.find(function (v) {
								return v.id == vendorOnLineItem;
							});

							if (vendorToCheck) {
								//Compare dates to check if transaction date is on/after vendor EDI integration date

								var parsedSalesOrderDate = Date.parse(salesOrderDate);
								var vendorEdiIntegrationStartDate = Date.parse(
									vendorToCheck.startDate
								);
								if (parsedSalesOrderDate > vendorEdiIntegrationStartDate) {
									return vendorToCheck;
								}
							}
						}

						function checkShipAccount() {
							var shipAccount = salesOrder.getSublistValue({
								sublistId: "item",
								fieldId: "custcol_spl_ship_account",
								line: x,
							});

							return shipAccount;
						}

						function getItemName() {
							return salesOrder.getSublistText({
								sublistId: "item",
								fieldId: "item",
								line: x,
							});
						}
					}
				}
			}

			function displayAlert() {
				var itemsMissingInfoText = itemsMissingInfo.join(", ");
				console.log(itemsMissingInfoText);

				alert(
					"This sales order is missing a shipping account for the following items.\n\n" +
						itemsMissingInfoText +
						"\n\nPlease correct to continue."
				);
			}
		}
		return true;
	}

	return {
		pageInit: pageInit,
		validateLine: validateLine,
		saveRecord: saveRecord,
	};
});

//NOW THE SCRIPT IS TRIGGERED WHEN APPROVING A SALES ORDER ONLY BECAUSE YOU FIRST NEED TO EDIT IT.
//IF DON'T NEED TO EDIT THE RECORD TO APPROVE ANYMORE CHANGE THIS TO TRIGGER ON FIELD CHANGE

//function fieldChanged(context) {

//    if (context.fieldId == 'orderstatus') {
//        var salesOrder = context.currentRecord;
//        var orderStatus = salesOrder.getValue('orderstatus');
//        console.log('Order Status ' + orderStatus);
//    }
//}
