/**
 * @description Partner class for Walmart
 *
 * @NApiVersion 2.1
 * <AUTHOR> <<EMAIL>>
 */

define([
  "exports",
  "require",
  "./edi_customer",
  "../../Template/edi_810_template",
  "../../Template/edi_856_template",
], (/** @type {any} */ exports, /** @type {any} */ require) => {
  const { EDICustomer } = require("./edi_customer");
  const { EDI810Template } = require("../../Template/edi_810_template");
  const { EDI856Template } = require("../../Template/edi_856_template");

  /**
   * Walmart Customer Class
   *
   * @class
   * @typedef {import("../../../Interfaces/Models/Partner/edi_partner").EDICustomerInterface} EDICustomerInterface
   * @typedef {import("../../../Interfaces/Models/Partner/edi_partner").EDIPartnerConstructorParams} EDIPartnerConstructorParams
   * @typedef {import("../../../Interfaces/Models/Partner/edi_partner").EDIParsingParams} EDIParsingParams
   * @typedef {import("../../../Interfaces/Models/Partner/edi_partner").EDIParsingInformation} EDIParsingInformation
   * @extends {EDICustomer}
   * @implements {EDICustomerInterface}
   */
  class EDIWalmart extends EDICustomer {
    /** @param {EDIPartnerConstructorParams} params Constructor params */
    constructor(params) {
      super();
      /** @type {string} */
      this.name = "Walmart";
      /** @type {string} */
      this.prodDirectory = `/edi/prod/customer/walmart/${params.direction}/${params.transactionType}`;
      /** @type {string} */
      this.testDirectory = `/edi/test/customer/walmart/${params.direction}/${params.transactionType}`;
      /** @type {string} */
      this.referenceDirectory = `/edi/reference/customer/walmart/${params.direction}/${params.transactionType}`;
      /** @type {boolean} */
      this.shouldIncludeIsReplacementFor = false;
      /** @type {boolean} */
      this.shouldIncludeDeactivateItem = false;
      /** @type {string} */
      this.subsidiary = "4";
      /** @type {string} */
      this.parent = "8454";
      /** @type {string} */
      this.purchasingSoftwareId = "7";
      /** @type {{[key:string]: string}} */
      this.billingAddress = {
        addressee: "WAL-MART STORES, INC.",
        street: "702 S.W. EIGHTH ST.",
        city: "Bentonville",
        state: "AR",
        zip: "72716",
        country: "US",
      };
      /** @type {EDIParsingParams} */
      this.delimiters = {
        fieldDelimiter: "*",
        segmentDelimiter: "~",
        fileDelimiter: "|",
        ediVersion: "00501",
        receiverQualifier: "08",
        receiverId: "925485US00",
      };
      const edi810Template = new EDI810Template({
        fieldDelimiter: this.delimiters.fieldDelimiter,
        segmentDelimiter: this.delimiters.segmentDelimiter,
      });
      const edi856Template = new EDI856Template({
        fieldDelimiter: this.delimiters.fieldDelimiter,
        segmentDelimiter: this.delimiters.segmentDelimiter,
      });
      this.template = {
        OUT: {
          810: {
            header:
              edi810Template.createISASegment() +
              edi810Template.createGSSegment() +
              edi810Template.createSTSegment() +
              edi810Template.createBIGSegment() +
              edi810Template.createREFSegment() +
              edi810Template.createNSupplierSegment() +
              edi810Template.createNCustomerSegment() +
              edi810Template.createITDSegment() +
              edi810Template.createDTMSegment() +
              edi810Template.createFOBSegment(),
            item: edi810Template.createItemSegment(),
            sac: edi810Template.createSACSegment(),
            summary:
              edi810Template.createTDSSegment() +
              edi810Template.createCTTSegment() +
              edi810Template.createSESegment() +
              edi810Template.createGESegment() +
              edi810Template.createIEASegment(),
            /** @type {{base: number, item: number}} */
            segmentCount: {
              base: 15, // ST to SE excluding item rows
              item: 2, // IT1 and PID
            },
          },
          856: {
            header:
              edi856Template.createISASegment() +
              edi856Template.createGSSegment() +
              edi856Template.createSTSegment() +
              edi856Template.createBSNSegment() +
              edi856Template.createHL1Segment() +
              edi856Template.createTD1Segment() +
              edi856Template.createTD5Segment() +
              edi856Template.createDTMSegment() +
              edi856Template.createFOBSegment() +
              edi856Template.createNSupplierSegment() +
              edi856Template.createNCustomerSegment() +
              edi856Template.createHL2Segment() +
              edi856Template.createPRFSegment(),
            tracking: edi856Template.createREFSegment(),
            item:
              edi856Template.createHLItemSegment() +
              edi856Template.createLINSegment() +
              edi856Template.createSN1Segment() +
              edi856Template.createPIDSegment(),
            summary:
              edi856Template.createCTTSegment() +
              edi856Template.createSESegment() +
              edi856Template.createGESegment() +
              edi856Template.createIEASegment(),
            /** @type {{base: number, item: number; hierarchy: number}} */
            segmentCount: {
              base: 17, // ST to SE exluding item and REF rows
              item: 4, // HL, LIN, SN1 and PID,
              hierarchy: 2, // HL for Shipping and Order
            },
          },
          997: {
            header:
              "ISA*00*          *00*          *SenderQualifier*ISASenderId*ReceiverQualifier*ISAReceiverId*ISADate*Time*:*EdiVersion*ControlNumber*0*T*>~\n" +
              "GS*FA*GSSenderId*GSReceiverId*GSDate*Time*ControlNumber*X*EdiVersion0~\n" +
              "ST*997*0001~\n" +
              "AK1*OR*SalesOrderControlNumber~\n" +
              "AK2*850*0001~\n" +
              "AK5*A~\n" +
              "AK9*A*1*1*1~\n" +
              "SE*6*0001~\n" +
              "GE*1*ControlNumber~\n" +
              "IEA*1*ControlNumber~",
          },
        },
      };
      /** @type {string} */
      this.targetStationId = "08925485US00";
      /** @type {string} */
      this.gln = "0078742028286";
      /** @type {string} */
      this.code = "7188210570";
    }

    /**
     * Create a SuiteQL Query String to lookup existing Walmart customer record in NetSuite
     *
     * @param {string} identifier GLN
     * @returns {string} SuiteQL Query
     */
    generateQueryString(identifier) {
      return `
                SELECT
                    id as id
                FROM
                    customer
                WHERE
                    custentity_crh_walmart_gln = '${identifier}'
                    AND isinactive = 'F'
            `;
    }

    /**
     * Get information for parsing the file from the Partner
     *
     * @param {object} params Parameters
     * @param {EDIParsingParams} params.delimiters Parsing parameters
     * @param {"in" | "out"} params.direction Direction
     * @returns {EDIParsingInformation} Partner-specific values
     */
    getParsingInformation({
      delimiters: {
        fieldDelimiter,
        segmentDelimiter,
        fileDelimiter,
        ediVersion,
        receiverQualifier,
        receiverId,
      },
      direction,
    }) {
      const partnerValues = {
        fieldDelimiter: fieldDelimiter,
        segmentDelimiter: segmentDelimiter,
        formattingInfo: [
          {
            name: "element delimiter",
            templateValue: "*",
            partnerValue: fieldDelimiter,
          },
          {
            name: "segmentDelimeiter",
            templateValue: "~\n",
            partnerValue: segmentDelimiter,
          },
          {
            name: "fileDelimiter",
            templateValue: ">",
            partnerValue: fileDelimiter,
          },
        ],
        isaGsInfo: [
          {
            name: "ISADate",
            // moment().format("YYMMDD")
            value: (() => {
              const date = new Date();
              const year = date.getFullYear().toString().slice(-2);
              const month = (date.getMonth() + 1).toString().padStart(2, "0");
              const day = date.getDate().toString().padStart(2, "0");

              return `${year}${month}${day}`;
            })(),
          },
          {
            name: "GSDate",
            //moment().format("YYYYMMDD")
            value: (() => {
              const date = new Date();
              const year = date.getFullYear().toString();
              const month = (date.getMonth() + 1).toString().padStart(2, "0");
              const day = date.getDate().toString().padStart(2, "0");

              return `${year}${month}${day}`;
            })(),
          },
          {
            name: "Time",
            // moment().format("HHmm")
            value: (() => {
              const date = new Date();
              const hours = date.getHours().toString().padStart(2, "0");
              const minutes = date.getMinutes().toString().padStart(2, "0");

              return `${hours}${minutes}`;
            })(),
          },
          {
            name: "EdiVersion",
            value: ediVersion,
          },
        ],
        senderInfo: (() => {
          switch (direction) {
            case "in":
              return [
                //Partner Info
                {
                  name: "SenderQualifier",
                  value: receiverQualifier,
                },
                {
                  name: "ISASenderId",
                  //Needs to be 15 characters total, slice extracts up to but not including end
                  value: (receiverId + "                 ").slice(0, 15),
                },
                {
                  name: "GSSenderId",
                  value: receiverId,
                },
              ];
            case "out":
              return [
                //Valmed Info
                {
                  name: "SenderQualifier",
                  value: "ZZ",
                },
                {
                  name: "ISASenderId",
                  value: this.code + "     ",
                },
                {
                  name: "GSSenderId",
                  value: this.code,
                },
              ];
            default:
              return [];
          }
        })(),
        receiverInfo: (() => {
          switch (direction) {
            case "in":
              return [
                //Valmed Info
                {
                  name: "ReceiverQualifier",
                  value: "ZZ",
                },
                {
                  name: "ISAReceiverId",
                  value: this.code + "     ",
                },
                {
                  name: "GSReceiverId",
                  value: this.code,
                },
              ];
            case "out":
              return [
                //Partner Info
                {
                  name: "ReceiverQualifier",
                  value: receiverQualifier,
                },
                {
                  name: "ISAReceiverId",
                  //Needs to be 15 characters total, slice extracts up to but not including end
                  value: (receiverId + "                 ").slice(0, 15),
                },
                {
                  name: "GSReceiverId",
                  value: receiverId,
                },
              ];
            default:
              return [];
          }
        })(),
      };

      return partnerValues;
    }
  }

  exports.EDIWalmart = EDIWalmart;
});
