/**
 *@NApiVersion 2.1
 *@NScriptType MapReduceScript
 */

//@ts-ignore
define(["N/log", "N/search", "N/record"], function (log, search, record) {
	function getInputData() {
		try {
			var startDate = "12/31/2020";
			var endDate = "10/7/2021";
			var item = 5701; // - PPE.056, 4869 - MD.442B//

			var searchObj = search.create({
				type: "invoice",
				filters: [
					["type", "anyof", "CustInvc"],
					"AND",
					["subsidiary", "anyof", "1"], //Supplyline
					"AND",
					[
						"datecreated",
						"within",
						startDate + " 12:00 am",
						endDate + " 11:59 pm",
					],
					"AND",
					["item", "anyof", item],
				],
				columns: [
					search.createColumn({ name: "internalid", label: "Internal ID" }),
					search.createColumn({ name: "tranid", label: "Document Number" }),
					search.createColumn({ name: "trandate", label: "Date" }),
					search.createColumn({ name: "item", label: "Item" }),
					search.createColumn({ name: "rate", label: "Item Rate" }),
					search.createColumn({ name: "amount", label: "Amount" }),
					search.createColumn({ name: "line", label: "Line ID" }),
				],
			});

			return searchObj;
		} catch (e) {
			throw e;
		}
	}

	function map(context) {
		try {
			var newCostRate = 0.52;

			var result = JSON.parse(context.value);

			var internalId = result.values.internalid.value;
			var invoiceNumber = result.values.tranid;
			var lineNumber = result.values.line - 1;

			var invoice = record.load({
				type: record.Type.INVOICE,
				id: internalId,
			});

			invoice.setSublistValue({
				sublistId: "item",
				fieldId: "costestimatetype",
				line: lineNumber,
				value: "CUSTOM",
			});

			invoice.setSublistValue({
				sublistId: "item",
				fieldId: "costestimaterate",
				line: lineNumber,
				value: newCostRate,
			});

			invoice.save();

			context.write({
				key: context.value,
				value: invoiceNumber,
			});
		} catch (e) {
			throw e;
		}
	}

	function summarize(context) {
		var mapKeys = [];

		context.mapSummary.keys.iterator().each(function (key) {
			mapKeys.push(key);
			return true;
		});

		log.debug("MAP keys processed", mapKeys);

		context.mapSummary.errors.iterator().each(function (key, error) {
			log.error("Map Error for key: " + key, error);
			return true;
		});

		var totalRecordsProcessed = 0;
		var recordsProcessed = [];
		context.output.iterator().each(function (key, value) {
			recordsProcessed.push(key.id + " - " + value);

			totalRecordsProcessed++;
			return true;
		});

		log.debug({
			title: "Records Processed",
			details: recordsProcessed,
		});
		var summaryMessage =
			"Usage: " +
			context.usage +
			" Total Records Processed: " +
			totalRecordsProcessed;
		log.debug({ title: "Summary of usase", details: summaryMessage });
	}

	return {
		getInputData: getInputData,
		map: map,
		summarize: summarize,
	};
});

//Pass in params from Suitelet - Get Input Data
// var params = runtime.getCurrentScript().getParameter({
// 	name: "custscript_filter_parameter",
// });

// log.debug({
// 	title: "params",
// 	details: params,
// });

// var jsonData = JSON.parse(params);

// log.debug({
// 	title: "JSON Data",
// 	details: jsonData,
// });

// var startDate = jsonData.startDate;
// var endDate = jsonData.endDate;
// var item = jsonData.item;

//Get price param - Map
// var filters = runtime
// 	.getCurrentScript()
// 	.getParameter({ name: "custscript_filter_parameter" });
// var jsonData = JSON.parse(filters);
