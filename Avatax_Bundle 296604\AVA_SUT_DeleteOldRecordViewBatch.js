/******************************************************************************************************
	Script Name - AVA_SUT_DeleteOldRecordViewBatch.js
	Company -     Avalara Technologies Pvt Ltd.
******************************************************************************************************/

/**
 *@NApiVersion 2.0
 *@NScriptType Suitelet
 */

define(['N/ui/serverWidget', 'N/search', 'N/record', 'N/redirect', 'N/url', './utility/AVA_Library'],
	function(ui, search, record, redirect, url, ava_library){
		function onRequest(context){
			try{
				if(ava_library.mainFunction('AVA_CheckSecurity', 38) == 0){
					if(context.request.method === 'GET'){
						var avaDeleteOldRecordForm = ui.createForm({
							title: 'View Batch'
						});
						
						avaDeleteOldRecordForm.clientScriptModulePath = './AVA_CLI_DeleteOldRecordViewBatch.js';
						
						addFormSublist(avaDeleteOldRecordForm);
						setSublistFieldsValue(avaDeleteOldRecordForm);
						
						avaDeleteOldRecordForm.addSubmitButton({
							label: 'Submit'
						});
						avaDeleteOldRecordForm.addButton({
							id: 'custpage_ava_deleteoldrecordviewbatch_ref',
							label: 'Refresh',
							functionName: 'AVA_DeleteOldRecordViewBatchRefresh()'
						});
						avaDeleteOldRecordForm.addPageLink({
							title: 'Create Batch',
							type: ui.FormPageLinkType.CROSSLINK,
							url: url.resolveScript({
								scriptId: 'customscript_ava_deleteoldrecordcb_suit',
								deploymentId: 'customdeploy_ava_deleteoldrecordcb_suit'
							})
						});
						context.response.writePage({
							pageObject: avaDeleteOldRecordForm
						});
					}
					else{
						var lineCount = context.request.getLineCount({
							group: 'custpage_deleteoldrecorbsublist'
						});

						for(var i = 0; i < lineCount; i++){
							var apply = context.request.getSublistValue({
								group: 'custpage_deleteoldrecorbsublist',
								line: i,
								name: 'apply'
							});

							if(apply == 'T'){
								var batchId = context.request.getSublistValue({
									group: 'custpage_deleteoldrecorbsublist',
									line: i,
									name: 'deleteoldrecordbatchid'
								});

								record.delete({
									type: 'customrecord_avadeleteoldrecord',
									id: batchId
								});
							}
						}
						redirect.toTaskLink({
							id: 'CARD_-29'
						});
					}
				}
				else{
					context.response.write({
						output: checkServiceSecurity
					});
				}
			}
			catch(e){
				log.error('ERROR', 'ERROR NAME = ' + e.name + ', ERROR TYPE = ' + e.type + ', ERROR MESSAGE = ' + e.message);
			}
		}

		function setSublistFieldsValue(avaDeleteOldRecordForm){
			try{
				var avaDeleteOldRecordSearchObj = search.create({
					type: "customrecord_avadeleteoldrecord",
					filters: [
						["isinactive", "is", "F"],
						"AND",
						["custrecord_ava_deleteoldrecordstatus", "isnot", "Delete"]
					],
					columns: [
						"name",
						"created",
						"custrecord_ava_deleteoldrecordstatus",
						"custrecord_ava_deleteoldrecordtype",
						"custrecord_ava_deleteoldrecordstartdate",
						"custrecord_ava_deleteoldrecordenddate"
					]
				});

				var deleteOldRecordSublist = avaDeleteOldRecordForm.getSublist({
					id: 'custpage_deleteoldrecorbsublist'
				});
				var lineNum = 0;

				avaDeleteOldRecordSearchObj.run().each(function (result){
					var deleteOldRecordBatchName = result.getValue({
						name: 'name'
					});
					var deleteOldRecordBatchCreatedDate = result.getValue({
						name: 'created'
					});
					var deleteOldRecordBatchStartDate = result.getValue({
						name: 'custrecord_ava_deleteoldrecordstartdate'
					});
					var deleteOldRecordBatchEndDate = result.getValue({
						name: 'custrecord_ava_deleteoldrecordenddate'
					});
					var deleteOldRecordBatchStatus = result.getValue({
						name: 'custrecord_ava_deleteoldrecordstatus'
					});

					var deleteOldRecordBatchRecordType = result.getValue({
						name: 'custrecord_ava_deleteoldrecordtype'
					});
					deleteOldRecordSublist.setSublistValue({
						id: 'deleteoldrecordbatchid',
						line: lineNum,
						value: result.id
					});
					deleteOldRecordSublist.setSublistValue({
						id: 'deleteoldrecordbatchname',
						line: lineNum,
						value: deleteOldRecordBatchName
					});
					deleteOldRecordSublist.setSublistValue({
						id: 'deleteoldrecordbatchcreateddate',
						line: lineNum,
						value: ava_library.mainFunction('AVA_DateFormat', ava_library.mainFunction('AVA_ConvertDate', deleteOldRecordBatchCreatedDate))
					});
					deleteOldRecordSublist.setSublistValue({
						id: 'deleteoldrecordbatchstartdate',
						line: lineNum,
						value: deleteOldRecordBatchStartDate
					});
					deleteOldRecordSublist.setSublistValue({
						id: 'deleteoldrecordbatchenddate',
						line: lineNum,
						value: deleteOldRecordBatchEndDate
					});
					deleteOldRecordSublist.setSublistValue({
						id: 'deleteoldrecordrecordtype',
						line: lineNum,
						value: deleteOldRecordBatchRecordType
					});
					deleteOldRecordSublist.setSublistValue({
						id: 'deleteoldrecordbatchstatus',
						line: lineNum,
						value: deleteOldRecordBatchStatus
					});
					
					lineNum++;
					return true;
				});

			}
			catch(e){
				log.error('setSublistFieldsValue', 'ERROR NAME = ' + e.name + ', ERROR TYPE = ' + e.type + ', ERROR MESSAGE = ' + e.message);
			}
		}

		function addFormSublist(avaDeleteOldRecordForm){
			try{
				var deleteOldRecordSublist = avaDeleteOldRecordForm.addSublist({
					id: 'custpage_deleteoldrecorbsublist',
					label: 'Select Batches',
					type: ui.SublistType.LIST
				});

				var batchId = deleteOldRecordSublist.addField({
					id: 'deleteoldrecordbatchid',
					label: 'Batch ID',
					type: ui.FieldType.TEXT
				});
				batchId.updateDisplayType({
					displayType: ui.FieldDisplayType.HIDDEN
				});
				deleteOldRecordSublist.addField({
					id: 'apply',
					label: 'Delete',
					type: ui.FieldType.CHECKBOX
				});
				deleteOldRecordSublist.addMarkAllButtons();
				deleteOldRecordSublist.addField({
					id: 'deleteoldrecordbatchname',
					label: 'Batch Name',
					type: ui.FieldType.TEXT
				});
				deleteOldRecordSublist.addField({
					id: 'deleteoldrecordbatchcreateddate',
					label: 'Created On',
					type: ui.FieldType.DATE
				});
				deleteOldRecordSublist.addField({
					id: 'deleteoldrecordbatchstartdate',
					label: 'Start Date',
					type: ui.FieldType.DATE
				});
				deleteOldRecordSublist.addField({
					id: 'deleteoldrecordbatchenddate',
					label: 'End Date',
					type: ui.FieldType.DATE
				});
				deleteOldRecordSublist.addField({
					id: 'deleteoldrecordrecordtype',
					label: 'Record Type',
					type: ui.FieldType.TEXT
				});
				deleteOldRecordSublist.addField({
					id: 'deleteoldrecordbatchstatus',
					label: 'Status',
					type: ui.FieldType.TEXT
				});
				
				return avaDeleteOldRecordForm;
			}
			catch(e){
				log.debug(JSON.stringify(e));
				log.error('addFormSublist', 'ERROR NAME = ' + e.name + ', ERROR TYPE = ' + e.type + ', ERROR MESSAGE = ' + e.message);
			}
		}

		return{
			onRequest: onRequest
		};
	}
);