/**
 * Class that wraps the query and email module
 *
 * Accepts either a query ID from a dataset or workbook
 *   or a custom SuiteQL string with a columns array
 * The column labels are not auto-generated based off of the SELECT clause
 *   whereas dataset/workbook columns contain the label information
 *   e.g. CUSTOMRECORD_SPL_ZIP_CODES.name to Name
 * User can set a description and a threshold to specify the intro message
 *   and the number of results that will be shown in the email
 *
 * @NApiVersion 2.1
 * <AUTHOR>
 */

define([
	"require",
	"exports",
	"N/query",
	"N/email",
	"N/file",
	"./vlmd_custom_error_object",
], (/** @type {any} */ require, /** @type {any} */ exports) => {
	const query = require("N/query");
	const email = require("N/email");
	const file = require("N/file");

	/** @type {import("./vlmd_custom_error_object").CustomErrorObject} */
	const CustomErrorObject = require("./vlmd_custom_error_object");

	const DEFAULT_PAGE_SIZE = 10;

	/**
	 * Query Email class
	 *
	 * @class
	 * @type {import("./spl_query_email").QueryEmail}
	 */
	class QueryEmail {
		/**
		 * Constructor
		 *
		 * @param {import("./spl_query_email").ConstructorParams} param Constructor parameters
		 */
		constructor({ queryId, queryString, columns, threshold, description }) {
			this.queryId = queryId || "";
			this.queryObjectFromId = queryId ? query.load({ id: queryId }) : null;
			this.queryString = queryString || "";
			this.queryResult = {
				columns: columns?.map((column) => ({ label: column })) || [],
				results: [],
			};
			this.tableHtml = "";
			this.threshold = threshold || 100;
			this.description = description || "";
		}

		/**
		 * Run the query object or SuiteQL to generate query results
		 * Notify the user if results are truncated
		 *
		 * @returns {import("./spl_query_email").QueryResult}
		 */
		generateQueryResults() {
			const customError = new CustomErrorObject();

			try {
				const pageSize =
					this.threshold < DEFAULT_PAGE_SIZE
						? this.threshold
						: DEFAULT_PAGE_SIZE;
				let pageIndex = 0;

				if (this.queryString) {
					query
						.runSuiteQLPaged({
							query: this.queryString,
							pageSize,
						})
						.iterator()
						.each((page) => {
							this.queryResult.results = this.queryResult.results.concat(
								page.value.data.results
							);
							return ++pageIndex * pageSize < this.threshold;
						});
				} else if (this.queryObjectFromId) {
					this.queryObjectFromId
						.runPaged({
							pageSize,
						})
						.iterator()
						.each((page) => {
							if (this.queryResult.columns.length < 1) {
								const columns = page.value.data.columns;
								this.queryResult.columns = columns.map((column) => ({
									label: column.label,
								}));
							}
							this.queryResult.results = this.queryResult.results.concat(
								page.value.data.results
							);
							return ++pageIndex * pageSize < this.threshold;
						});
				} else {
					customError.updateError({
						errorType: customError.ErrorTypes.MISSING_VALUE,
						summary: "NO_QUERY_STRING_OR_OBJECT",
						details:
							"There is no query string provided or object loaded from a query id.",
					});
				}
			} catch (/** @type {any} */ err) {
				customError.throwError({
					summaryText: "GENERATE_QUERY_RESULTS",
					error: err,
				});
			}

			return this.queryResult;
		}

		/**
		 * Build the table containing the query results
		 *
		 * @returns {string} Result in HTML table
		 */
		buildResultHtml() {
			// Message
			const message =
				this.description ||
				`These are the query results${
					this.queryId ? ` from ${this.queryId}` : ""
				}`;
			let tableHtml = `<p><span>${message}:</span></p><table style='width: 100%; padding-top: 10px'>`;

			// Table header
			if (this.queryResult.columns.length > 0) {
				tableHtml += "<tr>";
				this.queryResult.columns.forEach((column) => {
					tableHtml += `<th style="border: 1px solid black; padding: 3px"><strong>${column.label}<strong></th>`;
				});
				tableHtml += "</tr>";
			}

			// Table body
			for (
				let index = 0;
				index < this.queryResult.results.length && index < this.threshold;
				index++
			) {
				const result = this.queryResult.results[index];

				tableHtml += `<tr ${
					index % 2 === 1 ? `style="background-color: #ddd"` : ""
				}>`;

				result.values.forEach((value) => {
					if (value) {
						tableHtml += `<td style="border: 1px solid black; padding: 3px">${value}</td>`;
					} else {
						tableHtml +=
							"<td style='border: 1px solid black; padding: 3px'></td>";
					}
				});

				tableHtml += "</tr>";
			}

			tableHtml += "</table>";

			// Threshold note
			if (this.queryResult.results.length > this.threshold) {
				tableHtml += `<br>Results beyond row #${this.threshold} have been truncated...`;
			}

			this.tableHtml = tableHtml;

			return this.tableHtml;
		}

		formatMrResultsToCSV(context, fileName) {
			const customErrorObject = new CustomErrorObject();
			try {
				const resultsToSendArr = [];

				context.output.iterator().each(function (key, value) {
					if (key == "Results") {
						const parsedObj = JSON.parse(value); //The values are passed in as a string, turning back into object
						resultsToSendArr.push(parsedObj);
						return true; //To continue iterating the context.output
					}
				});

				const headerObj = resultsToSendArr[0];

				const csvFile = file.create({
					name: `${fileName} ${new Date().toLocaleDateString()}.csv`,
					fileType: file.Type.CSV,
					contents: `${Object.keys(headerObj).toString()}\n`,
				});

				resultsToSendArr.forEach((itemIssueObj) => {
					// Properly escape CSV values to handle commas
					const escapedValues = Object.values(itemIssueObj).map(value => {
						// Convert to string and handle null/undefined
						const strValue = (value !== null && value !== undefined) ? String(value) : '';
						
						// If value contains comma, quote, or newline, wrap in quotes and escape existing quotes
						if (strValue.includes(',') || strValue.includes('"') || strValue.includes('\n')) {
							// Replace any double quotes with two double quotes (CSV escaping standard)
							return '"' + strValue.replace(/"/g, '""') + '"';
						}
						return strValue;
					});
					
					csvFile.appendLine({
						value: escapedValues.join(',')
					});
				});

				this.csvFile = csvFile;
			} catch (err) {
				customErrorObject.throwError({
					summaryText: `SEND_MR_AS_CSV`,
					error: err,
				});
			}
		}

		/**
		 * Send email of results
		 *
		 * @param {{recipients: number[], subject: string}} param Internal ID of recipients and subject of the email
		 * @returns {void}
		 */
		sendResults({ recipients, subject, onlyIncludeHtml }) {
			email.send({
				subject,
				recipients,
				author: 223244,//Requests email
				body: onlyIncludeHtml ? this.tableHtml : "Please see attached file.",
				attachments: this.csvFile ? [this.csvFile] : null,
			});
		}
	}

	exports.QueryEmail = QueryEmail;

	return QueryEmail;
});
