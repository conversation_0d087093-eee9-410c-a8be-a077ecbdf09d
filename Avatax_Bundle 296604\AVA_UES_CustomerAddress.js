/******************************************************************************************************
	Script Name - AVA_UES_CustomerAddress.js
	Company -     Avalara Technologies Pvt Ltd.
******************************************************************************************************/

/**
 *@NApiVersion 2.0
 *@NScriptType UserEventScript
*/

define(['N/ui/serverWidget', 'N/runtime', 'N/search', 'N/record', 'N/cache', './utility/AVA_Library'],
	function(ui, runtime, search, record, cache, ava_library){
		function AVA_CustomerAddressBeforeLoad(context){
			try{
				var cForm = context.form;
				var nRecord = context.newRecord;
				var executionContext = runtime.executionContext;

				if(executionContext == 'USERINTERFACE' || executionContext == 'USEREVENT' || executionContext == 'WEBSERVICES' || executionContext == 'CSVIMPORT' || executionContext == 'SCHEDULED' || executionContext == 'SUITELET'){
					var avaConfigCache = cache.getCache({
						name: 'avaConfigCache',
						scope: cache.Scope.PROTECTED
					});

					var configCache = avaConfigCache.get({
						key: 'avaConfigObjRec',
						loader: ava_library.AVA_LoadValuesToGlobals
					});

					if(configCache != null && configCache.length > 0){
						configCache = JSON.parse(configCache);
					}
					
					cForm.clientScriptModulePath = './AVA_CLI_Entity.js';
					var baseRecordType = nRecord.getValue('baserecordtype');

					var useTaxAssessment;
					if(baseRecordType == 'vendor' && configCache.AVA_EnableUseTax == true){
						useTaxAssessment = cForm.getField({
							id: 'custentity_ava_usetaxassessment'
						});
						useTaxAssessment.updateDisplayType({
							displayType: ui.FieldDisplayType.NORMAL
						});
					}

					if(configCache.AVA_ServiceTypes != null && configCache.AVA_ServiceTypes.search('AddressSvc') != -1 && (configCache.AVA_DisableAddValidation == false || configCache.AVA_DisableAddValidation == 'F')){
						var addressBookSublist = cForm.getSublist({
							id: 'addressbook'
						}); 

						if(addressBookSublist != null){
							addressBookSublist.addButton({
								id : 'custpage_ava_validateaddress',
								label : 'Validate Address',
								functionName : 'AVA_ValidateAddress(0)'
							});
							
							if(configCache.AVA_EnableAddValFlag == true){
								var addval = addressBookSublist.addField({
									id : 'custpage_ava_addval',
									type : ui.FieldType.CHECKBOX,
									label : 'Validated'
								});
								addval.updateDisplayType({
									displayType: ui.FieldDisplayType.DISABLED
								});
							}

							if(baseRecordType != 'vendor'){
								addressBookSublist.addField({
									id : 'custpage_ava_latitude',
									type : ui.FieldType.TEXT,
									label : 'Latitude'
								});
								addressBookSublist.addField({
									id : 'custpage_ava_longitude',
									type : ui.FieldType.TEXT,
									label : 'Longitude'
								});
							}
						}

						if(context.type == context.UserEventType.EDIT || context.type == context.UserEventType.VIEW){
							if(addressBookSublist != null && baseRecordType != 'vendor'){
								var j = 0, latLongDetails = [];
								
								var searchRecord = search.create({
									type : 'customrecord_avacoordinates',
									filters :  ['custrecord_ava_custid', 'anyof', nRecord.id],
									columns : ['custrecord_ava_addid', 'custrecord_ava_latitude', 'custrecord_ava_longitude', 'custrecord_ava_custid']
								});
								searchRecord = searchRecord.run();
								var searchResult = searchRecord.getRange({
									start : 0,
									end : 1000
								});
								
								while(searchResult != null && searchResult.length > 0){
									for(var i = 0; i < searchResult.length; i++){
										latLongDetails.push(searchResult[i]);
										j++;
									}
									
									if(searchResult.length == 1000){
										searchResult = searchRecord.getRange({
											start: j,
											end: j + 1000
										});
									}
									else{
										break;
									}
								}

								var addressLineCount = nRecord.getLineCount({
									sublistId: 'addressbook'
								});

								for(var i = 0; i < addressLineCount && latLongDetails != null && latLongDetails.length > 0; i++){
									var addId = nRecord.getSublistValue({
										sublistId : 'addressbook',
										fieldId : 'id',
										line : i
									});

									for(var j = 0; j < latLongDetails.length; j++){
										if(latLongDetails[j].getValue('custrecord_ava_addid') == addId){
											nRecord.setSublistValue({
												sublistId : 'addressbook',
												fieldId : 'custpage_ava_latitude',
												line : i,
												value : latLongDetails[j].getValue('custrecord_ava_latitude')
											});
											nRecord.setSublistValue({
												sublistId : 'addressbook',
												fieldId : 'custpage_ava_longitude',
												line : i,
												value : latLongDetails[j].getValue('custrecord_ava_longitude')
											});
											break;
										}
									}
								}
							}

							if(addressBookSublist != null && configCache.AVA_EnableAddValFlag == true){
								var m = 0, addValidDetails = [];
								
								var searchRecord = search.create({
									type : 'customrecord_avaaddvalflag',
									filters : (baseRecordType == 'vendor') ? ['custrecord_avavendorid', 'anyof', nRecord.id] : ['custrecord_avacustid', 'anyof', nRecord.id],
									columns : ['custrecord_ava_address_id', 'custrecord_ava_addressvalidated', 'custrecord_ava_custvendinternalid']
								});

								searchRecord = searchRecord.run();
								var searchResult = searchRecord.getRange({
									start : 0,
									end : 1000
								});
								
								while(searchResult != null && searchResult.length > 0){
									for(var i = 0; i < searchResult.length; i++){
										addValidDetails.push(searchResult[i]);
										m++;
									}
									
									if(searchResult.length == 1000){
										searchResult = searchRecord.getRange({
											start: m,
											end: m + 1000
										});
									}
									else{
										break;
									}
								}

								var lineItemCount = nRecord.getLineCount({
									sublistId: 'addressbook'
								});

								for(var k =0; k < lineItemCount && addValidDetails != null && addValidDetails.length > 0; k++){
									var addId = nRecord.getSublistValue({
										sublistId : 'addressbook',
										fieldId : 'id',
										line : k
									});

									for(var m = 0; m < addValidDetails.length; m++){
										if(addValidDetails[m].getValue('custrecord_ava_address_id') == addId){
											nRecord.setSublistValue({
												sublistId : 'addressbook',
												fieldId : 'custpage_ava_addval',
												line : k,
												value : addValidDetails[m].getValue('custrecord_ava_addressvalidated')
											});
											break;
										}
									}
								}
							}
						}   
					}
				}
			}
			catch(err){
				log.debug({
					title: 'AVA_CustomerAddressBeforeLoad Try/Catch Error',
					details: err.message
				});
			}
		}   
		
		function AVA_CustomerAddressAfterSubmit(context){
			try{
				var nRecord = context.newRecord;
				var executionContext = runtime.executionContext;

				if(executionContext == 'USERINTERFACE' || executionContext == 'USEREVENT' || executionContext == 'WEBSERVICES' || executionContext == 'CSVIMPORT' || executionContext == 'SCHEDULED' || executionContext == 'SUITELET'){
					var avaConfigCache = cache.getCache({
						name: 'avaConfigCache',
						scope: cache.Scope.PROTECTED
					});

					var configCache = avaConfigCache.get({
						key: 'avaConfigObjRec',
						loader: ava_library.AVA_LoadValuesToGlobals
					});

					if(configCache != null && configCache.length > 0){
						configCache = JSON.parse(configCache);
					}

					if(configCache.AVA_ServiceTypes != null && configCache.AVA_ServiceTypes.search('AddressSvc') != -1){
						var baseRecordType = nRecord.getValue('baserecordtype');

						if(baseRecordType != 'vendor'){
							if(context.type == context.UserEventType.CREATE && nRecord.getValue('isinactive') == false){
								var custRec = record.load({
									type: record.Type.CUSTOMER,
									id: nRecord.id
								});

								var lineItemCount = custRec.getLineCount({
									sublistId: 'addressbook'
								});

								for(var i = 0; i < lineItemCount; i++){
									var latitude = nRecord.getSublistValue({
										sublistId: 'addressbook',
										fieldId: 'custpage_ava_latitude',
										line: i
									});
									var longitude = nRecord.getSublistValue({
										sublistId: 'addressbook',
										fieldId: 'custpage_ava_longitude',
										line: i
									});

									var addId = custRec.getSublistValue({
										sublistId: 'addressbook',
										fieldId: 'id',
										line: i
									});

									if(latitude != null && latitude.length > 0 && longitude != null && longitude.length > 0){
										var rec = record.create({
											type : 'customrecord_avacoordinates'
										});
										rec.setValue({
											fieldId : 'custrecord_ava_custid',
											value : nRecord.id
										});
										rec.setValue({
											fieldId : 'custrecord_ava_addid',
											value : (addId).toString()
										});
										rec.setValue({
											fieldId : 'custrecord_ava_latitude',
											value : latitude
										});
										rec.setValue({
											fieldId : 'custrecord_ava_longitude',
											value : longitude
										});
										rec.setValue({
											fieldId : 'custrecord_ava_customerinternalid',
											value : (nRecord.id).toString()
										});
										var recId = rec.save({});
									}   
								}
							}
							else if(context.type == context.UserEventType.EDIT){
								var custRec = record.load({
									type: record.Type.CUSTOMER,
									id: nRecord.id
								});
								
								var m = 0, latLongDetails = [];
								
								var search_Record = search.create({
									type : 'customrecord_avacoordinates',
									filters : ['custrecord_ava_custid', 'anyof', nRecord.id],
									columns : ['custrecord_ava_addid', 'custrecord_ava_latitude', 'custrecord_ava_longitude', 'custrecord_ava_custid']
								});
								
								var searchRecord = search_Record.run();
								var searchResult = searchRecord.getRange({
									start : 0,
									end : 1000
								});
								
								while(searchResult != null && searchResult.length > 0){
									for(var i = 0; i < searchResult.length; i++){
										latLongDetails.push(searchResult[i]);
										m++;
									}
									
									if(searchResult.length == 1000){
										searchResult = searchRecord.getRange({
											start: m,
											end: m + 1000
										});
									}
									else{
										break;
									}
								}

								var addressBookLineCount = custRec.getLineCount({
									sublistId: 'addressbook'
								});

								for(var ii = 0; latLongDetails != null && ii < latLongDetails.length; ii++){
									var addressIdDelete = 'F';
									for(var j = 0; j < addressBookLineCount && addressBookLineCount <= 10000; j++){
										var lineItemValue = custRec.getSublistValue({
											sublistId: 'addressbook',
											fieldId: 'id',
											line: j
										});

										if(latLongDetails[ii].getValue('custrecord_ava_addid') == lineItemValue){
											addressIdDelete = 'T';
											break; 
										}
									}

									if(addressIdDelete == 'F'){
										record.delete({
											type: 'customrecord_avacoordinates',
											id: latLongDetails[ii].id
										});
									}
								}

								var lineCount = custRec.getLineCount({
									sublistId: 'addressbook'
								});
								
								m = 0, latLongDetails = [];
								
								searchRecord = search_Record.run();
								searchResult = searchRecord.getRange({
									start : 0,
									end : 1000
								});
								
								while(searchResult != null && searchResult.length > 0){
									for(var i = 0; i < searchResult.length; i++){
										latLongDetails.push(searchResult[i]);
										m++;
									}
									
									if(searchResult.length == 1000){
										searchResult = searchRecord.getRange({
											start: m,
											end: m + 1000
										});
									}
									else{
										break;
									}
								}
								
								for(var iii = 0; iii < lineCount && lineCount <= 10000; iii++){
									var rec, existFlag = 'F', changeFlag = 0, delRec;

									var addId = custRec.getSublistValue({
										sublistId: 'addressbook',
										fieldId: 'id',
										line: iii
									});
									addId = (addId).toString();

									var latitude = nRecord.getSublistValue({
										sublistId: 'addressbook',
										fieldId: 'custpage_ava_latitude',
										line: iii
									});
								
									var longitude = nRecord.getSublistValue({
										sublistId: 'addressbook',
										fieldId: 'custpage_ava_longitude',
										line: iii
									});

									for(var customRec = 0; latLongDetails != null && customRec < latLongDetails.length; customRec++){
										if(latLongDetails[customRec].getValue('custrecord_ava_addid') == addId){
											if(latitude != null && latitude.length > 0 && longitude != null && longitude.length >0){
												if(latLongDetails[customRec].getValue('custrecord_ava_latitude') != latitude){
													record.submitFields({
														type : 'customrecord_avacoordinates',
														id : latLongDetails[customRec].id,
														values : {'custrecord_ava_latitude' : latitude}
													});
													changeFlag = 1;//Record exists but value changed
												}
												else{
													changeFlag = 2;//Record exists but value is not changed
												}

												if(latLongDetails[customRec].getValue('custrecord_ava_longitude') != longitude){
													record.submitFields({
														type : 'customrecord_avacoordinates',
														id : latLongDetails[customRec].id,
														values : {'custrecord_ava_longitude' : longitude}
													});
													changeFlag = 1;//Record exists but value changed
												}
												else{
													changeFlag = 2;//Record exists but value is not changed
												}
											}
											else{
												delRec = latLongDetails[customRec].id;
												changeFlag = 3;//Record exists with blank value
											}

											existFlag = 'T';
											break;
										}
									}
									
									if(latitude != null && latitude.length > 0 && longitude != null && longitude.length > 0){
										if(existFlag == 'F' && changeFlag == 0 && nRecord.getValue('isinactive') == false){
											rec = record.create({
												type : 'customrecord_avacoordinates'
											});
											rec.setValue({
												fieldId : 'custrecord_ava_custid',
												value : nRecord.id
											});
											rec.setValue({
												fieldId : 'custrecord_ava_addid',
												value : addId
											});
											rec.setValue({
												fieldId : 'custrecord_ava_latitude',
												value : latitude
											});
											rec.setValue({
												fieldId : 'custrecord_ava_longitude',
												value : longitude
											});
											rec.setValue({
												fieldId : 'custrecord_ava_customerinternalid',
												value : (nRecord.id).toString()
											});
											var recId = rec.save({});
										}
									}
									else{
										if(existFlag == 'T' && changeFlag == 3){
											if(executionContext == 'USERINTERFACE'){
												record.delete({
													type : 'customrecord_avacoordinates',
													id : delRec
												});
											}
										}
									}   
								}
							}
							else if(context.type == context.UserEventType.DELETE){
								var j = 0, latLongDetails = [];
								
								var searchRecord = search.create({
									type : 'customrecord_avacoordinates',
									filters : ['custrecord_ava_customerinternalid', 'is', (nRecord.id).toString()]
								});

								searchRecord = searchRecord.run();
								var searchResult = searchRecord.getRange({
									start : 0,
									end : 1000
								});
								
								while(searchResult != null && searchResult.length > 0){
									for(var i = 0; i < searchResult.length; i++){
										latLongDetails.push(searchResult[i]);
										j++;
									}
									
									if(searchResult.length == 1000){
										searchResult = searchRecord.getRange({
											start: j,
											end: j + 1000
										});
									}
									else{
										break;
									}
								}

								for(var i = 0; latLongDetails != null && i < latLongDetails.length; i++){
									record.delete({
										type : 'customrecord_avacoordinates',
										id : latLongDetails[i].id
									});
								}
							}   
						}

						if(configCache.AVA_DisableAddValidation == false && configCache.AVA_EnableAddValFlag == true && executionContext == 'USERINTERFACE'){
							if(context.type == context.UserEventType.CREATE && nRecord.getValue('isinactive') == false){
								var custRec = record.load({
									type: baseRecordType,
									id: nRecord.id
								});

								var lineItemCount = custRec.getLineCount({
									sublistId: 'addressbook'
								}); 

								for(var k = 0; k < lineItemCount; k++){
									var addId = custRec.getSublistValue({
										sublistId: 'addressbook',
										fieldId: 'id',
										line: k
									});

									var addVal = nRecord.getSublistValue({
										sublistId: 'addressbook',
										fieldId: 'custpage_ava_addval', 
										line: k
									});

									var rec = record.create({
										type : 'customrecord_avaaddvalflag'
									});

									if(baseRecordType == 'vendor'){
										rec.setValue({
											fieldId : 'custrecord_avavendorid',
											value : nRecord.id
										});
									}   
									else{
										rec.setValue({
											fieldId : 'custrecord_avacustid',
											value : nRecord.id
										});
									}

									rec.setValue({
										fieldId : 'custrecord_ava_address_id',
										value : (addId).toString()
									});
									rec.setValue({
										fieldId : 'custrecord_ava_addressvalidated',
										value : (addVal == 'T') ? true : false 
									});
									rec.setValue({
										fieldId : 'custrecord_ava_custvendinternalid',
										value : (nRecord.id).toString()
									});
									var recId = rec.save({});
								}   
							}
							else if(context.type == context.UserEventType.EDIT){
								var custRec = record.load({
									type: baseRecordType,
									id: nRecord.id
								});
								
								var k = 0, addValidDetails = [];
								
								var search_Record = search.create({
									type : 'customrecord_avaaddvalflag',
									filters : (baseRecordType == 'customer') ? ['custrecord_avacustid', 'anyof', nRecord.id] : ['custrecord_avavendorid', 'anyof', nRecord.id],
									columns : ['custrecord_ava_address_id', 'custrecord_ava_addressvalidated', 'custrecord_ava_custvendinternalid']
								});

								var searchRecord = search_Record.run();
								var searchResult = searchRecord.getRange({
									start : 0,
									end : 1000
								});
								
								while(searchResult != null && searchResult.length > 0){
									for(var i = 0; i < searchResult.length; i++){
										addValidDetails.push(searchResult[i]);
										k++;
									}
									
									if(searchResult.length == 1000){
										searchResult = searchRecord.getRange({
											start: k,
											end: k + 1000
										});
									}
									else{
										break;
									}
								}

								var lineItemCount = custRec.getLineCount({
									sublistId: 'addressbook'
								});

								for(var jj = 0; addValidDetails != null && jj < addValidDetails.length; jj++){
									var addressIdDelete = 'F';

									for(var kk = 0; kk < lineItemCount && lineItemCount <= 10000; kk++){
										var lineItemValue = custRec.getSublistValue({
											sublistId: 'addressbook',
											fieldId: 'id',
											line: kk
										});

										if(addValidDetails[jj].getValue('custrecord_ava_address_id') == lineItemValue){
											addressIdDelete = 'T';
											break;
										}
									}

									if(addressIdDelete == 'F'){
										record.delete({
											type: 'customrecord_avaaddvalflag',
											id: addValidDetails[jj].id
										});
									}
								} 
								
								k = 0, addValidDetails = [];
								
								searchRecord = search_Record.run();
								searchResult = searchRecord.getRange({
									start : 0,
									end : 1000
								});
								
								while(searchResult != null && searchResult.length > 0){
									for(var i = 0; i < searchResult.length; i++){
										addValidDetails.push(searchResult[i]);
										k++;
									}
									
									if(searchResult.length == 1000){
										searchResult = searchRecord.getRange({
											start: k,
											end: k + 1000
										});
									}
									else{
										break;
									}
								}

								for(var jjj = 0; jjj < lineItemCount && lineItemCount <= 10000; jjj++){
									var rec, existFlag = 'F', changeFlag = 0;
									
									var addId = custRec.getSublistValue({
										sublistId: 'addressbook',
										fieldId: 'id',
										line: jjj
									});
									addId = (addId).toString();

									var addVal = nRecord.getSublistValue({
										sublistId: 'addressbook',
										fieldId: 'custpage_ava_addval',
										line: jjj
									});

									for(var customRec = 0; addValidDetails != null && customRec < addValidDetails.length; customRec++){
										if(addValidDetails[customRec].getValue('custrecord_ava_address_id') == addId){
											record.submitFields({
												type : 'customrecord_avaaddvalflag',
												id : addValidDetails[customRec].id,
												values : {'custrecord_ava_addressvalidated' : addVal}
											});    
											existFlag = 'T';
											break;
										}
									}

									if(existFlag == 'F' && nRecord.getValue('isinactive') == false){
										rec = record.create({
											type : 'customrecord_avaaddvalflag'
										});

										if(baseRecordType == 'vendor'){
											rec.setValue({
												fieldId : 'custrecord_avavendorid',
												value : nRecord.id
											});
										}   
										else{
											rec.setValue({
												fieldId : 'custrecord_avacustid',
												value : nRecord.id
											});
										}
										rec.setValue({
											fieldId : 'custrecord_ava_address_id',
											value : addId
										});
										rec.setValue({
											fieldId : 'custrecord_ava_addressvalidated',
											value : (addVal == 'T') ? true : false
										});
										rec.setValue({
											fieldId : 'custrecord_ava_custvendinternalid',
											value : (nRecord.id).toString()
										});
										var recId = rec.save({});
									}
								}
							}
							else if(context.type == context.UserEventType.DELETE){
								var k = 0, addValidDetails = [];
								
								var searchRecord = search.create({
									type : 'customrecord_avaaddvalflag',
									filters : ['custrecord_ava_custvendinternalid', 'is', (nRecord.id).toString()]
								});

								searchRecord = searchRecord.run();
								var searchResult = searchRecord.getRange({
									start : 0,
									end : 1000
								});
								
								while(searchResult != null && searchResult.length > 0){
									for(var i = 0; i < searchResult.length; i++){
										addValidDetails.push(searchResult[i]);
										k++;
									}
									
									if(searchResult.length == 1000){
										searchResult = searchRecord.getRange({
											start: k,
											end: k + 1000
										});
									}
									else{
										break;
									}
								}

								for(var i = 0; addValidDetails != null && i < addValidDetails.length; i++){
									record.delete({
										type : 'customrecord_avaaddvalflag',
										id : addValidDetails[i].id
									});
								}
							}   
						}
					}
				}
			}
			catch(err){
				log.debug({
					title: 'AVA_CustomerAddressAfterSubmit Try/Catch Error',
					details: err.message
				});
			} 
		}
		
		return{
			beforeLoad: AVA_CustomerAddressBeforeLoad,
			afterSubmit: AVA_CustomerAddressAfterSubmit
		};
	}
);