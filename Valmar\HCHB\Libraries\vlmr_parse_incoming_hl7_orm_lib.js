//@ts-ignore
define(["N/log"], function (log) {
	function parseHl7Orm(ediFile, partnerValues) {
		const helperFunctions = (function () {
			function _getSegmentFields(segmentTitle, returnFirstResultOnly) {
				const regExp = new RegExp(
					/*segmentDelimiter + */ segmentTitle + ".*?" + segmentDelimiter,
					"gs"
				);

				const segmentArr = ediFile.match(regExp); //Returns array of matches found

				try {
					const withSegmentDelimiterRemoved = segmentArr.map(function (
						segment
					) {
						return segment.split(segmentDelimiter).join(""); //Removes segmentDelimters from segment string
					});
					const segmentArrayOfFields = withSegmentDelimiterRemoved.map(
						function (segment) {
							return segment.split(fieldDelimiter); //Split segment string into array of fields
						}
					);
					return returnFirstResultOnly
						? segmentArrayOfFields[0]
						: segmentArrayOfFields;
				} catch (e) {
					log.error("Error", e);
					errorLog.push("".concat(segmentTitle, " segment not found"));
					return false;
				}
			}

			function getTransactionControlNumber() {
				const regExp = new RegExp("MSH.*?" + segmentDelimiter);
				const mshSegment = ediFile.match(regExp);
				if (!mshSegment) {
					return;
				}
				try {
					return mshSegment[0].split(fieldDelimiter)[9];
				} catch (e) {
					errorLog.push(
						"No transaction control number found. Error: ".concat(e)
					);
				}
			}

			function getPatientObject() {
				const pidFieldsArr = _getSegmentFields("PID", true);
				const fullPatientName = pidFieldsArr[5];
				const fullPatientNameArr = fullPatientName.split(componentDelimiter);

				const firstName = fullPatientNameArr[fullPatientNameArr.length - 2];
				const middleInitial =
					fullPatientNameArr.length > 3 ? fullPatientNameArr[1] : "";
				const lastName = fullPatientNameArr[0];
				const dob = pidFieldsArr[7];
				const gender = pidFieldsArr[8];
				const language = pidFieldsArr[15];
				const addressField = pidFieldsArr[11];
				const addressArr = addressField.split(componentDelimiter);
				if (!pidFieldsArr) {
					log.error("PID segment not gotten");
					errorLog.push("PID segment not gotten successfully.");
				}

				const patientObj = {
					//revivalId: pidFieldsArr[2],
					revivalId: pidFieldsArr[3],
					name: {
						firstName: firstName,
						middleInitial: middleInitial,
						lastName: lastName,
					},
					dob: dob,
					address: {
						addressee: ""
							.concat(firstName)
							.concat(middleInitial ? " " + middleInitial : "", " ")
							.concat(lastName),
						streetAddress: addressArr[0],
						address2: addressArr[1],
						floor: "",
						room: "",
						city: addressArr[2],
						state: addressArr[3],
						zip: addressArr[4].replace("-", ""),
					},
					phoneNumber: pidFieldsArr[13],
					gender: gender,
					language: language,
				};
				return patientObj;
			}

			function getOrderDetails() {
				const orcFieldsArr = _getSegmentFields("ORC", true);

				const orderingRepFields = orcFieldsArr[11].split(componentDelimiter);
				const orderingRep = orderingRepFields[3]; //The ordering rep's name

				const orderDetails = { orderingRep: orderingRep };
				return orderDetails;
			}

			function getItems() {
				const rqdSegmentArr = _getSegmentFields("RQD", false);
				if (!rqdSegmentArr || rqdSegmentArr.length <= 0) {
					errorLog.push("No items found.}");
				}
				const itemsArr = [];
				rqdSegmentArr.forEach(function (itemArr) {
					const item = {
						hchbItemId: itemArr[1],
						itemName: itemArr[2].split(componentDelimiter)[0],
						itemDescription: itemArr[2].split(componentDelimiter)[1],
						quantity: itemArr[5],
						uom: itemArr[6],
					};
					itemsArr.push(item);
				});
				return itemsArr;
			}

			return {
				getTransactionControlNumber: getTransactionControlNumber,
				getPatientObject: getPatientObject,
				getOrderDetails: getOrderDetails,
				getItems: getItems,
			};
		})();

		const errorLog = [];

		const fieldDelimiter = partnerValues.formattingInfo[0].partnerValue;
		const segmentDelimiter = partnerValues.formattingInfo[1].partnerValue; //"\r" - although this variable doesn't print when logging, it is actually a string getting pulled.
		const componentDelimiter = partnerValues.formattingInfo[2].partnerValue;

		const ormObj = {
			transactionControlNumber: helperFunctions.getTransactionControlNumber(),
			patient: helperFunctions.getPatientObject(),
			orderDetails: helperFunctions.getOrderDetails(),
			items: helperFunctions.getItems(),
		};

		return { errorLog: errorLog, ormObj: ormObj };
	}
	return {
		parseHl7Orm: parseHl7Orm,
	};
});
