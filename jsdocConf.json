{"tags": {"allowUnknownTags": true, "dictionaries": ["jsdoc", "closure"]}, "source": {"include": ["Bridge/", "Classes/", "README.md"], "includePattern": "\\.js$", "excludePattern": "(node_modules/|Codebase_Documentation)"}, "plugins": ["plugins/markdown"], "opts": {"template": "node_modules/docdash", "destination": "./Codebase_Documentation/", "recurse": true, "verbose": true}, "markdown": {"parser": "gfm", "hardwrap": true, "idInHeadings": true}, "recurseDepth": 10, "sourceType": "module", "templates": {"cleverLinks": false, "monospaceLinks": false, "default": {"outputSourceFiles": true, "includeDate": false, "useLongnameInNav": true}}, "docdash": {"sort": false, "search": true, "collapse": "top", "static": true, "wrap": true, "disqus": "", "typedefs": true, "removeQuotes": "none", "scripts": [], "sectionOrder": ["Classes", "<PERSON><PERSON><PERSON>", "Externals", "Events", "Namespaces", "Mixins", "Tutorials", "Interfaces"], "navLevel": [10], "menu": {"GitHub Repo": {"href": "https://github.com/Centers-Health/valmed-netsuite", "target": "_blank", "class": "menu-item", "id": "github_link"}, "Jira Board": {"href": "https://valmardynamics.atlassian.net/jira/software/c/projects/LDEV/boards/17", "target": "_blank", "class": "menu-item", "id": "jira_link"}, "Confluence": {"href": "https://valmardynamics.atlassian.net/wiki/spaces/LDEV/pages/**********/Dynamic+Internal+Notes", "target": "_blank", "class": "menu-item", "id": "confluence_link"}, "SuiteScripts Folder": {"href": "https://5802576.app.netsuite.com/app/common/media/mediaitemfolders.nl?folder=897619", "target": "_blank", "class": "menu-item", "id": "website_link"}}}}