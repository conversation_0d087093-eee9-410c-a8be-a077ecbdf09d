/**
 * @description Example of how to load and use embeddings generated by the embedding MapReduce script
 * 
 * This example demonstrates how to:
 * 1. Load the embeddings JSON file
 * 2. Access individual embeddings
 * 3. Calculate similarity between embeddings (basic example)
 * 
 * <AUTHOR>
 */

// Example function to load embeddings from file
function loadEmbeddingsFromFile(fileId) {
    try {
        const file = require('N/file');
        
        const embeddingsFile = file.load({
            id: fileId // Use the file ID returned by the embedding MapReduce script
        });

        const fileContent = embeddingsFile.getContents();
        const embeddingsData = JSON.parse(fileContent);

        log.audit({
            title: 'Embeddings loaded successfully',
            details: `Total embeddings: ${embeddingsData.totalCount}, Last updated: ${embeddingsData.lastUpdated}`
        });

        return embeddingsData;
    } catch (err) {
        log.error({
            title: 'Error loading embeddings file',
            details: err
        });
        return null;
    }
}

// Example function to find similar descriptions using basic cosine similarity
function findSimilarDescriptions(targetDescription, embeddingsData, topN = 5) {
    try {
        // This is a simplified example - in practice you would:
        // 1. Generate an embedding for the target description
        // 2. Calculate cosine similarity with all stored embeddings
        // 3. Return the top N matches
        
        const similarities = [];
        
        // For demonstration purposes, this shows the data structure
        embeddingsData.embeddings.forEach((item, index) => {
            // In a real implementation, you would calculate cosine similarity here
            // const similarity = calculateCosineSimilarity(targetEmbedding, item.embedding);
            
            similarities.push({
                index: index,
                description: item.description,
                similarity: Math.random() // Placeholder - replace with actual similarity calculation
            });
        });

        // Sort by similarity (highest first) and return top N
        similarities.sort((a, b) => b.similarity - a.similarity);
        return similarities.slice(0, topN);
        
    } catch (err) {
        log.error({
            title: 'Error finding similar descriptions',
            details: err
        });
        return [];
    }
}

// Example usage in a SuiteLet or other script
function exampleUsage() {
    // Replace with the actual file ID returned by your embedding MapReduce script
    const EMBEDDINGS_FILE_ID = 'YOUR_EMBEDDINGS_FILE_ID_HERE';
    
    // Load the embeddings
    const embeddingsData = loadEmbeddingsFromFile(EMBEDDINGS_FILE_ID);
    
    if (embeddingsData) {
        // Example: Find similar descriptions to a target
        const targetDescription = "Medical surgical gloves";
        const similarItems = findSimilarDescriptions(targetDescription, embeddingsData, 3);
        
        log.audit({
            title: 'Similar items found',
            details: JSON.stringify(similarItems, null, 2)
        });
    }
}

// Data structure returned by the embedding MapReduce script:
/*
{
    "embeddings": [
        {
            "description": "Customer item description text",
            "embedding": [0.1, 0.2, 0.3, ...] // Array of numbers representing the embedding vector
        },
        // ... more embedding objects
    ],
    "lastUpdated": "2024-01-01T12:00:00.000Z",
    "totalCount": 500,
    "modelFamily": "COHERE_EMBED_ENGLISH"
}
*/
