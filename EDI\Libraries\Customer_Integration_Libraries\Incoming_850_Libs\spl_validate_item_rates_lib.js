/**
 * @NApiVersion 2.1
 */

define(["N/record", "N/search", "Numeral"], function (record, search, numeral) {
	function validateItemsRates(salesOrderId, purchaseOrderItems, customerId) {
		var itemRateErrors = [];

		function _processFail(logMessage, programmingError) {
			itemRateErrors.push({ logMessage, programmingError });
		}

		try {
			if (!salesOrderId) {
				_processFail("No Sales Order ID", true);
				return;
			}

			var salesOrder = record.load({
				type: record.Type.SALES_ORDER,
				id: salesOrderId,
			});

			var itemLineCount = salesOrder.getLineCount({
				sublistId: "item",
			});

			for (var x = 0; x < itemLineCount; x++) {
				var priceLevel = getPriceLevel();

				var itemId = getItemId();
				var itemName = getItemName();
				var itemRate = getItemRate();

				var isBasePriceLevel = checkIfBasePriceLevel();
				var isIndividualPriceLevel = checkIfIndividualPriceLevel();
				var isPpdItemForVentFacility = checkIfPpdItemForVentFacility();
				if (!isPpdItemForVentFacility) {
					checkIfRateDiscrepancy();
				}

				//#region getValues
				function getPriceLevel() {
					return salesOrder.getSublistValue({
						sublistId: "item",
						fieldId: "price",
						line: x,
					});
				}

				function getItemId() {
					return salesOrder.getSublistValue({
						sublistId: "item",
						fieldId: "item",
						line: x,
					});
				}

				function getItemName() {
					return salesOrder.getSublistText({
						sublistId: "item",
						fieldId: "item",
						line: x,
					});
				}

				function getItemRate() {
					return salesOrder.getSublistText({
						sublistId: "item",
						fieldId: "rate",
						line: x,
					});
				}
				//#endregion

				function checkIfBasePriceLevel() {
					if (priceLevel == 1) {
						_processFail(
							itemName +
								" is missing a custom price. Please correct the pricing on the sales order and on the customer's parent record. "
						);
						return true;
					}
				}

				function checkIfIndividualPriceLevel() {
					if (priceLevel == 21) {
						_processFail(
							`${itemName} is an individual pricing item and the customer does not have custom price entered in NetSuite for this item.`
						);

						return true;
					}
				}

				function checkIfPpdItemForVentFacility() {
					var ventFacilities = [
						"2179", //Richmond Center
						"2186", //Triboro Center
						"2171", //Northern Manor
						"2151", //Concord Nursing Home
					];

					var isVentFacility = ventFacilities.some(function (ventFac) {
						return ventFac === customerId;
					});

					if (isVentFacility) {
						var itemObj = search.lookupFields({
							type: search.Type.ITEM,
							id: itemId,
							columns: "custitem_spl_ppd_item",
						});

						var isPpdItem = itemObj["custitem_spl_ppd_item"];

						if (isPpdItem) {
							_processFail(
								`${itemName} is a PPD item for a vent facility so the price is $0.`
							);

							salesOrder.setSublistValue({
								sublistId: "item",
								fieldId: "rate",
								line: x,
								value: 0,
							});

							return true;
						}
					}
				}

				function checkIfRateDiscrepancy() {
					var itemOnPurchaseOrder = purchaseOrderItems.find((item) => {
						return (
							item.itemName.toUpperCase().split(".").join("") ==
								itemName.toUpperCase().split(".").join("") ||
							item.newItemName?.toUpperCase().split(".").join("") ==
								itemName.toUpperCase().split(".").join("")
						);
					});

					if (!itemOnPurchaseOrder) {
						//Maybe is a matrix item on the SO and has the full item name => search by string after the colon
						const regExp = new RegExp(/(?<=:).*$/g);
						let itemIdOnly = itemName.match(regExp);

						let formattedItem =
							itemIdOnly &&
							itemIdOnly.length > 0 &&
							itemIdOnly.pop().split(".").join("").toUpperCase().trim();

						itemOnPurchaseOrder = purchaseOrderItems.find((item) => {
							return (
								item.itemName.toUpperCase().split(".").join("").trim() ==
								formattedItem
							);
						});
					}

					if (!itemOnPurchaseOrder) {
						_processFail(`No matching item found on SO for ${itemName}`);
						return true;
					}

					if (
						numeral(itemOnPurchaseOrder.rate).format("$0,0.00") !=
							numeral(itemRate).format("$0,0.00") || //Prices dont match (Need to format so the numbers will be in the same format in order to compare.)
						itemOnPurchaseOrder.missingPricingInNetSuite
					) {
						var useCustomersPrice =
							/*1	*/ isBasePriceLevel ||
							/*2	*/ isIndividualPriceLevel ||
							/*3	*/ parseInt(itemOnPurchaseOrder.rate.split(",").join("")) >
								parseInt(itemRate.split(",").join("")) || //Customer's price on the PO is higher than their price in NetSuite
							/*4	*/ itemOnPurchaseOrder.missingPricingInNetSuite; //There is no pricing in NetSuite - this is already set
						//when the sales order is created so that the sales order doesnt fail.

						if (useCustomersPrice) {
							//Use the customer's price instead
							salesOrder.setSublistValue({
								sublistId: "item",
								fieldId: "rate",
								line: x,
								value: itemOnPurchaseOrder.rate,
							});
						}

						var errorText = `The customer sent a rate of ${numeral(
							itemOnPurchaseOrder.rate
						).format("$0,0.00")} for ${itemName}.`;

						if (
							!isBasePriceLevel &&
							!isIndividualPriceLevel &&
							!itemOnPurchaseOrder.missingPricingInNetSuite
						) {
							errorText += ` The price in NetSuite is ${numeral(
								itemRate
							).format("$0,0.00")}.\n `;
						}

						if (useCustomersPrice) {
							errorText += ` The customer's price was used because `;

							if (isBasePriceLevel) {
								errorText += `this item doesn't have a price assigned to it other than the base level price.`;
							} else if (isIndividualPriceLevel) {
								errorText += `this item is assigned to individual pricing in NetSuite.`;
							} else if (
								parseInt(itemOnPurchaseOrder.rate.split(",").join("")) >
								parseInt(itemRate.split(",").join(""))
							) {
								errorText += `the customer sent a rate on their purchase order that is higher than their price in NetSuite.`;
							} else if (itemOnPurchaseOrder.missingPricingInNetSuite) {
								errorText += `this item doesn't have any pricing set up in NetSuite`;
							} else {
								errorText += `reason unclear.`;
							}
						} else {
							errorText += " NetSuite's price was used.";
						}

						_processFail(errorText);
					}
				}
			}

			salesOrder.save();
		} catch (e) {
			_processFail(`Error Validating Rates ${e}`, true);
		}

		return itemRateErrors;
	}

	return {
		validateItemsRates: validateItemsRates,
	};
});
