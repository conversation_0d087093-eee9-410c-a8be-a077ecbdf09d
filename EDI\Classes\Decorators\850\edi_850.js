/**
 * @description Represents the incoming Purchase Order or outgoing Sales Order
 *
 * @NApiVersion 2.1
 * <AUTHOR> <<EMAIL>>
 */

define([
    "exports",
    "require",
    "N/file",
    "N/log",
    "../edi_decorator",
    "../../Models/File/edi_incoming",
    "../../Models/File/edi_outgoing",
], (/** @type {any} */ exports, /** @type {any} */ require) => {
    const file = require("N/file");
    const log = require("N/log");
    const { EDIDecorator } = require("../edi_decorator");
    const { EDIIncoming } = require("../../Models/File/edi_incoming");
    const { EDIOutgoing } = require("../../Models/File/edi_outgoing");

    /**
     * 850 Class
     * 
     * @class
     * @typedef {import("../../Interfaces/Models/File/edi_file").EDIFileInterface} EDIFileInterface
     * @typedef {import("../../Interfaces/Models/File/edi_file").EDIPostProcessEmail} EDIPostProcessEmail
     * @typedef {import("../../Interfaces/Models/Server/edi_server").EDIServerInterface} EDIServerInterface
     * @typedef {import("../../Interfaces/Models/Server/edi_server").EDIMFTServerInterface} EDIMFTServerInterface
     * @typedef {import("../../Interfaces/Decorators/850/edi_850").EDI850Interface} EDI850Interface
     * @typedef {import("../../Interfaces/Decorators/edi_decorator").DecoratorParams} DecoratorParams
     * @typedef {import("../../Decorators/850/edi_850_parser").EDI850Parser} EDI850Parser
     * @typedef {import("../../Decorators/edi_processor").EDIProcessor} EDIProcessor
     * @typedef {import("../../../../Classes/vlmd_custom_error_object").CustomErrorObject} CustomErrorObject
     * @implements {EDI850Interface}
     * @extends {EDIDecorator}
     */

    class EDI850 extends EDIDecorator {
        /** @param {DecoratorParams} params */
        constructor(params) {
            super();
            
            //EDI File
            /** @type {CustomErrorObject | null} */
            this.customError = null;
            /** @type {EDI850Parser | null} */
            this.parser = params.parser;
            /** @type {EDIProcessor | null} */
            this.processor = params.processor;
            /** @type {EDIServerInterface | EDIMFTServerInterface} */
            this.server;
            /** @type {string} */
            this.type = params.type;
            /** @type {number} */
            this.typeId = params.typeId;

            // EDI Outgoing
            /** @type {file.File | null} */
            this.file = null;
            /** @type {string} */
            this.fileName = "";
            /** @type {number} */
            this.fileId = 0;
            /** @type {number} */
            this.folderId = 0;
            /** @type {string} */
            this.emailSubject = ""

            // EDI Incoming
            /** @type {any} */
            this.transaction = null;
        }

        /**
         * Decorate the EDI File with 850 functions
         *
         * @param {EDIFileInterface} ediFile Object to decorate
         */
        decorate(ediFile) {
            log.debug("EDI 850 (decorate)", `Decorating the EDI File: ${ediFile.ediType}`);
            ediFile.type = this.type;
            ediFile.typeId = this.typeId;
            if (ediFile instanceof EDIIncoming) {
                ediFile.parser = this.parser;
                ediFile.parse = this.parse;
                ediFile.transform = this.transform;
                ediFile.summarize = this.summarize;
                ediFile.archive = this.archive;
            } else if (ediFile instanceof EDIOutgoing) {
                ediFile.processor = this.processor;
                ediFile.fileName = this.fileName;
                ediFile.folderId = this.folderId;
                ediFile.emailSubject = this.emailSubject;
                ediFile.load = this.load;
                ediFile.process = this.process;
                ediFile.create = this.create;
                ediFile.save = this.save;
                ediFile.email = this.email;
                ediFile.upload = this.upload;
            }
        }

        /**
         * Parse the incoming EDI File
         *
         * @returns {void}
         */
        parse() {
            this.parser?.parse();
        }

        /**
         * Transform the EDI data to a NetSuite record
         *
         * @returns {void}
         */
        transform() {
            this.parser?.transform();
        }

        /**
         * Move the EDI File to the Reference directory
         *
         * @param {object} params Archive params
         * @param {string} [params.filename] File name
         * @param {string} [params.source] Source directory
         * @param {string} [params.target] Target directory
         * @param {string} [params.content] File content to upload
         * @returns {void}
         */
        archive({content}) {
            try {
                log.debug("EDI 850 (upload)", `Uploading file..`);
                this.server?.connect({
                    target: "REF",
                });
                if (content && this.server?.connection) {
                    const fileToUpload = file.create({
                        name: this.parser?.filename || `edi_in_850_${new Date().getTime()}.txt`,
                        fileType: file.Type.PLAINTEXT,
                        contents: content
                    });
                    this.server.connection.upload({
                        file: fileToUpload,
                        replaceExisting: true,
                    });
                } else {
                    log.error("ERROR: EDI 850 (archive)", "Cannot upload file to reference directory. No connection or file created.")
                }
            } catch (/** @type {any} */ err) {
                log.error("ERROR: EDI 850 (archive)", err);
                throw this.customError?.updateError({
                    errorType: this.customError.ErrorTypes.SFTP_CONNECTION_FAILURE,
                    summary: "FILE_NOT_UPLOADED",
                    details: `Error uploading file: ${err}`,
                });
            }
        }

        /**
         * Save the EDI data as a NetSuite EDI Transaction
         * Override the return type of parent
         *
         * @returns {EDIPostProcessEmail|null}
         */
        summarize() {
            this.parser?.summarize();

            return this.parser?.email ?? null;
        }

        /**
         * Create the NetSuite File Record
         *
         * @param {{[key:string]: any}} [params] Parameters
         * @returns {void}
         */
        create(params) {};

        /**
         * Return the query to load the records
         *
         * @param {{[key:string]: any}} [params] Parameters
         * @returns {string} Query string
         */
        load(params) { return ""; };

        /**
         * Process the record object using the decorator processor
         *
         * @param {{[key:string]: any}} [params] Parameters
         * @returns {string} Invoice row as string
         */
        process(params) { return "" };
    
        /**
         * Save the NetSuite File to File Cabinet
         *
         * @param {{[key:string]: any}} [params] Parameters
         * @returns {void}
         */
        save(params) {};

        /**
         * Send the NetSuite File as an E-mail attachment
         *
         * @param {{[key:string]: any}} [params] Parameters
         * @returns {void}
         */
        email(params) {};

        /**
         * Upload the NetSuite File to the Partner's SFTP Server
         *
         * @param {{[key:string]: any}} [params] Parameters
         * @returns {void}
         */
        upload(params) {};

        /**
         * Mark the transaction as processed
         *
         * @param {{[key:string]: any}} [params] Parametrised object
         * @returns {number} Document Control Number record ID
         */
        complete(params) { return 0; }
    }

    exports.EDI850 = EDI850;
});