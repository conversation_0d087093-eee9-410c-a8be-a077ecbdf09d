/**
 * @description Class containing function template specific to parsing 810 Incoming file
 *
 * @NApiVersion 2.1
 * <AUTHOR> <<EMAIL>>
 */

define([
    "exports",
    "require",
    "N/log",
    "../edi_parser",
], (/** @type {any} */ exports, /** @type {any} */ require) => {
    const log = require("N/log");
    const { EDIParser } = require("../edi_parser");

    /**
     * 810 Bill Parser Class
     *
     * @class
     * @typedef {import("../../Interfaces/Models/File/edi_file").EDIPostProcessEmail} EDIPostProcessEmail
     * @typedef {import("../../Interfaces/Decorators/810/edi_810_parser").EDI810ParserInterface} EDI810ParserInterface
     * @extends {EDIParser}
     * @implements {EDI810ParserInterface}
     */
    class EDI810Parser extends EDIParser {
        /** @param {{[key:string]: any}} params */
        constructor(params) {
            super(params);
        }

        parse() {
            log.error("EDI 810 Parser", "Error: Parent parse function needs to be implemented by child class.");
        }

        transform() {
            log.error("EDI 810 Parser", "Error: Parent transform function needs to be implemented by child class.");
        }

        summarize() {
            log.error("EDI 810 Parser", "Error: Parent summarize function needs to be implemented by child class.");
        }
    }

    exports.EDI810Parser = EDI810Parser;
});