import record from "N/record";
import { EDIInvoice } from "../../../../../EDI/Classes/Models/Transaction/edi_invoice";

describe("edi_invoice", () => {
  describe("getLineItems", () => {
    const getSublistValue = jest.fn().mockImplementation((param) => {
      if (
        param.fieldId === "custcol_crh_gtin" ||
        param.fieldId === "custcol_crh_upc"
      ) {
        return "";
      } else {
        return 10;
      }
    });
    const getSublistText = jest.fn().mockImplementation((param) => {
      if (param.fieldId === "item" && param.line === 0) {
        return "IJS.7675";
      } else {
        return param.fieldId;
      }
    });
    /** @type {EDIInvoice} */
    let ediInvoice;
    beforeEach(() => {
      // @ts-ignore missing the following properties from type 'Record'
      jest.spyOn(record, "load").mockReturnValue({
        getValue: (param) => {
          // @ts-ignore Property 'fieldId' does not exist on type 'string | GetFieldOptions'.
          switch (param.fieldId) {
            case "subtotal":
              return "100";
            default:
              return "default";
          }
        },
        getSublistValue,
        getSublistText,
        getLineCount: () => 2,
      });
      ediInvoice = new EDIInvoice({});
      ediInvoice.getLineItems();
    });
    it("get line items", () => {
      expect(ediInvoice.lineItems).toEqual([
        {
          quantity: 10,
          units: "units",
          rate: 10,
          name: "IJS.7675",
          description: "description",
          amount: 10,
          gtin: "00850052327675",
          upc: "850052327675",
        },
        {
          quantity: 10,
          units: "units",
          rate: 10,
          name: "item",
          description: "description",
          amount: 10,
          gtin: "0850052327668",
          upc: "850052327668",
        },
      ]);
    });
  });
});
