/**
 * @NApiVersion 2.1
 */

//@ts-ignore
define(["N/log", "Moment"], function (log, moment) {
	function parse850(ediFile, partnerValues, customer) {
		try {
			const errorLog = [];
			let purchaseOrderObj;

			const fieldDelimiter = partnerValues.formattingInfo[0].partnerValue;
			const segmentDelimiter = partnerValues.formattingInfo[1].partnerValue;

			ediFile = ediFile.split("\n").join("").split("\r").join("");

			var is997 = checkIfFileIs997();

			if (!is997) {
				purchaseOrderObj = getPurchaseOrderObject();
			}

			return {
				purchaseOrderObj,
				errorLog,
				is997,
			};

			/**********Parse Invoice Helper Functions**********/
			function getPurchaseOrderObject() {
				return {
					transactionControlNumber: getTransactionControlNumber(),
					poNumber: getPONumber(),
					poDate: getPoDate(),
					customerExternalId: getCustomerExternalId(),
					streetAddress: getStreetAddress(),
					customerNameInTheirSystem: getCustomerNameInTheirSystem(),
					address: getShipToAddress(),
					totalAmount: getTotalAmount(),
					memo: getMemo(),
					communicationObj: getCommunicationObj()??{},
					items: getItems(),
				};
			}

			function getSegment(segmentTitle, errorIfNotFound) {
				const regExp = new RegExp(
					segmentDelimiter + segmentTitle + ".*?" + segmentDelimiter
				);
				let segment = ediFile.match(regExp);

				try {
					segment = segment[0].split(segmentDelimiter).join("");
					return segment;
				} catch (e) {
					if (errorIfNotFound) {
						errorLog.push(`${segmentTitle} segment not found`);
					}

					return false;
				}
			}

			function checkIfFileIs997() {
				const stSegment = getSegment("ST", true);

				if (!stSegment || !stSegment.split(fieldDelimiter)[2]) {
					errorLog.push(`No EDI file type found.`);
				}

				if (stSegment.split(fieldDelimiter)[1] == "997") {
					return true;
				}
			}

			function getTransactionControlNumber() {
				const stSegment = getSegment("ST", true);

				if (!stSegment) {
					return;
				}
				try {
					return stSegment.split(fieldDelimiter)[2];
				} catch (e) {
					errorLog.push(`No transaction control number found. Error: ${e}`);
				}
			}

			function getPONumber() {
				const begSegment = getSegment("BEG", true);

				if (!begSegment) {
					is997 = true;
					return;
				}
				try {
					return begSegment.split(fieldDelimiter)[3];
				} catch (e) {
					errorLog.push(`No purchase order number found. Error: ${e}`);
				}
			}

			function getPoDate() {
				try {
					return moment().format("YYYYMMDD");
				} catch (e) {
					errorLog.push(`Error getting Purchase Order date. Error: ${e}`);
				}
			}

			function getCustomerExternalId() {
				const n1Segment = getSegment("N1\\" + fieldDelimiter + "ST", true);

				try {
					return n1Segment.split(fieldDelimiter)[4].trim();
				} catch (e) {
					errorLog.push(`No customer external ID found. Error: ${e}`);
				}
			}

			function getStreetAddress() {
				try {
					const n3Segment = getSegment("N3", true);

					return n3Segment.split(fieldDelimiter)[1];
				} catch (e) {
					errorLog.push(`Error processing street address. Error: ${e}`);
				}
			}

			function getCustomerNameInTheirSystem() {
				try {
					const n1Segment = getSegment("N1\\" + fieldDelimiter + "ST", true);

					return n1Segment.split(fieldDelimiter)[2];
				} catch (e) {
					errorLog.push(`Error processing name. Error: ${e}`);
				}
			}

			function getShipToAddress() {
				try {
					const n4Segment = getSegment("N4", true);

					return {
						streetAddress: getStreetAddress(),
						city: n4Segment.split(fieldDelimiter)[1],
						state: n4Segment.split(fieldDelimiter)[2],
						zip: n4Segment.split(fieldDelimiter)[3],
					};
				} catch (e) {
					errorLog.push(`Error processing address. Error: ${e}`);
				}
			}

			function getTotalAmount() {
				try {
					const amtSegment = getSegment("AMT", false);

					if (amtSegment) {
						return amtSegment.split(fieldDelimiter)[2];
					}
				} catch (e) {
					errorLog.push(`Error processing amount total. Error: ${e}`);
				}
			}

			function getMemo() {
				const msgSegment = getSegment("MSG", false);

				if (msgSegment) {
					return msgSegment.split(fieldDelimiter)[1];
				}
			}

			function getCommunicationObj() {
				const perSegment = getSegment("PER", false);

				if (perSegment) {
					return {
						communicationContact: perSegment.split(fieldDelimiter)[2],
						communicationMethod: perSegment.split(fieldDelimiter)[3],
						contactNumber: perSegment.split(fieldDelimiter)[4],
					};
				}
			}

			function getItems() {
				const itemsArr = [];

				try {
					const fileHasPID = getSegment("PID", false);

					const regExp = new RegExp(
						"PO1.*?" +
							segmentDelimiter +
							(fileHasPID ? "PID.*?" + segmentDelimiter : ""),
						"gs"
					);

					const itemSegments = ediFile.match(regExp);

					if (!itemSegments) {
						errorLog.push(`No items found to parse.`);
						return;
					}

					itemSegments.forEach(function (itemSegment) {
						let po1SegmentArr = itemSegment.split(segmentDelimiter);
						po1SegmentArr = po1SegmentArr[0].split(fieldDelimiter);

						let item = {
							lineNumber: po1SegmentArr[1],
							itemName: po1SegmentArr[7].trim(),
							quantity: po1SegmentArr[2],
							uom: po1SegmentArr[3],
							rate: po1SegmentArr[4],
						};

						if (fileHasPID) {
							let ackSegmentArr = itemSegment.split(segmentDelimiter);
							ackSegmentArr = ackSegmentArr[1].split(fieldDelimiter);

							item.description = ackSegmentArr[5];
						}

						itemsArr.push(item);
					});

					if (itemsArr.length <= 0) {
						errorLog.push(`No items added to parsed PO object.`);
					}

					return itemsArr;
				} catch (e) {
					errorLog.push(`Error processing items. Error: ${e}`);
				}
			}
		} catch (err) {
			log.error("ERROR_PARSING_FILE", err.message);
		}
	}

	return {
		parse850: parse850,
	};
});
