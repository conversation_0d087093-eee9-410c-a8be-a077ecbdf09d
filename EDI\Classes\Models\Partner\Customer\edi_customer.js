/**
 * @description Customer class implementation
 *
 * @NApiVersion 2.1
 * <AUTHOR> <<EMAIL>>
 */

define([
    "exports",
    "require",
    "../edi_partner",
    "N/log",
], (/** @type {any} */ exports, /** @type {any} */ require) => {
    const { EDIPartner } = require("../edi_partner");
    const log = require("N/log");

    /**
     * EDI Partner class representing customers
     *
     * @class
     * @typedef {import("../../../Interfaces/Models/Partner/edi_partner").EDICustomerInterface} EDICustomerInterface
     * @implements {EDICustomerInterface}
     * @extends {EDIPartner}
     */
    class EDICustomer extends EDIPartner {
        constructor() {
            super();
            /** @type {boolean} */
            this.shouldIncludeIsReplacementFor = false;
            this.shouldIncludeDeactivateItem = false;
        }

        /**
         * Create a SuiteQL Query String to lookup existing customer record in NetSuite
         *
         * @param {string} identifier Customer ID
         * @returns {string} SuiteQL Query
         */
        generateQueryString(identifier) {
            log.error({
                title: "EDI Customer (generateQueryString)",
                details: "Error: Function should be implemented by child class."
            });
            return "";
        }
    }

    exports.EDICustomer = EDICustomer;
});