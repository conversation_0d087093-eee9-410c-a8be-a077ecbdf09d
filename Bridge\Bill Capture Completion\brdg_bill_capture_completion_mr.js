/**
* @description Creates PO and Vendor Bill for all flagged vendor bills created via Bill Capture
*              Deletes Bill Capture Vendor Bills Afterwards
* </br><b>Schedule:</b> Runs every night at 12:30 AM
*
* @NApiVersion 2.1
* @NScriptType MapReduceScript
* @NAmdConfig /SuiteScripts/config.json
*
* <AUTHOR>
* @module brdg_bill_capture_completion_mr
 */
define([
	"require",
	"N/record",
	"N/runtime",
	"N/query",
	"N/search",
	"N/file",
	"N/cache",
	"../../Classes/vlmd_custom_error_object",
	"../../Classes/vlmd_mr_summary_handling",
],(require) => {
        const record = require("N/record");
        const runtime = require("N/runtime");
		const query = require("N/query");
		const search = require("N/search");
		const file = require("N/file");
		const cache = require("N/cache");
		
        const CustomErrorObject = require("../../Classes/vlmd_custom_error_object");
        const customErrorObject = new CustomErrorObject();
		
	function getInputData(context) {
        try {
			let billCaptureVendorBillIds = [];
			const billCaptureQuery = `
				SELECT id FROM transaction WHERE BUILTIN.DF(status) LIKE '%Pending Approval' AND custbody_brdg_bill_capture = 'T'
			`;
			query.runSuiteQL({
				query: billCaptureQuery,
			}).asMappedResults().forEach((result) => {
				billCaptureVendorBillIds.push(result.id);
			});
			
			return billCaptureVendorBillIds;
		} catch (err) {
			customErrorObject.throwError({
				summaryText: `GET_INPUT_DATA_ERROR`,
				error: err,
				errorWillBeGrouped: true,
			});
		}
	}
	
	function reduce(context) {
		try {
			const vendorBillId = context.values[0];
			const originalVendorBill = record.load({
				type: record.Type.VENDOR_BILL,
				id: vendorBillId,
			});

			// Create Purchase Order from Vendor Bill
			const poId = createPurchaseOrderFromVendorBill(originalVendorBill, vendorBillId);

			// Create new Vendor Bill from the Purchase Order
			const newVendorBillId = createVendorBillFromPurchaseOrder(poId, originalVendorBill, vendorBillId);

			// Mark original vendor bill for voiding/cancellation
			// markOriginalBillForCancellation(vendorBillId);
			markOriginalBillForCancellation(newVendorBillId);

			log.audit('Bill Capture Completion Success', {
				originalVendorBillId: vendorBillId,
				purchaseOrderId: poId,
				newVendorBillId: newVendorBillId
			});

			context.write({
				key: "BILL_CAPTURE_COMPLETED",
				value: {
					originalVendorBillId: vendorBillId,
					purchaseOrderId: poId,
					newVendorBillId: newVendorBillId,
					status: "SUCCESS"
				}
			});
		} catch (err) {
			log.error('Bill Capture Completion Error', {
				vendorBillId: context.key,
				error: err.message,
				stack: err.stack
			});

			customErrorObject.throwError({
				summaryText: `BILL_CAPTURE_COMPLETION_ERROR`,
				error: err,
				recordId: context.key,
				recordType: "VENDOR_BILL",
				errorWillBeGrouped: true,
			});
		}
	}

	function summarize(context) {
		const StageHandling = require("../../Classes/vlmd_mr_summary_handling");
		const stageHandling = new StageHandling(context);
  
		stageHandling.printScriptProcessingSummary();
  
		stageHandling.printRecordsProcessed({
			includeLineBreak: true,
			includeKey: true
		});
  
		stageHandling.printErrors({
			groupErrors: true,
		});
	}

	/**
	 * Creates a comprehensive Purchase Order from a Vendor Bill with full field mapping
	 *
	 * @param {Object} vendorBillRecord - Source vendor bill record
	 * @param {string|number} vendorBillId - Vendor bill internal ID
	 * @returns {string|number} Created purchase order internal ID
	 */
	function createPurchaseOrderFromVendorBill(vendorBillRecord, vendorBillId) {
		try {
			log.audit('Creating Purchase Order', {
				sourceVendorBillId: vendorBillId
			});

			// Create PO record
			const poRecord = record.create({
				type: record.Type.PURCHASE_ORDER,
				isDynamic: false,
			});

			// Map header fields from Vendor Bill to Purchase Order
			mapVendorBillHeaderToPO(vendorBillRecord, poRecord, vendorBillId);

			// Map line items from Vendor Bill to Purchase Order
			mapVendorBillLinesToPO(vendorBillRecord, poRecord, vendorBillId);

			// Save the Purchase Order
			const poId = poRecord.save({
				enableSourcing: true,
				ignoreMandatoryFields: true,
			});

			log.audit('Purchase Order Created Successfully', {
				sourceVendorBillId: vendorBillId,
				purchaseOrderId: poId
			});

			return poId;

		} catch (error) {
			log.error('Purchase Order Creation Failed', {
				sourceVendorBillId: vendorBillId,
				error: error.message,
				stack: error.stack
			});
			throw new Error(`Failed to create Purchase Order from Vendor Bill ${vendorBillId}: ${error.message}`);
		}
	}

	/**
	 * Maps header fields from Vendor Bill to Purchase Order
	 *
	 * @param {Object} vendorBillRecord - Source vendor bill record
	 * @param {Object} poRecord - Target purchase order record
	 * @param {string|number} vendorBillId - Vendor bill internal ID for logging
	 */
	function mapVendorBillHeaderToPO(vendorBillRecord, poRecord, vendorBillId) {
		const fieldMappings = [
			// Core required fields
			{ source: "entity", target: "entity", required: true },
			{ source: "subsidiary", target: "subsidiary", required: true },
			{ source: "trandate", target: "trandate", required: true },

			// Standard fields
			{ source: "memo", target: "memo", required: false },
			{ source: "terms", target: "terms", required: false },
			{ source: "currency", target: "currency", required: false },
			{ source: "exchangerate", target: "exchangerate", required: false },
			{ source: "location", target: "location", required: false },
			{ source: "department", target: "department", required: false },
			{ source: "class", target: "class", required: false },
		];

		const mappingResults = [];

		fieldMappings.forEach(mapping => {
			try {
				const sourceValue = vendorBillRecord.getValue({ fieldId: mapping.source });

				if (sourceValue !== null && sourceValue !== undefined && sourceValue !== "") {
					poRecord.setValue({
						fieldId: mapping.target,
						value: sourceValue,
					});
					mappingResults.push({
						field: mapping.source,
						value: sourceValue,
						status: "SUCCESS"
					});
				} else if (mapping.required) {
					throw new Error(`Required field ${mapping.source} is missing or empty`);
				} else {
					mappingResults.push({
						field: mapping.source,
						value: null,
						status: "SKIPPED_EMPTY"
					});
				}
			} catch (fieldError) {
				if (mapping.required) {
					throw fieldError;
				} else {
					log.debug('Optional Field Mapping Failed', {
						vendorBillId: vendorBillId,
						field: mapping.source,
						error: fieldError.message
					});
					mappingResults.push({
						field: mapping.source,
						status: "ERROR",
						error: fieldError.message
					});
				}
			}
		});

		// Add reference to original vendor bill
		try {
			poRecord.setValue({
				fieldId: "memo",
				value: `Created from Bill Capture Vendor Bill ${vendorBillId}. Original memo: ${vendorBillRecord.getValue({ fieldId: "memo" }) || "None"}`
			});
		} catch (memoError) {
			log.debug('Memo Field Update Failed', {
				vendorBillId: vendorBillId,
				error: memoError.message
			});
		}

		log.debug('Header Field Mapping Completed', {
			vendorBillId: vendorBillId,
			mappingResults: mappingResults
		});
	}

	/**
	 * Creates a new Vendor Bill from a Purchase Order and attaches files from the original Bill Capture vendor bill
	 *
	 * @param {string|number} poId - Purchase order internal ID
	 * @param {Object} originalVendorBillRecord - Original Bill Capture vendor bill record
	 * @param {string|number} originalVendorBillId - Original vendor bill internal ID
	 * @returns {string|number} Created vendor bill internal ID
	 */
	function createVendorBillFromPurchaseOrder(poId, originalVendorBillRecord, originalVendorBillId) {
		try {
			log.audit('Creating Vendor Bill from Purchase Order', {
				purchaseOrderId: poId,
				originalVendorBillId: originalVendorBillId
			});

			const additionalFieldMappings = [
				// Additional required fields
				{ source: "tranid", target: "tranid", required: true },
				{ source: "nextapprover", target: "nextapprover", required: true },
			];

			// Transform Purchase Order to Vendor Bill
			const newVendorBill = record.transform({
				fromType: record.Type.PURCHASE_ORDER,
				fromId: poId,
				toType: record.Type.VENDOR_BILL,
			});

			// Set reference to original Bill Capture vendor bill
			const originalMemo = originalVendorBillRecord.getValue({ fieldId: "memo" }) || "";
			const newMemo = `Created from Bill Capture process. Original Bill ID: ${originalVendorBillId}. ${originalMemo}`;

			try {
				additionalFieldMappings.forEach(mapping => {
					const sourceValue = originalVendorBillRecord.getValue({ fieldId: mapping.source });

					if (sourceValue !== null && sourceValue !== undefined && sourceValue !== "") {
						newVendorBill.setValue({
							fieldId: mapping.target,
							value: sourceValue,
						});
					} else if (mapping.required) {
						throw new Error(`Required field ${mapping.source} is missing or empty`);
					}
				});

				newVendorBill.setValue({
					fieldId: "memo",
					value: newMemo
				});

			} catch (memoError) {
				log.debug('Additional Vendor Bill Field Update Failed', {
					originalVendorBillId: originalVendorBillId,
					error: memoError.message
				});
			}

			// Save the new vendor bill
			const newVendorBillId = newVendorBill.save({
				enableSourcing: true,
				ignoreMandatoryFields: true,
			});

			log.audit('New Vendor Bill Created Successfully', {
				originalVendorBillId: originalVendorBillId,
				purchaseOrderId: poId,
				newVendorBillId: newVendorBillId
			});

			// Attach files from original Bill Capture vendor bill to new vendor bill
			attachFilesFromOriginalBill(originalVendorBillId, newVendorBillId);

			return newVendorBillId;
		} catch (error) {
			log.error('Vendor Bill Creation from PO Failed', {
				purchaseOrderId: poId,
				originalVendorBillId: originalVendorBillId,
				error: error.message,
				stack: error.stack
			});
			throw new Error(`Failed to create Vendor Bill from Purchase Order ${poId}: ${error.message}`);
		}
	}

	/**
	 * Attaches files from the original Bill Capture vendor bill to the new vendor bill
	 *
	 * @param {string|number} originalVendorBillId - Original vendor bill internal ID
	 * @param {string|number} newVendorBillId - New vendor bill internal ID
	 */
	function attachFilesFromOriginalBill(originalVendorBillId, newVendorBillId) {
		try {
			log.debug('Attaching Files from Original Bill', {
				originalVendorBillId: originalVendorBillId,
				newVendorBillId: newVendorBillId || 'N/A'
			});

			// Get files attached to the original vendor bill
			const fileResults = search.lookupFields({
				type: search.Type.TRANSACTION,
				id: originalVendorBillId,
				columns: "file.internalid",
			})["file.internalid"];

			if (fileResults && fileResults.length > 0) {
				const attachmentResults = [];

				fileResults.forEach((fileResult) => {
					try {
						const fileId = fileResult.value;

						// Move file to another folder
						const fileObj = file.load({
							id: fileId
						});
						fileObj.folder = 8451007;
						fileObj.save();

						// Attach each file to the new vendor bill
						record.attach({
							record: {
								type: 'file',
								id: fileId
							},
							to: {
								type: record.Type.VENDOR_BILL,
								id: newVendorBillId
							}
						});

						attachmentResults.push({
							fileId: fileId,
							status: "SUCCESS"
						});

						log.debug('File Attached Successfully', {
							fileId: fileId,
							originalVendorBillId: originalVendorBillId,
							newVendorBillId: newVendorBillId
						});

					} catch (attachError) {
						log.error('File Attachment Failed', {
							fileId: fileResult.value,
							originalVendorBillId: originalVendorBillId,
							newVendorBillId: newVendorBillId,
							error: attachError.message
						});

						attachmentResults.push({
							fileId: fileResult.value,
							status: "ERROR",
							error: attachError.message
						});
					}
				});

				log.audit('File Attachment Process Completed', {
					originalVendorBillId: originalVendorBillId,
					newVendorBillId: newVendorBillId,
					totalFiles: fileResults.length,
					attachmentResults: attachmentResults
				});

			} else {
				log.debug('No Files Found to Attach', {
					originalVendorBillId: originalVendorBillId,
					newVendorBillId: newVendorBillId
				});
			}

		} catch (error) {
			log.error('File Attachment Process Failed', {
				originalVendorBillId: originalVendorBillId,
				newVendorBillId: newVendorBillId,
				error: error.message,
				stack: error.stack
			});
			// Don't throw error here - file attachment failure shouldn't stop the main process
		}
	}

	/**
	 * Marks the original Bill Capture vendor bill for cancellation by setting approval status to Rejected
	 *
	 * @param {string|number} originalVendorBillId - Original vendor bill internal ID
	 */
	function markOriginalBillForCancellation(originalVendorBillId) {
		try {
			record.submitFields({
				type: record.Type.VENDOR_BILL,
				id: originalVendorBillId,
				values: {
					'cancelvendbill': 'T'
				}
			});
		} catch (error) {
			log.error('Failed to Mark Original Bill for Voiding/Cancellation', {
				originalVendorBillId: originalVendorBillId,
				purchaseOrderId: poId,
				newVendorBillId: newVendorBillId,
				error: error.message,
				stack: error.stack
			});
			// Don't throw error here - this shouldn't stop the main process
		}
	}

	/**
	 * Maps line items from Vendor Bill to Purchase Order
	 *
	 * @param {Object} vendorBillRecord - Source vendor bill record
	 * @param {Object} poRecord - Target purchase order record
	 * @param {string|number} vendorBillId - Vendor bill internal ID for logging
	 */
	function mapVendorBillLinesToPO(vendorBillRecord, poRecord, vendorBillId) {
		const lineCount = vendorBillRecord.getLineCount({ sublistId: "item" });
		const lineResults = [];

		for (let i = 0; i < lineCount; i++) {
			try {
				const lineFieldMappings = [
					{ source: "item", target: "item", required: true },
					{ source: "quantity", target: "quantity", required: true },
					{ source: "rate", target: "rate", required: true },
					{ source: "amount", target: "amount", required: false },
					{ source: "description", target: "description", required: false },
					{ source: "units", target: "units", required: false },
					{ source: "location", target: "location", required: false },
					{ source: "department", target: "department", required: false },
					{ source: "class", target: "class", required: false },
				];

				const lineResult = { line: i + 1, mappings: [] };

				lineFieldMappings.forEach(mapping => {
					try {
						const sourceValue = vendorBillRecord.getSublistValue({
							sublistId: "item",
							fieldId: mapping.source,
							line: i,
						});

						if (sourceValue !== null && sourceValue !== undefined && sourceValue !== "") {
							poRecord.setSublistValue({
								sublistId: "item",
								fieldId: mapping.target,
								line: i,
								value: sourceValue,
							});
							lineResult.mappings.push({
								field: mapping.source,
								value: sourceValue,
								status: "SUCCESS"
							});
						} else if (mapping.required) {
							throw new Error(`Required line field ${mapping.source} is missing on line ${i + 1}`);
						}
					} catch (fieldError) {
						if (mapping.required) {
							throw fieldError;
						} else {
							lineResult.mappings.push({
								field: mapping.source,
								status: "ERROR",
								error: fieldError.message
							});
						}
					}
				});

				lineResults.push(lineResult);

			} catch (lineError) {
				log.error('Line Item Mapping Failed', {
					vendorBillId: vendorBillId,
					line: i + 1,
					error: lineError.message
				});
				throw new Error(`Failed to map line ${i + 1}: ${lineError.message}`);
			}
		}

		log.debug('Line Items Mapping Completed', {
			vendorBillId: vendorBillId,
			totalLines: lineCount,
			lineResults: lineResults
		});
	}

	return {
		getInputData,
		reduce,
		summarize,
	};
});
