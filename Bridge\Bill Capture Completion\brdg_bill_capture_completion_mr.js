/**
* @description Creates PO and Vend<PERSON> Bill for all flagged vendor bills created via Bill Capture
*              Deletes Bill Capture Vendor Bills Afterwards
* </br><b>Schedule:</b> Runs every night at 12:30 AM
*
* @NApiVersion 2.1
* @NScriptType MapReduceScript
* @NAmdConfig /SuiteScripts/config.json
*
* <AUTHOR>
* @module brdg_bill_capture_completion_mr
 */
define([
	"require",
	"N/record",
	"N/runtime",
	"N/query",
	"N/search",
	"N/file",
	"../../Classes/vlmd_custom_error_object",
	"../../Classes/vlmd_mr_summary_handling",
],(require) => {
        const record = require("N/record");
        const runtime = require("N/runtime");
		const query = require("N/query");
		const search = require("N/search");
		const file = require("N/file");
		
        const CustomErrorObject = require("../../Classes/vlmd_custom_error_object");
        const customErrorObject = new CustomErrorObject();
		
	function getInputData() {
        try {
			const billCaptureQuery = `
				SELECT id FROM transaction WHERE BUILTIN.DF(status) LIKE '%Pending Approval' AND custbody_brdg_bill_capture = 'T'
			`;
			const billCaptureVendorBillIds = query.runSuiteQL({
				query: billCaptureQuery,
			}).asMappedResults().map(result => result.id);

			return billCaptureVendorBillIds;
		} catch (err) {
			customErrorObject.throwError({
				summaryText: `GET_INPUT_DATA_ERROR`,
				error: err,
			});
		}
	}
	
	function reduce(context) {
		try {
			const vendorBillId = context.values[0];
			const originalVendorBill = record.load({
				type: record.Type.VENDOR_BILL,
				id: vendorBillId,
			});

			// Create Purchase Order from Vendor Bill
			const poId = createPurchaseOrderFromVendorBill(originalVendorBill, vendorBillId);

			// Create new Vendor Bill from the Purchase Order
			const newVendorBillId = createVendorBillFromPurchaseOrder(poId, originalVendorBill, vendorBillId);

			// Mark original vendor bill for voiding/cancellation
			markOriginalBillForCancellation(vendorBillId);

			log.audit('Bill Capture Completion Success', {
				originalVendorBillId: vendorBillId,
				purchaseOrderId: poId,
				newVendorBillId: newVendorBillId
			});

			context.write({
				key: "BILL_CAPTURE_COMPLETED",
				value: {
					originalVendorBillId: vendorBillId,
					purchaseOrderId: poId,
					newVendorBillId: newVendorBillId,
					status: "SUCCESS"
				}
			});
		} catch (err) {
			customErrorObject.throwError({
				summaryText: `BILL_CAPTURE_COMPLETION_ERROR`,
				error: err,
			});
		}
	}

	function summarize(context) {
		const StageHandling = require("../../Classes/vlmd_mr_summary_handling");
		const stageHandling = new StageHandling(context);
  
		stageHandling.printScriptProcessingSummary();
  
		stageHandling.printRecordsProcessed({
			includeLineBreak: true,
			includeKey: true
		});
  
		stageHandling.printErrors({
			groupErrors: true,
		});
	}

	/**
	 * Creates a comprehensive Purchase Order from a Vendor Bill with full field mapping
	 *
	 * @param {Object} vendorBillRecord - Source vendor bill record
	 * @param {string|number} vendorBillId - Vendor bill internal ID
	 * @returns {string|number} Created purchase order internal ID
	 */
	function createPurchaseOrderFromVendorBill(vendorBillRecord, vendorBillId) {
		try {
			log.audit('Creating Purchase Order', {
				sourceVendorBillId: vendorBillId
			});

			// Create PO record
			const poRecord = record.create({
				type: record.Type.PURCHASE_ORDER,
				isDynamic: false,
			});

			// Map header fields from Vendor Bill to Purchase Order
			mapVendorBillHeaderToPO(vendorBillRecord, poRecord, vendorBillId);

			// Map line items from Vendor Bill to Purchase Order
			mapVendorBillLinesToPO(vendorBillRecord, poRecord, vendorBillId);

			// Save the Purchase Order
			const poId = poRecord.save({
				enableSourcing: true,
				ignoreMandatoryFields: true,
			});

			log.audit('Purchase Order Created Successfully', {
				sourceVendorBillId: vendorBillId,
				purchaseOrderId: poId
			});

			return poId;
		} catch (err) {
			customErrorObject.throwError({
				summaryText: `PURCHASE_ORDER_CREATION_FAILED`,
				error: err,
			});
		}
	}

	/**
	 * Maps header fields from Vendor Bill to Purchase Order
	 *
	 * @param {Object} vendorBillRecord - Source vendor bill record
	 * @param {Object} poRecord - Target purchase order record
	 * @param {string|number} vendorBillId - Vendor bill internal ID for logging
	 */
	function mapVendorBillHeaderToPO(vendorBillRecord, poRecord, vendorBillId) {
		const fieldMappings = [
			// Core required fields
			{ source: "entity", target: "entity", required: true },
			{ source: "subsidiary", target: "subsidiary", required: true },
			{ source: "trandate", target: "trandate", required: true },

			// Standard fields
			{ source: "memo", target: "memo", required: false },
			{ source: "terms", target: "terms", required: false },
			{ source: "currency", target: "currency", required: false },
			{ source: "exchangerate", target: "exchangerate", required: false },
			{ source: "location", target: "location", required: false },
			{ source: "department", target: "department", required: false },
			{ source: "class", target: "class", required: false },
		];

		const mappingResults = [];

		fieldMappings.forEach(mapping => {
			try {
				const sourceValue = vendorBillRecord.getValue({ fieldId: mapping.source });

				if (sourceValue !== null && sourceValue !== undefined && sourceValue !== "") {
					poRecord.setValue({
						fieldId: mapping.target,
						value: sourceValue,
					});
					mappingResults.push({
						field: mapping.source,
						value: sourceValue,
						status: "SUCCESS"
					});
				} else if (mapping.required) {
					throw new Error(`Required field ${mapping.source} is missing or empty`);
				} else {
					mappingResults.push({
						field: mapping.source,
						value: null,
						status: "SKIPPED_EMPTY"
					});
				}
			} catch (err) {
				if (mapping.required) {
					throw err;
				} else {
					log.debug('Optional Field Mapping Failed', {
						vendorBillId: vendorBillId,
						field: mapping.source,
						error: err.message
					});
					mappingResults.push({
						field: mapping.source,
						status: "ERROR",
						error: err.message
					});
				}
			}
		});

		// Add reference to original vendor bill
		try {
			poRecord.setValue({
				fieldId: "memo",
				value: `Created from Bill Capture Vendor Bill ${vendorBillId}. Original memo: ${vendorBillRecord.getValue({ fieldId: "memo" }) || "None"}`
			});
		} catch (err) {
			log.debug('Memo Field Update Failed', {
				vendorBillId: vendorBillId,
				error: err.message
			});
		}

		log.debug('Header Field Mapping Completed', {
			vendorBillId: vendorBillId,
			mappingResults: mappingResults
		});
	}

	/**
	 * Creates a new Vendor Bill from a Purchase Order and attaches files from the original Bill Capture vendor bill
	 *
	 * @param {string|number} poId - Purchase order internal ID
	 * @param {Object} originalVendorBillRecord - Original Bill Capture vendor bill record
	 * @param {string|number} originalVendorBillId - Original vendor bill internal ID
	 * @returns {string|number} Created vendor bill internal ID
	 */
	function createVendorBillFromPurchaseOrder(poId, originalVendorBillRecord, originalVendorBillId) {
		try {
			log.audit('Creating Vendor Bill from Purchase Order', {
				purchaseOrderId: poId,
				originalVendorBillId: originalVendorBillId
			});

			const additionalFieldMappings = [
				// Additional required fields
				{ source: "tranid", target: "tranid", required: true },
				{ source: "nextapprover", target: "nextapprover", required: true },
			];

			// Transform Purchase Order to Vendor Bill
			const newVendorBill = record.transform({
				fromType: record.Type.PURCHASE_ORDER,
				fromId: poId,
				toType: record.Type.VENDOR_BILL,
			});

			// Set reference to original Bill Capture vendor bill
			const originalMemo = originalVendorBillRecord.getValue({ fieldId: "memo" }) || "";
			const newMemo = `Created from Bill Capture process. Original Bill ID: ${originalVendorBillId}. ${originalMemo}`;

			try {
				additionalFieldMappings.forEach(mapping => {
					const sourceValue = originalVendorBillRecord.getValue({ fieldId: mapping.source });

					if (sourceValue !== null && sourceValue !== undefined && sourceValue !== "") {
						newVendorBill.setValue({
							fieldId: mapping.target,
							value: sourceValue,
						});
					} else if (mapping.required) {
						throw new Error(`Required field ${mapping.source} is missing or empty`);
					}
				});

				newVendorBill.setValue({
					fieldId: "memo",
					value: newMemo
				});

			} catch (err) {
				log.debug('Additional Vendor Bill Field Update Failed', {
					originalVendorBillId: originalVendorBillId,
					error: err.message
				});
			}

			// Save the new vendor bill
			const newVendorBillId = newVendorBill.save({
				enableSourcing: true,
				ignoreMandatoryFields: true,
			});

			log.audit('New Vendor Bill Created Successfully', {
				originalVendorBillId: originalVendorBillId,
				purchaseOrderId: poId,
				newVendorBillId: newVendorBillId
			});

			// Attach files from original Bill Capture vendor bill to new vendor bill
			attachFilesFromOriginalBill(originalVendorBillId, newVendorBillId);

			return newVendorBillId;
		} catch (err) {
			customErrorObject.throwError({
				summaryText: `VENDOR_BILL_CREATION_FROM_PO_FAILED`,
				error: err,
			});
		}
	}

	/**
	 * Attaches files from the original Bill Capture vendor bill to the new vendor bill
	 *
	 * @param {string|number} originalVendorBillId - Original vendor bill internal ID
	 * @param {string|number} newVendorBillId - New vendor bill internal ID
	 */
	function attachFilesFromOriginalBill(originalVendorBillId, newVendorBillId) {
		try {
			// Get files attached to the original vendor bill
			const fileResults = search.lookupFields({
				type: search.Type.TRANSACTION,
				id: originalVendorBillId,
				columns: "file.internalid",
			})["file.internalid"];

			// Get target folder ID where bill capture file will be moved
			const targetFolderId = runtime.getCurrentScript().getParameter({
				name: "custscript_brdg_bll_cptr_fl_trgt_fldr_id"
			});

			if (!targetFolderId) {
				customErrorObject.throwError({
					summaryText: `MISSING_TARGET_FOLDER_ID`,
					error: new Error(`Target folder ID is missing`),
				});
				
				return;
			}
			
			if (fileResults && fileResults.length > 0) {
				const attachmentResults = [];
				fileResults.forEach((fileResult) => {
					try {
						const fileId = fileResult.value;

						// Move file to target folder first
						const fileObj = file.load({
							id: fileId
						});
						fileObj.folder = targetFolderId;
						fileObj.save();

						record.attach({
							record: {
								type: 'file',
								id: fileId
							},
							to: {
								type: record.Type.VENDOR_BILL,
								id: newVendorBillId
							}
						});

						attachmentResults.push({
							fileId: fileId,
							status: "SUCCESS"
						});
					} catch (err) {
						customErrorObject.throwError({
							summaryText: `FILE_ATTACHMENT_FAILED`,
							error: err,
						});

						attachmentResults.push({
							fileId: fileResult.value,
							status: "ERROR",
							error: err.message
						});
					}
				});

				log.audit('File Attachment Process Completed', {
					originalVendorBillId: originalVendorBillId,
					newVendorBillId: newVendorBillId,
					totalFiles: fileResults.length,
					attachmentResults: attachmentResults
				});

			} else {
				log.audit('No Files Found to Attach', {
					originalVendorBillId: originalVendorBillId,
					newVendorBillId: newVendorBillId
				});
			}

		} catch (err) {
			customErrorObject.throwError({
				summaryText: `FILE_ATTACHMENT_PROCESS_FAILED`,
				error: err,
			});
		}
	}

	/**
	 * Marks the original Bill Capture vendor bill for cancellation by setting approval status to Rejected
	 *
	 * @param {string|number} originalVendorBillId - Original vendor bill internal ID
	 */
	function markOriginalBillForCancellation(originalVendorBillId) {
		try {
			record.submitFields({
				type: record.Type.VENDOR_BILL,
				id: originalVendorBillId,
				values: {
					'cancelvendbill': 'T'
				}
			});
		} catch (err) {
			customErrorObject.throwError({
				summaryText: `FAILED_TO_MARK_ORIGINAL_BILL_FOR_CANCELLATION`,
				error: err,
			});
		}
	}

	/**
	 * Maps line items from Vendor Bill to Purchase Order
	 *
	 * @param {Object} vendorBillRecord - Source vendor bill record
	 * @param {Object} poRecord - Target purchase order record
	 * @param {string|number} vendorBillId - Vendor bill internal ID for logging
	 */
	function mapVendorBillLinesToPO(vendorBillRecord, poRecord, vendorBillId) {
		const lineCount = vendorBillRecord.getLineCount({ sublistId: "item" });
		const lineResults = [];

		for (let i = 0; i < lineCount; i++) {
			try {
				const lineFieldMappings = [
					{ source: "item", target: "item", required: true },
					{ source: "quantity", target: "quantity", required: true },
					{ source: "rate", target: "rate", required: true },
					{ source: "amount", target: "amount", required: false },
					{ source: "description", target: "description", required: false },
					{ source: "units", target: "units", required: false },
					{ source: "location", target: "location", required: false },
					{ source: "department", target: "department", required: false },
					{ source: "class", target: "class", required: false },
				];

				const lineResult = { line: i + 1, mappings: [] };

				lineFieldMappings.forEach(mapping => {
					try {
						const sourceValue = vendorBillRecord.getSublistValue({
							sublistId: "item",
							fieldId: mapping.source,
							line: i,
						});

						if (sourceValue !== null && sourceValue !== undefined && sourceValue !== "") {
							poRecord.setSublistValue({
								sublistId: "item",
								fieldId: mapping.target,
								line: i,
								value: sourceValue,
							});
							lineResult.mappings.push({
								field: mapping.source,
								value: sourceValue,
								status: "SUCCESS"
							});
						} else if (mapping.required) {
							throw new Error(`Required line field ${mapping.source} is missing on line ${i + 1}`);
						}
					} catch (err) {
						if (mapping.required) {
							throw err;
						} else {
							lineResult.mappings.push({
								field: mapping.source,
								status: "ERROR",
								error: err.message
							});
						}
					}
				});

				lineResults.push(lineResult);

			} catch (err) {
				customErrorObject.throwError({
					summaryText: `LINE_ITEM_MAPPING_FAILED`,
					error: err,
				});
			}
		}

		log.audit('Line Items Mapping Completed', {
			vendorBillId: vendorBillId,
			totalLines: lineCount,
			lineResults: lineResults
		});
	}

	return {
		getInputData,
		reduce,
		summarize,
	};
});
