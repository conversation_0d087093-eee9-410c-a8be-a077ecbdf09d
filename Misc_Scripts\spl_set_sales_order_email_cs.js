/**
 *@NApiVersion 2.x
 *@NScriptType ClientScript
 * @NAmdConfig /SuiteScripts/config.json
 */


define(["N/log", "GetOrderEmailAddressLib"], function (
	log,
	getOrderEmailAddress
) {
	function pageInit(context) {
		if (context.mode !== "create") {
			var salesOrder = context.currentRecord;

			if (salesOrder.getValue("tobeemailed")) {
				//If To Be Emailed is checked off on this sales order (to override the customer's default email)
				return; //Don't set the transaction email
			}

			var customerId = salesOrder.getValue("entity");
			var results = getOrderEmailAddress.getSalesOrderEmailAddress(customerId);
			results = results.join(";");
			salesOrder.setValue("email", results);
		}
	}

	return {
		pageInit: pageInit,
	};
});
