/**
 * @NApiVersion 2.1
 * @NScriptType Suitelet
 * @NAmdConfig /SuiteScripts/config.json
 * <AUTHOR>
 * @description SL to allow BRDG COGS handler to update COGS per line item on specified transactions.
 * @module brdg_calcualte_cogs_per_line_sl
 */

define([
	"require",
	"Moment",
	"../../Classes/vlmd_custom_error_object",
	"N/log",
	"N/ui/serverWidget",
	"N/ui/message",
	"N/url",
	"N/task",
	"N/redirect",
	"N/runtime",
], function (require, moment) {
	const log = require("N/log");
	const serverWidget = require("N/ui/serverWidget");
	const message = require("N/ui/message");
	const url = require("N/url");
	const task = require("N/task");
	const redirect = require("N/redirect");
	const runtime = require("N/runtime");

	/** @type {import("../../Classes/vlmd_custom_error_object").CustomErrorObject} */

	const CustomErrorObject = require("../../Classes/vlmd_custom_error_object");
	const customErrorObject = new CustomErrorObject();

	var helperFunctions = (function () {
		function updateFormWithFromPostDetails(context, form) {
			try {
				const mapReduceTaskId = context.request.parameters["task_id"];

				if (!mapReduceTaskId) {
					throw customErrorObject.updateError({
						errorType: customErrorObject.ErrorTypes.MISSING_VALUE,
						summary: "MISSING_TASK_ID",
						details: `No map reduce task id passed in from post.`,
					});
				}

				let taskStatus = task.checkStatus({
					taskId: mapReduceTaskId,
				});

				form.addPageInitMessage({
					type: message.Type.INFORMATION,
					title: "Task Results",
					message: `Task Status: ${taskStatus.status}`,
					duration: 10000,
				});
			} catch (err) {
				throw customErrorObject.updateError({
					errorType: customErrorObject.ErrorTypes.VALUE_NOT_SET,
					summary: "ERROR_SETTING_DETAILS_FROM_POST",
					details: `Errors setting details when hit GET via a redirect from POST.`,
				});
			}
		}

		function addParametersFieldGroup(form) {
			try {
				var parametersFieldGroup = form.addFieldGroup({
					id: "parametersFieldGroup",
					label: "Parameters",
				});

				parametersFieldGroup.isBorderHidden = true;
				parametersFieldGroup.setSingleColumn;

				form.addField({
					id: "custpage_get_created",
					label: `Get transactions created within time frame.`,
					type: serverWidget.FieldType.CHECKBOX,
					container: "parametersFieldGroup",
				}).defaultValue = "F";

				form.addField({
					id: "custpage_get_edited",
					label: `Get transactions edited within time frame.`,
					type: serverWidget.FieldType.CHECKBOX,
					container: "parametersFieldGroup",
				}).defaultValue = "T";

				form.addField({
					id: "custpage_subsidiary",
					label: `Subsidiary (leave unselected to include all subsidiariees)`,
					type: serverWidget.FieldType.SELECT,
					source: "subsidiary",
					container: "parametersFieldGroup",
				});

				form.addField({
					id: "custpage_start_date",
					label: "Start Date",
					type: serverWidget.FieldType.DATE,
					container: "parametersFieldGroup",
				}).defaultValue = moment()
					.subtract(1, "months")
					.startOf("month")
					.format("MM/DD/YYYY");

				form.addField({
					id: "custpage_end_date",
					label: "End Date",
					type: serverWidget.FieldType.DATE,
					container: "parametersFieldGroup",
				}).defaultValue = moment()
					.subtract(1, "months")
					.endOf("month")
					.format("MM/DD/YYYY");

				form.addSubmitButton({
					label: "Calculate COGS",
				});

				form.addResetButton();
			} catch (err) {
				throw customErrorObject.updateError({
					errorType: customErrorObject.ErrorTypes.VALUE_NOT_SET,
					summary: "PARAMETER_FIELDS_NOT_SET",
					details: `Errors setting details when hit GET via link: ${err}`,
				});
			}
		}

		function createMapReduceTask(paramsObj) {
			try {
				const { getCreated, getEdited, subsidiary, startDate, endDate } =
					paramsObj;
				var userId = runtime.getCurrentUser().id;

				var mapReduceTask = task.create({ taskType: task.TaskType.MAP_REDUCE });

				mapReduceTask.scriptId = "customscript_brdg_clclt_cogs_per_line_mr";
				mapReduceTask.deploymentId = "customdeploy_brdg_clclt_cogs_pr_ln_mr_sl";

				mapReduceTask.params = {
					custscript_brdg_cogs_process_owner: userId,
					custscript_get_transactions_created: getCreated,
					custscript_get_transactions_edited: getEdited,
					custscript_subsidiary: subsidiary,
					custscript_start_date: startDate,
					custscript_end_date: endDate,
				};

				return mapReduceTask.submit();
			} catch (err) {
				throw customErrorObject.updateError({
					errorType: customErrorObject.ErrorTypes.UNHANDLED_ERROR,
					summary: "CREATE_MR_TASK_ERROR",
					details: `Error in create MR task function: ${err}`,
				});
			}
		}

		function redirectToSuitelet(taskId) {
			try {
				var suiteletURL = url.resolveScript({
					scriptId: "customscript_brdg_clclt_cogs_per_line_sl",
					deploymentId: "customdeploy_brdg_clclt_cogs_per_line_sl",
					params: {
						redirected_from_sl: true,
						task_id: taskId,
					},
				});

				redirect.redirect({ url: suiteletURL });
			} catch (err) {
				throw customErrorObject.updateError({
					errorType: customErrorObject.ErrorTypes.UNHANDLED_ERROR,
					summary: "ERROR_REDIRECTING_TO_SL",
					details: `Error redirecting to SL: ${err}`,
				});
			}
		}

		return {
			updateFormWithFromPostDetails,
			addParametersFieldGroup,
			createMapReduceTask,
			redirectToSuitelet,
		};
	})();

	return {
		onRequest: function (context) {
			try {
				if (context.request.method === "GET") {
					const form = serverWidget.createForm({
						title: "Calculate COGS Per Line",
					});

					const redirectedFromSl =
						context.request.parameters["redirected_from_sl"];

					if (redirectedFromSl) {
						helperFunctions.updateFormWithFromPostDetails(context, form);
					} else {
						helperFunctions.addParametersFieldGroup(form);
					}

					context.response.writePage(form);
				} else {
					//POST Request
					const taskId = helperFunctions.createMapReduceTask({
						getCreated: context.request.parameters.custpage_get_created,
						getEdited: context.request.parameters.custpage_get_edited,
						subsidiary: context.request.parameters.custpage_subsidiary,
						startDate: context.request.parameters.custpage_start_date,
						endDate: context.request.parameters.custpage_end_date,
					});

					helperFunctions.redirectToSuitelet(taskId);

					return;
				}
			} catch (err) {
				customErrorObject.throwError({
					summaryText: `ERROR_IN_SL`,
					error: err,
				});
			}
		},
	};
});
