/******************************************************************************************************
	Script Name - AVA_CLI_Config.js
	Company -     Avalara Technologies Pvt Ltd.
******************************************************************************************************/

/**
 *@NApiVersion 2.0
 *@NScriptType ClientScript
*/

define(['N/https', 'N/url', 'N/currentRecord', 'N/search', 'N/record', 'N/runtime', './utility/AVA_Library', 'N/ui/message'],
	function(https, url, currentRecord, search, record, runtime, ava_library, message){
		var ConfigLogs = {};
		
		function configSave(context){
			var cRecord = context.currentRecord;
			
			if(cRecord.getField('ava_servicetype') != null){
				var customerCode = cRecord.getValue({
					fieldId: 'ava_customercode'
				});
				if(customerCode >= 3 && customerCode != 6 && customerCode != 8){
					if(runtime.isFeatureInEffect('prm') == false){
						alert('Partner information cannot be passed to service as the required features are not enabled.');
						return false;
					}
				}
				
				var enableDiscount = cRecord.getValue({
					fieldId: 'ava_enablediscount'
				});
				if(enableDiscount == true){
					var discountTaxcode = cRecord.getValue({
						fieldId: 'ava_discounttaxcode'
					});
					if(discountTaxcode == null || discountTaxcode.length == 0)
					{
						alert('Please Enter Discount Tax Code');
						return false;
					}
				}
				
				var subsidiaryFlag = 'F';
				var subsidiaryListCount = cRecord.getLineCount({
					sublistId: 'custpage_subsidiarylist'
				});
				if(subsidiaryListCount != null){
					var cnt = 0;
					for(i = 0; i < subsidiaryListCount; i++){
						var taxCodeValidate = cRecord.getSublistValue({
							sublistId: 'custpage_subsidiarylist',
							fieldId: 'ava_taxcodevalidate',
							line: i
						});
						var subsidiaryName = cRecord.getSublistValue({
							sublistId: 'custpage_subsidiarylist',
							fieldId: 'ava_subsidiary',
							line: i
						});
						var defaultAddr = cRecord.getSublistValue({
							sublistId: 'custpage_subsidiarylist',
							fieldId: 'ava_defaultaddr',
							line: i
						});
						var companyAddr = cRecord.getSublistValue({
							sublistId: 'custpage_subsidiarylist',
							fieldId: 'ava_companyaddr',
							line: i
						});
						var shipAddr = cRecord.getSublistValue({
							sublistId: 'custpage_subsidiarylist',
							fieldId: 'ava_shipaddr',
							line: i
						});
						
						if(taxCodeValidate == ' '){
							subsidiaryFlag = 'F';
							cnt++;
						}
						else if(taxCodeValidate == '2'){
							alert('The Tax Code selected for subsidiary \"' + subsidiaryName + '\" does not have any association with the associated nexus for the subsidiary.');
							subsidiaryFlag = 'F';
							return false;
						}
						else if(taxCodeValidate == '3'){
							alert('The Tax Rate assigned for subsidiary \"' + subsidiaryName + '\" should be equal to zero.');
							subsidiaryFlag = 'F';
							return false;
						}
						else{
							if(defaultAddr == 'companyaddr' && (companyAddr == null || companyAddr.length == 0 || companyAddr == ' ')){
								alert('The Company Address assigned for subsidiary \"' + subsidiaryName + '\" cannot be blank.');
								return false;
							}
							else if(defaultAddr == 'shippingaddr' && (shipAddr == null || shipAddr.length == 0 || shipAddr == ' ')){
								alert('The Shipping Address assigned for subsidiary \"' + subsidiaryName + '\" cannot be blank.');
								return false;
							}
							
							subsidiaryFlag = 'T';
						}
					}
					
					if(subsidiaryFlag == 'F' && cnt == subsidiaryListCount){
						alert('The configuration options cannot be saved without any taxcodes being assigned for subsidiaries');
						return false;
					}
				}
				
				var enableUseTax = cRecord.getValue({
					fieldId: 'ava_enableusetax'
				});
				if(enableUseTax == true){
					var creditAccount = cRecord.getValue({
						fieldId: 'ava_creditacc'
					});
					var debitAccount = cRecord.getValue({
						fieldId: 'ava_debitacc'
					});
					var glAccounts = cRecord.getValue({
						fieldId: 'ava_glacc'
					});
					if(creditAccount == null || creditAccount.length == 0){
						alert('Please select Use Tax Payable Liability Account');
						return false;
					}
					if(glAccounts == 'glaccount' && (debitAccount == null || debitAccount.length == 0)){
						alert('Please select Use Tax Debit Account');
						return false;
					}
				}
				
				var searchCustom = search.create({
					type: 'customrecord_avasubsidiaries',
					columns: ['custrecord_ava_subsidiary', 'custrecord_ava_subdeftaxcode', 'custrecord_ava_iscompanyaddr', 'custrecord_ava_defcompanycode']
				});
				var searchresult = searchCustom.run();
				searchresult = searchresult.getRange({
					start: 0,
					end: 1000
				});
				
				var subsidiaryConfigLogs = '', logsFlag = 0;
				for(var k = 0; k < subsidiaryListCount; k++){
					var subLogs = '';
					var subValueChangedFlag = 0;
					var subRecord = cRecord.getSublistValue({
						sublistId: 'custpage_subsidiarylist',
						fieldId: 'ava_customsubid',
						line: k
					});
					
					if(subRecord !=null && subRecord.length > 0){
						var defaultTaxcode = cRecord.getSublistValue({
							sublistId: 'custpage_subsidiarylist',
							fieldId: 'ava_deftaxcode',
							line: k
						});
						defaultTaxcode = (defaultTaxcode == ' ') ? '' : defaultTaxcode;
						var defaultAddress = cRecord.getSublistValue({
							sublistId: 'custpage_subsidiarylist',
							fieldId: 'ava_defaultaddr',
							line: k
						});
						var defaultCompanyCode = cRecord.getSublistValue({
							sublistId: 'custpage_subsidiarylist',
							fieldId: 'ava_defcompanycode',
							line: k
						});
						defaultCompanyCode = (defaultCompanyCode == ' ') ? '' : defaultCompanyCode;
						var subsidiaryId = cRecord.getSublistValue({
							sublistId: 'custpage_subsidiarylist',
							fieldId: 'ava_subsidiary',
							line: k
						});
						record.submitFields({
							type: 'customrecord_avasubsidiaries',
							id: subRecord,
							values: {
								'custrecord_ava_subdeftaxcode':  cRecord.getSublistValue({
									sublistId: 'custpage_subsidiarylist',
									fieldId: 'ava_deftaxcode',
									line: k
								}),
								'custrecord_ava_configrecord': cRecord.getValue({
									fieldId: 'ava_recordid'
								}),
								'custrecord_ava_iscompanyaddr': (defaultAddress == 'companyaddr') ? true : false,
								'custrecord_ava_defcompanycode': cRecord.getSublistValue({
									sublistId: 'custpage_subsidiarylist',
									fieldId: 'ava_defcompanycode',
									line: k
								}),
								'custrecord_ava_compid': cRecord.getSublistValue({
									sublistId: 'custpage_subsidiarylist',
									fieldId: 'ava_companyid',
									line: k
								})
							},
							options: {
						        enableSourcing: false
						    }
						});
						
						if(defaultTaxcode != searchresult[k].getValue('custrecord_ava_subdeftaxcode')){
							logsFlag = 1;
							subValueChangedFlag = 1;
							subLogs += 'DefaultTaxCode:' + defaultTaxcode + '||';
						}
						
						var isCompanyAddr = (defaultAddress == 'companyaddr') ? true : false;
						if(isCompanyAddr != searchresult[k].getValue('custrecord_ava_iscompanyaddr')){
							logsFlag = 1;
							subValueChangedFlag = 1;
							subLogs += 'ShipFromAddess:' + ((isCompanyAddr == true) ? 'Company Address' : 'Shipping Address') + '||';
						}
						
						if(defaultCompanyCode != searchresult[k].getValue('custrecord_ava_defcompanycode')){
							logsFlag = 1;
							subValueChangedFlag = 1;
							subLogs += 'DefaultCompanyCode:' + defaultCompanyCode + '||';
						}
						
						if(subValueChangedFlag == 1){
							subsidiaryConfigLogs +=  subsidiaryId + '>' + subLogs + ' ~ ';
						}
					}
				}
				
				var enableLogEntries = cRecord.getValue({
					fieldId: 'ava_enablelogentries'
				});
				if(enableLogEntries == true){
					var avaConfigObjRecvd = ava_library.mainFunction('AVA_LoadValuesToGlobals', '');
					
					var accountValue = cRecord.getValue({
						fieldId: 'ava_accvalue'
					});
					var serviceUrl = cRecord.getValue({
						fieldId: 'ava_serviceurl'
					});
					
					if(logsFlag == 1){
						ava_library.mainFunction('AVA_Logs', (accountValue + '~~' + serviceUrl + '~~' + '0' + '~~' + '' + '~~' + '' + '~~' + '' + '~~' + '' + '~~' + 'ConfigChanges' + '~~' + 'ConfigAudit' + '~~' + 'Informational' + '~~' + 'ConfigurationForm' + '~~' + 'AVA_ConfigWizardSave' + '~~' + subsidiaryConfigLogs + '~~' + '' + '~~' + 1 + '~~' + 0 + '~~' + '' + '~~' + avaConfigObjRecvd.AVA_AdditionalInfo3));
					}
					
					var configLogs = JSON.stringify(ConfigLogs);
					
					if(configLogs != null){
						configLogs = configLogs.replace(/"/g, "");
					}
					
					if(configLogs.length > 2){
						ava_library.mainFunction('AVA_Logs', (accountValue + '~~' + serviceUrl + '~~' + '0' + '~~' + '' + '~~' + '' + '~~' + '' + '~~' + '' + '~~' + 'ConfigChanges' + '~~' + 'ConfigAudit' + '~~' + 'Informational' + '~~' + 'ConfigurationForm' + '~~' + 'AVA_ConfigWizardSave' + '~~' + configLogs + '~~' + '' + '~~' + 1 + '~~' + 0 + '~~' + '' + '~~' + avaConfigObjRecvd.AVA_AdditionalInfo3));
					}
				}
			}
			
			return true;
		}
		
		function configTestConnection(cRecord, additionalInfo3){
			var serviceTypes = '';
			var serviceUrl = cRecord.getValue({
				fieldId: 'ava_serviceurl'
			});
			
			if(additionalInfo3 != 0){
				var AvaTax = ava_library.mainFunction('AVA_InitSignatureObject', serviceUrl);
				var subscriptions = AvaTax.subscriptions(additionalInfo3);
				
				try{
					var taxSvc = '', certCaptureSvc = '';
					
					var response = https.get({
						url: subscriptions.url,
						headers: subscriptions.headers
					});
					
					if(response.code == 200){
						var responseBody = JSON.parse(response.body);
						var values = responseBody.value;
						
						for(var i = 0; values != null && i < values.length; i++){
							if(values[i].subscriptionTypeId == 1 || values[i].subscriptionTypeId == 2 || values[i].subscriptionTypeId == 3){
								taxSvc = 'TaxSvc, AddressSvc, ';
							}
							else if(values[i].subscriptionTypeId == 10 || values[i].subscriptionTypeId == 12){
								certCaptureSvc = 'AvaCert2Svc';
							}
						}
						
						serviceTypes = taxSvc + certCaptureSvc;
						
						if(serviceTypes != null && serviceTypes.length > 0){
							cRecord.setValue({
								fieldId: 'ava_servicetypes',
								value: serviceTypes,
								ignoreFieldChange: true
							});
							
							return true;
						}
						else{
							alert("No services enabled for this account. Please contact Avalara Support.");
							return false;
						}
					}
					else{
						var msg = '';
						
						if(response.code == 401 && (response.body == null || response.body == '')){
							msg = 'No services enabled for this account. Please contact your Customer Account Manager for assistance.';
						}
						else{
							var responseBody = JSON.parse(response.body);
							msg = responseBody.error.message;
						}
						
						alert(msg);
						return false;
					}
				}
				catch(err){
					alert(err.message);
					return false;
				}
			}
			else{
				return false;
			}
		}
		
		function configChanged(context){
			var cRecord = context.currentRecord;
			var fieldValue = cRecord.getValue({
				fieldId: context.fieldId
			});
			
			switch(context.fieldId){
				case 'ava_taxcodemapping':
					ConfigLogs.EnableTaxCodeMapping = fieldValue;
					break;
				case 'ava_taxcodeprecedence':
					ConfigLogs.TaxCodePrecedence =fieldValue;
					break;
				case 'ava_udf1':
					ConfigLogs.UserDefined1 = fieldValue;
					break;
				case 'ava_udf2':
					ConfigLogs.UserDefined2 = fieldValue;
					break;
				case 'ava_itemaccount':
					ConfigLogs.SendItemAccountToAvalara = fieldValue;
					break;
				case 'ava_disableitemsync':
					ConfigLogs.DisableSynchronizationOfItemsToAvalara = fieldValue;
					break;
					
				case 'ava_customercode':
					ConfigLogs.CustomerCode = fieldValue;
					if(fieldValue >= 3 && fieldValue != 6 && fieldValue != 8){
						if(runtime.isFeatureInEffect('prm') == false){
							alert('Partner information cannot be passed to service as the required features are not enabled.');
						}
						else if(runtime.isFeatureInEffect('multipartner') == true){
							alert('Customer information will be passed to the service as Multi-Partner Management feature is enabled.');
						}
					}
					break;
					
				case 'ava_vendorcode':
					ConfigLogs.VendorCode = fieldValue;
					break;
				case 'ava_markcusttaxable':
					ConfigLogs.DefaultCustomersToTaxable = fieldValue;
					break;
				case 'ava_defaultcustomer':
					ConfigLogs.ApplyDefaultTaxcodeTo = fieldValue;
					break;
				case 'ava_entityusecode':
					ConfigLogs.EnableEntityUseCode = fieldValue;
					break;
				case 'ava_defshipcode':
					ConfigLogs.DefaultShippingCode = fieldValue;
					break;
				case 'ava_showmessages':
					ConfigLogs.ShowWarningsError = fieldValue;
					break;
				case 'ava_billtimename':
					ConfigLogs.BillableTimeName = fieldValue;
					break;
				case 'ava_enablelogentries':
					ConfigLogs.EnableLogEntries = fieldValue;
					break;
					
				case 'ava_disabletax':
					if(fieldValue == true){
						if(confirm('Are you sure you want to disable AvaTax Tax Calculation ?') == true){
							ConfigLogs.DisableTaxCalculation = fieldValue;
							disableTaxFields(cRecord, fieldValue);
						}
						else{
							cRecord.setValue({
								fieldId: 'ava_disabletax',
								value: false,
								ignoreFieldChange: true
							});
						}
					}
					else{
						ConfigLogs.DisableTaxCalculation = fieldValue;
						disableTaxFields(cRecord, fieldValue);
					}
					break;
					
				case 'ava_disabletaxquote':
					ConfigLogs.DisableTaxCalculationQuote = fieldValue;
					break;
				case 'ava_disabletaxsalesorder':
					ConfigLogs.DisableTaxCalculationOrder = fieldValue;
					break;
				case 'ava_disableline':
					ConfigLogs.DisableTaxCalculationLineLevel = fieldValue;

					if(fieldValue == false){
						alert('Line Level Tax calculation may cause incremental AvaTax usage charges as this option will automatically calculate tax when a line is added, removed, or changed.');
					}
					break;
				case 'ava_enablelogging':
					ConfigLogs.EnableLogging = fieldValue;
					break;
				case 'ava_taxondemand':
					ConfigLogs.CalculateTaxOnDemand = fieldValue;
					break;
				case 'ava_taxinclude':
					ConfigLogs.TaxIncludedCapability = fieldValue;
					break;
					
				case 'ava_enablediscount':
					ConfigLogs.EnableDiscountMechanism = fieldValue;
					var discountMapping = cRecord.getField({
						fieldId: 'ava_discountmapping'
					});
					var discountTaxcode = cRecord.getField({
						fieldId: 'ava_discounttaxcode'
					});
					var disabled = (fieldValue == true) ? false : true;
					discountMapping.isDisabled = disabled;
					discountTaxcode.isDisabled = disabled;
					break;
					
				case 'ava_discountmapping':
					ConfigLogs.DiscountMapping = fieldValue;
					break;
				case 'ava_discounttaxcode':
					ConfigLogs.DiscountTaxCode = fieldValue;
					break;
				case 'ava_taxrate':
					ConfigLogs.TaxRate = fieldValue;
					break;
				case 'ava_decimalplaces':
					ConfigLogs.RoundOffTaxPercentage = fieldValue;
					break;
				case 'ava_usepostingperiod':
					ConfigLogs.UsePostingPeriod = fieldValue;
					break;
				case 'ava_disableloccode':
					ConfigLogs.DisableLocationCode = fieldValue;
					break;
				case 'ava_enableupccode':
					ConfigLogs.EnableUPCCode = fieldValue;
					break;
				case 'ava_useinvoiceaddress':
					ConfigLogs.UseInvoiceAddressOnReturns = fieldValue;
					break;
				case 'ava_committransaction':
					ConfigLogs.AllowWorkflowApprovalForPosting = fieldValue;
					break;
				case 'ava_usebilltoaddress':
					ConfigLogs.useBillToAddress = fieldValue;
					break;
				case 'ava_abortbulkbilling':
					ConfigLogs.AbortBulkBilling = fieldValue;
					break;
				case 'ava_abortuserinterfaces':
					ConfigLogs.AbortUserInterfaces = fieldValue;
					break;
				case 'ava_abortwebservices':
					ConfigLogs.AbortWebservices = fieldValue;
					break;
				case 'ava_abortcsvimports':
					ConfigLogs.AbortCSVImports = fieldValue;
					break;
				case 'ava_abortscheduledscripts':
					ConfigLogs.AbortScheduledScripts = fieldValue;
					break;
				case 'ava_abortsuitelets':
					ConfigLogs.AbortSuitelets = fieldValue;
					break;
				case 'ava_abortworkflowactionscripts':
					ConfigLogs.AbortWorkFlowActionsScripts = fieldValue;
					break;
					
				case 'ava_enableusetax':
					ConfigLogs.EnableUseTaxAssessment = fieldValue;
					
					if(fieldValue == true && runtime.isFeatureInEffect('advtaxengine') == false){
						alert('Please enable Advanced Taxes feature to use UseTax Assessment feature.');
						cRecord.setValue({
							fieldId: 'ava_enableusetax',
							value: false,
							ignoreFieldChange: true
						});
						return;
					}
					
					var disabled = (fieldValue == true) ? false : true;
					var creditAccount = cRecord.getField({
						fieldId: 'ava_creditacc'
					});
					var glAccounts = cRecord.getField({
						fieldId: 'ava_glacc'
					});
					var debitAccount = cRecord.getField({
						fieldId: 'ava_debitacc'
					});
					var billApproved = cRecord.getField({
						fieldId: 'ava_billapproved'
					});
					var autoAssess = cRecord.getField({
						fieldId: 'ava_autoassess'
					});
					var vendorTaxItem = cRecord.getField({
						fieldId: 'ava_vendortaxitem'
					});
					var taxAccrualDate = cRecord.getField({
						fieldId: 'ava_taxaccrualdate'
					});
					
					creditAccount.isDisabled = disabled;
					glAccounts.isDisabled = disabled;
					var glAccount = cRecord.getValue({
						fieldId: 'ava_glacc'
					});
					if(glAccount == 'glaccount'){
						debitAccount.isDisabled = disabled;
					}
					billApproved.isDisabled = disabled;
					autoAssess.isDisabled = disabled;
					vendorTaxItem.isDisabled = disabled;
					taxAccrualDate.isDisabled = disabled;
					break;
					
				case 'ava_creditacc':
					ConfigLogs.UseTaxPayableAccount = fieldValue;
					break;
					
				case 'ava_glacc':
					ConfigLogs.GLAccountDebit = fieldValue;
					var debitAccount = cRecord.getField({
						fieldId: 'ava_debitacc'
					});
					var disabled = (fieldValue == 'glaccount') ? false : true;
					debitAccount.isDisabled = disabled;
					break;
					
				case 'ava_debitacc':
					ConfigLogs.UseTaxDebitAccount = fieldValue;
					break;
				case 'ava_billapproved':
					ConfigLogs.VendorBillApproved = fieldValue;
					break;
				case 'ava_autoassess':
					ConfigLogs.AutoAssessImportedBill = fieldValue;
					break;
					
				case 'ava_vendortaxitem':
					if(fieldValue != null && fieldValue.length > 0){
						var itemType = search.lookupFields({
							type: search.Type.ITEM,
							id: fieldValue,
							columns: 'type'
						});
						
						if(itemType.type[0].value != 'InvtPart' && itemType.type[0].value != 'NonInvtPart'){
							alert('Please select item of inventory or non-inventory type only.')
							cRecord.setValue({
								fieldId: 'ava_vendortaxitem',
								value: ' ',
								ignoreFieldChange: true
							});
						}
						else{
							ConfigLogs.ItemForVendorChargedTax = fieldValue;
						}
					}
					else{
						ConfigLogs.ItemForVendorChargedTax = fieldValue;
					}
					break;
					
				case 'ava_taxaccrualdate':
					ConfigLogs.TaxAccrualDate = fieldValue;
					
					if(fieldValue == 2 && runtime.isFeatureInEffect('accountingperiods') == false){
						alert('Please enable Accounting Periods feature to use Posting Period option.');
						cRecord.setValue({
							fieldId: 'ava_taxaccrualdate',
							value: '0',
							ignoreFieldChange: true
						});
					}
					break;
					
				case 'ava_enablevatin':
					ConfigLogs.EnableInputVAT = fieldValue;
					
					if(fieldValue == true && runtime.isFeatureInEffect('advtaxengine') == false){
						alert('Please enable Advanced Taxes feature to use Input VAT Verification feature.');
						cRecord.setValue({
							fieldId: 'ava_enablevatin',
							value: false,
							ignoreFieldChange: true
						});
					}
					break;
					
				case 'ava_disableaddvalidation':
					ConfigLogs.DisableAddressValidation = fieldValue;
					var enableAddrValidationOnTrans = cRecord.getField({
						fieldId: 'ava_enableaddvalontran'
					});
					var enableAddrValidationFlag = cRecord.getField({
						fieldId: 'ava_enableaddvalflag'
					});
					var upperCaseAddr = cRecord.getField({
						fieldId: 'ava_uppercaseaddress'
					});
					var addrBatchProcessing = cRecord.getField({
						fieldId: 'ava_addbatchprocessing'
					});
					
					enableAddrValidationOnTrans.isDisabled = fieldValue;
					enableAddrValidationFlag.isDisabled = fieldValue;
					upperCaseAddr.isDisabled = fieldValue;
					addrBatchProcessing.isDisabled = fieldValue;
					break;
					
				case 'ava_enableaddvalontran':
					ConfigLogs.EnableAddrValTransaction = fieldValue;
					break;
				case 'ava_enableaddvalflag':
					ConfigLogs.TrackPreviouslyValAddr = fieldValue;
					break;
				case 'ava_uppercaseaddress':
					ConfigLogs.ResultInUpperCase = fieldValue;
					break;
				case 'ava_addbatchprocessing':
					ConfigLogs.BatchProcessing = fieldValue;
					break;
					
				case 'ava_deftaxcode':
					var val = cRecord.getCurrentSublistValue({
						sublistId: 'custpage_subsidiarylist',
						fieldId: 'ava_deftaxcode'
					});
					val = (val != null && val.length > 0) ? val.substr(val.lastIndexOf('+') + 1, val.length) : '';
					
					cRecord.setCurrentSublistValue({
						sublistId: 'custpage_subsidiarylist',
						fieldId: 'ava_deftaxcoderate',
						value: val,
						ignoreFieldChange: true
					});
					cRecord.setCurrentSublistValue({
						sublistId: 'custpage_subsidiarylist',
						fieldId: 'ava_deftaxcodecountry',
						value: val,
						ignoreFieldChange: true
					});
					var currentTaxcode = cRecord.getCurrentSublistValue({
						sublistId: 'custpage_subsidiarylist',
						fieldId: 'ava_deftaxcodecountry'
					});
					var subNexuses = cRecord.getCurrentSublistValue({
						sublistId: 'custpage_subsidiarylist',
						fieldId: 'ava_subnexuses'
					});
					subNexuses = subNexuses.split(',');
					
					if(currentTaxcode != null && currentTaxcode.length > 0 && subNexuses != null && (subNexuses.length - 1) > 0){
						for(var i = 0; i < subNexuses.length - 1; i++){
							var defTaxcodeCountry = cRecord.getCurrentSublistText({
								sublistId: 'custpage_subsidiarylist',
								fieldId: 'ava_deftaxcodecountry'
							});
							if(defTaxcodeCountry == subNexuses[i]){
								var taxrate = cRecord.getCurrentSublistText({
									sublistId: 'custpage_subsidiarylist',
									fieldId: 'ava_deftaxcoderate'
								});
								taxrate = (taxrate.indexOf('%') == -1)? taxrate : taxrate.substr(0, taxrate.indexOf('%'));
								
								var taxCodeValidate = (parseFloat(taxrate) > 0)? 3 : 1; 
								cRecord.setCurrentSublistValue({
									sublistId: 'custpage_subsidiarylist',
									fieldId: 'ava_taxcodevalidate',
									value: taxCodeValidate,
									ignoreFieldChange: true
								});
								break;
							}
							else{
								cRecord.setCurrentSublistValue({
									sublistId: 'custpage_subsidiarylist',
									fieldId: 'ava_taxcodevalidate',
									value: 2,
									ignoreFieldChange: true
								});
							}
						}
					}
					else{
						cRecord.setCurrentSublistValue({
							sublistId: 'custpage_subsidiarylist',
							fieldId: 'ava_taxcodevalidate',
							value: ' ',
							ignoreFieldChange: true
						});
					}
					break;
					
				case 'ava_defcompanycode':
					var selectedValue = cRecord.getCurrentSublistValue({
						sublistId: 'custpage_subsidiarylist',
						fieldId: 'ava_defcompanycode'
					});
					var customSubId = cRecord.getCurrentSublistValue({
						sublistId: 'custpage_subsidiarylist',
						fieldId: 'ava_customsubid'
					});
					var subsidiaryListCount = cRecord.getLineCount({
						sublistId: 'custpage_subsidiarylist'
					});
					
					for(var i = 0; i < subsidiaryListCount; i++){
						var customSubsidiaryId = cRecord.getSublistValue({
							sublistId: 'custpage_subsidiarylist',
							fieldId: 'ava_customsubid',
							line: i
						});
						if(customSubId != customSubsidiaryId){
							var defCompCode = cRecord.getSublistValue({
								sublistId: 'custpage_subsidiarylist',
								fieldId: 'ava_defcompanycode',
								line: i
							});
							if(selectedValue != null && selectedValue.length > 0){
								if(defCompCode != null && defCompCode.length > 0){
									if(defCompCode == selectedValue){
										alert('Company Already Mapped !!');
										cRecord.setCurrentSublistValue({
											sublistId: 'custpage_subsidiarylist',
											fieldId: 'ava_defcompanycode',
											value: '',
											ignoreFieldChange: true
										});
										cRecord.setCurrentSublistValue({
											sublistId: 'custpage_subsidiarylist',
											fieldId: 'ava_companyid',
											value: '',
											ignoreFieldChange: true
										});
										return;
									}
								}
							}
						}
					}
					
					var serviceUrl = cRecord.getValue({
						fieldId: 'ava_serviceurl'
					});
					
					var avaConfigObjRecvd = ava_library.mainFunction('AVA_LoadValuesToGlobals', '');
					var companyInfo = ava_library.mainFunction('AVA_CompanyFetch', (avaConfigObjRecvd.AVA_AdditionalInfo3 + '~~' + serviceUrl));
					
					if(companyInfo != null && companyInfo.length > 0){
						if(selectedValue != null && selectedValue.length > 0){
							for(var i = 0; i < companyInfo.length; i++){
								if(companyInfo[i][0] == selectedValue){
									cRecord.setCurrentSublistValue({
										sublistId: 'custpage_subsidiarylist',
										fieldId: 'ava_companyid',
										value: companyInfo[i][2].toString(),
										ignoreFieldChange: true
									});
									break;
								}
							}
						}
						else{
							var flag = 0;
							
							for(var i = 0; companyInfo != null && i < companyInfo.length; i++){
								var subsidiaryId = cRecord.getCurrentSublistValue({
									sublistId: 'custpage_subsidiarylist',
									fieldId: 'ava_subid'
								});
								if(companyInfo[i][0] == subsidiaryId){
									cRecord.setCurrentSublistValue({
										sublistId: 'custpage_subsidiarylist',
										fieldId: 'ava_companyid',
										value: companyInfo[i][2].toString(),
										ignoreFieldChange: true
									});
									flag = 1;
									break;
								}
							}
							
							if(flag == 0){
								cRecord.setCurrentSublistValue({
									sublistId: 'custpage_subsidiarylist',
									fieldId: 'ava_companyid',
									value: '',
									ignoreFieldChange: true
								});
							}
						}
					}
					break;
					
				default :
					break;
			}
		}
		
		function disableTaxFields(cRecord, disabled){
			var disableTaxQuote = cRecord.getField({
				fieldId: 'ava_disabletaxquote'
			});
			var disableTaxSalesOrder = cRecord.getField({
				fieldId: 'ava_disabletaxsalesorder'
			});
			var disableLineLevelTax = cRecord.getField({
				fieldId: 'ava_disableline'
			});
			var enableLogging = cRecord.getField({
				fieldId: 'ava_enablelogging'
			});
			var taxOnDemand = cRecord.getField({
				fieldId: 'ava_taxondemand'
			});
			var taxInclude = cRecord.getField({
				fieldId: 'ava_taxinclude'
			});
			var enableDiscount = cRecord.getField({
				fieldId: 'ava_enablediscount'
			});
			var discountMapping = cRecord.getField({
				fieldId: 'ava_discountmapping'
			});
			var discountTaxcode = cRecord.getField({
				fieldId: 'ava_discounttaxcode'
			});
			var taxrate = cRecord.getField({
				fieldId: 'ava_taxrate'
			});
			var decimalPlace = cRecord.getField({
				fieldId: 'ava_decimalplaces'
			});
			var usePostingPeriod = cRecord.getField({
				fieldId: 'ava_usepostingperiod'
			});
			var disableLocationCode = cRecord.getField({
				fieldId: 'ava_disableloccode'
			});
			var enableUpcCode = cRecord.getField({
				fieldId: 'ava_enableupccode'
			});
			var useInvoiceAddress = cRecord.getField({
				fieldId: 'ava_useinvoiceaddress'
			});
			var workflowApproval = cRecord.getField({
				fieldId: 'ava_committransaction'
			});
			var useBillToAddress = cRecord.getField({
				fieldId: 'ava_usebilltoaddress'
			});
			var abortBulkBilling = cRecord.getField({
				fieldId: 'ava_abortbulkbilling'
			});
			var abortUserInterfaces = cRecord.getField({
				fieldId: 'ava_abortuserinterfaces'
			});
			var abortWebservices = cRecord.getField({
				fieldId: 'ava_abortwebservices'
			});
			var abortCsvImports = cRecord.getField({
				fieldId: 'ava_abortcsvimports'
			});
			var abortScheduledScripts = cRecord.getField({
				fieldId: 'ava_abortscheduledscripts'
			});
			var abortSuitelets = cRecord.getField({
				fieldId: 'ava_abortsuitelets'
			});
			var abortWorkflowScript = cRecord.getField({
				fieldId: 'ava_abortworkflowactionscripts'
			});
			
			disableTaxQuote.isDisabled = disabled;
			disableTaxSalesOrder.isDisabled = disabled;
			disableLineLevelTax.isDisabled = disabled;
			enableLogging.isDisabled = disabled;
			taxOnDemand.isDisabled = disabled;
			taxInclude.isDisabled = disabled;
			enableDiscount.isDisabled = disabled;
			var display = (disabled == true) ? true : ((enableDiscount == true) ? false : true);
			discountMapping.isDisabled = display;
			discountTaxcode.isDisabled = display;
			taxrate.isDisabled = disabled;
			decimalPlace.isDisabled = disabled;
			usePostingPeriod.isDisabled = disabled;
			disableLocationCode.isDisabled = disabled;
			enableUpcCode.isDisabled = disabled;
			useInvoiceAddress.isDisabled = disabled;
			workflowApproval.isDisabled = disabled;
			useBillToAddress.isDisabled = disabled;
			abortBulkBilling.isDisabled = disabled;
			abortUserInterfaces.isDisabled = disabled;
			abortWebservices.isDisabled = disabled;
			abortCsvImports.isDisabled = disabled;
			abortScheduledScripts.isDisabled = disabled;
			abortSuitelets.isDisabled = disabled;
			abortWorkflowScript.isDisabled = disabled;
		}
		
		function editCredentials(){
			window.onbeforeunload = undefined;
			var Url = url.resolveScript({
				scriptId: 'customscript_avaconfig_wizard',
				deploymentId: 'customdeploy_ava_configurewizard',
				params: {
					'custparam_value' : 'noredirect'
				}
			});
			window.open(Url, '_self');
		}
		
		function next(){
			window.onbeforeunload = undefined;
			var cRecord = currentRecord.get();
			var taxAgency = cRecord.getValue({
				fieldId: 'ava_taxagency'
			});
			var taxcode = cRecord.getValue({
				fieldId: 'ava_taxcode'
			});
			
			if(taxAgency == false || taxcode == false){
				var msg = 'The required minimum configuration setting (Tax Agency Created and Tax Code Created) to calculate tax are not completed.\n\nDo you want to still continue?';
				
				if(!confirm(msg)){
					return;
				}
			}
			
			var searchRecord = search.create({
				type: 'customrecord_avaconfig'
			});
			var searchresult = searchRecord.run();
			searchresult = searchresult.getRange({
				start: 0,
				end: 5
			});
			
			var rec = record.load({
				type: 'customrecord_avaconfig',
				id: searchresult[0].id
			});
			rec.setValue({
				fieldId: 'custrecord_ava_configflag',
				value: true
			});
			rec.save({
			});
			
			var Url = url.resolveScript({
				scriptId: 'customscript_avaconfig_suitlet',
				deploymentId: 'customdeploy_configuration'
			});
			window.open(Url, '_self');
		}
		
		function createCompany(){
			var Url = url.resolveScript({
				scriptId: 'customscript_ava_createcompany_suitelet',
				deploymentId: 'customdeploy_ava_createcompany_suitelet'
			});
			window.open(Url, 'Create AvaTax Company', 'scrollbars = yes, width = 1024, height = 600, left = 200, top = 120');
		}
		
		function avalaraIdentityCall(){
			try{
				var details;
				var cRecord = currentRecord.get();
				var serviceUrl = cRecord.getValue({
					fieldId: 'ava_serviceurl'
				});
				
				if(serviceUrl == 1){
					details = 'sbx-netsuite-identity:Netsbx123';
					authorizationUrl = 'https://ai-sbx.avlr.sh/connect/deviceauthorization';
				}
				else{
					details = 'prd-netsuite-identity:yD7)o#Uk@!GH%1#PVahs5Lx0';
					authorizationUrl = 'https://identity.avalara.com/connect/deviceauthorization';
				}
				
				var Url = url.resolveScript({
					scriptId: 'customscript_ava_general_restlet',
					deploymentId: 'customdeploy_ava_general_restlet'
				});
				Url = Url + '&type=do' + '&details=' + details;
				var resp = https.get({
					url: Url
				});
				
				var header = {};
				var authorizationUrl;
				header["Authorization"] = "Basic " + resp.body;
				header["Cache-Control"] = "no-cache";
				header["Content-Type"] = "application/x-www-form-urlencoded";
				var body = 'scope=offline_access openid profile avatax_api avatax email netsuite-api-scope';
				
				var response = https.request({
					method: https.Method.POST,
					url: authorizationUrl,
					body: body,
					headers: header
				});
				
				if(response.code == 200){
					var myresponse_body = JSON.parse(response.body);
					var deviceCode = myresponse_body.device_code;
					var userCode = myresponse_body.user_code;
					var verificationURL = myresponse_body.verification_uri;
					var verificationUrlComplete = myresponse_body.verification_uri_complete;
					
					if(verificationURL || usedCode){
						document.getElementById('tdbody_ava_click_to_authorise').style.display = 'block';
						cRecord.setValue({ fieldId: 'ava_usercode', value: userCode, ignoreFieldChange: true });
						cRecord.setValue({ fieldId: 'ava_devicecode', value: deviceCode, ignoreFieldChange: true });
						cRecord.setValue({ fieldId: 'ava_verificationlink', value: verificationURL, ignoreFieldChange: true });
						
						var userCodeRecvdMsg = message.create({
							title: "Response Received",
							message: 'The AvaTax user code has been received, you must authenticate AvaTax to proceed.',
							type: message.Type.INFORMATION
						});
						userCodeRecvdMsg.show({
							duration: 5000
						});
					}
				}
				else{
					alert('response.code::' + response.code)
				}
			}
			catch(err){
				alert('error:::' + err)
			}
		}
		
		var myMessage;
		function pollToken(error){
			var details;
			var cRecord = currentRecord.get();
			var verificationUrl = cRecord.getValue({ fieldId: 'ava_verificationlink' });
			
			if(!error && verificationUrl){
				window.open(verificationUrl);
			}
			
			var serviceUrl = cRecord.getValue({
				fieldId: 'ava_serviceurl'
			});
			
			if(serviceUrl == 1){
				details = 'sbx-netsuite-identity:Netsbx123';
				pollUrl = 'https://ai-sbx.avlr.sh/connect/token';
			}
			else{
				details = 'prd-netsuite-identity:yD7)o#Uk@!GH%1#PVahs5Lx0';
				pollUrl = 'https://identity.avalara.com/connect/token';
			}
			
			var Url = url.resolveScript({
				scriptId: 'customscript_ava_general_restlet',
				deploymentId: 'customdeploy_ava_general_restlet'
			});
			Url = Url + '&type=do' + '&details=' + details;
			var resp = https.get({
				url: Url
			});
			
			var header = {};
			var pollUrl;
			header["Authorization"] = "Basic " + resp.body;
			header["Cache-Control"] = "no-cache";
			header["Content-Type"] = "application/x-www-form-urlencoded";
			var body = 'grant_type=urn:ietf:params:oauth:grant-type:device_code&device_code=' + cRecord.getValue({ fieldId: 'ava_devicecode' });
			
			https.post.promise({
				url: pollUrl,
				body: body,
				headers: header
			}).then(function (response){
				if(response.code == 200){
					var accValue;
					var myresponse_body = JSON.parse(response.body);
					var additionalInfo3 = myresponse_body.access_token;
					var additionalInfo4 = myresponse_body.refresh_token;
					var addInfo = parseAddInfo(myresponse_body.id_token)
					
					if(addInfo.avatax_account_id != null && addInfo.avatax_account_id.length >0){
						accValue = addInfo.avatax_account_id;
					}
					
					document.getElementById('tdbody_ava_click_to_authorise').style.display = 'none';
					document.getElementById('tdbody_ava_identity_call').style.display = 'none';
					if(myMessage){
						myMessage.hide();
						myMessage = null;
					}
					
					var informationMessage = message.create({
						title: 'Authentication Successful',
						message: 'Successful',
						type: message.Type.CONFIRMATION
					});
					informationMessage.show();
					
					var avaConfigConnection =  configTestConnection(cRecord, additionalInfo3);
					
					if(avaConfigConnection){
						try{
							var rec;
							var searchRecord = search.create({
								type: 'customrecord_avaconfig'
							});
							var searchresult = searchRecord.run();
							searchresult = searchresult.getRange({
								start: 0,
								end: 5
							});
							
							if(searchresult != null && searchresult.length > 0){
								rec = record.load({
									type: 'customrecord_avaconfig',
									id: searchresult[0].id
								});
							}
							else{
								rec = record.create({
								type: 'customrecord_avaconfig'
								});
							}
							
							rec.setValue({
								fieldId: 'custrecord_ava_accountvalue',
								value: accValue
							});
							rec.setValue({
								fieldId: 'custrecord_ava_additionalinfo3',
								value: additionalInfo3
							});
							rec.setValue({
								fieldId: 'custrecord_ava_additionalinfo4',
								value: additionalInfo4
							});
							rec.setValue({
								fieldId: 'custrecord_ava_url',
								value: serviceUrl
							});
							rec.setValue({
								fieldId: 'custrecord_ava_servicetypes',
								value: cRecord.getValue('ava_servicetypes')
							});
							rec.save();
						}
						catch(err){
							alert('Error::' + err.message)
						}
					}
					else{
						return;
					}
					
					if(cRecord.getValue({ fieldId: 'ava_setupconfig' }) == 'F'){
						window.onbeforeunload = undefined;
						var UrlNew = url.resolveScript({
							scriptId: 'customscript_avaconfig_wizard1',
							deploymentId: 'customdeploy_avaconfig_wizard1'
						});
						window.open(UrlNew, '_self');
					}
					else{
						window.onbeforeunload = undefined;
						var Url = url.resolveScript({
							scriptId: 'customscript_avaconfig_suitlet',
							deploymentId: 'customdeploy_configuration'
						});
						window.open(Url, '_self');
					}
				}
				else if(response.code == 400){
					myresponse_body = JSON.parse(response.body)
					var error = myresponse_body.error
					setTimeout(function(){
						if(!myMessage){
							myMessage = message.create({
								title: "Awaiting Authentication",
								message: error,
								type: message.Type.ERROR,
							});
							myMessage.show();
						}
						
						pollToken(error);
					}, 1000);
				}
			}).catch(function onRejected(reason){
				log.debug({
					title: 'Invalid Request: ',
					details: reason
				});
			})
		}
		
		function parseAddInfo(info){
			var info1 = info.split('.')[1];
			var info2 = info1.replace(/-/g, '+').replace(/_/g, '/');
			
			var payload = decodeURIComponent(window.atob(info2).split('').map(function(c){
				return '%' + ('00' + c.charCodeAt(0).toString(16)).slice(-2);
			}).join(''));
			
			return JSON.parse(payload);
		}
		
		return{
			fieldChanged: configChanged,
			saveRecord: configSave,
			editCredentials: editCredentials,
			next: next,
			createCompany: createCompany,
			avalaraIdentityCall: avalaraIdentityCall,
			pollToken: pollToken
		};
	}
);
