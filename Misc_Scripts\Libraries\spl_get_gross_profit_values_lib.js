//@ts-ignore
define(["N/log", "GetGrossProfitValuesHelperLib"], function (
	log,
	getGrossProfitValuesHelperLib
) {
	function getGrossProfitValues(
		invoice,
		invoiceInternalId,
		recordType,
		createdFromRecordType
	) {
		let errorLog = [];
		let grossProfitValuesObj = {};

		try {
			const shippingValuesObj =
				getGrossProfitValuesHelperLib.getShippingValuesObj(
					invoice,
					invoiceInternalId,
					recordType
				);

			const revenueValuesObj = getRevenueValues(
				invoice,
				shippingValuesObj.shippingRevenue
			);

			const expenseValuesObj = getExpenseValues(
				invoice,
				invoiceInternalId,
				revenueValuesObj.itemsSubtotal,
				shippingValuesObj.shippingCOGS
			);

			let grossProfit =
				revenueValuesObj.netRevenue - expenseValuesObj.netExpenses;

			if (recordType == "CREDIT_MEMO") {
				grossProfit = grossProfit * -1; //Flip gross profit to be a negative number
			}

			grossProfitPercentage =
				recordType == "CREDIT_MEMO" || !revenueValuesObj.netRevenue
					? -100
					: (grossProfit * 100) / revenueValuesObj.netRevenue;

			grossProfitValuesObj = {
				...shippingValuesObj,
				...revenueValuesObj,
				...expenseValuesObj,
				...{ grossProfit, grossProfitPercentage },
			};
		} catch (e) {
			errorLog.push(e.message);
		}

		return {
			grossProfitValuesObj,
			errorLog,
		};

		function getRevenueValues(invoice, shippingRevenue) {
			var itemsSubtotal = invoice.getValue("subtotal") ?? 0;

			let netRevenue = itemsSubtotal + shippingRevenue;

			if (netRevenue == null || netRevenue == undefined) {
				throw `No net revenue returned: itemsSubtotal - ${itemsSubtotal}, shippingRevenue - ${shippingRevenue}`;
			}

			return {
				itemsSubtotal,
				netRevenue,
			};
		}

		function getExpenseValues(
			invoice,
			invoiceInternalId,
			itemsSubtotal,
			shippingCOGS
		) {
			const itemsCOGS =
				recordType != "CREDIT_MEMO" ||
				(recordType == "CREDIT_MEMO" &&
					createdFromRecordType == "RETURN_AUTHORIZATION")
					? invoice.getValue("totalcostestimate")
					: 0 ?? 0;

			const purchasingSoftwareFee =
				recordType != "CREDIT_MEMO"
					? getGrossProfitValuesHelperLib.getPurchasingSoftwareFee(
							invoiceInternalId,
							itemsSubtotal
					  )
					: 0;

			let gpoReferralFee =
				recordType != "CREDIT_MEMO"
					? getGrossProfitValuesHelperLib.getGPOReferralFee(
							invoiceInternalId,
							itemsSubtotal
					  )
					: 0;

			const netExpenses =
				itemsCOGS + shippingCOGS + purchasingSoftwareFee + gpoReferralFee;

			if (!netExpenses && netExpenses != 0) {
				throw `No net expenses returned: itemsCOGS - ${itemsCOGS}, purchasingSoftwareFee - ${purchasingSoftwareFee}, gpoReferralFee - ${gpoReferralFee}`;
			}

			return {
				itemsCOGS,
				purchasingSoftwareFee,
				gpoReferralFee,
				netExpenses,
			};
		}
	}

	return {
		getGrossProfitValues,
	};
});
