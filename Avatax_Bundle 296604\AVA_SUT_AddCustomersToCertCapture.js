/******************************************************************************************************
	Script Name - AVA_SUT_AddCustomersToCertCapture.js
	Company -     Avalara Technologies Pvt Ltd.
******************************************************************************************************/

/**
 *@NApiVersion 2.0
 *@NScriptType Suitelet
 */

define(['N/ui/serverWidget', 'N/url', 'N/search', 'N/redirect', './utility/AVA_Library'],
	function(ui, url, search, redirect, ava_library){
		function onRequest(context){
			try{
				var checkServiceSecurity = ava_library.mainFunction('AVA_CheckService', 'AvaCert');
				if(checkServiceSecurity == 0){
					checkServiceSecurity = ava_library.mainFunction('AVA_CheckSecurity', 29);
				}

				if(checkServiceSecurity == 0){
					if(context.request.method === 'GET'){
						var searchRecord = search.create({
							type: 'customrecord_avaconfig',
							columns: ['custrecord_ava_additionalinfo3', 'custrecord_ava_configflag']
						});
						var searchresult = searchRecord.run();
						searchresult = searchresult.getRange({
							start: 0,
							end: 5
						});
						
						if(searchresult != null && searchresult.length > 0){
							var additionalInfo3 = searchresult[0].getValue('custrecord_ava_additionalinfo3');
							var configFlag = searchresult[0].getValue('custrecord_ava_configflag');
							
							if(additionalInfo3 != null && additionalInfo3.length > 0 && configFlag == true){
								var avaCertCaptureForm = ui.createForm({
									title: 'Create Batch'
								});
								avaCertCaptureForm.clientScriptModulePath = './AVA_CLI_AddCustomersToCertCapture.js';
								addFormFields(avaCertCaptureForm, context);
								avaCertCaptureForm.addSubmitButton({
									label: 'Submit'
								});
								avaCertCaptureForm.addButton({
									id: 'custpage_ava_certcapture_reset',
									label: 'Reset',
									functionName: 'AVA_CertCaptureReset()'
								});
								avaCertCaptureForm.addPageLink({
									title: 'View Batch',
									type: ui.FormPageLinkType.CROSSLINK,
									url: url.resolveScript({
										scriptId: 'customscript_avacertviewbatch_suitelet',
										deploymentId: 'customdeploy_avacertcaptureviewbatch'
									})
								});
								context.response.writePage({
									pageObject: avaCertCaptureForm
								});
							}
							else{
								redirect.toSuitelet({
									scriptId: 'customscript_avaconfig_wizard',
									deploymentId: 'customdeploy_ava_configurewizard'
								});
							}
						}
						else{
							redirect.toSuitelet({
								scriptId: 'customscript_avaconfig_wizard',
								deploymentId: 'customdeploy_ava_configurewizard'
							});
						}
					}
					else{
						var param = new Array();
						param['batchname'] = context.request.parameters.ava_batchname;
						param['subsidiary'] = context.request.parameters.ava_subsidiary;
						param['namestartswith'] = context.request.parameters.ava_customernamestartswith;
						param['namecontains'] = context.request.parameters.ava_customernamecontains;
						param['customertype'] = context.request.parameters.ava_companytype;
						param['startdate'] = context.request.parameters.ava_startddate;
						param['enddate'] = context.request.parameters.ava_enddate;
						context.response.sendRedirect("SUITELET", "customscript_avacustlistforcert_suitelet", "customdeploy_avacustslistforcertcapture", false, param);
					}
				}
				else{
					context.response.write({
						output: checkServiceSecurity
					});
				}
			}
			catch(e){
				log.error('onRequest', 'ERROR NAME = ' + e.name + ', ERROR TYPE = ' + e.type + ', ERROR MESSAGE = ' + e.message);
			}
		}

		function addFormFields(avaCertCaptureForm, context){
			try{
				avaCertCaptureForm.addFieldGroup({
					id: 'ava_batchdata',
					label: '<b>Batch Information</b>'
				});
				var avaBatchName = avaCertCaptureForm.addField({
					id: 'ava_batchname',
					label: 'Batch Name',
					type: ui.FieldType.TEXT,
					container: 'ava_batchdata'
				});
				avaBatchName.isMandatory = true;
				avaBatchName.defaultValue = context.request.parameters.batchname;
				avaBatchName.updateDisplaySize({
					width: 40,
					height: 0
				});
				var customerNameStartsWith = avaCertCaptureForm.addField({
					id: 'ava_customernamestartswith',
					label: 'Customer Name Starts With',
					type: ui.FieldType.TEXT,
					container: 'ava_batchdata'
				});
				customerNameStartsWith.defaultValue = context.request.parameters.namestartswith;
				customerNameStartsWith.updateDisplaySize({
					width: 40,
					height: 0
				});
				var avaSubsidiary = avaCertCaptureForm.addField({
					id: 'ava_subsidiary',
					label: 'Subsidiary',
					type: ui.FieldType.SELECT,
					source: 'subsidiary',
					container: 'ava_batchdata'
				});
				avaSubsidiary.isMandatory = true;
				avaSubsidiary.defaultValue = context.request.parameters.subsidiary;
				var customerNameContains = avaCertCaptureForm.addField({
					id: 'ava_customernamecontains',
					label: 'Customer Name Contains',
					type: ui.FieldType.TEXT,
					container: 'ava_batchdata'
				});
				customerNameContains.defaultValue = context.request.parameters.namecontains;
				customerNameContains.updateDisplaySize({
					width: 40,
					height: 0
				});
				avaCertCaptureForm.addFieldGroup({
					id: 'ava_customertypes',
					label: '<b>Customer Type</b>'
				});
				var customerType = avaCertCaptureForm.addField({
					id: 'ava_companytype',
					label: 'Type',
					type: ui.FieldType.SELECT,
					container: 'ava_customertypes'
				});
				customerType.addSelectOption({
					value: 'B',
					text: 'Both'
				});
				customerType.addSelectOption({
					value: 'T',
					text: 'Individual'
				});
				customerType.addSelectOption({
					value: 'F',
					text: 'Company'
				});
				customerType.defaultValue = context.request.parameters.customertype;
				avaCertCaptureForm.addFieldGroup({
					id: 'ava_createddate',
					label: '<b>Customer Created Date</b>'
				});
				var avaStartDate = avaCertCaptureForm.addField({
					id: 'ava_startddate',
					label: 'Start Date',
					type: ui.FieldType.DATE,
					container: 'ava_createddate'
				});
				avaStartDate.isMandatory = true;
				avaStartDate.defaultValue = context.request.parameters.startdate;
				var avaEndDate = avaCertCaptureForm.addField({
					id: 'ava_enddate',
					label: 'End Date',
					type: ui.FieldType.DATE,
					container: 'ava_createddate'
				});
				avaEndDate.isMandatory = true;
				avaEndDate.defaultValue = context.request.parameters.enddate;
			}
			catch(e){
				log.error('addFormFields', 'ERROR NAME = ' + e.name + ', ERROR TYPE = ' + e.type + ', ERROR MESSAGE = ' + e.message);
			}
		}
		
		return{
			onRequest: onRequest
		};
	}
);
