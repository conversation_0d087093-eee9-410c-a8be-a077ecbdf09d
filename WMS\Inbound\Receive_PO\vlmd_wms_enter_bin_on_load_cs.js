/**
 * @description Stores page name in state to be used by succeeding pages
 * 
 * </br><b>Parent Page:</b> poReceiving_enterBin_1
 * </br><b>Action:</b> poReceiving_enterBin_onLoadClientFunction
 * 
 * <AUTHOR>
 * @module vlmd_wms_enter_bin_on_load
 */
(function(){
    let clientFunctions = {};
    clientFunctions.setOriginPage = function () {
        try {
            let dataRecord = mobile.getRecordFromState();
            dataRecord.scriptParams.originPage = "poReceiving_enterBin_1";
            mobile.setRecordInState(dataRecord);
        } catch (err) {
            alert(
                `An error occured.\nPlease take a screenshot of this message and file a support ticket.\n\nParent Page: poReceiving_enterBin_1\nAction: poReceiving_enterBin_onLoadClientFunction\nError Message: ${err.message}`
            );
        }
    }

    return clientFunctions; 
} ());