/**
 * @description Enables Post Item Receipt button based on PO's total received quantity.
 * 
 * </br><b>Parent Page:</b> poReceiving_selectItem_1
 * </br><b>Action:</b> poReceiving_selectItem_afterLoadClientFunction
 * 
 * <AUTHOR>
 * @module vlmd_wms_select_item_on_load_cs
 */
(function(){
    let clientFunctions = {};
    clientFunctions.enablePostItemReceiptBtn = function () {
        try {
            // Store page name in state to be used by succeeding pages
            let dataRecord = mobile.getRecordFromState();
            dataRecord.scriptParams.originPage = "poReceiving_selectItem_1";
            mobile.setRecordInState(dataRecord);
            
            let getPostIRItemsPromiseObj = mobile.callRestlet(
                'customscript_vlmd_wms_get_post_ir_items', // script_id
                'customdeploy_vlmd_wms_get_post_ir_items', // deployment_id
                {
                    warehouseLocationId: dataRecord.auxParams.warehouseLocation_LocationTbl.id,
                    transactionInternalId: dataRecord.scriptParams.transactionInternalId,
                }, // restlet parameters
                'post' // http method
            );
            getPostIRItemsPromiseObj.then(async (totalQtyReceived) => {
                // If no items received, do nothing
                if(await totalQtyReceived == 0) return;

                // If some/all items received, enable the button
                mobile.enableField('poReceiving_selectItem_postItemReceiptBtn');
            });
        } catch (err) {
            alert(`An error occured.\nPlease take a screenshot of this message and file a support ticket.\n\nParent Page: poReceiving_selectItem_1\nAction: poReceiving_selectItem_afterLoadClientFunction\nError Message: ${err.message}`);
        }
    }

    return clientFunctions;
} ());