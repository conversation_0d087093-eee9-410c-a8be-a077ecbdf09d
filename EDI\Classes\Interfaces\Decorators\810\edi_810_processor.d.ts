/**
 * @description EDI 810 Processor Types and Interface
 *
 * <AUTHOR> <<EMAIL>>
 */

import { Search } from "N/search";
import { SuiteQLObjectReference } from "../../Models/File/edi_outgoing";
import { File } from "N/file";

export interface EDI810ProcessorInterface {
    /** Return SuiteQL query string or Search object to retrieve NetSuite records */
    load(params: {[key:string]: any}): SuiteQLObjectReference;
    /** Load the template and fill out values from the Invoice or Credit Memo record */
    process(): string;
    /** Create the EDI File */
    create(params: {[key:string]: any}): File | null;
    /** Mark the transaction as processed */
    complete(params: {[key:string]: any}): number;
}

/** Service, Allowance and Charge Item */
export type EDI810SACItem = {
    name: string;
    code: string;
}