/**
 * Bridge Royal Discount class used to build the subsidiaries object containing transaction line indices
 *
 * @NApiVersion 2.1
 * <AUTHOR>
 * @module BridgeRoyalDiscount
 */

define(["exports"], (/** @type {any} */ exports) => {
	/**
	 * Bridge Royal Discount
	 *
	 * @class
	 * @type {import("./brdg_transaction_object").BridgeRoyalDiscount}
	 */
	class BridgeRoyalDiscount {
		/**
		 * Constructor
		 *
		 * @param {import("N/record").Type} recordType Item record
		 */
		constructor(recordType) {
			this.casesCount = 0;
			this.transactionsObj = {};
			this.currentTransactionObj = {
				transactionId: "",
				sublistLineIndex: 0,
				additionalCases: 0,
				subsidiaryId: "",
				isCase: false,
				linkedTransaction: "",
			};
			this.recordType = recordType;
		}

		/**
		 * Set the current transaction object to the specified query result from the query iterator
		 *
		 * @param {import("./brdg_transaction_object").BridgeRoyalDiscountQueryResult} transactionObj
		 */
		setCurrentTransactionObject(transactionObj) {
			this.currentTransactionObj = transactionObj;
		}

		/**
		 * Determine if indices array exists on the transactions object
		 *
		 * @returns {boolean} True if indices array already exists on the transactions object
		 */
		doesIndicesArrayExist() {
			return (
				this.transactionsObj[this.currentTransactionObj.transactionId] &&
				this.transactionsObj[this.currentTransactionObj.transactionId]
					.indices &&
				Array.isArray(
					this.transactionsObj[this.currentTransactionObj.transactionId].indices
				)
			);
		}

		/**
		 * Add the transaction line sequence number to the indices array of the transaction object
		 *
		 * @returns {void}
		 */
		addSublistIndex() {
			this.transactionsObj[this.currentTransactionObj.transactionId] =
				this.doesIndicesArrayExist()
					? {
							recordType: this.recordType,
							indices: [
								...this.transactionsObj[
									this.currentTransactionObj.transactionId
								].indices,
								this.currentTransactionObj.sublistLineIndex,
							],
					  }
					: {
							recordType: this.recordType,
							indices: [this.currentTransactionObj.sublistLineIndex],
					  };
		}

		/**
		 * Increment the cases count of the subsidiary
		 *
		 * @returns {void}
		 */
		incrementCasesCount() {
			if (this.currentTransactionObj.isCase) {
				this.casesCount += this.currentTransactionObj.additionalCases;
			}
		}

		/**
		 * Assign the next or previous transaction linked to the transaction
		 *
		 * @returns {void}
		 */
		setLinkedTransaction() {
			this.transactionsObj[this.currentTransactionObj.transactionId][
				"linkedTransaction"
			] = this.currentTransactionObj.linkedTransaction;
		}
	}

	exports.BridgeRoyalDiscount = BridgeRoyalDiscount;

	return BridgeRoyalDiscount;
});
