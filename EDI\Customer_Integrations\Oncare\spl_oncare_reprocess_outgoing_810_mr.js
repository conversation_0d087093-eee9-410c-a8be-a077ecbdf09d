/**
 * @NApiVersion 2.1
 * @NScriptType MapReduceScript
 * @NAmdConfig /SuiteScripts/config.json
 */

define([
	"require",
	"N/query",
	"Get810InternalIdsLib",
	"GetEdiPartnerValuesLib",
	"ProcessOutgoingEdiFileEmailLib",
	"PushEdiEmailInfoToDBLib",
	"N/runtime",
	"../../Libraries/Process_EDI_File/spl_process_outgoing_810_lib",
	"../../../Classes/vlmd_custom_error_object",
	"../../../Classes/vlmd_mr_summary_handling",
], function (
	require,
	query,
	getInvoiceInternalIdsLib,
	getEdiPartnerValuesLib,
	processOutgoingEdiFileEmailLib,
	pushEdiEmailInfoToDBLib,
	runtime
) {
	/** @type {import("../../../Classes/vlmd_custom_error_object").CustomErrorObject} */
	const CustomErrorObject = require("../../../Classes/vlmd_custom_error_object");

	const process810Lib = require("../../Libraries/Process_EDI_File/spl_process_outgoing_810_lib");

	const noTransactionsToProcess = [];

	const dataObj = {
		prodGuidBool: true,
		prodDirectoryBool: true,
		decodeContent: true,
		prodGUID: "07e654a466ff4202abd5a86868e0feb4",
		sandboxGUID: "",
		testDirectory: "/Receive from OnCare/Test/Invoices",
		transactionType: "Invoice/Credit Memo",
		purchasingSoftware: "Oncare",
		documentTypeId: 6,
		purchasingSoftwareId: 1,
		pushEmailToDB: true,
	};

	var partnerValues;

	function getInputData(context) {
		const customErrorObject = new CustomErrorObject();

		try {
			const searchArr = [];

			const currentScript = runtime.getCurrentScript();
			const customerId = currentScript.getParameter({
				name: "custscript_spl_rprcs_otgng_810_oc_cstmr",
			});

			// Value should be Comma delimited Invoice Number enclosed in single quotes ('INV835965409','INV835965466','INV835965803')
			const documentNumbersVals = currentScript.getParameter({
				name: "custscript_spl_rprcs_otgng_810_docnum_oc",
			});

			const { customerName, integrationFolder, integrationId } =
				_getParameterValues(customerId);

			partnerValues = getEdiPartnerValuesLib.getOncareValues();

			if (!partnerValues) {
				throw `No partner values gotten.`;
			}

			const customerFoldersArr = [
				{
					customerId,
					integrationFolder,
					customerName,
					integrationId,
				},
			];

			customerFoldersArr.forEach((customer) => {
				const transactionsArr = getInvoiceInternalIdsLib.getInternalIds(
					customer.customerId,
					"",
					"",
					"",
					documentNumbersVals,
					customErrorObject
				);

				if (transactionsArr.length <= 0) {
					noTransactionsToProcess.push(customer.customerName);
				} else {
					transactionsArr.forEach((internalId) => {
						searchArr.push({
							transactionObj: internalId,
							customer: customer,
							partnerValues: partnerValues,
							dataObj: dataObj,
						});
					});
				}
			});

			function _getParameterValues(customerId) {
				try {
					const parameterQuery = `SELECT
					c.altname customer_name,
					BUILTIN.DF( eir.custrecord_spl_prchsng_fclty ) integration_folder,
					eir.id integration_id,
				 FROM
					CUSTOMER c 
					JOIN
					   customrecord_vlmd_edi_integration eir 
					   ON c.custentity_spl_edi_integration_record = eir.id 
				 WHERE
					c.ID = ${customerId}`;

					const resultIterator = query
						.runSuiteQL({
							query: parameterQuery,
						})
						.asMappedResults();

					return {
						customerName: resultIterator[0]["customer_name"],
						integrationFolder: resultIterator[0]["integration_folder"],
						integrationId: resultIterator[0]["integration_id"],
					};
				} catch (e) {
					throw `${e}: ${customerId}`;
				}
			}

			return searchArr;
		} catch (err) {
			customErrorObject.throwError({
				summaryText: "GET_INPUT_DATA_ERROR",
				error: err,
			});
		}
	}

	function map(context) {
		const customErrorObject = new CustomErrorObject();

		var parsedResult = JSON.parse(context.value);
		var { transactionObj, customer, partnerValues, dataObj } = parsedResult;

		try {
			dataObj.prodDirectory = `/Receive from OnCare/${customer.integrationFolder}/OUT/810`;

			const docCtrlNumRecIdVal = process810Lib.process810(
				transactionObj,
				customer,
				partnerValues,
				dataObj,
				customErrorObject
			);

			context.write(customer.customerName, docCtrlNumRecIdVal);
		} catch (err) {
			customErrorObject.throwError({
				summaryText: `MAP_ERROR`,
				error: err,
				recordId: transactionObj?.transactionId,
				recordType: transactionObj?.transactionType,
				errorWillBeGrouped: true,
			});
		}
	}

	function reduce(context) {
		context.write({
			key: context.key,
			value: context.values,
		});
	}

	function summarize(context) {
		try {
			var sentEmailObj = processOutgoingEdiFileEmailLib.processEmail(
				context,
				noTransactionsToProcess,
				dataObj.purchasingSoftware,
				dataObj.transactionType
			);

			if (dataObj.pushEmailToDB) {
				try {
					pushEdiEmailInfoToDBLib.pushEdiEmailInfoToDB(dataObj, sentEmailObj);
				} catch (e) {
					throw `Error pushing EDI email to database: ${e}`;
				}
			}
		} catch (err) {
			customErrorObject.throwError({
				summaryText: `SUMMARIZE_ERROR`,
				error: err,
			});
		}
	}

	return {
		getInputData,
		map,
		reduce,
		summarize,
	};
});
