/**
 *@NApiVersion 2.1
 *@NScriptType workflowactionscript
 * @NAmdConfig /SuiteScripts/config.json
 */

//@ts-ignore
define(["N/log", "N/record", "GetRepToAssignToLib"], function (
	log,
	record,
	getRepToAssignToLib
) {
	function onAction(context) {
		var scriptDeployment = getScriptDeployment();

		var assigneeType = scriptDeployment.getValue(
			"custscript_spl_get_asgnee_type"
		);

		var repAssignedLastTask = scriptDeployment.getValue(
			"custscript_spl_last_rep_assigned_to"
		);

		var nextRepToAssignTo;

		if (assigneeType == 1) {
			//	CSR Rep to Assign Task To
			var repArr = getRepToAssignToLib.getTaskRepArr("8"); //Lakewood CSR
			nextRepToAssignTo = getRepToAssignToLib.getRepBasedOffOfLastAssigned(
				repArr,
				repAssignedLastTask
			);
		} else if (assigneeType == 2) {
			//Offshore CSR Rep to Assign Task To
			var repArr = getRepToAssignToLib.getTaskRepArr("9"); // Offshore CSR
			nextRepToAssignTo = getRepToAssignToLib.getRepBasedOffOfLastAssigned(
				repArr,
				repAssignedLastTask
			);
		} else if (assigneeType == 3) {
			//CSR Rep to Assign Case To
			nextRepToAssignTo =
				getRepToAssignToLib.getRepBasedOffOfCaseRatio(repAssignedLastTask);
		} else {
			throw `Assignee type ${assigneeType} not recognized`;
		}

		updateLastRepScripParam(nextRepToAssignTo);
		return nextRepToAssignTo;

		function getScriptDeployment() {
			var workflowId = context.workflowId;
			var deploymentId;
			if (workflowId == 45) {
				deploymentId = "3378";
			} else if (workflowId == 43) {
				deploymentId = "3379";
			} else {
				throw `Script Deployment ${workflowId} not found.`;
			}
			return record.load({
				type: record.Type.SCRIPT_DEPLOYMENT,
				id: deploymentId,
			});
		}

		function updateLastRepScripParam(nextRepToAssignTo) {
			scriptDeployment.setValue({
				fieldId: "custscript_spl_last_rep_assigned_to",
				value: nextRepToAssignTo,
			});

			scriptDeployment.save();
		}
	}

	return {
		onAction: onAction,
	};
});
