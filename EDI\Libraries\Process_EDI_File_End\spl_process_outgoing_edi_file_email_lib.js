/**
 * @NApiVersion 2.1
 */

//@ts-ignore
define(["N/email", "N/log"], function (email, log) {
	function processEmail(
		summaryContext,
		noTransactionsToProcess,
		purchasingSoftware,
		documentType
	) {
		var docCtrlNumRecIdArr = [];

		var summaryMessage = `Usage Consumed: ${summaryContext.usage}
		Concurrency Number: ${summaryContext.concurrency}
		Number of Yields: ${summaryContext.yields}`;

		var documentsProcessedSuccesfullyText =
			getDocumentsProcessedSuccessfully(summaryContext);
		var errorMessagesText = getErrorMessages(summaryContext);
		var resultsData = setResultsData();

		if (resultsData.status == "Failure") {
			//sendEmail();
		}

		logResults();

		return resultsData;

		function getDocumentsProcessedSuccessfully(summary) {
			try {
				var resultsLog = [];
				summary.output.iterator().each(function (key, value) {
					var parsedValue = JSON.parse(value);

					if (parsedValue.length > 0) {
						//At this point will be an arr of strings - either a regular string of a JSON string

						var docNumOnEmail = "";

						parsedValue.forEach(function (objValue) {
							const isJsonObj = objValue.includes('{"');

							docNumOnEmail += isJsonObj
								? `${JSON.parse(objValue).custAndDocNum}, `
								: objValue;
							docCtrlNumRecIdArr.push(
								isJsonObj ? JSON.parse(objValue).docCtrlNumRecIdVal : objValue
							);
						});
					}

					if (docNumOnEmail) {
						resultsLog.push({ customer: key, documents: docNumOnEmail });
					}

					return true;
				});

				if (resultsLog.length > 0) {
					var summaryText = `${documentType} Processed Successfully:`;
					resultsLog.forEach((result) => {
						var parsedResults = result.documents
							.replace(/\[|\]/g, "")
							.split(",");
						summaryText += `${result.customer} - ${parsedResults}`;

						return true;
					});

					return summaryText;
				}
			} catch (err) {
				log.debug("ps err", err);
				throw `Error getting documents processed successfully: ${err}`;
			}
		}

		function getErrorMessages(summary) {
			try {
				var mapErrors = getErrorInStage("Map", summary.mapSummary);
				var reduceErrors = getErrorInStage("Reduce", summary.reduceSummary);

				var errorArr = [...mapErrors, ...reduceErrors];
				if (errorArr.length <= 0) {
					return "";
				}
				var errorText = `EDI ${documentType} Errors:
					
					`;

				errorArr.forEach(function (error) {
					errorText += error + "\n\n";
				});

				return errorText;
			} catch (err) {
				log.debug("em err", err);
				throw `Error getting error messages: ${err}`;
			}
		}

		function getErrorInStage(stage, summary) {
			try {
				var errorsLog = [];
				summary.errors.iterator().each(function (key, value) {
					var msg = `${JSON.parse(value).message}`;

					errorsLog.push(msg);

					return true;
				});

				return errorsLog;
			} catch (err) {
				throw `Error getting error in stage: ${err}`;
			}
		}

		function setResultsData() {
			try {
				var data = {};
				data.recipients = ["<EMAIL>"];
				data.documentControlNumbers = docCtrlNumRecIdArr;
				setStatus();
				data.subject = `${data.status}: EDI ${purchasingSoftware} ${documentType} Results:
			`;
				setBody();
				return data;

				function setStatus() {
					if (
						errorMessagesText.length <= 0 &&
						documentsProcessedSuccesfullyText
					) {
						data.status = "Success";
						data.processingStatus = 1; //Processed With No Errors
					} else if (
						errorMessagesText.length <= 0 &&
						!documentsProcessedSuccesfullyText
					) {
						data.status = `No ${documentType}s to Process`;
						data.processingStatus = 5; //No Transactions To Process
					} else {
						data.status = "Failure";
						data.processingStatus = 3; //Programming Error;
					}
				}

				function setBody() {
					data.body = `EDI ${purchasingSoftware} ${documentType} Results
					

					`;
					if (documentsProcessedSuccesfullyText) {
						data.body += `${documentsProcessedSuccesfullyText}
					
					`;
					}
					if (errorMessagesText.length > 0) {
						data.body += `${errorMessagesText}
					
					`;
					}

					if (noTransactionsToProcess.length > 0) {
						data.body += `
					
					Script ran, no results found for:
					
					`;
						noTransactionsToProcess.forEach(function (customer) {
							data.body += customer + "\n\n";
						});
					}

					data.body += summaryMessage;
				}
			} catch (err) {
				throw `Error setting results data: ${err}`;
			}
		}

		function sendEmail() {
			try {
				email.send({
					author: 262579, //EDI
					recipients: resultsData.recipients,
					subject: resultsData.subject,
					body: resultsData.body,
				});
			} catch (e) {
				throw `${purchasingSoftware} ${documentType} Email Not Sent. Error: ${e}`;
			}
		}

		function logResults() {
			if (documentsProcessedSuccesfullyText) {
				log.debug({
					title: `${documentType}s Processed`,
					details: documentsProcessedSuccesfullyText,
				});
			}
			if (errorMessagesText.length > 0) {
				log.error({
					title: "Error Log",
					details: errorMessagesText,
				});
			}
		}
	}

	return {
		processEmail,
	};
});
