/**
 * @description EDI Transaction Class
 *
 * @NApiVersion 2.1
 * <AUTHOR> <<EMAIL>>
 */

define([
    "exports",
    "require",
    "N/format",
    "N/log",
    "N/record",
    "N/search",
], function (/** @type {any} */ exports, /** @type {any} */ require) {
    const format = require("N/format");
    const record = require("N/record");

    /**
     * Transaction Class
     *
     * @typedef {import("../../../../Classes/vlmd_custom_error_object").CustomErrorObject} CustomErrorObject
     * @typedef {import("../../Interfaces/Transactions/edi_transaction").EDIAddress} EDIAddress
     * @class
     */
    class EDITransaction {
        /** @param {{[key:string]: any}} params Constructor params */
        constructor(params){
            /** @type {CustomErrorObject} */
            this.customError = params.customError;
            /** @type {number} */
            this.index = params.index;
            /** @type {number} */
            this.id = params.id;
            /** @type {record.Type} */
            this.type = params.type;
            /** @type {string} */
            this.documentNumber = params.tranid || "";
            /** @type {number} */
            this.subtotal = 0;
            /** @type {string} */
            this.departmentNumber = params.departmentNumber || "";
            /** @type {number} */
            this.createdfrom = params.createdfrom || 0;
            /** @type {Date} */
            this.transactionDate = params.trandate 
                ? new Date(format.parse({ type: format.Type.DATE, value: params.trandate }))
                : new Date();
            /** @type {string} */
            this.transactionDateFormatted = this.formatToYYYYMMDD(this.transactionDate);
            /** @type {EDIAddress} */
            this.address = {
                street: params.street || "",
                city: params.city || "",
                state: params.state || "",
                zip: params.zip || ""
            };
            /** @type {number} */
            this.controlNumber = params.controlNumber;
            /** @type {string} */
            this.supplierName = params.supplierName || "";
            /** @type {EDIAddress} */
            this.supplierAddress = {
                street: params.supplierStreet || "",
                city: params.supplierCity || "",
                state: params.supplierState || "",
                zip: params.supplierZip || ""
            }
        }

        /**
         * @param {Date} date Date to format
         */
        formatToYYYYMMDD(date) {
            const year = date.getFullYear();
            const month = (date.getMonth() + 1).toString().padStart(2, '0');
            const day = date.getDate().toString().padStart(2, '0');

            return `${year}${month}${day}`;
        }
    }

    exports.EDITransaction = EDITransaction;
});

