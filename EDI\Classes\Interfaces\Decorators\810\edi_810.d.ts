/**
 * @description EDI 810 Decorator Types and Interface
 *
 * <AUTHOR> <<EMAIL>>
 */

import {EDIFileInterface} from "../../Models/File/edi_file";
import {EDIIncomingInterface} from "../../Models/File/edi_incoming";
import {EDIOutgoingInterface} from "../../Models/File/edi_outgoing";

export interface EDI810Interface extends EDIFileInterface, EDIIncomingInterface, EDIOutgoingInterface {}

export type EDI810ParsedInvoice = {
    /** Transaction Control Number */
    transactionControlNumber: string;
    /** Invoice Number */
    invoiceNumber: string;
    /** Purchase Order linked to the Invoice */
    poNumber: string;
    /** Total Amount */
    total: number;
    /** Shipping Amount */
    shippingAmount: number;
    /** Tax Amount */
    taxAmount: number;
    /** Date String */
    date: string;
    /** Item IDs attached to the Invoice */
    items: number[];
}