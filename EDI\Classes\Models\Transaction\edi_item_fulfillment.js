/**
 * @description Represent an EDI Item Fulfillment, which is saved as an Item Fulfillment in NetSuite
 *
 * @NApiVersion 2.1
 * <AUTHOR> <<EMAIL>>
 */

define([
    "require",
    "exports",
    "N/log",
    "N/record",
    "./edi_sales_transaction"
], function (/** @type {any} */ require, /** @type {any} */ exports) {
    const log = require("N/log");
    const record = require("N/record");
    const { EDISalesTransaction } = require("./edi_sales_transaction");

    /**
     * EDI ItemFulfillment Class
     *
     * @extends {EDISalesTransaction}
     * @class
     */
    class EDIItemFulfillment extends EDISalesTransaction {
        /** @param {{[key:string]: any}} params Constructor params */
        constructor(params){
            super(params);
            /** @type {string} */
            this.shipCarrier = params.shipCarrier;
            /** @type {number} */
            this.ladingQuantity = params.ladingQuantity;
            /** @type {number} */
            this.shipWeight = params.shipWeight;
            /** @type {string} */
            this.trackingInformation = params.trackingInformation;
            /** @type {string[]} */
            this.trackingNumbers = this.trackingInformation
                ? [this.trackingInformation]
                : params.trackingNumbers
                    ? params.trackingNumbers.split(",").map((/** @type {string} */ x) => x.trim())
                    : [];
            /** @type {{quantity: number, units: string, name: string, description: string, po1LineNumber: number}[]} */
            this.lineItems = [];
        }

        /**
         * Sets the instance amount and line items properties
         *
         * @returns {void}
         */
        getLineItems() {
            try {
                const transaction = record.load({
                    type: this.type,
                    id: this.id
                });

                this.subtotal = Number(transaction.getValue({ fieldId: "subtotal" }));
                const itemFulfillmentLineCount = transaction.getLineCount({ sublistId: "item" });

                /** @type {{[key:string]: any}} */
                const itemFulfillmentLookup = {};
                for (let i = 0; i < itemFulfillmentLineCount; i++) {
                    const itemId = transaction.getSublistValue({
                        sublistId: "item",
                        fieldId: "item",
                        line: i,
                    }) || 0;
                    const quantity = transaction.getSublistValue({
                        sublistId: "item",
                        fieldId: "quantity",
                        line: i,
                    });
                    itemFulfillmentLookup[itemId.toString()] = { quantity };
                }

                const salesOrder = record.load({
                    type: record.Type.SALES_ORDER,
                    id: this.createdfrom,
                });
                const salesOrderLineCount = salesOrder.getLineCount({ sublistId: "item" });

                let transactionLineCount = 0;
                for (let i = 0; i < salesOrderLineCount; i++) {
                    const itemId = salesOrder.getSublistValue({
                        sublistId: "item",
                        fieldId: "item",
                        line: i,
                    }) || 0;

                    if (!itemFulfillmentLookup[itemId.toString()]) {
                        continue;
                    }

                    const quantity = Number(itemFulfillmentLookup[itemId.toString()].quantity);
                    const units = salesOrder.getSublistText({
                        sublistId: "item",
                        fieldId: "units",
                        line: i,
                    });
                    const name = salesOrder.getSublistText({
                        sublistId: "item",
                        fieldId: "item",
                        line: i,
                    });
                    const description = salesOrder.getSublistText({
                        sublistId: "item",
                        fieldId: "description",
                        line: i,
                    });
                    const po1LineNumber = Number(salesOrder.getSublistValue({
                        sublistId: "item",
                        fieldId: "custcol_spl_hb_po1_line_number",
                        line: i,
                    }) || 1);
                    transactionLineCount++;
                    this.lineItems.push({ quantity, units, name, description, po1LineNumber });
                }
                this.lineCount = transactionLineCount;
            } catch (/** @type {any} */err) {
                log.error("EDI Item Fulfillment (getLineItems)", err);
                throw this.customError.updateError({
                    errorType: this.customError.ErrorTypes.NO_VALUE_RETURNED,
                    summary: "NO_ITEMS_RETRIEVED",
                    details: `Error getting line items from the transaction: ${err}`,
                });
            } 
        }
    }    

    exports.EDIItemFulfillment = EDIItemFulfillment;
});
