/**
 * Deprecated since absorbed into brdg_rip_create_records_mr script.
 * Create Bill Credit for new Vendor Bills that failed to get processed after approval
 *
 * @NApiVersion 2.1
 * @NScriptType MapReduceScript
 * @NAmdConfig /SuiteScripts/config.json
 * 
 * <AUTHOR>
 * @module brdg_rip_create_bill_credit_mr
 */

define([
	"N/log",
	"N/record",
	"N/search",
	"N/runtime",
	"N/query",
	"CreateRipBillCreditLib"
], (
	log,
	record,
	search,
	runtime,
	query,
	createRipBillCreditLib
) => {

	/**
	 * Get input stage of the Map/Reduce script
	 * Either return Bills created today
	 * or retrieved via user saved search
	 *
	 * @param {import("@hitc/netsuite-types/N/types").EntryPoints.MapReduce.getInputDataContext} context Get input data context
	 * @returns {object[]|undefined}
	 */
	function getInputData(context) {
		try {
			const vendorBillIdsArr = [];
			const currentScript = runtime.getCurrentScript();
			const vendorBillSavedSearchId = currentScript.getParameter({
				name: "custscript_brdg_vendor_bill_saved_search"
			});

			if (vendorBillSavedSearchId) {
				const vendorBillSavedSearch = search.load({
					id: vendorBillSavedSearchId,
					type: search.Type.TRANSACTION
				});
				const pagedData = vendorBillSavedSearch.runPaged({
					pageSize: 1000
				});
				pagedData.pageRanges.forEach((pageRange) => {
					const pageResults = pagedData.fetch({
						index: pageRange.index
					});
	
					pageResults.data.forEach((result) => {
						vendorBillIdsArr.push({
							id: result.id
						});
					});
				});
			} else {
				query.runSuiteQLPaged({
					query: createRipBillCreditLib.getBillsToProcessQuery(),
					pageSize: 1000
				}).iterator().each((page) => {
					page.value.data.results.forEach((result) => {
						vendorBillIdsArr.push({
							id: result.values[0]
						});
					});
					return true;
				});	
			}

			return vendorBillIdsArr;
		} catch(error) {
			log.error("Get Input Data error", error);
		}
	}

	/**
	 * Map stage of the Map/Reduce script
	 * Load-save the Vendor Bill to trigger the UE that creates the Bill Credit
	 *
	 * @param {import("@hitc/netsuite-types/N/types").EntryPoints.MapReduce.mapContext} context Map context
	 * @returns {void}
	 */
	function map(context) {
		try {
			const vendorBill = JSON.parse(context.value);

			if (vendorBill && vendorBill.id) {
				record.load({
					type: record.Type.VENDOR_BILL,
					id: vendorBill.id
				}).save();
			}
		} catch(error) {
			log.error("Map error", error);
		}
	}

	/**
	 * The summarize stage of the Map/Reduce script.
	 *
	 * @param {import("@hitc/netsuite-types/N/types").EntryPoints.MapReduce.summarizeContext} summary Summarize context
	 * @returns {void}
	 */
	function summarize(summary) {
		const mapKeysArr = [];
		summary.mapSummary.keys.iterator().each(function (key){
			mapKeysArr.push(key);
			return true;
		});
		log.audit("Finished processing Bills", mapKeysArr.length);
	}

	return {
		getInputData,
		map,
		summarize
	};
});