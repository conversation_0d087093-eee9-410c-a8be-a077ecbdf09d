/**
 * 
 * @description Runs when a RIP vendor credit is deleted - to clear out any sales lines that had this credit applied to it
 * 
 * @NApiVersion 2.1
 * @NScriptType UserEventScript
 * 
 * <AUTHOR>
 * @module brdg_rip_deleted_bill_credit_ue
 */
define([
  "require",
  "N/log",
  "N/query",
  "N/task",
], (/** @type {any} */ require) => {
  const log = require("N/log");
  const query = require("N/query");
  const task = require("N/task");

  function afterSubmit(context) {
    if (context.type === context.UserEventType.DELETE) {
      try{
      const billCreditRec = context.newRecord;

      // Query to find linked transactions
      const linkedTransactionsQuery = `
      SELECT DISTINCT
        tl.transaction AS transactionId,
        BUILTIN.DF(tl.transaction) AS transactionName,
        t.type AS recordType,
        tl.item AS itemId,
        tl.id AS lineId 
      FROM
        transactionline tl 
        JOIN
          transaction t 
          ON t.id = tl.transaction 
      WHERE
        tl.custcol_item_rip_bill_credit_applied = ?
      `;

      const results = query
        .runSuiteQL({
          query: linkedTransactionsQuery,
          params: [billCreditRec.id],
        })
        .asMappedResults();

      if (results.length > 0) {
        const mrTask = task.create({
          taskType: task.TaskType.MAP_REDUCE,
          scriptId: "customscript_brdg_remove_bc_links_mr",
          deploymentId: "customdeploy_brdg_remove_bc_links_mr",
          params: {
            custscript_transactions_to_update: JSON.stringify(results),
          },
        });

        mrTask.submit();
      }
    }
    catch(e){
      log.error("Error in finding related transactions!", e);
    }
    }
  }

  return {
    afterSubmit,
  };
});
