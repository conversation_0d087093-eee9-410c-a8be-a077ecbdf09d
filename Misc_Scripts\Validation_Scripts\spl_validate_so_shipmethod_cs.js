/**
 * @NApiVersion 2.x
 * @NScriptType ClientScript
 */

//@ts-ignore
define([], function () {
    function saveRecord(context) {
        var salesOrder = context.currentRecord;
        var subsidiary = salesOrder.getValue("subsidiary");
			if (subsidiary != 1) {
				//Supplyline
				return true;
			}
            
        var ismultishipto = salesOrder.getValue('ismultishipto');
        if (!ismultishipto) {
            var shipMethod = salesOrder.getValue('shipmethod');
            if (!shipMethod) {
                alert('Please choose a ship method.');
                return false;
            }
        } else {
            var salesOrderLineCount = salesOrder.getLineCount({ sublistId: 'item' });
            var itemsMissingShipping = [];
            for (var x = 0; x < salesOrderLineCount; x++) {

                var itemType = salesOrder.getSublistValue({
                    sublistId: 'item',
                    fieldId: 'itemtype',
                    line: x
                });

                var shipMethod = salesOrder.getSublistValue({
                    sublistId: 'item',
                    fieldId: 'shipmethod',
                    line: x
                });

                if (!shipMethod && itemType != 'Markup' && itemType != 'Subtotal') {
                    var itemName = salesOrder.getSublistText({
                        sublistId: 'item',
                        fieldId: 'item',
                        line: x
                    });

                    itemsMissingShipping.push(itemName);
                } else {
                    console.log("Didn't hit error log");
                }
            }

            if (itemsMissingShipping.length > 0) {
                console.log('Items Missing Shipping ' + itemsMissingShipping);
                var itemsNames = '';
                itemsMissingShipping.forEach(function (name) {
                    itemsNames += name + ', ';
                });
                alert('No ship method for these item(s): ' + itemsNames + '. Please add and save again.');
                return false;
            }
            return true;
        };
        return true;
    }

    return {
        saveRecord: saveRecord
    };
});