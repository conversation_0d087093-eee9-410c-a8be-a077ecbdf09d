/******************************************************************************************************
	Script Name - AVA_SUT_SetupAssistant.js
	Company -     Avalara Technologies Pvt Ltd.
******************************************************************************************************/

/**
 *@NApiVersion 2.0
 *@NScriptType Suitelet
*/

define(['N/ui/serverWidget', 'N/search', 'N/runtime', 'N/redirect', 'N/record'],
	function(ui, search, runtime, redirect, record){
		function onRequest(context){
			if(context.request.method === 'GET'){
				var form = ui.createForm({
					title: 'Setup Assistant'
				});
				form.clientScriptModulePath = './AVA_CLI_Config.js';
				
				var redirectToConfigPage = context.request.parameters.custparam_value; // on edit  credential button
				
				var inlineHTML = form.addField({
					id:'ava_click_to_authorise_inline',
					type:'inlinehtml',
					label:'inlineHTML'
				});
				
				var userCodeField = form.addField({
					id: 'ava_usercode',
					label: 'User Code',
					type: ui.FieldType.TEXT
				});
				userCodeField.updateDisplayType({
					displayType: ui.FieldDisplayType.DISABLED
				});
				userCodeField.updateDisplaySize({
					width: 40,
					height: 0
				});
				userCodeField.setHelpText('Please contact your administrator.');
				
				var deviceCodeField = form.addField({
					id: 'ava_devicecode',
					label: 'Device Code',
					type: ui.FieldType.TEXT
				});
				deviceCodeField.updateDisplayType({
					displayType: ui.FieldDisplayType.HIDDEN
				});
				
				var verificationLink = form.addField({
					id: 'ava_verificationlink',
					label: 'Verification Link',
					type: ui.FieldType.TEXT
				});
				verificationLink.updateDisplayType({
					displayType: ui.FieldDisplayType.HIDDEN
				});
				
				var additionalInfo2 = form.addField({
					id: 'ava_addtionalinfo2',
					label: 'Additional Info2',
					type: ui.FieldType.PASSWORD
				});
				additionalInfo2.maxLength = 200;
				additionalInfo2.updateDisplayType({
					displayType: ui.FieldDisplayType.HIDDEN
				});
				
				var serviceUrl = form.addField({
					id: 'ava_serviceurl',
					label: 'Service URL',
					type: ui.FieldType.SELECT
				});
				serviceUrl.addSelectOption({
					value : '0',
					text: 'Production'
				});
				serviceUrl.addSelectOption({
					value : '1',
					text: 'Development'
				});
				serviceUrl.setHelpText("Select the type of AvaTax account you're connecting to in AvaTax.<br><br> Select 'Development' if you are in NetSuite Sandbox or Test environment.<br><br> Select 'Production' if you are in NetSuite Live / Production environment.");
				
				if(runtime.envType != runtime.EnvType.PRODUCTION){
					serviceUrl.defaultValue = '1';
				}
				
				var setupConfigFlag = form.addField({
					id: 'ava_setupconfig',
					label: 'Setup/Config Flag',
					type: ui.FieldType.TEXT
				});
				setupConfigFlag.updateDisplayType({
					displayType: ui.FieldDisplayType.HIDDEN
				});
				setupConfigFlag.defaultValue = 'F';
				
				var serviceTypes = form.addField({
					id: 'ava_servicetypes',
					label: 'Service Types',
					type: ui.FieldType.TEXT
				});
				serviceTypes.updateDisplayType({
					displayType: ui.FieldDisplayType.HIDDEN
				});
				
				var helpLink = form.addField({
					id: 'ava_helplink',
					label: '<a href= "https://knowledge.avalara.com/bundle/chz1662726137134_chz1662726137134/page/Establish_a_connection_between_NetSuite_and_AvaTax.html" target="_blank" style="color:blue; font-size:13px">Help</a>',
					type: ui.FieldType.HELP
				});
				helpLink.updateLayoutType({
					layoutType: ui.FieldLayoutType.OUTSIDEBELOW
				});
				helpLink.updateBreakType({
					breakType: ui.FieldBreakType.STARTROW
				});
				
				var searchRecord = search.create({
					type: 'customrecord_avaconfig',
					columns: ['custrecord_ava_url', 'custrecord_ava_configflag', 'custrecord_ava_additionalinfo3']
				});
				var searchresult = searchRecord.run();
				searchresult = searchresult.getRange({
					start: 0,
					end: 5
				});
				
				if(searchresult != null && searchresult.length > 0){
					var key = searchresult[0].getValue('custrecord_ava_additionalinfo3');
					serviceUrl.defaultValue = searchresult[0].getValue('custrecord_ava_url');

					if(key != null && key.length > 0 && searchresult[0].getValue('custrecord_ava_configflag') == true){
						if(!redirectToConfigPage){
							redirect.toSuitelet({
								scriptId: 'customscript_avaconfig_suitlet',
								deploymentId: 'customdeploy_configuration'
							});
						}
						else{
							form.title = 'Avalara Configuration';
							setupConfigFlag.defaultValue = 'T';
						}
					}
				}
				
				form.addButton({
					id: 'ava_identity_call',
					label: 'Add AvaTax User Code',
					functionName: 'avalaraIdentityCall'
				});
				
				var authButton = form.addButton({
					id: 'ava_click_to_authorise',
					label: 'Authenticate AvaTax',
					functionName: 'pollToken'
				});
				
				inlineHTML.defaultValue = '<html><body><script>document.getElementById("tdbody_ava_click_to_authorise").style.display = "none";</script></body></html>';
				
				context.response.writePage({
					pageObject: form
				});
			}
		}
		
		return{
			onRequest: onRequest
		};
	}
);
