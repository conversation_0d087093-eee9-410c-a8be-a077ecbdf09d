/**
 * @NApiVersion 2.1
 */

//@ts-ignore
define(["N/record", "N/log"], function (record, log) {
	function setSalesOrderCorrectionStatus(salesOrderId, processingLog) {
		var setCorrectionErrors = [];

		try {
			var salesOrder = record.load({
				type: record.Type.SALES_ORDER,
				id: salesOrderId,
			});

			salesOrder.setValue(
				"custbody_spl_so_correction_status",
				processingLog.length <= 0 ? 1 : 2
			);
			salesOrder.save();
		} catch (e) {
			log.error("setSalesOrderCorrectionStatus", e.name + ": " + e.message);
			setCorrectionErrors.push(`Errors setting correction status ${e.message}`);
		}

		return setCorrectionErrors;
	}

	function createEdiNoteOnSalesOrder(salesOrderId, processingLog) {
		var createNoteErrors = [];

		if (processingLog.length > 0) {
			try {
				var note = record.create({
					type: record.Type.NOTE,
				});
				note.setValue("notetype", 9); //EDI Processing Log
				note.setValue("transaction", salesOrderId);
				var text = "";
				processingLog.forEach(
					(logValue) => (text += logValue.logMessage + "\n\n")
				);
				note.setValue("note", text);
				note.setValue("title", "EDI Transaction Errors");
				note.setValue("folder", 6511263); // EDI Files

				note.save();
			} catch (e) {
				log.error("createEdiNoteOnSalesOrder", e.name + ": " + e.message);
				createNoteErrors.push(`Error creating correction note ${e}`);
			}
		}

		return createNoteErrors;
	}

	return {
		setSalesOrderCorrectionStatus,
		createEdiNoteOnSalesOrder,
	};
});
