/**
 * @NApiVersion 2.1
 */

//Summary: A lib used to process an incoming ORM file and send an order email to the Valmar team
//Called by: VLMR/HCHB Incoming 850 script

//@ts-ignore
define(["N/error", "N/log", "N/email", "MoveFileLib", "Moment"], function (
	error,
	log,
	email,
	moveFileLib,
	moment
) {
	function processIncomingOrm(dataObj, fileName, ormObj) {
		const helperFunctions = (function () {
			function _processFail(logMessage) {
				programmingErrors.push({
					logMessage: logMessage,
				});

				throw logMessage;
			}

			function createHchbOrderEmail(ormObj) {
				const emailSubject = `New Revival Order For ${ormObj.patient.name.firstName} ${ormObj.patient.name.lastName}`;
				const emailAuthor = 223546; //Revival Orders
				const emailRecepient = "<EMAIL>";
				const emailCc = [];
				let emailBodyText = `PO# : ${ormObj.transactionControlNumber}

				Customer Info:
				First Name : ${ormObj.patient.name.firstName} 
				Last Name: ${ormObj.patient.name.lastName}
				DOB: ${moment(ormObj.patient.dob).format("MM/DD/YYYY")}
				Phone Number: ${ormObj.patient.phoneNumber}
				Gender: ${ormObj.patient.gender}
				Language: ${ormObj.patient.language}
				Revival ID : ${ormObj.patient.revivalId}
				Ordering Rep : ${ormObj.orderDetails.orderingRep}
				
				Address: 
				${ormObj.patient.address.addressee}
				${ormObj.patient.address.streetAddress}
				${ormObj.patient.address.city}, ${ormObj.patient.address.state}	${
					ormObj.patient.address.zip
				}

				Transaction Info:
				Transaction Control Number: ${ormObj.transactionControlNumber}
				EDI File Name: ${fileName}

				Items:

				`;

				ormObj.items.forEach((item) => {
					emailBodyText += `	Item : ${item.itemName}
					Description : ${item.itemDescription}
					Quantity : ${item.quantity}

				`;
				});

				return {
					emailSubject,
					emailAuthor,
					emailRecepient,
					emailCc,
					emailBodyText,
				};
			}

			function sendHchbOrderEmail(hchbOrderEmail) {
				const {
					emailSubject,
					emailAuthor,
					emailRecepient,
					emailCc,
					emailBodyText,
				} = hchbOrderEmail;

				email.send({
					subject: emailSubject,
					author: emailAuthor,
					recipients: emailRecepient,
					cc: emailCc,
					body: emailBodyText,
				});
			}

			function moveFile() {
				try {
					const sbDirectory = `/users/Valmar/HCHB/TEST/IN/Test_Reference`;
					const prodDirectory = `/EDI Reference Files/Valmar/HCHB/IN/ORM`;

					dataObj.referenceDirectory = prodDirectory;
					try {
						var moveErrorLog = moveFileLib.moveFile(
							dataObj,
							fileName,
							programmingErrors.length <= 0
						);
					} catch (e) {
						throw e;
					}
					if (moveErrorLog.length > 0) {
						_processFail(`${fileName} not moved. Error: ${moveErrorLog}`);
					}
				} catch (e) {
					throw error.create({
						name: "Error Moving File",
						message: e,
					});
				}
			}

			function sendErrorEmail(programmingErrors, ormObj, fileName) {
				try {
					let emailBodyText = `
					${programmingErrors.join("\n")}\n
					FILE: ${fileName}\n
					ORM OBJECT: ${JSON.stringify(ormObj)}\n\n
					`;
					email.send({
						subject: `Error processing new order from Revival`,
						author: 223546, //Revival Orders
						recipients: "<EMAIL>",
						cc: [],
						body: emailBodyText,
					});
				} catch (e) {
					log.error("Error Processing Email", e);

					throw error.create({
						message: e,
						name: "Error Processing Email",
					});
				}
			}

			return {
				createHchbOrderEmail,
				sendHchbOrderEmail,
				moveFile,
				sendErrorEmail,
			};
		})();

		const programmingErrors = [];

		try {
			const hchbOrderEmail = helperFunctions.createHchbOrderEmail(ormObj);
			helperFunctions.sendHchbOrderEmail(hchbOrderEmail);
			helperFunctions.moveFile();
		} catch (e) {
			programmingErrors.push(`Error: ${e}`);
		}

		if (programmingErrors.length > 0) {
			helperFunctions.sendErrorEmail(programmingErrors, ormObj, fileName);
		}
		return { programmingErrors };
	}
	return {
		processIncomingOrm,
	};
});
