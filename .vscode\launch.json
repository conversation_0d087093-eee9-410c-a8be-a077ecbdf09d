{
	// Use IntelliSense to learn about possible attributes.
	// Hover to view descriptions of existing attributes.
	// For more information, visit: https://go.microsoft.com/fwlink/?linkid=830387
	"version": "0.2.0",
	"configurations": [
		{
			"name": "Launch NetSuite",
			"request": "launch",
			"type": "chrome",
			"url": "https://5802576.app.netsuite.com/app/center/card.nl?sc=-29&whence=",
			"webRoot": "${SuiteScripts}"
		},
		{
			"name": "Launch Sandbox",
			"request": "launch",
			"type": "chrome",
			"url": "https://5802576-sb1.app.netsuite.com/app/center/card.nl?sc=-29&whence=",
			"webRoot": "${SuiteScripts}"
		},
		{
			"type": "node",
			"request": "launch",
			"name": "Launch Program",
			"program": "${workspaceFolder}\\${file}", 
            "args": ["1"], 
            "cwd": "${workspaceFolder}"
		}
	]
}
