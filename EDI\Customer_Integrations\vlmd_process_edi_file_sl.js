/**
 * @NApiVersion 2.1
 * @NScriptType Suitelet
 * @NAmdConfig /SuiteScripts/config.json
 * <AUTHOR>
 * @description SL to allow user to generate a price file for a given parent customer
 * @module vlmd_process_edi_file_sl
 */

define([
	"require",
	"../../Classes/vlmd_custom_error_object",
	"N/ui/serverWidget",
	"N/ui/message",
	"N/url",
	"N/task",
	"N/redirect",
	"N/runtime",
], function (require) {
	const serverWidget = require("N/ui/serverWidget");
	const message = require("N/ui/message");
	const url = require("N/url");
	const task = require("N/task");
	const redirect = require("N/redirect");
	const runtime = require("N/runtime");

	/** @type {import("../../Classes/vlmd_custom_error_object").CustomErrorObject} */

	const CustomErrorObject = require("../../Classes/vlmd_custom_error_object");
	const customErrorObject = new CustomErrorObject();

	var helperFunctions = (function () {
		function checkStatus(taskId) {
			try {
				if (!taskId) {
					throw customErrorObject.updateError({
						errorType: customErrorObject.ErrorTypes.MISSING_VALUE,
						summary: "MISSING_TASK_ID",
						details: `No map reduce task id passed in from post.`,
					});
				}

				return task.checkStatus({
					taskId: taskId,
				}).status;
			} catch (err) {
				throw customErrorObject.updateError({
					errorType: customErrorObject.ErrorTypes.NO_VALUE_RETURNED,
					summary: "ERROR_GETTING_TASK_STATUS",
					details: `Task status not gotten successfully: ${err}.`,
				});
			}
		}

		function displayStatusMessage(taskStatus, form) {
			try {
				let messageType = "";
				let titleText = "";
				let messageText = "";
				let durationTime;

				switch (taskStatus) {
					case "PENDING":
						messageType = "INFORMATION";
						titleText = "Pending";
						messageText = `Your request has been submitted and is pending processing. Refresh the page to see updated status.`;
						durationTime = 100000;
						break;
					case "PROCESSING":
						messageType = "INFORMATION";
						titleText = "Processing";
						messageText = `Your request is being processed. Refresh the page to see updated status.`;
						durationTime = 100000;
						break;
					case "COMPLETE":
						messageType = "CONFIRMATION";
						titleText = "Complete";
						messageText = `Your task is complete!`;
						durationTime = 5000;
						break;
					default:
						messageType = "ERROR";
						titleText = "";
						messageText = `Error displaying task status. Please refresh the page`;
				}

				form.addPageInitMessage({
					type: message.Type[messageType],
					title: titleText,
					message: messageText,
					duration: durationTime,
				});
			} catch (err) {
				throw customErrorObject.updateError({
					errorType: customErrorObject.ErrorTypes.UNHANDLED_ERROR,
					summary: "ERROR_DISPLAYING_STATUS_MESSAGE",
					details: `Error displaying task status message: ${err}`,
				});
			}
		}

		function addParametersFieldGroup(form) {
			try {
				//#region send via
				form.addFieldGroup({
					id: "send_via",
					label: "Send Via",
				}).isBorderHidden = true;

				const helpField = form.addField({
					id: "custpage_send_via_help",
					label: "Help Label ",
					type: serverWidget.FieldType.INLINEHTML,
					container: "send_via",
				});
				helpField.defaultValue = "Select the method(s) to send the file.";
				helpField.updateBreakType({
					breakType: serverWidget.FieldBreakType.STARTCOL,
				});

				const sendViaEdi = form.addField({
					id: "custpage_send_via_edi",
					label: `Send via EDI`,
					type: serverWidget.FieldType.CHECKBOX,
					container: "send_via",
				});
				sendViaEdi.defaultValue = "T";

				const emailToUser = form.addField({
					id: "custpage_email_to_user",
					label: `Email File to Me`,
					type: serverWidget.FieldType.CHECKBOX,
					container: "send_via",
				});
				emailToUser.defaultValue = "F";

				//#endregion

				//#region Price Catalog
				form.addTab({ id: "custpage_master_catalog", label: "Master Catalog" });

				form.addField({
					id: "custpage_master_catalog_purchasing_software",
					label: `Purchasing Software`,
					type: serverWidget.FieldType.SELECT,
					source: "customrecord_vlmd_purchasing_software",
					container: "custpage_master_catalog",
				});

				form.addTab({
					id: "custpage_price_catalog",
					label: "Coming Soon - Price Catalog",
				});

				form
					.addField({
						id: "custpage_parent_customer",
						label: `Parent Customer`,
						type: serverWidget.FieldType.SELECT,
						source: "customer",
						container: "custpage_price_catalog",
					})
					.updateLayoutType({
						layoutType: serverWidget.FieldLayoutType.ENDROW,
					});
				//#endregion

				form.addSubmitButton({
					label: "Generate File",
				});

				form.addResetButton();
			} catch (err) {
				throw customErrorObject.updateError({
					errorType: customErrorObject.ErrorTypes.VALUE_NOT_SET,
					summary: "PARAMETER_FIELDS_NOT_SET",
					details: `Errors setting details when hit GET via link: ${err}`,
				});
			}
		}

		function createMapReduceTask(paramsObj) {
			try {
				const { sendViaEdi, emailToUser, masterCatalogPurchasingSoftwareId } =
					paramsObj;

				if (!masterCatalogPurchasingSoftwareId) {
					throw customErrorObject.updateError({
						errorType: customErrorObject.ErrorTypes.MISSING_PARAM,
						summary: "MISSING_PURCHASING_SOFTWARE",
						details: `No purchasing software id param was passed in.`,
					});
				}
				const documentType = "masterCatalog";

				var userId = runtime.getCurrentUser().id;

				var mapReduceTask = task.create({
					taskType: task.TaskType.MAP_REDUCE,
				});

				const scriptsDirectory = {
					masterCatalog: {
						scriptId: "customscript_edi_832_out_item_catalog",
						deploymentId: "customdeploy_edi_832_out_item_mr_od",
						params: {
							custscript_832_out_software:
								masterCatalogPurchasingSoftwareId,
							custscript_832_out_is_emailing: emailToUser,
							custscript_832_out_is_submitting: sendViaEdi,
							custscript_832_out_is_prod: "T",
						},
					},
				};

				mapReduceTask.scriptId = scriptsDirectory[documentType].scriptId;
				mapReduceTask.deploymentId =
					scriptsDirectory[documentType].deploymentId;
				mapReduceTask.params = scriptsDirectory[documentType].params;

				return mapReduceTask.submit();
			} catch (err) {
				throw customErrorObject.updateError({
					errorType: customErrorObject.ErrorTypes.UNHANDLED_ERROR,
					summary: "CREATE_MR_TASK_ERROR",
					details: `Error in create MR task function: ${err}`,
				});
			}
		}

		function redirectToSuitelet(paramsObj) {
			try {
				var suiteletURL = url.resolveScript({
					scriptId: "customscript_edi_process_file_sl",
					deploymentId: "customdeploy_edi_process_file_sl",
					params: {
						redirected_from_sl: paramsObj.redirectedFromSl,
						task_id: paramsObj.taskId,
					},
				});

				redirect.redirect({ url: suiteletURL });
			} catch (err) {
				throw customErrorObject.updateError({
					errorType: customErrorObject.ErrorTypes.UNHANDLED_ERROR,
					summary: "ERROR_REDIRECTING_TO_SL",
					details: `Error redirecting to SL: ${err}`,
				});
			}
		}

		return {
			checkStatus,
			displayStatusMessage,
			addParametersFieldGroup,
			createMapReduceTask,
			redirectToSuitelet,
		};
	})();

	return {
		onRequest: function (context) {
			try {
				if (context.request.method === "GET") {
					const form = serverWidget.createForm({
						title: "Generate EDI File",
					});

					const redirectedFromSl =
						context.request.parameters["redirected_from_sl"];

					if (redirectedFromSl && redirectedFromSl !== "false") {
						const taskStatus = helperFunctions.checkStatus(
							context.request.parameters["task_id"]
						);

						helperFunctions.displayStatusMessage(taskStatus, form);
					}

					helperFunctions.addParametersFieldGroup(form);
					context.response.writePage(form);
				} else {
					//POST Request
					const taskId = helperFunctions.createMapReduceTask({
						sendViaEdi: context.request.parameters.custpage_send_via_edi,
						emailToUser: context.request.parameters.custpage_email_to_user,
						masterCatalogPurchasingSoftwareId:
							context.request.parameters
								.custpage_master_catalog_purchasing_software,
					});

					helperFunctions.redirectToSuitelet({
						taskId,
						redirectedFromSl: true,
					});

					return;
				}
			} catch (err) {
				customErrorObject.throwError({
					summaryText: `ERROR_IN_SL`,
					error: err,
				});
			}
		},
	};
});
