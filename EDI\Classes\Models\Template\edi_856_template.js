/**
 * @description EDI class for the 856 template
 *
 * @NApiVersion 2.1
 * <AUTHOR> <<EMAIL>>
 */

define([
    "exports",
    "require",
    "./edi_template"
], (/** @type {any} */ exports, /** @type {any} */ require) => {
    const { EDITemplate } = require("./edi_template");

    /**
     * EDI 856 Template Class
     * 
     * @class
     * @typedef {import("../../Interfaces/Models/Template/edi_template").EDITemplateParams} EDITemplateParams
     */
    class EDI856Template extends EDITemplate {
        /** @param {EDITemplateParams} params */
        constructor(params){
            super(params);
        };

        /**
         * ISA Segment
         * Pad the Authorization and Security Information with spaces to match the segment length requirement
         *
         * @returns {string}
         */
        createISASegment() {
            return `ISA` +
                `${this.fd}00` +
                `${this.fd}          ` +
                `${this.fd}00` +
                `${this.fd}          ` +
                `${this.fd}SenderQualifier` +
                `${this.fd}ISASenderId` +
                `${this.fd}ReceiverQualifier` +
                `${this.fd}ISAReceiverId` +
                `${this.fd}ISADate` +
                `${this.fd}Time` +
                `${this.fd}U` +
                `${this.fd}EdiVersion` +
                `${this.fd}ControlNumber` +
                `${this.fd}0` +
                `${this.fd}P` +
                `${this.fd}>${this.sd}\n`;
        }

        /**
         * GS Segment
         * Date and Time are calculated based on the partner
         *
         * @returns {string}
         */
        createGSSegment() {
            return `GS` +
                `${this.fd}SH` +
                `${this.fd}GSSenderId` +
                `${this.fd}GSReceiverId` +
                `${this.fd}GSDate` +
                `${this.fd}Time` +
                `${this.fd}ControlNumber` +
                `${this.fd}X` +
                `${this.fd}EdiVersion0` +
                `${this.sd}\n`;
        }

        /**
         * ST Segment
         * Transaction Set Header
         * Control number assigned is the same as the one in SE
         *
         * @returns {string}
         */
        createSTSegment() {
            return `ST` +
                `${this.fd}856` +
                `${this.fd}0001` +
                `${this.sd}\n`;
        }

        /**
         * BSN Segment
         * Beginning segment for Invoice
         *
         * @returns {string}
         */
        createBSNSegment() {
            return `BSN` +
                `${this.fd}00` +
                `${this.fd}DocumentNumber` +
                `${this.fd}GSDate` +
                `${this.fd}Time` +
                `${this.sd}\n`;
        }

        /**
         * HL1 Segment
         * Hierarchical Level - Order
         *
         * @returns {string}
         */
        createHL1Segment() {
            return `HL` +
                `${this.fd}1` +
                `${this.fd}` +
                `${this.fd}S` +
                `${this.sd}\n`;
        }

        /**
         * TD1 Segment
         * Carrier Details - Quantity and Weight
         * Default UOM is pounds (LB)
         *
         * @returns {string}
         */
        createTD1Segment() {
            return `TD1` +
                `${this.fd}CAS` +
                `${this.fd}LadingQuantity` +
                `${this.fd}` +
                `${this.fd}` +
                `${this.fd}` +
                `${this.fd}G` +
                `${this.fd}ShipWeight` +
                `${this.fd}LB` +
                `${this.sd}\n`;
        }

        /**
         * TD5 Segment
         * Carrier Details - Routing Sequence/Transit Time
         *
         * @returns {string}
         */
        createTD5Segment() {
            return `TD5` +
                `${this.fd}` +
                `${this.fd}2` +
                `${this.fd}ShipCarrier` +
                `${this.fd}M` +
                `${this.sd}\n`;
        }

        /**
         * DTM Segment
         * Date & Time Reference
         *
         * @returns {string}
         */
        createDTMSegment() {
            return `DTM` +
                `${this.fd}011` +
                `${this.fd}DocumentDate` +
                `${this.sd}\n`;
        }

        /**
         * FOB Segment
         * Method of Payment is CC (Collect)
         *
         * @returns {string}
         */
        createFOBSegment() {
            return `FOB` +
                `${this.fd}CC` +
                `${this.sd}\n`;
        }

        /**
         * Supplier N1, N3 and N4 Segments
         * Party Identification and Location - Shipment
         *
         * @returns {string}
         */
        createNSupplierSegment() {
            return `N1` +
                `${this.fd}SF` +
                `${this.fd}SupplierName` +
                `${this.sd}\n` +
                `N3` +
                `${this.fd}SupplierStreet` +
                `${this.sd}\n` +
                `N4` +
                `${this.fd}SupplierCity` +
                `${this.fd}SupplierState` +
                `${this.fd}SupplierZip` +
                `${this.sd}\n`;
        }

        /**
         * Customer N1, N3 and N4 Segments
         * Party Identification and Location - Shipment
         *
         * @returns {string}
         */
        createNCustomerSegment() {
            return `N1` +
                `${this.fd}ST` +
                `${this.fd}CustomerName` +
                `${this.fd}UL` +
                `${this.fd}CustomerGLN` +
                `${this.sd}\n` +
                `N3` +
                `${this.fd}CustomerStreet` +
                `${this.sd}\n` +
                `N4` +
                `${this.fd}CustomerCity` +
                `${this.fd}CustomerState` +
                `${this.fd}CustomerZip` +
                `${this.sd}\n`;
        }

        /**
         * HL2 Segment
         * Hierarchical Level - Order
         *
         * @returns {string}
         */
        createHL2Segment() {
            return `HL` +
                `${this.fd}2` +
                `${this.fd}1` +
                `${this.fd}O` +
                `${this.sd}\n`;
        }

        /**
         * PRF Segment
         * Purchase Order Reference - Order
         *
         * @returns {string}
         */
        createPRFSegment() {
            return `PRF` +
                `${this.fd}PoNumber` +
                `${this.fd}GSDate` +
                `${this.sd}\n`;
        }

        /**
         * REF Segment
         * Reference Information - Shipment
         *
         * @returns {string}
         */
        createREFSegment() {
            return `REF` +
                `${this.fd}BM` +
                `${this.fd}TrackingNumber` +
                `${this.sd}\n`;
        }

        /**
         * HL Item Segment
         * Hierarchical Level - Order
         *
         * @returns {string}
         */
        createHLItemSegment() {
            return `HL` +
                `${this.fd}ItemHierarchy` +
                `${this.fd}2` +
                `${this.fd}I` +
                `${this.sd}\n`;
        }

        /**
         * LIN Segment
         * Item Identification
         *
         * @returns {string}
         */
        createLINSegment() {
            return `LIN` +
                `${this.fd}` +
                `${this.fd}VN` +
                `${this.fd}ItemName` +
                `${this.sd}\n`;
        }

        /**
         * SN1 Segment
         * Item Detail - Shipment
         *
         * @returns {string}
         */
        createSN1Segment() {
            return `SN1` +
                `${this.fd}` +
                `${this.fd}Quantity` +
                `${this.fd}UOM` +
                `${this.sd}\n`;
        }

        /**
         * PID Segment
         * Product Description
         *
         * @returns {string}
         */
        createPIDSegment() {
            return `PID` +
                `${this.fd}F` +
                `${this.fd}` +
                `${this.fd}` +
                `${this.fd}` +
                `${this.fd}ItemDescription` +
                `${this.sd}\n`;
        }

        /**
         * CTT Segment
         * Transaction totals
         * Provide the number of IT1 segment count
         *
         * @returns {string}
         */
        createCTTSegment() {
            return `CTT` +
                `${this.fd}LineCount` +
                `${this.sd}\n`;
        }

        /**
         * SE Segment
         * Transaction Set Trailer
         * Control number assigned is the same as the one in ST
         *
         * @returns {string}
         */
        createSESegment() {
            return `SE` +
                `${this.fd}SegmentCount` +
                `${this.fd}0001` +
                `${this.sd}\n`;
        }

        /**
         * GE Segment
         * Functional Group Trailer
         * Number of transaction sets is always 1
         *
         * @returns {string}
         */
        createGESegment() {
            return `GE` +
                `${this.fd}1` +
                `${this.fd}ControlNumber` +
                `${this.sd}\n`;
        }

        /**
         * IEA Segment
         * Interchange control trailer
         * Number of functional groups is always 1
         *
         * @returns {string}
         */
        createIEASegment() {
            return `IEA` +
                `${this.fd}1` +
                `${this.fd}ControlNumber` +
                `${this.sd}\n`;
        }
    }

    exports.EDI856Template = EDI856Template;
});