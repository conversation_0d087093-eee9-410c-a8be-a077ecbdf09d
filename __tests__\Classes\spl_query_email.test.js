// @ts-nocheck
import QueryEmail from "../../Classes/spl_query_email";
import query from "N/query";
import email from "N/email";

beforeEach(() =>  {
    jest.resetModules();
    jest.resetAllMocks();
    jest.clearAllMocks();
});

describe("spl_query_email", () => {

    const mockPage = {
        value: {
            data: {
                results: [
                    {values: [1,2]},
                    {values: ["A","B"]}
                ],
                columns: [
                    {label: "Foo", id: "foo"},
                    {label: "Bar", id: "bar"}
                ]
            }
        }
    };
    const mockRun = () => ({
        iterator: () => ({
            each: (callbackFn) => callbackFn(mockPage)
        })
    })
    let queryEmail;
    let tableHtml;

    beforeEach(() => {
        jest.spyOn(email, "send").mockImplementation(() => {});
    });

    describe("when using an existing workbook", () => {

        const htmlResult = 
            `<p><span>These are the query results from dummyQueryId:</span></p>` +
            `<table style='width: 100%; padding-top: 10px'>` +
                `<tr>` +
                    `<th style="border: 1px solid black; padding: 3px"><strong>Foo<strong></th>` +
                    `<th style="border: 1px solid black; padding: 3px"><strong>Bar<strong></th>` +
                `</tr>` +
                `<tr >`+
                    `<td style="border: 1px solid black; padding: 3px">1</td>` +
                    `<td style="border: 1px solid black; padding: 3px">2</td>` +
                `</tr>` +
                `<tr style="background-color: #ddd">` +
                    `<td style="border: 1px solid black; padding: 3px">A</td>` +
                    `<td style="border: 1px solid black; padding: 3px">B</td>` +
                `</tr>` +
            `</table>`;

        beforeEach(() => {
            jest.spyOn(query, "load").mockImplementation(() => ({
                runPaged: mockRun
            }));
            queryEmail = new QueryEmail({ queryId: "dummyQueryId", threshold: 71 });
        });

        describe("constructor", () => {

            it("saves the workbook ID as query ID", () => {
                expect(queryEmail.queryId).toEqual("dummyQueryId");
            });

            it("saves the threshold", () => {
                expect(queryEmail.threshold).toEqual(71);
            });

            it("loads the query from the workbook", () => {
                expect(query.load).toHaveBeenCalledWith({id: "dummyQueryId"});
            });

            it("has empty initial query result columns", () => {
                expect(queryEmail.queryResult.columns).toEqual([]);
            });

            it("has no queryString", () => {
                expect(queryEmail.queryString).toEqual("");
            });

            it("has no description", () => {
                expect(queryEmail.description).toEqual("");
            });
        })

        describe("generateQueryResults", () => {
    
            beforeEach(() => {
                queryEmail.generateQueryResults();
            });

            it("generates the columns based off of the workbook columns", () => {
                expect(queryEmail.queryResult.columns).toEqual([{label: "Foo"}, {label: "Bar"}]);
            });
    
            it("generates the results", () => {
                expect(queryEmail.queryResult.results).toEqual([
                    {values: [1,2]},
                    {values: ["A","B"]}
                ]);
            });

        });

        describe("buildHtml", () => {
            
            beforeEach(() => {
                queryEmail.generateQueryResults();
                tableHtml = queryEmail.buildResultHtml();
            });

            it("generates the table HTML containing the results", () => {
                expect(tableHtml).toEqual(htmlResult);
            })

        });

        describe("sendResults", () => {
            beforeEach(() => {
                queryEmail.generateQueryResults();
                tableHtml = queryEmail.buildResultHtml();
                queryEmail.sendResults({recipients: [1,2,3], subject: "Dummy Subject"});
            });

            it("sends results via email", () => {
                expect(email.send).toHaveBeenCalledWith({
                    author: 3288,
                    body: htmlResult,
                    recipients: [1,2,3],
                    subject: "Dummy Subject",
                    attachments: null,
                    author: 223244,
                    body: "Please see attached file."
                });
            })

        });

    });

    describe("when using a SuiteQL string", () => {

        const htmlResult = 
            `<p><span>dummy description:</span></p>` +
            `<table style='width: 100%; padding-top: 10px'>` +
                `<tr>` +
                    `<th style="border: 1px solid black; padding: 3px"><strong>foo<strong></th>` +
                    `<th style="border: 1px solid black; padding: 3px"><strong>bar<strong></th>` +
                `</tr>` +
                `<tr >`+
                    `<td style="border: 1px solid black; padding: 3px">1</td>` +
                    `<td style="border: 1px solid black; padding: 3px">2</td>` +
                `</tr>` +
                `<tr style="background-color: #ddd">` +
                    `<td style="border: 1px solid black; padding: 3px">A</td>` +
                    `<td style="border: 1px solid black; padding: 3px">B</td>` +
                `</tr>` +
            `</table>`;

        beforeEach(() => {
            jest.spyOn(query, "load");
            jest.spyOn(query, "runSuiteQLPaged").mockImplementation(mockRun);
            queryEmail = new QueryEmail({
                columns: ["foo", "bar"],
                queryString: "dummyQueryString",
                description: "dummy description"
            });
        });

        describe("constructor", () => {

            it("has no query ID", () => {
                expect(queryEmail.queryId).toEqual("");
            });

            it("uses the default threshold", () => {
                expect(queryEmail.threshold).toEqual(100);
            });

            it("does not load the query", () => {
                expect(query.load).not.toHaveBeenCalled();
            });

            it("has empty initial query result columns", () => {
                expect(queryEmail.queryResult.columns).toEqual([{label: "foo"}, {label: "bar"}]);
            });

            it("has a queryString", () => {
                expect(queryEmail.queryString).toEqual("dummyQueryString");
            });

            it("has a description", () => {
                expect(queryEmail.description).toEqual("dummy description");
            });

        });

        describe("generateQueryResults", () => {

            beforeEach(() => {
                queryEmail.generateQueryResults();
            });

            it("generates the columns based off of the workbook columns", () => {
                expect(queryEmail.queryResult.columns).toEqual([{label: "foo"}, {label: "bar"}]);
            });
    
            it("generates the results", () => {
                expect(queryEmail.queryResult.results).toEqual([
                    {values: [1,2]},
                    {values: ["A","B"]}
                ]);
            });

        });

        describe("buildHtml", () => {
            
            beforeEach(() => {
                queryEmail.generateQueryResults();
                tableHtml = queryEmail.buildResultHtml();
            });

            it("generates the table HTML containing the results", () => {
                expect(tableHtml).toEqual(htmlResult);
            })

        });

        describe("sendResults", () => {
            beforeEach(() => {
                queryEmail.generateQueryResults();
                tableHtml = queryEmail.buildResultHtml();
                queryEmail.sendResults({recipients: [1], subject: "Dummy Subject"});
            });

            it("sends results via email", () => {
                expect(email.send).toHaveBeenCalledWith({
                    author: 3288,
                    body: htmlResult,
                    recipients: [1],
                    subject: "Dummy Subject",
                    attachments: null,
                    author: 223244,
                    body: "Please see attached file."
                });
            })

        });

    });
});
