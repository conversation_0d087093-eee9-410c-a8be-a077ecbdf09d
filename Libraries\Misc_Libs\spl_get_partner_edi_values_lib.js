/**
 * @NApiVersion 2.1
 */

//@ts-ignore
define(["N/log", "Moment"], function (log, moment) {
	function getOncareValues() {
		const fieldDelimiter = "*";
		const segmentDelimiter = "~";
		const fileDelimiter = ">";
		const ediVersion = "00401";
		const receiverQualifier = "ZZ";
		const receiverId = "ONCARE_1078";

		return getValues(
			fieldDelimiter,
			segmentDelimiter,
			fileDelimiter,
			ediVersion,
			receiverQualifier,
			receiverId
		);
	}

	function getProcurementPartnersValues() {
		const fieldDelimiter = "*";
		const segmentDelimiter = "~";
		const fileDelimiter = ">";
		const ediVersion = "00401";
		const receiverQualifier = "ZZ";
		const receiverId = "PROCUREPRTNR";

		return getValues(
			fieldDelimiter,
			segmentDelimiter,
			fileDelimiter,
			ediVersion,
			receiverQualifier,
			receiverId
		);
	}

	function getAgoraValues(integrationAccountNumber) {
		const integrationObjArr = [
			{
				integrationName: "Agora",
				integrationAccountNumber: "0019",
				receiverId: "AGORASERV",
			},
			{
				integrationName: "Infinite Care",
				integrationAccountNumber: "2579",
				receiverId: "AGORAINFCARE",
			},
			{
				integrationName: "Venza Care",
				integrationAccountNumber: "5104",
				receiverId: "AGORAVENZACARE",
			},
			{
				integrationName: "Empire Care",
				integrationAccountNumber: "0119",
				receiverId: "PSEMPIRE",
			},
			{
				integrationName: "Evergreen Healthcare",
				integrationAccountNumber: "170252",
				receiverId: "PSOLVERITAS",
			},
			{
				integrationName: "Recover Care Platform Solutions",
				integrationAccountNumber: "171620",
				receiverId: "PSRECOVER",
     	},
			{   
				integrationName: "Eden Health Care",
				integrationAccountNumber: "168286",
				receiverId: "PSEDENHC",
			},
		];

		const fieldDelimiter = "*";
		const segmentDelimiter = "~";
		const fileDelimiter = ">";
		const ediVersion = "00401";
		const receiverQualifier = "ZZ";
		const integrationObj = integrationObjArr.find(
			(obj) => obj.integrationAccountNumber == integrationAccountNumber
		);

		if (!integrationObj) {
			return;
		}

		const receiverId = integrationObj.receiverId;

		return getValues(
			fieldDelimiter,
			segmentDelimiter,
			fileDelimiter,
			ediVersion,
			receiverQualifier,
			receiverId
		);
	}

	function getDssiDelimiterValues() {
		const fieldDelimiter = "*";
		const segmentDelimiter = "~";
		const fileDelimiter = ">";

		return {
			formattingInfo: [
				{
					name: "element delimiter",
					templateValue: "*",
					partnerValue: fieldDelimiter,
				},
				{
					name: "segmentDelimeiter",
					templateValue: "~",
					partnerValue: segmentDelimiter,
				},
				{
					name: "fileDelimiter",
					templateValue: ">",
					partnerValue: fileDelimiter,
				},
			],
		};
	}

	function getDSSIValues(integrationAccountNumber) {
		const integrationObjArr = [
			{
				integrationName: "iCare",
				integrationAccountNumber: "2218",
				receiverId: "8004053774ICA",
			},
			{
				integrationName: "Apex",
				integrationAccountNumber: "2371",
				receiverId: "8004053774APH",
			},
			{
				integrationName: "OPCO",
				integrationAccountNumber: "3738",
				receiverId: "8004053774OHP",
			},
			{
				integrationName: "Crown",
				integrationAccountNumber: "0043",
				receiverId: "8004053774NCC",
			},
			{
				integrationName: "Onyx",
				integrationAccountNumber: "4890",
				receiverId: "8004053774BRI",
			},
			{
				integrationName: "Envive",
				integrationAccountNumber: "166793",
				receiverId: "8004053774ENV",
			},
			{
				integrationName: "Everview",
				integrationAccountNumber: "0165",
				receiverId: "8004053774EVR",
			},
			{
				integrationName: "Parkside",
				integrationAccountNumber: "163221",
				receiverId: "8004053774PSG",
			},
			{
				integrationName: "CompleteCare",
				integrationAccountNumber: "0041",
				receiverId: "8004053774CMT",
			},
			{
				integrationName: "Aspire",
				integrationAccountNumber: "170172",
				receiverId: "8004053774LA",
			},
			{
				integrationName: "MajesticCare",
				integrationAccountNumber: "2357",
				receiverId: "8004053774MJC",
			},
			{
				integrationName: "Bombay",
				integrationAccountNumber: "171412",
				receiverId: "8004053774WH",
			},
		];

		const fieldDelimiter = "*";
		const segmentDelimiter = "~";
		const fileDelimiter = ">";

		if (!integrationAccountNumber) {
		}
		const ediVersion = "00401";
		const receiverQualifier = "12";
		const integrationObj = integrationObjArr.find(
			(obj) => obj.integrationAccountNumber == integrationAccountNumber
		);

		if (!integrationObj) {
			return;
		}

		const receiverId = integrationObj.receiverId;

		return getValues(
			fieldDelimiter,
			segmentDelimiter,
			fileDelimiter,
			ediVersion,
			receiverQualifier,
			receiverId
		);
	}

	function getAdelpoValues() {
		const fieldDelimiter = "*";
		const segmentDelimiter = "~";
		const fileDelimiter = ">";
		const ediVersion = "00401";
		const receiverQualifier = "ZZ";
		const receiverId = "ADESPL";

		return getValues(
			fieldDelimiter,
			segmentDelimiter,
			fileDelimiter,
			ediVersion,
			receiverQualifier,
			receiverId
		);
	}

	function getDriveValues() {
		const fieldDelimiter = "^";
		const segmentDelimiter = "~";
		const fileDelimiter = "|";
		const ediVersion = "00401";
		const receiverQualifier = "12";
		const receiverId = "10057";

		return getValues(
			fieldDelimiter,
			segmentDelimiter,
			fileDelimiter,
			ediVersion,
			receiverQualifier,
			receiverId
		);
	}

	function getMedacureValues() {
		const fieldDelimiter = "*"; // Corrected to match the EDI file
		const segmentDelimiter = "~";
		const fileDelimiter = "|";
		const ediVersion = "00401";
		const receiverQualifier = "12";
		const receiverId = "7185961120";
	
		return getValues(
			fieldDelimiter,
			segmentDelimiter,
			fileDelimiter,
			ediVersion,
			receiverQualifier,
			receiverId
		);
	}

	function getTrueCommerceValues() {
		const fieldDelimiter = "*";
		const segmentDelimiter = "~";
		const fileDelimiter = "|";
		const ediVersion = "00401";
		const receiverQualifier = "12";
		const receiverId = "7188525330";
	
		return getValues(
			fieldDelimiter,
			segmentDelimiter,
			fileDelimiter,
			ediVersion,
			receiverQualifier,
			receiverId
		);
	}
	
	function getMedlineValues() {
		const fieldDelimiter = "^";
		const segmentDelimiter = "~";
		const fileDelimiter = "|"; 
		const ediVersion = "00401";
		const receiverQualifier = "ZZ";
		const receiverId = "MEDLINE";

		return getValues(
			fieldDelimiter,
			segmentDelimiter,
			fileDelimiter,
			ediVersion,
			receiverQualifier,
			receiverId
		);
	}

	function getHomecareHomebaseValues() {
		const fieldDelimiter = "|";
		const segmentDelimiter = "\r";
		const componentDelimiter = "^";

		return {
			formattingInfo: [
				{
					name: "elementDelimiter",
					templateValue: "*",
					partnerValue: fieldDelimiter,
				},
				{
					name: "segmentDelimiter",
					templateValue: "~",
					partnerValue: segmentDelimiter,
				},
				{
					name: "componentDelimiter",
					templateValue: "^",
					partnerValue: componentDelimiter,
				},
			],
		};
	}

	function getValues(
		fieldDelimiter,
		segmentDelimiter,
		fileDelimiter,
		ediVersion,
		receiverQualifier,
		recieverId
	) {
		const partnerValues = {
			fieldDelimiter: fieldDelimiter,
			segmentDelimiter: segmentDelimiter,
			formattingInfo: [
				{
					name: "element delimiter",
					templateValue: "*",
					partnerValue: fieldDelimiter,
				},
				{
					name: "segmentDelimeiter",
					templateValue: "~",
					partnerValue: segmentDelimiter,
				},
				{
					name: "fileDelimiter",
					templateValue: ">",
					partnerValue: fileDelimiter,
				},
			],
			isaGsInfo: [
				{
					name: "ISADate",
					value: moment().format("YYMMDD"),
				},
				{
					name: "GSDate",
					value: moment().format("YYYYMMDD"),
				},
				{
					name: "Time",
					value: moment().format("HHmm"),
				},
				{
					name: "EdiVersion",
					value: ediVersion,
				},
			],
			senderInfo: [
				//Our Info
				{
					name: "SenderQualifier",
					value: "12",
				},
				{
					name: "ISASenderId",
					value: "7328137750" + "     ",
				},
				{
					name: "GSSenderId",
					value: "7328137750",
				},
			],
			receiverInfo: [
				//Their Info
				{
					name: "ReceiverQualifier",
					value: receiverQualifier,
				},
				{
					name: "ISAReceiverId",
					value: (recieverId + "                 ").slice(0, 15), //Needs to be 15 characters total, slice extracts up to but not including end
				},
				{
					name: "GSReceiverId",
					value: recieverId,
				},
			],
		};

		return partnerValues;
	}

	return {
		getOncareValues: getOncareValues,
		getProcurementPartnersValues: getProcurementPartnersValues,
		getAgoraValues: getAgoraValues,
		getDssiDelimiterValues: getDssiDelimiterValues,
		getAdelpoValues: getAdelpoValues,
		getDSSIValues: getDSSIValues,
		getDriveValues: getDriveValues,
		getMedacureValues: getMedacureValues,
		getTrueCommerceValues: getTrueCommerceValues,
		getMedlineValues: getMedlineValues,
		getHomecareHomebaseValues: getHomecareHomebaseValues,
	};
});
