/**
 * @description Prevents inventory adjustment from being saved if any line has a unit cost that is blank or $0
 *
 * </br><b>Deployed On:</b> BRDG Inventory Adjustments
 * </br><b>Execution Context:</b> ALL
 * </br><b>Event Type/Mode:</b> ALL
 * </br><b>Entry Points:</b> beforeSubmit
 *
 * @NApiVersion 2.1
 * @NScriptType UserEventScript
 * @NAmdConfig /SuiteScripts/config.json
 *
 * <AUTHOR>
 * @module brdg_inventory_adjustment_ue
 */


define([
	"require",
	"N/error",
	"./Libraries/brdg_helper_functions_lib",
	"../Classes/brdg_item_record",
], (/** @type {any} */ require) => {
	const error = require("N/error");

	const bridgeHelperFunctionsLib = require("./Libraries/brdg_helper_functions_lib");
	const BridgeItemRecord = require("../Classes/brdg_item_record");

	return {
		/**
		 * Validate line items
		 * Throw an error if a line has no or 0 unit cost
		 *
		 * @param {import("@hitc/netsuite-types/N/types").EntryPoints.UserEvent.beforeSubmitContext} context Before submit script context
		 * @returns {void}
		 */
		beforeSubmit: (context) => {
			const { newRecord } = context;
			/** @type {import("../Classes/brdg_item_record").BridgeItemRecord} */
			const bridgeItemRecord = new BridgeItemRecord(newRecord);
			const bridgeSubsidiaries =
				bridgeHelperFunctionsLib.getBridgeSubsidiaries();
			const isBridgeSubsidiary = bridgeHelperFunctionsLib.isBridgeSubsidiary(
				bridgeItemRecord.subsidiaries,
				bridgeSubsidiaries
			);

			if (!isBridgeSubsidiary) {
				return;
			}

			const itemsWithInvalidUnitCosts =
				bridgeHelperFunctionsLib.getItemsWithInvalidUnitCosts({
					currentRecord: newRecord,
					sublistId: "inventory",
				});

			if (itemsWithInvalidUnitCosts.length > 0) {
				throw error.create({
					name: "ITEM_ERROR",
					message: `The unit cost for the following items should not be blank or 0: ${itemsWithInvalidUnitCosts}`,
				});
			}
		},
	};
});