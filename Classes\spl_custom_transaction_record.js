/**
 * Transaction Record class
 * containing custom and searched data
 * that represents the NetSuite Transaction
 *
 * @NApiVersion 2.1
 * <AUTHOR>
 */

define(["N/search", "TransactionRecord"], (search, TransactionRecord) => {
	/**
	 * Custom Transaction Record class
	 *
	 * @class
	 * @type {import("./spl_transaction_record").CustomTransactionRecord}
	 */
	class CustomTransactionRecord extends TransactionRecord {
		constructor(transaction) {
			super(transaction);

			// Columns will be used for search.lookupFields
			this.Column = {
				balance: "balance",
				consolidatedBalance: "consolbalance",
				creditLimit: "creditlimit",
				parent: "parent",
				dontUseCustomerInNewOrders: "custentity_spl_dont_use_cstmr_in_new_so",
				parentIsFreeFreightEligible: "custentity_spl_free_freight_eligible",
				freeFreightItems: "custentity_spl_free_freight_items",
			};

			/**
			 * Get the value from a search lookup object array
			 *
			 * @param {object} searchLookup Search lookup object
			 * @param {string} field Field in the lookup object
			 * @param {"value"|"text"} property Property to retrieve from the object
			 * @returns {any} Value from the lookup object
			 */
			const getLookupArrayProperty = (searchLookup, field, property) => {
				return (
					searchLookup &&
					searchLookup[field] &&
					Array.isArray(searchLookup[field]) &&
					searchLookup[field][0] &&
					searchLookup[field][0][property]
				);
			};

			const setParentFreeFreightInfo = (parentObjectId) => {
				const freeFreightLookup = search.lookupFields({
					type: search.Type.CUSTOMER,
					id: parentObjectId,
					columns: [
						this.Column.parentIsFreeFreightEligible,
						this.Column.freeFreightItems,
					],
				});

				this.parentIsFreeFreightEligible =
					freeFreightLookup[this.Column.parentIsFreeFreightEligible];

				if (this.parentIsFreeFreightEligible) {
					this.freeFreightItems = freeFreightLookup[
						this.Column.freeFreightItems
					].map((itemObj) => {
						return {
							id: itemObj.value,
							name: itemObj.text,
						};
					});
				}
			};

			//Set customer values
			this.customerLookup =
				this.customerId &&
				search.lookupFields({
					type: search.Type.CUSTOMER,
					id: this.customerId,
					columns: [
						this.Column.balance,
						this.Column.consolidatedBalance,
						this.Column.creditLimit,
						this.Column.parent,
						this.Column.dontUseCustomerInNewOrders,
					],
				});

			//Set parent id and name
			if (this.customerLookup) {
				this.customerParentObj = {
					id: getLookupArrayProperty(
						this.customerLookup,
						this.Column.parent,
						"value"
					),
					name: getLookupArrayProperty(
						this.customerLookup,
						this.Column.parent,
						"text"
					),
				};
				this.dontUseCustomerInNewOrders =
					this.customerLookup[this.Column.dontUseCustomerInNewOrders];
			}

			//Set parent values
			if (this.customerLookup && this.customerParentObj?.id) {
				setParentFreeFreightInfo(this.customerParentObj.id);
			}
		}
	}

	return CustomTransactionRecord;
});
