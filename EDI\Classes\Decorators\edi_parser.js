/**
 * @description Decorator Parser Class
 *
 * @NApiVersion 2.1
 * <AUTHOR> <<EMAIL>>
 */

define([
    "exports",
], function (/** @type {any} */ exports) {

    /**
     * Parser Class
     * 
     * @typedef {import("../Interfaces/Models/File/edi_file").EDIPostProcessEmail} EDIPostProcessEmail
     * @class
     */
    class EDIParser {
        /** @param {{[key:string]: any}} params */
        constructor (params) {
            /** @type {EDIPostProcessEmail} */
            this.email;
            /** @type {string} */
            this.filename = params.filename;
        }
    }

    exports.EDIParser = EDIParser;
});

