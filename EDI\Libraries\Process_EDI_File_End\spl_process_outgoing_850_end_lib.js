/**
 * @NApiVersion 2.1
 */

//@ts-ignore
define(["N/log", "N/email"], function (log, email) {
	function processEnd(
		processingLog,
		transactionsProcessedSuccessfully,
		processedSuccesfully,
		vendorName
	) {
		var resultsData = setResultsDataAndSendEmail();
		logEndResult();

		return resultsData;

		function setResultsDataAndSendEmail() {
			var data = {};
			var errorText = "";
			processingLog.forEach(function (error) {
				errorText += error + "\n\n";
			});
			var transactionsProcessed = "";
			transactionsProcessedSuccessfully.forEach(function (transaction) {
				transactionsProcessed += transaction + "\n";
			});

			if (processedSuccesfully) {
				processCreatedSuccessfully();
			} else {
				if (transactionsProcessedSuccessfully.length > 0) {
					processCreatedWithErrors();
					sendEmail();
				} else {
					processFailedData();
					sendEmail();
				}
			}

			return data;

			function processCreatedSuccessfully() {
				data.subject = `Success: ${vendorName} EDI Purchase Orders Processed Successfully`;
				data.recipients = ["<EMAIL>"];
				data.body = `The following ${vendorName} purchase orders were processed successfully via EDI:
                    
                    ${transactionsProcessed}`;
				data.logTitle = "Processed Successfully";
				data.processingStatus = 1; //Processed With No Errors
			}

			function processCreatedWithErrors() {
				data.subject = `Errors: Please Review and Correct ${vendorName} EDI Purchase Orders`;
				data.recipients = ["<EMAIL>"];
				data.body = `The following ${vendorName} purchase orders were processed successfully via EDI:
                    
                    ${transactionsProcessed}

                    Please review the errors below.
                    
                    ${errorText}`;
				data.logTitle = "Created with Errors";
				data.processingStatus = 2; //Processed With Errors
			}

			function processFailedData() {
				data.subject = `Failure: ${vendorName} EDI purchase orders not processed successfully.`;
				data.recipients = ["<EMAIL>"];
				data.cc = ["<EMAIL>"];
				data.body = `Please review the errors below.
                    
                    ${errorText}`;
				data.logTitle = "Purchased Orders Not Processed";
				data.processingStatus = 4; //Document Not Created
			}

			function sendEmail(salesOrderId) {
				try {
					email.send({
						author: 262579, //EDI
						recipients: data.recipients,
						cc: data.cc,
						subject: data.subject,
						body: data.body,
					});
				} catch (error) {
					throw "Vendor EDI Purchase Order email not sent.";
				}
			}
		}

		function logEndResult() {
			log.debug({
				title: resultsData.logTitle,
				details: resultsData.logDetails,
			});
		}
	}

	return {
		processEnd: processEnd,
	};
});
