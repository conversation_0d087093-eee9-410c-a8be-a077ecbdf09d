/**
 * @description Class containing functions to create 850 Customer
 *
 * @NApiVersion 2.1
 * <AUTHOR> <<EMAIL>>
 */
define([
    "exports",
    "require",
    "N/record",
    "N/query",
    "N/log",
], (
    /** @type {any} */ exports,
    /** @type {any} */ require,
) => {
    const record = require("N/record");
    const query = require("N/query");
    const log = require("N/log");

    /**
     * 850 Customer Class
     *
     * @class
     * @typedef {import("../../Interfaces/Decorators/850/edi_850").EDI850Entity} EDI850Entity
     * @typedef {import("../../Interfaces/Decorators/850/edi_850").EDI850CustomerParams} EDI850CustomerParams
     * @typedef {import("../../Interfaces/Models/Partner/edi_partner").EDIPartnerInterface} EDIPartnerInterface
     * @typedef {import("../../../../Classes/vlmd_custom_error_object").CustomErrorObject} CustomErrorObject
     */
    class EDI850Customer {
        /**
         * Constructor
         *
         * @param {EDI850CustomerParams} params
         */
        constructor (params) {
            /** @type {EDI850Entity} */
            this.parsedCustomer = params.customer;
            /** @type {EDIPartnerInterface} */
            this.partner = params.partner;
            /** @type {import("N/record").Record | null} */
            this.dynamicRecord = null;
            /** @type {import("N/record").Record | null} */
            this.staticRecord = null;
            /** @type {CustomErrorObject} */
            this.customError = params.customError;
        }

        create() {
            try {
                this.dynamicRecord = record.create({
                    type: record.Type.CUSTOMER,
                    isDynamic: true,
                });
            } catch (/** @type {any} */ err) {

            }
        }

        /**
         * Search for an existing customer based on GLN
         * Create new one if none exists
         *
         * @param {string} queryString
         * @returns {Number} Customer Internal ID
         */

        lookup(queryString) {
            try {
                const customerId = query.runSuiteQL({
                    query: queryString
                })?.results[0]?.values[0];
    
                log.debug({
                    title: "EDI850Customer (lookup): customerId",
                    details: JSON.stringify({ queryString, customerId }),
                })
    
                if (customerId) {
                    return Number(customerId);
                } else {
    
                    log.debug({
                        title: "EDI850Customer (lookup): lookup",
                        details: `Creating customer with the GLN '${this.parsedCustomer.identifier}'..`,
                    });
    
                    this.dynamicRecord = record.create({
                        type: record.Type.CUSTOMER,
                        isDynamic: true,
                    });
    
                    this.setHeaderValues();
                    this.setBillingAddress()
                    this.setShippingAddress();
    
                    return this.save();
                }
            } catch (/** @type {any} */ err) {
                log.error({
                    title: "EDI850Customer (lookup)",
                    details: err,
                });
                throw this.customError.updateError({
                    errorType: this.customError.ErrorTypes.RECORD_NOT_LOADED,
                    summary: "EDI850Customer_lookup",
                    details: err,
                });
            }
        }

        /**
         * Set header values for the newly created customer
         *
         * @returns {void}
         */
        setHeaderValues() {
            try {
                if (!this.dynamicRecord) {
                    return;
                }

                log.debug({
                    title: "EDI850Customer (setHeaderValues)",
                    details: this.partner,
                });

                this.dynamicRecord.setValue({
                    fieldId: "customform",
                    value: "95", // Creoh Customer Form
                });

                this.dynamicRecord.setValue({
                    fieldId: "parent",
                    value: this.partner.parent,
                });

                this.dynamicRecord.setValue({
                    fieldId: "subsidiary",
                    value: this.partner.subsidiary,
                });

                this.dynamicRecord.setValue({
                    fieldId: "custentity_spl_purchasing_software",
                    value: this.partner.purchasingSoftwareId,
                });

                this.dynamicRecord.setValue({
                    fieldId: "companyname",
                    value: this.parsedCustomer.name,
                });

                this.dynamicRecord.setValue({
                    fieldId: "altname",
                    value: this.parsedCustomer.name,
                });

                this.dynamicRecord.setValue({
                    fieldId: "custentity_crh_walmart_gln",
                    value: this.parsedCustomer.identifier,
                });
            } catch (/** @type {any} */ err) {
                log.error({
                    title: "EDI850Customer (setHeaderValues)",
                    details: err,
                });
                throw this.customError.updateError({
                    errorType: this.customError.ErrorTypes.VALUE_NOT_SET,
                    summary: "EDI850Customer_setHeaderValues",
                    details: err,
                });
            }
        }

        /**
         * Set the shipping address
         *
         * @returns {void}
         */
        setShippingAddress() {
            try {
                if (!this.dynamicRecord) {
                    return;
                }
    
                log.debug({
                    title: "EDI850Customer (setShippingAddress)",
                    details: this.parsedCustomer.address,
                });
    
                this.dynamicRecord.setCurrentSublistValue({
                    sublistId: "addressbook",
                    fieldId: "defaultshipping",
                    value: true,
                });
    
                this.dynamicRecord.setCurrentSublistValue({
                    sublistId: "addressbook",
                    fieldId: "defaultbilling",
                    value: false,
                });
    
                var shippingAddressSubrecord = this.dynamicRecord.getCurrentSublistSubrecord({
                    sublistId: "addressbook",
                    fieldId: "addressbookaddress",
                });
    
                shippingAddressSubrecord.setValue({
                    fieldId: "addressee",
                    value: this.parsedCustomer.name,
                });
    
                shippingAddressSubrecord.setValue({
                    fieldId: "addr1",
                    value: this.parsedCustomer.address.street,
                });
    
                shippingAddressSubrecord.setValue({
                    fieldId: "city",
                    value: this.parsedCustomer.address.city,
                });
    
                shippingAddressSubrecord.setValue({
                    fieldId: "state",
                    value: this.parsedCustomer.address.state,
                });
    
                shippingAddressSubrecord.setValue({
                    fieldId: "zip",
                    value: this.parsedCustomer.address.zip,
                });
    
                shippingAddressSubrecord.setValue({
                    fieldId: "country",
                    value: "US",
                });
    
                this.dynamicRecord.commitLine({
                    sublistId: "addressbook",
                });
            } catch (/** @type {any} */ err) {
                throw this.customError.updateError({
                    errorType: this.customError.ErrorTypes.VALUE_NOT_SET,
                    summary: "EDI850Customer_setShippingAddress",
                    details: err,
                });
            }
        }

        /**
         * Set the billing address
         *
         * @returns {void}
         */
        setBillingAddress() {
            try {
                if (!this.dynamicRecord) {
                    return;
                }
    
                log.debug({
                    title: "EDI850Customer (setBillingAddress)",
                    details: this.partner.billingAddress,
                });
    
                this.dynamicRecord.selectNewLine({
                    sublistId: "addressbook",
                });
    
                this.dynamicRecord.setCurrentSublistValue({
                    sublistId: "addressbook",
                    fieldId: "defaultbilling",
                    value: true,
                });
    
                var billingAddressSubrecord = this.dynamicRecord.getCurrentSublistSubrecord({
                    sublistId: "addressbook",
                    fieldId: "addressbookaddress",
                });
    
                billingAddressSubrecord.setValue({
                    fieldId: "addressee",
                    value: this.partner.billingAddress.addressee,
                });
    
                billingAddressSubrecord.setValue({
                    fieldId: "addr1",
                    value: this.partner.billingAddress.street,
                });
    
                billingAddressSubrecord.setValue({
                    fieldId: "city",
                    value: this.partner.billingAddress.city,
                });
    
                billingAddressSubrecord.setValue({
                    fieldId: "state",
                    value: this.partner.billingAddress.state,
                });
    
                billingAddressSubrecord.setValue({
                    fieldId: "zip",
                    value: this.partner.billingAddress.zip,
                });
    
                billingAddressSubrecord.setValue({
                    fieldId: "country",
                    value: this.partner.billingAddress.country,
                });
    
                this.dynamicRecord.commitLine({
                    sublistId: "addressbook",
                });
            } catch (/** @type {any} */ err) {
                log.error({
                    title: "EDI850Customer (setBillingAddress)",
                    details: err,
                });
                throw this.customError.updateError({
                    errorType: this.customError.ErrorTypes.VALUE_NOT_SET,
                    summary: "EDI850Customer_setBillingAddress",
                    details: err,
                });
            }
        }

        /**
         * Save the NetSuite record for the Customer
         *
         * @returns {number} Customer ID
         */
        save() {
            try {
                if (this.dynamicRecord) {

                    log.debug({
                        title: "EDI850Customer (save)",
                        details: this.partner,
                    });
    
                    return this.dynamicRecord.save({
                        ignoreMandatoryFields: true,
                    });
                } else {
                    return 0;
                }
            } catch (/** @type {any} */ err) {
                log.error({
                    title: "EDI850Customer (save)",
                    details: err,
                });
                throw this.customError.updateError({
                    errorType: this.customError.ErrorTypes.RECORD_NOT_SAVED,
                    summary: "EDI850Customer_save",
                    details: err,
                });
            }
        }
    }

    exports.EDI850Customer = EDI850Customer;
});