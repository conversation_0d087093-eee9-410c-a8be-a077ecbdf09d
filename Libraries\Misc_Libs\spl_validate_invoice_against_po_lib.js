/**
 * @NApiVersion 2.x
 */

define([
    'Moment',
    'Numeral'
],
    function (
        moment,
        numeral
    ) {
        function validateInvoiceAgainstPurchaseOrder(purchaseOrder, invoice) {
            var processingLog = []; 

            return processingLog;
        };

        return {
            validateInvoiceAgainstPurchaseOrder: validateInvoiceAgainstPurchaseOrder
        }
    });
