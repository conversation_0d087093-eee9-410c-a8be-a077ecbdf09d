// @ts-nocheck
import error from "N/error";
import runtime from "N/runtime";
import log from "N/log";

import brdg_vendor_bill_ue from "../../Bridge/brdg_vendor_bill_ue";

beforeEach(() =>  {
	jest.resetModules();
	jest.resetAllMocks();
	jest.clearAllMocks();
});

jest.mock("BridgeHelperFunctionsLib", () => ({
	getBridgeSubsidiaries: () => {},
	isBridgeSubsidiary: () => {},
	getValueByRelatedValue: () => {},
    getSalesPriceForLocation: () => {}
}), { virtual: true });

import bridgeHelperFunctionsLib from "BridgeHelperFunctionsLib";

describe("brdg_vendor_bill_ue", () => {
	describe("beforeSubmit", () => {

        const itemId = "foo";
        const locationId = 99;

		beforeEach(() => {
			jest.spyOn(error, "create").mockImplementation((error) => error);
			jest.replaceProperty(runtime, "executionContext", "csvimport");
			jest.spyOn(bridgeHelperFunctionsLib, "isBridgeSubsidiary").mockReturnValue(true);
            jest.spyOn(log, "error");
		});

        it("returns early when subsidiary is not Bridge", () => {
            const context = {
                type: "FOO",
                newRecord: {
                    getValue: ({fieldId}) => fieldId,
                    getLineCount: jest.fn()
                }
            };
            jest.spyOn(bridgeHelperFunctionsLib, "isBridgeSubsidiary").mockReturnValue(false);
            brdg_vendor_bill_ue.beforeSubmit(context);
            expect(context.newRecord.getLineCount).not.toHaveBeenCalled();
        });

        it("logs error when cost/rate is not available", () => {
            const context = {
                type: "FOO",
                newRecord: {
                    getValue: ({fieldId}) => fieldId,
                    getLineCount: () => 3,
                    getSublistValue: ({fieldId}) => {
                        const dummyValues = {
                            item: "foo",
                            item_display: "displayName"
                        };
                        return dummyValues[fieldId];
                    }
                }
            };
            brdg_vendor_bill_ue.beforeSubmit(context);
            expect(log.error).toHaveBeenCalledWith("ITEM_ERROR", "No cost for item displayName in line 1, No cost for item displayName in line 2, No cost for item displayName in line 3");
        });
        it("logs error when location is not available", () => {
            const context = {
                type: "FOO",
                newRecord: {
                    getValue: () => null,
                    getLineCount: () => 3,
                    getSublistValue: ({fieldId}) => {
                        const dummyValues = {
                            item: "foo",
                            item_display: "displayName",
                            rate: 1
                        };
                        return dummyValues[fieldId];
                    }
                }
            };
            brdg_vendor_bill_ue.beforeSubmit(context);
            expect(log.error).toHaveBeenCalledWith("MISSING_LOCATION", "No location for this transaction. (newRecord: {})");
        });
        it("logs error when price level is not available", () => {
            const context = {
                type: "FOO",
                newRecord: {
                    getValue: ({fieldId}) => fieldId,
                    getLineCount: () => 2,
                    getSublistValue: ({fieldId}) => {
                        const dummyValues = {
                            item: "foo",
                            item_display: "displayName",
                            rate: 1,
                            location: 25
                        };
                        return dummyValues[fieldId];
                    }
                }
            };
            brdg_vendor_bill_ue.beforeSubmit(context);
            expect(log.error).toHaveBeenCalledWith("ITEM_ERROR", "No price level returned for item displayName in line 1. (Bill: transactionnumber), No price level returned for item displayName in line 2. (Bill: transactionnumber)");
        });
        describe("when rate and location are available", () => {
            const context = {
                type: "FOO",
                newRecord: {
                    getValue: ({fieldId}) => fieldId,
                    getLineCount: () => 3,
                    getSublistValue: ({fieldId}) => {
                        const dummyValues = {
                            item: itemId,
                            item_display: "displayName",
                            rate: 1,
                            location: locationId,
                            unitconversionrate: "rate"
                        };
                        return dummyValues[fieldId];
                    }
                }
            };
            it("logs error when sales price is not available", () => {
                jest.spyOn(bridgeHelperFunctionsLib, "getValueByRelatedValue").mockReturnValue("priceLevelId");
                jest.spyOn(bridgeHelperFunctionsLib, "getSalesPriceForLocation").mockReturnValue();
                
                brdg_vendor_bill_ue.beforeSubmit(context);
                expect(bridgeHelperFunctionsLib.getValueByRelatedValue).toHaveBeenCalledWith("location", locationId, "priceLevel");
                expect(bridgeHelperFunctionsLib.getSalesPriceForLocation).toHaveBeenCalledWith(itemId, "priceLevelId", "rate");
                expect(log.error).toHaveBeenCalledWith("ITEM_ERROR", "No sales price returned for item displayName in line 1. (Bill: transactionnumber), No sales price returned for item displayName in line 2. (Bill: transactionnumber), No sales price returned for item displayName in line 3. (Bill: transactionnumber)");
            });
            it("logs error when sales price is not available", () => {
                jest.spyOn(bridgeHelperFunctionsLib, "getValueByRelatedValue").mockReturnValue("priceLevelId");
                jest.spyOn(bridgeHelperFunctionsLib, "getSalesPriceForLocation").mockReturnValue(0.1);

                brdg_vendor_bill_ue.beforeSubmit(context);
                expect(bridgeHelperFunctionsLib.getValueByRelatedValue).toHaveBeenCalledWith("location", locationId, "priceLevel");
                expect(bridgeHelperFunctionsLib.getSalesPriceForLocation).toHaveBeenCalledWith(itemId, "priceLevelId", "rate");
                expect(log.error).toHaveBeenCalledWith("ITEM_ERROR", "The sales price of $0.1 for item displayName in line 1 is below the cost. (Bill: transactionnumber), The sales price of $0.1 for item displayName in line 2 is below the cost. (Bill: transactionnumber), The sales price of $0.1 for item displayName in line 3 is below the cost. (Bill: transactionnumber)");
            });
            it("exists successfully when sales price is valid", () => {
                jest.spyOn(bridgeHelperFunctionsLib, "getValueByRelatedValue").mockReturnValue("priceLevelId");
                jest.spyOn(bridgeHelperFunctionsLib, "getSalesPriceForLocation").mockReturnValue(100);
                brdg_vendor_bill_ue.beforeSubmit(context);
                expect(bridgeHelperFunctionsLib.getValueByRelatedValue).toHaveBeenCalledWith("location", locationId, "priceLevel");
                expect(bridgeHelperFunctionsLib.getSalesPriceForLocation).toHaveBeenCalledWith(itemId, "priceLevelId", "rate");
            });
        });
    });
});