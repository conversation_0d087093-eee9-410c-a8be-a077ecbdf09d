  /**
   * @description Editable script to delete all links of applied bill credits by date
   *
   * </br><b>Schedule:</b> Run on Demand
   *
   * @NApiVersion 2.1
   * @NScriptType MapReduceScript
   *
   * <AUTHOR>
   * @module brdg_rip_clear_applied_sales
   */
  define([
    "require",
    "N/log",
    "N/record",
    "N/runtime",
    "N/query",
    "N/format",
    "N/email",
    "N/task",
    "../../../../Classes/vlmd_custom_error_object",
  ], (/** @type {any} */ require) => {
    const record = require("N/record");
    const runtime = require("N/runtime");
    const query = require("N/query");
    const format = require("N/format");
    const email = require("N/email");
    const task = require("N/task");

    /** @type {import("../../../../Classes/vlmd_custom_error_object").CustomErrorObject} */

    const CustomErrorObject = require("../../../../Classes/vlmd_custom_error_object");
    const customErrorObject = new CustomErrorObject();

    function getInputData(context) {
      try {
        const startDate = runtime.getCurrentScript().getParameter({
          name: "custscript_deletion_start_date",
        });

        const endDate = runtime.getCurrentScript().getParameter({
          name: "custscript_deletion_end_date",
        });

        const specificBillCredit = runtime.getCurrentScript().getParameter({
          name: "custscript_specific_bill_credit",
        });

        if ((!startDate || !endDate) && !specificBillCredit) {
          customErrorObject.updateError({
            errorType: customErrorObject.ErrorTypes.MISSING_PARAMETER,
            summary: "MISSING_DATES",
            details: `Missing start and/or end dates or specific bill credit id for bill credit link deletion!`,
          });
        }

        const sqlQuery = /*sql*/`
        SELECT
   t.id as transaction_id,
   t.tranid as transaction_number,
   t.type as transaction_type,
   t.trandate as transaction_date,
   tl.item as item_id,
   tl.custcol_item_rip_bill_credit_applied as applied_bill_credit,
   tl.custcol_item_rip_quantity_used as quantity_used,
   tl.custcol_item_rip_rate as rip_rate 
FROM
   transaction t 
   JOIN
      transactionline tl 
      ON t.id = tl.transaction 
WHERE
   t.type IN 
   (
      'CashSale',
      'Invoice'
   )
   AND tl.mainline = 'F' 
   AND tl.custcol_item_rip_bill_credit_applied IS NOT NULL ${specificBillCredit ? `
   AND tl.custcol_item_rip_bill_credit_applied = ${specificBillCredit}` : `
   AND t.trandate >= TO_DATE('${format.format({
    value: new Date(startDate), type: format.Type.DATE, pattern: "MM / dd / yy", })}', 'MM / DD / YY')
 
    AND t.trandate <= TO_DATE('${format.format({
    value: new Date(endDate), type: format.Type.DATE, pattern: "MM / dd / yy", })}', 'MM / DD / YY')`
    } 
ORDER BY
   t.trandate ASC,
   t.id,
   tl.linesequencenumber
    `;

        const results = query.runSuiteQL({ query: sqlQuery }).asMappedResults();
        const billCreditMap = {};

        results.forEach((line) => {
          const billCreditId = line.applied_bill_credit;

          if (!billCreditMap[billCreditId]) {
            billCreditMap[billCreditId] = {
              billCreditId: billCreditId,
              sales: [],
              totalQuantityUsed: 0,
              itemId: line.item_id,
            };
          }

          billCreditMap[billCreditId].sales.push({
            transactionId: line.transaction_id,
            type: line.transaction_type,
            quantityUsed: line.quantity_used,
            itemId: line.item_id,
          });

          billCreditMap[billCreditId].totalQuantityUsed += Number(
            line.quantity_used || 0
          );
        });

        const finalArray = Object.values(billCreditMap);
        return finalArray;
      } catch (e) {
        customErrorObject.throwError({
          summaryText: "GET_INPUT_DATA_ERROR",
          error: e,
        });
      }
    }

    function map(context) {
      try {
        //Sample billCreditData: {
        //   "billCreditId": 8697273,
        //   "sales": [
        //     {
        //       "transactionId": 9720577,
        //       "type": "CashSale",
        //       "quantityUsed": 1,
        //       "itemId": 11979
        //     },
        //     {
        //       "transactionId": 9724560,
        //       "type": "CashSale",
        //       "quantityUsed": 1,
        //       "itemId": 11979
        //     }
        //   ],
        //   "totalQuantityUsed": 2,
        //   "itemId": 11979
        // }
        const billCreditData = JSON.parse(context.value);
        // Pass each sale to reduce stage along with the bill credit info
        billCreditData.sales.forEach((sale) => {
          context.write({
            key: billCreditData.billCreditId,
            value: {
              sale: sale,
              billCreditId: billCreditData.billCreditId,
              itemId: billCreditData.itemId,
              totalQuantityUsed: billCreditData.totalQuantityUsed,
            },
          });
        });
      } catch (e) {
        context.write({
          key: "Error",
          value: {
            billCreditId: context.value,
            error: e.message,
          },
        });
      }
    }

    function reduce(context) {
      try {
        // Process all values (sales) for this bill credit
        context.values.forEach((value, index) => {
          const data = JSON.parse(value);
          const sale = data.sale;

          try {
            const salesRecord = record.load({
              type: sale.type,
              id: sale.transactionId,
              isDynamic: false,
            });

            const lineNumber = salesRecord.findSublistLineWithValue({
              sublistId: "item",
              fieldId: "custcol_item_rip_bill_credit_applied",
              value: data.billCreditId,
            });

            if (lineNumber !== -1) {
              salesRecord.setSublistValue({
                sublistId: "item",
                fieldId: "custcol_item_rip_bill_credit_applied",
                line: lineNumber,
                value: "",
              });
              salesRecord.setSublistValue({
                sublistId: "item",
                fieldId: "custcol_item_rip_quantity_used",
                line: lineNumber,
                value: "",
              });
              salesRecord.setSublistValue({
                sublistId: "item",
                fieldId: "custcol_item_rip_rate",
                line: lineNumber,
                value: "",
              });
              salesRecord.setSublistValue({
                sublistId: "item",
                fieldId: "custcol_item_rip_applied_sales",
                line: lineNumber,
                value: false,
              });
              salesRecord.setSublistValue({
                sublistId: "item",
                fieldId: "custcol_total_cog_rip",
                line: lineNumber,
                value: "",
              });

              salesRecord.save({
                ignoreMandatoryFields: true,
                enableSourcing: false,
              });
            }

            // Process bill credit record only after the last sale
            if (index === context.values.length - 1) {
              const billCreditRecord = record.load({
                type: record.Type.VENDOR_CREDIT,
                id: data.billCreditId,
                isDynamic: false,
              });

              const bcLineNumber = billCreditRecord.findSublistLineWithValue({
                sublistId: "item",
                fieldId: "custcol_rip_item_link",
                value: data.itemId,
              });

              const quantityRemaining = billCreditRecord.getSublistValue({
                sublistId: "item",
                fieldId: "custcol_rip_quantity_remaining",
                line: bcLineNumber,
              });

              if (quantityRemaining !== null) {
                const newQuantity =
                  Number(quantityRemaining) + Number(data.totalQuantityUsed);
                billCreditRecord.setSublistValue({
                  sublistId: "item",
                  fieldId: "custcol_rip_quantity_remaining",
                  line: bcLineNumber,
                  value: newQuantity,
                });
              }

              billCreditRecord.save({
                ignoreMandatoryFields: true,
                enableSourcing: false,
              });
            }

            context.write({
              key: "Success",
              value: {
                billCreditId: data.billCreditId,
                saleId: sale.transactionId,
                quantityRestored: sale.quantityUsed,
              },
            });
          } catch (e) {
            log.error(`Error processing sale ${sale.transactionId}`, e);
            context.write({
              key: "Error",
              value: JSON.stringify({
                billCreditId: data.billCreditId,
                saleId: sale.transactionId,
                error: e.message,
              }),
            });
          }
        });
      } catch (e) {
        log.error("Reduce Error", e);
        context.write({
          key: "Error",
          value: JSON.stringify({
            error: e.message,
          }),
        });
      }
  }


    function summarize(context) {
      let successCount = 0;
      let errorCount = 0;
      const billCreditSummary = {};
      const errors = [];

    context.output.iterator().each(function (key, value) {
        const result = JSON.parse(value);

        if (key === "Success") {
          const billCreditId = result.billCreditId;

          if (!billCreditSummary[billCreditId]) {
            billCreditSummary[billCreditId] = {
              billCreditId: billCreditId,
              sales: [],
              totalQuantityRestored: 0,
            };
          }

          billCreditSummary[billCreditId].sales.push(result.saleId);
          billCreditSummary[billCreditId].totalQuantityRestored += Number(
            result.quantityRestored || 0
          );
          successCount++;
        } else if (key === "Error") {
            errors.push({
                billCreditId: result.billCreditId,
                error: result.error,
            });
            errorCount++;
        }

        return true;
    });

    try {
        const htmlContent = `
        <html>
        <body style="font-family: Arial, sans-serif; line-height: 1.6;">
            <h2>RIP Clear Applied Sales Processing Summary</h2>
            
            <div style="margin: 20px 0;">
                <p><strong>Total Successful:</strong> ${successCount}</p>
                <p><strong>Total Errors:</strong> ${errorCount}</p>
            </div>

            ${
              Object.values(billCreditSummary).length > 0
                ? `
                <div style="margin: 20px 0;">
                    <h3>Successfully Processed:</h3>
                    <ul style="list-style-type: none; padding-left: 0;">
                        ${Object.values(billCreditSummary)
                          .map(
                            (summary) => `
                                <li style="margin-bottom: 10px;">
                                    Bill Credit: ${summary.billCreditId}<br>
                                    Sales Processed: ${summary.sales.join(
                                      ", "
                                    )}<br>
                                    Total Sales: ${summary.sales.length}<br>
                                    Total Quantity Restored: ${
                                      summary.totalQuantityRestored
                                    }
                                </li>
                            `
                          )
                          .join("")}
                    </ul>
                </div>
            `
                : ""
            }

            ${
              errors.length > 0
                ? `
                <div style="margin: 20px 0;">
                    <h3 style="color: #cc0000;">Errors:</h3>
                    <ul style="list-style-type: none; padding-left: 0;">
                        ${errors
                          .map(
                            (error) => `
                                <li style="margin-bottom: 10px; color: #cc0000;">
                                    Bill Credit: ${error.billCreditId}<br>
                                    Error: ${error.error}
                                </li>
                            `
                          )
                          .join("")}
                    </ul>
                </div>
            `
                : ""
            }
        </body>
        </html>
    `;

        email.send({
            author: 15131,
            recipients: "<EMAIL>",
            subject: "RIP Clear Applied Sales Processing Summary",
            body: htmlContent,
            isHtml: true,
        });
    } catch (e) {
        log.debug("Error sending email", e.message);
      }


    log.audit("Processing Summary", {
        totalSuccessful: successCount,
        totalErrors: errorCount,
        billCreditSummary: billCreditSummary,
        errors: errors,
      });

      const triggeredFromBtn = runtime.getCurrentScript().getParameter({
        name: "custscript_triggered_from_btn",
      });

      const specificBillCredit = runtime.getCurrentScript().getParameter({
          name: "custscript_specific_bill_credit",
        });


      if (triggeredFromBtn) {
        let mrTask = task.create({
          taskType: task.TaskType.MAP_REDUCE,
          scriptId: "customscript_brdg_rip_apply_sales",
          deploymentId: "customdeploy_brdg_rip_apply_sales",
          params: {
              custscript_apply_specific_bill_credit: specificBillCredit,
          },
          });


        const taskId = mrTask.submit();
      }
    }

    return {
      getInputData,
      map,
      reduce,
      summarize,
    };
  });
