/**
 * @NApiVersion 2.1
 * @NScriptType ClientScript
 * @NAmdConfig /SuiteScripts/config.json
 */

//@ts-ignore
define([
	"N/log",

	"/SuiteScripts/Libraries/External_Libraries/vlmd_anime_lib",
], function (log, anime) {
	function pageInit(context) {
		const url = new URL(document.location.href);
		// const messageTypeText = url.searchParams.get("message_type");
		//alert("alert");
		console.log("url" + url);

		const myButton = document.getElementById("runaway-btn");

		["mouseover", "click"].forEach(function (el) {
            //@ts-ignore
			myButton.addEventListener(el, function (event) {
				function getRandomNumber(num) {
					return Math.floor(Math.random() * (num + 1));
				}

				const animateMove = (element, prop, pixels) =>
					anime({
						targets: element,
						[prop]: `${pixels}px`,
						easing: "easeOutCirc",
					});

				const top = getRandomNumber(window.innerHeight - this.offsetHeight);
				const left = getRandomNumber(window.innerWidth - this.offsetWidth);

				animateMove(this, "left", left).play();
				animateMove(this, "top", top).play();
			});
		});
	}

	return {
		pageInit: pageInit,
	};
});
