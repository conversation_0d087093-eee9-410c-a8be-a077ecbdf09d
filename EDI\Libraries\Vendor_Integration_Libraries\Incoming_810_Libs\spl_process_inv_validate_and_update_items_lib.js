/**
 * @NApiVersion 2.x
 */

define(["N/record", "Numeral"], function (record, numeral) {
	function validateAndUpdateItems(
		netSuiteBillRefNumber,
		vendorItems,
		netSuiteBill,
		vendorName
	) {
		var errorLog = [];
		var netSuiteItems = getItems();

		compareItems();
		removeNetSuiteItemsMissingInEdiFile();
		checkNetSuiteForMissingItems();
		saveBill();

		/**********Validate Items Helper Functions**********/
		function _processFail(logMessage, continueProcessing = false) {
			errorLog.push(logMessage);
			if (!continueProcessing) {
				throw logMessage;
			}
		}

		function getItems() {
			var arr = [];
			try {
				itemLineCount = netSuiteBill.getLineCount({
					sublistId: "item",
				});

				for (var x = 0; x < itemLineCount; x++) {
					var itemName = netSuiteBill.getSublistText({
						sublistId: "item",
						fieldId: "vendorname",
						line: x,
					});

					var quantity = netSuiteBill.getSublistText({
						sublistId: "item",
						fieldId: "quantity",
						line: x,
					});
					quantity = numeral(quantity).format("0,00");

					var rate = netSuiteBill.getSublistText({
						sublistId: "item",
						fieldId: "rate",
						line: x,
					});
					rate = numeral(rate).format("0.00");
					var lineIndex = x;
					arr.push({ itemName, quantity, rate, lineIndex });
				}
			} catch (e) {
				_processFail(
					`Items not gotten for ${netSuiteBillRefNumber}. Error: ${e}`
				);
			}
			return arr;
		}

		function compareItems() {
			try {
				if (!vendorItems) {
					_processFail("No items in the vendor bill to compare");
					return;
				}

				netSuiteItems.forEach((netSuiteItem, index) => {
					var matchingVendorItem = vendorItems.find(
						(vendorItem) =>
							netSuiteItem.itemName
								.toUpperCase()
								.split(".")
								.join("")
								.split("-")
								.join("") ==
							vendorItem.itemName
								.toUpperCase()
								.split(".")
								.join("")
								.split("-")
								.join("")
					);
					if (!matchingVendorItem) {
						netSuiteItem.missingInEdiFile = true;
						_processFail(
							`The ${vendorName} EDI file that was sent for ${netSuiteBillRefNumber} is missing ${netSuiteItem.itemName}.`,
							true
						);
					} else {
						compareQuantity();
						compareRate();
					}

					function compareQuantity() {
						var isEqual = _checkIfEqual(
							"quantity",
							netSuiteItem.quantity,
							matchingVendorItem.quantity
						);
						if (!isEqual) {
							_setCorrectValue("quantity", matchingVendorItem.quantity);
						}
					}

					function compareRate() {
						var isEqual = _checkIfEqual(
							"rate",
							netSuiteItem.rate,
							matchingVendorItem.rate
						);
						if (!isEqual) {
							_setCorrectValue("rate", matchingVendorItem.rate);
						}
					}

					function _checkIfEqual(propName, netSuiteObjProp, vendorObjProp) {
						if (
							numeral(netSuiteObjProp).format("0.00") !=
							numeral(vendorObjProp).format("0.00")
						) {
							_processFail(
								`The ${propName} that ${vendorName} sent for ${
									netSuiteItem.itemName
								} doesn't match what's in NetSuite.
                                   What ${vendorName} sent: ${numeral(
									vendorObjProp
								).format("0,0.00")}
                                   What we have in NetSuite: ${netSuiteObjProp}`,
								true
							);
							return false;
						}
						return true;
					}

					function _setCorrectValue(propName, netSuiteObjProp) {
						netSuiteBill.setSublistValue({
							sublistId: "item",
							fieldId: propName,
							line: index,
							value: netSuiteObjProp,
						});
					}
				});
			} catch (e) {
				_processFail(
					`Error comparing items for ${netSuiteBillRefNumber}. Error: ${e}`
				);
			}
		}

		function removeNetSuiteItemsMissingInEdiFile() {
			try {
				netSuiteItems = netSuiteItems.sort((a, b) => b.lineIndex - a.lineIndex); //Descending
				netSuiteItems.forEach((netSuiteItem) => {
					if (netSuiteItem.missingInEdiFile) {
						netSuiteBill.removeLine({
							sublistId: "item",
							line: netSuiteItem.lineIndex,
						});
					}
				});
			} catch (error) {
				_processFail(
					`Error removing items that are missing on the EDI file from NetSuite bill ${netSuiteBillRefNumber}. Error: ${error}`,
					true
				);
			}
		}

		function checkNetSuiteForMissingItems() {
			try {
				var itemsMissingInNetSuite = _.differenceBy(
					vendorItems,
					netSuiteItems,
					"itemName"
				);

				if (itemsMissingInNetSuite.length > 0) {
					itemsMissingInNetSuite.forEach((item) => {
						_processFail(
							`${netSuiteBillRefNumber} in NetSuite is missing ${item.itemName}`,
							true
						);
					});
				}
			} catch (e) {
				_processFail(
					`Error checking ${vendorName} invoice for missing items ${netSuiteBillRefNumber}. Error: ${e}`,
					true
				);
			}
		}

		function saveBill() {
			try {
				return netSuiteBill.save();
			} catch (error) {
				processFail(
					`New bill for ${billObj.poNumber} not saved. Error: ${error}.`
				);
			}
		}

		return errorLog;
	}

	return {
		validateAndUpdateItems: validateAndUpdateItems,
	};
});
