/**
 * @description Uses 'N/url' to account for multiple environments when getting the URL for IR and PO records
 * 
 * </br><b>Called By:</b> vlmd_wms_receipt_posted_after_load_cs
 *
 * @NApiVersion 2.1
 * @NScriptType Restlet
 * 
 * <AUTHOR>
 * @module vlmd_wms_get_ir_po_urls_rl
 */
define([
    "require",
    "N/url",
    "../../../Classes/vlmd_custom_error_object"
],(require) => {
        const url = require("N/url");
        const CustomErrorObject = require("../../../Classes/vlmd_custom_error_object");
        const customErrorObject = new CustomErrorObject();

        const get = (requestBody) => {
            log.audit('requestBody', requestBody);

            try {  
                let { purchaseOrderId, itemReceiptId } = requestBody;

                if(typeof purchaseOrderId == 'undefined') {
                    throw customErrorObject.updateError({
                        errorType: customErrorObject.ErrorTypes.MISSING_PARAM,
                        summary: "MISSING_PARAMETER",
                        details: `Parameter purchaseOrderId is missing.`,
                    });
                }
                if(typeof itemReceiptId == 'undefined') {
                    throw customErrorObject.updateError({
                        errorType: customErrorObject.ErrorTypes.MISSING_PARAM,
                        summary: "MISSING_PARAMETER",
                        details: `Parameter itemReceiptId is missing.`,
                    });
                }

                let domainUrlStr = url.resolveDomain({
                    hostType: url.HostType.APPLICATION
                });

                let itemReceiptPathStr = url.resolveRecord({
                    recordType: 'itemreceipt',
                    recordId: itemReceiptId,
                    isEditMode: false
                });

                let purchaseOrderPathStr = url.resolveRecord({
                    recordType: 'purchaseorder',
                    recordId: purchaseOrderId,
                    isEditMode: false
                });

                return {
                    "data": {
                        itemReceiptUrlStr: `https://${domainUrlStr + itemReceiptPathStr}`,
                        purchaseOrderUrlStr: `https://${domainUrlStr + purchaseOrderPathStr}`
                    }
                };
            } catch (err) {
				customErrorObject.throwError({
					summaryText: "ERROR_GETTING_URLS",
					error: err,
				});
            }
        }

        return {get}
    });
