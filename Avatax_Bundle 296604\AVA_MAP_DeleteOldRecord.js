/******************************************************************************************************
    Script Name - AVA_MAP_DeleteOldRecord.js
    Company -     Avalara Technologies Pvt Ltd.
******************************************************************************************************/

/**
 *@NApiVersion 2.0
 *@NScriptType MapReduceScript
 */

define(['N/record', 'N/search', 'N/runtime'],
	function(record, search, runtime){
		function getInputData(context){
			try{
				var deleteOldRecordSearchObj = [];
				var deleteOldRecordDetails = runtime.getCurrentScript().getParameter({
					name: 'custscript_ava_deleteoldrecorddetails'
				});
				
				if(deleteOldRecordDetails){
					deleteOldRecordDetails = JSON.parse(deleteOldRecordDetails);
					
					if(deleteOldRecordDetails.newbatchid){
						record.submitFields({
							type: 'customrecord_avadeleteoldrecord',
							id: deleteOldRecordDetails.newbatchid,
							values: {
								custrecord_ava_deleteoldrecordstatus: 'In Progress'
							},
							options: {
								enableSourcing: false,
								ignoreMandatoryFields: true
							}
						});
						var type = deleteOldRecordDetails.recordtype;
						var startDate = deleteOldRecordDetails.startdate;
						var endDate = deleteOldRecordDetails.enddate;
						startDate += ' 12:00 am';
						endDate += ' 11:59 pm';
						var filtersArray = [];
						var columnsArray = [];
						filtersArray.push(["created", "within", startDate, endDate]);
						columnsArray.push(search.createColumn({
							name: "internalid"
						}));
						deleteOldRecordSearchObj = getSearchResults({
							type: type,
							filters: filtersArray,
							columns: columnsArray
						});
					}
				}
			}
			catch(e){
				log.error('getInputData', 'ERROR NAME = ' + e.name + ', ERROR TYPE = ' + e.type + ', ERROR MESSAGE = ' + e.message);
			}
			
			return deleteOldRecordSearchObj;
		}

		function map(context){
			try{
				var value = JSON.parse(context.value);
				record.delete({
					type: value.recordType,
					id: value.id
				});
			}
			catch(e){
				log.error('map', 'ERROR NAME = ' + e.name + ', ERROR TYPE = ' + e.type + ', ERROR MESSAGE = ' + e.message);
			}
		}

		function summarize(context){
			try{
				var deleteOldRecordDetails = runtime.getCurrentScript().getParameter({
					name: 'custscript_ava_deleteoldrecorddetails'
				});
				deleteOldRecordDetails = JSON.parse(deleteOldRecordDetails);

				if(deleteOldRecordDetails.newbatchid){
					var deleteOldRecordBatchId = record.submitFields({
						type: 'customrecord_avadeleteoldrecord',
						id: deleteOldRecordDetails.newbatchid,
						values: {
							custrecord_ava_deleteoldrecordstatus: 'Completed'
						},
						options: {
							enableSourcing: false,
							ignoreMandatoryFields: true
						}
					});
					log.debug('summarize', 'Status Completed - deleteOldRecordBatchId = ' + deleteOldRecordBatchId);
				}
			}
			catch(e){
				log.error('summarize', 'ERROR NAME = ' + e.name + ', ERROR TYPE = ' + e.type + ', ERROR MESSAGE = ' + e.message);
			}
		}

		function getSearchResults(options){
			try{
				var recordType = options.type;
				var filter = options.filters;
				var cols = options.columns;
				var arrResults = [];
				var count = 1000;
				var start = 0;
				var end = 1000;
				var searchObj = search.create({
					type: recordType,
					filters: filter,
					columns: cols
				});
				var searchResult = searchObj.run();
				
				while(count == 1000){
					var results = searchResult.getRange(start, end);
					arrResults = arrResults.concat(results);
					start = end;
					end += 1000;
					count = results.length;
				}
				
				return arrResults;
			}
			catch(e){
				log.error('getSearchResults', 'ERROR NAME = ' + e.name + ', ERROR TYPE = ' + e.type + ', ERROR MESSAGE = ' + e.message);
			}
		}
		
		return{
			getInputData: getInputData,
			map: map,
			summarize: summarize
		};
	}
);