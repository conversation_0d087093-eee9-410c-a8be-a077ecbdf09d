/**
 * @NApiVersion 2.1
 */

//@ts-ignore
define(["N/log", "Moment", "Numeral"], function (log, moment, numeral) {
	function getAsnAsEDI(partnerValues, asnObj) {
		var hlSegmentCtr = 1;
		var linCtr = 0;

		var beginningOfFile = `ISA*00*          *00*          *SenderQualifier*ISASenderId*ReceiverQualifier*ISAReceiverId*ISADate*Time*U*EdiVersion*ControlNumber*0*P*|~
GS*PR*GSSenderId*GSReceiverId*GSDate*Time*ControlNumber*X*EdiVersion0~
ST*856*0001~`;
		formatDateTimeEdiVersion();
		formatSender();
		formatReceiver();

		var bsn = `BSN*BSN_01*BSN_02*BSN_03*BSN_04~`;
		formatBSN();

		//HL03='S' for 'Shipment'
		var hlShipment_group = `HL*HL_01**S~
TD1**TD1_02*****TD1_07*TD1_08~
TD5**TD5_02*TD5_03**TD5_05~
DTM*DTM_01*DTM_02~
N1*ST*N1_02*N1_03*N1_04~
N3*N3_01~
N4*N4_01*N4_02*N4_03~
`;
		format_hlShipment();

		//HL03='O' for 'Order'
		var hlOrder_group = `HL*HL_01*1*O~
PRF*PRF_01~
`;
		format_hlOrder();

		var hlItems_group = getHlItemsSegments();

		var endOfFile = `CTT*TotalLineItems~
SE*NumberOfSegments*0001~
GE*1*ControlNumber~
IEA*1*ControlNumber~`;

formatEnd();

var ediFile = `${beginningOfFile}
${bsn}
${hlShipment_group}${hlOrder_group}${hlItems_group}${endOfFile}
`;

		formatFormatting();
		formatSegmantNumber();
		formatControlNumber();
		validateNoUndefinedOrInvalid();

		return ediFile;

		/**Write Outgoing 856 Helper Functions */
		function formatDateTimeEdiVersion() {
			var dateTimeInfo = partnerValues.isaGsInfo;

			dateTimeInfo.forEach(function (x) {
				beginningOfFile = beginningOfFile.split(x.name).join(x.value);
			});
		}

		function formatSender() {
			var senderInfo = partnerValues.senderInfo;

			senderInfo.forEach(function (x) {
				beginningOfFile = beginningOfFile.split(x.name).join(x.value);
			});
		}

		function formatReceiver() {
			var receiverInfo = partnerValues.receiverInfo;

			receiverInfo.forEach(function (x) {
				beginningOfFile = beginningOfFile.split(x.name).join(x.value);
			});
		}

		function formatBSN() {
			var bsnInfo = [
				{
					name: "BSN_01",
					value: "00",
				},
				{
					name: "BSN_02",
					value: "01",
				},
				{
					name: "BSN_03",
					value: asnObj.documentInfo.shipmentDate,
				},
				{
					name: "BSN_04",
					value: asnObj.documentInfo.shipmentTime,
				},
			];

			bsnInfo.forEach(function (x) {
				bsn = bsn.replace(x.name, x.value);
			});
		}

		//HL>S
		function format_hlShipment() {
			//HL*HL_01**S~
			var hls = [
				{
					name: "HL_01",
					value: hlSegmentCtr,
				},
			];

			hls.forEach(function (x) {
				hlShipment_group = hlShipment_group.replace(x.name, x.value);
			});

			//TD1**TD1_02*****TD1_07*TD1_08~ //An optional segment
			var td1 = [
				{
					name: "TD1_02",
					value: "PKG", //'PKG' for Package
				},
				{
					name: "TD1_07",
					value: asnObj.documentInfo.shipmentWeight,
				},
				{
					name: "TD1_08",
					value: "01", //Unit or Basis for Measurement Code. '01' for Actual Pounds
				},
			];

			td1.forEach(function (x) {
				hlShipment_group = hlShipment_group.replace(x.name, x.value);
			});

			//TD5**TD5_02*TD5_03**TD5_05~
			var td5 = [
				{
					name: "TD5_02",
					value: "2",
				},
				{
					name: "TD5_03",
					value: asnObj.documentInfo.shipmentMethod,
				},
				{
					name: "TD5_05",
					value: "SH",
				},
			];

			td5.forEach(function (x) {
				hlShipment_group = hlShipment_group.replace(x.name, x.value);
			});

			//DTM*DTM_01*DTM_02~
			var dtm = [
				{
					name: "DTM_01",
					value: "011", //011 for 'SHIPPED'
				},
				{
					name: "DTM_02",
					value: asnObj.documentInfo.shipmentDate,
				},
			];

			dtm.forEach(function (x) {
				hlShipment_group = hlShipment_group.replace(x.name, x.value);
			});

			// N1*ST*N1_02*N1_03*N1_04~
			var n1 = [
				{
					name: "N1_02",
					value: asnObj.address.addressShipTo,
				},
				{
					name: "N1_03",
					value: "ZZ", //'ZZ' for Mutually Defined
				},
				{
					name: "N1_04",
					value: asnObj.documentInfo.facilityNumber,
				},
			];

			n1.forEach(function (x) {
				hlShipment_group = hlShipment_group.replace(x.name, x.value);
			});

			// N3*N3_01~
			var n3 = [
				{
					name: "N3_01",
					value: asnObj.address.streetAddress,
				},
			];

			n3.forEach(function (x) {
				hlShipment_group = hlShipment_group.replace(x.name, x.value);
			});

			// N4*N4_01*N4_02*N4_03~ 
			var n4 = [
				{
					name: "N4_01",
					value: asnObj.address.city,
				},
				{
					name: "N4_02",
					value: asnObj.address.state,
				},
				{
					name: "N4_03",
					value: asnObj.address.zip,
				},
			];

			n4.forEach(function (x) {
				hlShipment_group = hlShipment_group.replace(x.name, x.value);
			});

			hlSegmentCtr++;
			return hlShipment_group;
			
		}

		function format_hlOrder() {
			//HL*HL_01*1*O~
			var hlo = [
				{
					name: "HL_01",
					value: hlSegmentCtr,
				},
			];

			hlo.forEach(function (x) {
				hlOrder_group = hlOrder_group.replace(x.name, x.value);
			});

			//PRF*PRF_01~
			var prf = [
				{
					name: "PRF_01",
					value: asnObj.documentInfo.poNumber,
				},
			];

			prf.forEach(function (x) {
				hlOrder_group = hlOrder_group.replace(x.name, x.value);
			});

			//REF*08*REF_02~
			var trackingNumberVals = asnObj.documentInfo.trackingNumbers;
			var refSegments = "";

			//Iterate over each tracking # and create an REF segment for each
			trackingNumberVals.forEach(function (trackingNumberVaue) {
				var ref08Val = `REF*08*REF_02~`; //REF*08 - '08' for Carrier Assigned Package Identification Number

				var ref = [
					{
						name: "REF_02",
						value: trackingNumberVaue,
					},
				];

				ref.forEach(function (x) {
					ref08Val = ref08Val.replace(x.name, x.value);
				});

				refSegments += ref08Val + '\n';
			});

			hlOrder_group += refSegments;
			hlSegmentCtr++;
		}

		function getHlItemsSegments() {
			var itemVals = asnObj.items;
			var hliSegments = "";



			itemVals.forEach(function (itemObj) {
				linCtr++;

				//HL03='I' for 'Item'
				var hlItem_group = `HL*HL_01*2*I~
LIN*LIN_01*LIN_02*LIN_03*LIN_04*LIN_05~
SN1*SN1_01*SN1_02*SN1_03~
PID*PID_01****PID_05~
`;

				//HL*HL_01*2*I~
				var hli = [
					{
						name: "HL_01",
						value: hlSegmentCtr,
					},
				];

				hli.forEach(function (x) {
					hlItem_group = hlItem_group.replace(x.name, x.value);
				});

				//LIN*LIN_01*LIN_02*LIN_03*LIN_04*LIN_05~
				var lin = [
					{
						name: "LIN_01",
						value: linCtr,
					},
					{
						name: "LIN_02",
						value: "VN", //Vendor’s Item Number or Vendor Order Number
					},
					{
						name: "LIN_03",
						value: itemObj.itemName,
					},
					{
						name: "LIN_04",
						value: "IN",
					},
					{
						name: "LIN_05",
						value: itemObj.itemName,
					},
				];

				lin.forEach(function (x) {
					hlItem_group = hlItem_group.replace(x.name, x.value);
				});

				//SN1*SN1_01*SN1_02*SN1_03~
				var sn1 = [
					{
						name: "SN1_01",
						value: itemObj.po1LineNumFinal,
					},
					{
						name: "SN1_02",
						value: itemObj.itmquantity,
					},
					{
						name: "SN1_03",
						value: itemObj.itmUom,
					},
				];

				sn1.forEach(function (x) {
					hlItem_group = hlItem_group.replace(x.name, x.value);
				});

				//PID*PID_01****PID_05~
				var pid = [
					{
						name: "PID_01",
						value: "F", //'F' for Free Form
					},
					{
						name: "PID_05",
						value: itemObj.itmDesc,
					},
				];

				pid.forEach(function (x) {
					hlItem_group = hlItem_group.replace(x.name, x.value);
				});

				hliSegments += hlItem_group;
				hlSegmentCtr++;
			});

			return hliSegments;
		}

		function formatFormatting() {
			var formattingInfo = partnerValues.formattingInfo;

			formattingInfo.forEach(function (x) {
				ediFile = ediFile.split(x.templateValue).join(x.partnerValue);
			});
		}

		function formatSegmantNumber() {
			var segments = ediFile.match(/[\n]/gm);

			if (!segments) {
				throw "Error matching segments, no segments returned.";
			}

			var numberOfSegments = segments.length - 4;

			ediFile = ediFile.replace(
				"NumberOfSegments",
				numberOfSegments.toString()
			);
		}

		function formatControlNumber() {
			ediFile = ediFile
				.split("ControlNumber")
				.join(asnObj.documentInfo.controlNumber);
		}

		function formatEnd() {
			var endInfo = [
				{
					name: "TotalLineItems",
					value: linCtr,
				},
			];

			endInfo.forEach(function (x) {
				endOfFile = endOfFile.replace(x.name, x.value);
			});

			ediFile += endOfFile;
		}

		function validateNoUndefinedOrInvalid() {
			if (
				beginningOfFile.search("undefined") != -1 ||
				beginningOfFile.search("Invalid") != -1
			) {
				//Search returned the result found instead of -1 when no match was found
				ediFile = ""; //Returning ediFile as an empty string so that the value is false
			}
		}
	}

	return {
		getAsnAsEDI,
	};
});
