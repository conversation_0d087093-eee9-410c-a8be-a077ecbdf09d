/**
 * @description Updates the email field on invoices based on customer's custom email field
 *
 * </br><b>Deployed On:</b> Invoices
 * </br><b>Execution Context:</b> ALL
 * </br><b>Event Type/Mode:</b> ALL
 * </br><b>Entry Points:</b> beforeSubmit
 *
 * @NApiVersion 2.x
 * @NScriptType UserEventScript
 *
 * <AUTHOR> HuviBoxer
 * @module vlmd_invoice_multiple_email_addresses_ue
 */

define(["N/search", "N/log"], function (search, log) {
  const notificationFieldId = "custentity_2663_email_address_notif";
  const dontOverrideEmailFieldId = "custbody_vlmd_dont_override_invc_email";
  
  /**
   * Updates the email field with the customer's notification email
   * @param {Object} currentRecord - The current record
   * @returns {void}
   */
  function updateEmailField(currentRecord) {
    try {
      const dontOverrideEmail = currentRecord.getValue(
        dontOverrideEmailFieldId
      );
      if (dontOverrideEmail === true || dontOverrideEmail === "T") return;

      const entityId = currentRecord.getValue("entity");
      if (!entityId) return;

      const notificationEmail = getCustomerNotificationEmail(
        entityId,
        notificationFieldId
      );
      if (!notificationEmail) return;

      const currentEmail = currentRecord.getValue("email");
      if (currentEmail !== notificationEmail) {
        log.debug(
          "Overriding native email with notification email",
          "Current Email: " +
            currentEmail +
            ", Notification Email: " +
            notificationEmail
        );

        currentRecord.setValue({
          fieldId: "email",
          value: notificationEmail,
        });
      }
    } catch (err) {
      log.error({
        title: "Error updating email field",
        details: err,
      });
    }
  }

  /**
   * Gets the notification email address for the customer
   * @param {string|number} customerId - The customer ID
   * @param {string} fieldId - The notification field ID
   * @returns {string|null} The notification email or null
   */
  function getCustomerNotificationEmail(customerId, fieldId) {
    try {
      const lookupResult = search.lookupFields({
        type: search.Type.CUSTOMER,
        id: customerId,
        columns: [fieldId],
      });

      if (!lookupResult || !lookupResult[fieldId]) {
        return null;
      }

      var emailAddress = lookupResult[fieldId];
      if (!emailAddress) return null;

      // Clean up email format - replace commas with semicolons and remove spaces
      emailAddress = emailAddress
        .toString()
        .split(",")
        .join(";")
        .split(" ")
        .join("");

      return emailAddress;
    } catch (err) {
      log.error({
        title: "Error retrieving customer notification email",
        details: err,
      });

      return null;
    }
  }

  /**
   * Function that executes when the page completes loading - makes sure the email is set correctly in all contexts
   * @param {Object} context - The context object
   * @param {Record} context.newRecord - New form record
   * @returns {void}
   */
  function beforeSubmit(context) {
    updateEmailField(context.newRecord);
  }

  return {
    beforeSubmit: beforeSubmit,
  };
});
