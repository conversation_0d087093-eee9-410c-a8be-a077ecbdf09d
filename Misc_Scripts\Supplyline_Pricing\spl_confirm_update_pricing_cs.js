/**
 * @NApiVersion 2.1
 * @NScriptType ClientScript
 * @NAmdConfig /SuiteScripts/config.json
 * @param {import ("N/types")}
 * <AUTHOR>
 * @description CS script to confirm pricing changes from price tier SL
 * @module spl_confirm_update_pricing_cs
 */

define(["require", "N/log"], function (require) {
	const log = require("N/log");

	function saveRecord() {
		var checked = confirm("Are you sure you want to update this price tier?");
		if (checked) {
			return true;
		} else {
			return false;
		}
	}

	return {
		saveRecord,
	};
});
