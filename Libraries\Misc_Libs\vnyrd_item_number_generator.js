function getGeneratedItemNumber(itemCategory) {
	var lastItemName = getLastItemName(itemCategory);
	var lastItemNumber = formatLastItemName(lastItemName);
	var nextItemNumber = getNextItemNumber(lastItemNumber);
	var itemNumberIsUnique = validateNextItemNumber(nextItemNumber);

	while (!itemNumberIsUnique) {
		nextItemNumber = getNextItemNumber(itemNumber);
		itemNumberIsUnique = validateNextItemNumber(nextItemNumber);
	}

	var validatedItemNumber = nextItemNumber;
	log.debug("nextItemNumber", nextItemNumber);

	return validatedItemNumber;

	function getLastItemName(itemCategory) {
		var itemSearchObj = search.create({
			type: "item",
			filters: [
				["subsidiary", "anyof", "22"], //Vineyard
				"AND",
				["custitem_spos_sposcategory", "is", "F"],
				"AND",
				["class", "anyof", itemCategory],
			],
			columns: [
				search.createColumn({
					name: "itemid",
					sort: search.Sort.DESC, //Sorted descending so that the last item name will display in the results first
					label: "Last Name",
				}),
			],
		});

		var lastItemNameUsed = itemSearchObj.run().getRange({
			start: 0,
			end: 10,
		})[0]; //Assinging only the first result in the array

		var lastItemNameResult = firstSearchResult.itemid.getValue;
		log.debug("lastItemNameResult", lastItemNameResult);

		return lastItemNameResult;
	}

	function formatLastItemName(lastItemName) {
		var regex = new RegExp("d+"); //Pulling on the item numbers since the item name is returned as Category : ItemNumber
		var lastItemNumber = lastItemName.match(regex);

		log.debug("lastItemNumber", lastItemNumber);

		return lastItemNumber;
	}

	function getNextItemNumber(lastItemNumber) {
		return lastItemNumber + 1;
	}

	function validateNextItemNumber(nextItemNumber) {
		var itemSearchObj = search.create({
			type: "item",
			filters: [
				["subsidiary", "anyof", "22"], //Vineyard
				"AND",
				["custitem_spos_sposcategory", "is", "F"],
			],
			columns: [
				search.createColumn({ name: "itemid", label: "Name" }),
				search.createColumn({ name: "class", label: "Product Category" }),
			],
		});

		itemSearchObj.run().each(function (result) {
			if (result.itemid.includes(nextItemNumber)) {
				itemNumberIsUnique = false;
			} else {
				itemNumberIsUnique = true;
			}
		});

		return itemNumberIsUnique;
	}
}
