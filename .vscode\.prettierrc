{"arrowParens": "avoid", "bracketSameLine": false, "bracketSpacing": true, "cursorOffset": -1, "embeddedLanguageFormatting": "auto", "endOfLine": "lf", "experimentalOperatorPosition": "end", "experimentalTernaries": false, "filepath": "", "htmlWhitespaceSensitivity": "css", "insertPragma": false, "jsxSingleQuote": false, "objectWrap": "preserve", "overrides": [], "parser": "", "plugins": [], "printWidth": 80, "proseWrap": "preserve", "quoteProps": "consistent", "rangeStart": 0, "requirePragma": false, "semi": true, "singleAttributePerLine": false, "singleQuote": false, "tabWidth": 2, "trailingComma": "es5", "useTabs": false, "vueIndentScriptAndStyle": false}