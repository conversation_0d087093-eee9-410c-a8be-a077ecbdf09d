/**
 * @description Updates the rates on the related item receipts if they don't match the rate on the bill
 * Moving this to depracated since the functionality will be handled on the MR, 
 * This script was created as a stepping stone to get to the MR
 * 
 * </br><b>Deployed On:</b> BRDG Vendor bills
 * </br><b>Excectuion Context:</b> ALL
 * </br><b>Event Type/Mode:</b> ALL
 * </br><b>Entry Points:</b> afterSubmit
 * 
 * @NApiVersion 2.1
 * @NScriptType UserEventScript
 * @NAmdConfig /SuiteScripts/config.json
 * 
 * <AUTHOR>
 * @module brdg_update_item_receipt_ue
 */

//@ts-ignore
define([
	"N/log",
	"BridgeHelperFunctionsLib",
	"UpdateItemReceiptFunctionsLib",
], function (log, BridgeHelperFunctionsLib, functionsLib) {
	function afterSubmit(context) {
		try {
			const netSuiteBillRecord = context.oldRecord
				? context.oldRecord
				: context.newRecord;

			if (!netSuiteBillRecord) {
				log.error("No bill id found.");
				return true; //The SQL Query needs an id to run, so return if no bill id found.
			}

			const billId = netSuiteBillRecord.id;

			const subsidiariesArr = netSuiteBillRecord.getValue({
				fieldId: "subsidiary"
			});
			const bridgeSubsidiaries = BridgeHelperFunctionsLib.getBridgeSubsidiaries();
			const isBridgeSubsidiary = BridgeHelperFunctionsLib.isBridgeSubsidiary(subsidiariesArr, bridgeSubsidiaries);

			if (!isBridgeSubsidiary) {
				return true;
			}

			const sqlResults = functionsLib.runSqlQuery(billId);

			if (sqlResults.length <= 0) {
				log.debug("Results", `No item receipt found for bill. (ID: ${billId})`);
				return;
			}

			const itemLinesObjArrWithCorrectRate =
				functionsLib.calculateCorrectRates(sqlResults);

			const itemsWithRateDiscrepancies = functionsLib.getRateDiscrepancies(
				itemLinesObjArrWithCorrectRate
			);

			if (itemsWithRateDiscrepancies.length <= 0) {
				log.debug("Results", "No item receipts to update.");
				return true;
			}

			const resultsObj = functionsLib.updateItemReceipt(
				itemsWithRateDiscrepancies
			);

			functionsLib.logErrors(resultsObj);
			// functionsLib.sendEmail(resultsObj.errorLog); @Leah-Aschkenasy email miri
		} catch (e) {
			log.error(`Error saving bill (ID: ${billId}):`, e);
		}
		return true;
	}

	return {
		afterSubmit,
	};
});
