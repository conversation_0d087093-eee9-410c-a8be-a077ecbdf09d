/******************************************************************************************************
	Script Name - AVA_BIS_BundleInstallation.js
	Company -     Avalara Technologies Pvt Ltd.
******************************************************************************************************/

/**
 *@NApiVersion 2.0
 *@NScriptType BundleInstallationScript
*/

define(['N/search', 'N/record', 'N/runtime'],
	function(search, record, runtime){
		function beforeInstall(params){
			var bundleId = runtime.getCurrentScript().bundleIds[0];
			
			if(runtime.isFeatureInEffect({feature: 'tax_overhauling'}) == false && runtime.isFeatureInEffect({feature: 'Subsidiaries'}) == false && bundleId != 1894){
				throw new Error("Installation failed because you're trying to install an incorrect bundle. Uninstall the existing bundle and install the 'Avalara AvaTax Basic' bundle.");
			}
			else if(runtime.isFeatureInEffect({feature: 'tax_overhauling'}) == true && bundleId != 280313){
				throw new Error("Installation failed because you're trying to install an incorrect bundle. Uninstall the existing bundle and install the 'Avalara AvaTax for SuiteTax' bundle.");
			}
		}
		
		function afterUpdate(params){
			var searchRecord = search.create({
				type: 'customrecord_avaconfig'
			});
			var searchResult = searchRecord.run();
			searchResult = searchResult.getRange({
				start: 0,
				end: 5
			});
			
			if(searchResult != null && searchResult.length > 0){
				for(var i = 0; i < searchResult.length; i++){
					var configRecord = record.load({
						type: 'customrecord_avaconfig',
						id: searchResult[i].id
					});
					
					var serviceUrl = configRecord.getValue('custrecord_ava_url');
					if(serviceUrl != null && serviceUrl.length > 1){
						serviceUrl = (serviceUrl.search('development') != -1) ? '1' : '0';
						configRecord.setValue({
							fieldId: 'custrecord_ava_url',
							value: serviceUrl
						});
					}
					
					if(parseFloat(params.fromVersion) < 7.6){
						configRecord.setValue({
							fieldId: 'custrecord_ava_configflag',
							value: false
						});
					}
					
					configRecord.save();
				}
			}
		}
		
		return{
			beforeInstall: beforeInstall,
			afterUpdate: afterUpdate
		};
	}
);