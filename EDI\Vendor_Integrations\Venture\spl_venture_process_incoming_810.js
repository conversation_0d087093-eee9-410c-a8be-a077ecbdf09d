/**
 * @NApiVersion 2.1
 * @NScriptType scheduledscript
 * @NAmdConfig /SuiteScripts/config.json
 */

define([
    'N/sftp',
    'ProcessIncoming810Lib',
    'LoDash',
    'PapaParse'

],
    function (
        sftp,
        processInvLib,
        _,
        papaParse
    ) {
        function execute(context) {
            var hostKey = "AAAAB3NzaC1yc2EAAAABIwAAAIEA2YIvZPcsWA3jP6yq5J" +
                "5XYunNySNspUZUhtzxXoePDZEhxJFIF/am9o+15CqO4hlyEDni0m+VW4" +
                "LVSoiUnj31xJis91EcYrUeCewIcSwof4Xs05ghth/2SKn0cITsR5hOeST" +
                "l4jFsMf9oK0+b2gr4jdV4fria4E/eF2u0qQArWuc=";

            var vendorData = {
                prodGuid: 'ac4fde2468944f40a40116e1ad310898',
                sandboxGuid: '********************************',
                prodDirectory: '/users/Venture/Invoices',
                testDirectory: '/users/Venture/Test/Invoices',
                vendorInternalId: 762,
                vendorName: 'Venture'
            }

            var errorLog = [];
            var rowsNotAdded = [];

            var connection = createConnection();
            var directoryList = getDirectoryList();
            var fileName = getFileName();
            if (fileName) {
                var downloadConnection = getDownloadConnection()
                var fileContent = getFileContent();
                var invoiceObjArr = getInvoiceObjArr();
                log.debug({
                    title: 'inv obj arr',
                    details: invoiceObjArr
                });

                if (rowsNotAdded.length > 0) {
                    rowsNotAddedText = 'The items at the following rows are incorrect and were not added to the invoices: ';
                    rowsNotAdded.forEach(row => rowsNotAddedText += row + ', ');
                    errorLog.push(rowsNotAddedText);
                }
                if (invoiceObjArr.length > 0) {
                    processInvoices();
                } else {
                    log.error({
                        title: 'No Contents in Invoice Object Array',
                        details: errorLog
                    });
                }
            } else {
                log.debug('Script ran, no results found');
            }

            function createConnection() {
                return sftp.createConnection({
                    username: 'FTPadmin',
                    passwordGuid: vendorData.prodGuid, //production account
                    //passwordGuid: vendorData.sandboxGuid, //sandbox account,
                    url: '************',
                    hostKey: hostKey,
                    directory: vendorData.testDirectory
                });
            }

            function getDirectoryList() {
                return connection.list({
                    path: ''
                });
            }

            function getFileName() {
                try {
                    for (var x = 0; x < directoryList.length; x++) {
                        if (!directoryList[x].directory) {
                            return directoryList[x].name;
                        }
                    };
                } catch (error) {
                    throw error;
                }
            }

            function getDownloadConnection() {
                return sftp.createConnection({
                    username: 'FTPadmin',
                    passwordGuid: vendorData.prodGuid, //production account
                    //passwordGuid: vendorData.sandboxGuid, //sandbox account,
                    url: '************',
                    hostKey: hostKey
                });
            }

            function getFileContent() {
                try {
                    var downloadedFile = downLoadFile();
                    var fileContent = getContent();
                    return fileContent;

                    function downLoadFile() {
                        return downloadConnection.download({
                            directory: vendorData.testDirectory,
                            filename: fileName
                        });
                    }

                    function getContent() {
                        var content = downloadedFile.getContents();
                        return {
                            content: content,
                            name: fileName
                        };
                    }
                } catch (error) {
                    throw error;
                }
            }

            function getInvoiceObjArr() {
                var objArr = [];
                var lines = getLines();
                log.debug({
                    title: 'lines',
                    details: lines
                });
                prcoessLinesAndAddObjects();
                return objArr;


                function getLines() {
                    var linesObj = papaParse.parse(fileContent.content, {
                        header: true,
                        skipEmptyLines: true
                    });

                    if (_.isEmpty(linesObj)) {
                        throw 'Error geting file lines';
                    }
                    return linesObj.data;
                }

                function prcoessLinesAndAddObjects() {
                    var invoiceObj = {};

                    lines.forEach(function (line, index) {
                        log.debug('line number ' + line['Line Nbr.']);
                        log.debug({
                            title: 'obj arr',
                            details: objArr
                        })
                        log.debug({
                            title: 'inv obj',
                            details: invoiceObj
                        })
                        if (line['Line Nbr.'] == 1) { //if is a new invoice
                            log.debug('is first line of po index ' + index);
                            if (index != 0) { //add obj to arr if isn't the first row so already created
                                log.debug('already created the obj');
                                pushObjectToArray(invoiceObj);
                            }
                            invoiceObj = createNewInvoiceObj(line);
                            addItem(line, index);
                        } else {
                            log.debug('isnt first line of po');
                            addItem(line, index);
                        }
                        if (index == lines.length - 1) {//is last line so add to arr bec finished looping
                            pushObjectToArray(invoiceObj);
                        }
                    });



                    function createNewInvoiceObj(line) {
                        log.debug('creating new obj. ')
                        return {
                            poNumber: line['Customer Order Nbr.'],
                            total: line['Total Amount'],
                            invoiceNumber: line['Reference Nbr.'],
                            items: []
                        }
                    }

                    function addItem(line, index) {
                        var item = {
                            itemName: line['Inventory ID'].trim(),
                            quantity: line['Quantity'],
                            rate: line['Unit Price']
                        }
                        log.debug({
                            title: 'item',
                            details: item
                        })
                        if (item.itemName) {
                            log.debug('item has name at line ' + line);
                            invoiceObj.items.push(item);
                        } else {
                            log.debug('item missing name at line ' + line);
                            rowsNotAdded.push(index);
                        }
                    }

                    function pushObjectToArray(invoiceObj) {
                        if (validatepoNumber(invoiceObj)) {
                            objArr.push(invoiceObj);
                        } else {
                                    /*throw*/ errorLog.push(
                            `Invoice ${invoiceObj.invoiceNumber} wasn't added to the array because PO number ${invoiceObj.poNumber} isn't valid`
                        );
                        }
                    }

                    function validatepoNumber(invoiceObj) {

                        if (invoiceObj.poNumber === "") {
                            //log.debug({
                            //    title: 'inv obj',
                            //    details: invoiceObj
                            //});
                            //log.debug({
                            //    title: 'is empty',
                            //    details: invoiceObj.poNumber == ""
                            //});
                            return false;
                        }
                        if (/\s/g.test(invoiceObj.poNumber)) {
                            //log.debug({
                            //    title: 'inv obj',
                            //    details: invoiceObj
                            //});
                            //log.debug({
                            //    title: 'contains space',
                            //    details: /\s/g.test(invoiceObj.poNumber)
                            //});
                            return false;
                        }
                        if (/-\d$/.test(invoiceObj.poNumber)) {
                            //log.debug({
                            //    title: 'inv obj',
                            //    details: invoiceObj
                            //});
                            //log.debug({
                            //    title: 'contains dash',
                            //    details: /-\d$/.test(invoiceObj.poNumber)
                            //});
                            return false;
                        }
                        //if (!invoiceObj.poNumber.toUpperCase().startsWith('PO')) {
                        //    //log.debug({
                        //    //    title: 'inv obj',
                        //    //    details: invoiceObj
                        //    //});
                        //    //log.debug({
                        //    //    title: 'doesnt start with PO',
                        //    //    details: invoiceObj.poNumber.toUpperCase().startsWith('PO')
                        //    //})
                        //    return false
                        //}

                        return true;
                    }
                }
            }

            function processInvoices() {

                log.debug({
                    title: 'error log',
                    details: errorLog
                })
                //invoiceObjArr.forEach(invoiceObj => {
                try {
                    processInvLib.processInvoice(
                        invoiceObjArr[0],//invoiceObj,
                        fileName,
                        errorLog,
                        vendorData,
                        downloadConnection,
                        vendorData.testDirectory
                    );
                } catch (e) {
                    log.debug({
                        title: 'Error processing invoice',
                        details: e
                    });
                    //log.debug({
                    //    title: 'Inv Obj',
                    //    details: invoiceObj
                    //});
                }
                // });
            }
        }

        return {
            execute: execute
        }
    });
