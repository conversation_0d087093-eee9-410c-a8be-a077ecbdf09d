/**
 * EDI Data class
 * represents the information needed to process this edi transaction
 *
 * @NApiVersion 2.1
 */

//@ts-ignore
define([], () => {
	/**
	 * EDI Data class
	 *
	 * @class
	 * @type {import("./vlmd_edi_transaction").EdiDataObject}
	 */
	class EdiDataObject {
		constructor(json) {
			this.prodGuidBool = json["prodGuidBool"];
			this.prodDirectoryBool = json["prodDirectoryBool"];
			this.decodeContent = json["decodeContent"];
			this.prodGUID = json["prodGUID"];
			this.sandboxGUID = json["sandboxGUID"];
			this.prodDirectory = json["prodDirectory"];
			this.referenceDirectory = json["referenceDirectory"];
			this.testDirectory = json["testDirectory"];
			this.documentType = json["documentType"];
			this.documentTypeId = json["documentTypeId"];
			this.purchasingSoftware = json["purchasingSoftware"];
			this.purchasingSoftwareId = json["purchasingSoftwareId"];
			this.pushEmailToDB = json["pushEmailToDB"];
		}
	}

	return EdiDataObject;
});
