/**
 * @description Class containing functions to validate and update 810 items
 *
 * @NApiVersion 2.1
 * <AUTHOR> <<EMAIL>>
 */

define([
    "exports",
    "require",
    "../../../../Libraries/External_Libraries/vlmd_numeral_lib",
    "../../../../Classes/vlmd_custom_error_object",
    "N/log",
    "N/query",
], function (
    /** @type {any} */ exports,
    /** @type {any} */ require,
    /** @type {any} */ numeral,
) {
    /** @type {CustomErrorObject} */
    const CustomErrorObject = require("../../../../Classes/vlmd_custom_error_object");
    const log = require("N/log");
    const query = require("N/query");

    /**
     * 810 Item Class
     *
     * @class
     * @typedef {import("../../../../Classes/vlmd_custom_error_object").CustomErrorObject} CustomErrorObject
     * @typedef {import("../../Interfaces/Decorators/810/edi_810_item").EDI810ItemObject} EDI810ItemObject
     */
    class EDI810Item {
        /** @param {{[key:string]: any}} params */
        constructor (params) {
            /** @type {import("N/record").Record} */
            this.bill = params.bill;
            /** @type {string} */
            this.referenceNumber = params.referenceNumber;
            /** @type {string[]} */
            this.logs = [];
            /** @type {any[]} */
            this.vendorItems = params.vendorItems;
            /** @type {string} */
            this.vendorName = params.vendorName;
            /** @type {EDI810ItemObject[]} */
            this.list = [];
            /** @type {CustomErrorObject} */
            this.customError = new CustomErrorObject();
        }

        /**
         * Consolidate error messages for the Item class
         *
         * @param {Object} args Parameters
         * @param {string} args.functionName Function identifier
         * @param {string} args.logMessage Log message to save
         */
        logErrors({functionName, logMessage}) {
            log.error({
                title: functionName,
                details: logMessage
            });
            this.logs.push(logMessage);
        }

        /**
         * Create a list of items from the bill record
         *
         * @returns {EDI810ItemObject[]} Item object array
         */
        getItems() {
            /** @type {EDI810ItemObject[]} */
            const items = [];

            try {
                const itemLineCount = this.bill.getLineCount({
                    sublistId: "item",
                });

                for (var lineIndex = 0; lineIndex < itemLineCount; lineIndex++) {
                    const itemName = this.bill.getSublistText({
                        sublistId: "item",
                        fieldId: "vendorname",
                        line: lineIndex,
                    });

                    let quantity = this.bill.getSublistText({
                        sublistId: "item",
                        fieldId: "quantity",
                        line: lineIndex,
                    });
                    quantity = numeral(quantity).format("0,00");

                    let rate = this.bill.getSublistText({
                        sublistId: "item",
                        fieldId: "rate",
                        line: lineIndex,
                    });
                    rate = numeral(rate).format("0.00");
                    items.push({ itemName, quantity, rate, lineIndex });
                }

            } catch (e) {
                this.logErrors({
                    functionName: "EDI 810 Item (getItems)",
                    logMessage: `Items not gotten for ${this.referenceNumber}. Error: ${e}`,
                });
                throw this.customError?.updateError({
                    errorType: this.customError.ErrorTypes.UNHANDLED_ERROR,
                    summary: "ITEM_NOT_UPDATED",
                    details: `Items not gotten for ${this.referenceNumber}. Error: ${e}`,
                });
            }

            this.list = items;
            log.debug({ title: "EDI 810 Item (getItems)", details: JSON.stringify(this.list) });

            return items;
        }

        /**
         * Update the item on the line to the new version based on the old-new item mapping
         *
         * @param {object} params
         * @param {number} params.index Line index
         * @param {string} params.oldItemName Item name used in the NetSuite transaction
         * @param {{[key:string]: any}} params.oldToNewItemMapping Old-new Item Name Mapping
         * @returns {void}
         */
        _updateBillItem({index, oldItemName, oldToNewItemMapping}) {
            const currentSublistItemId = this.bill.getSublistValue({
                sublistId: "item",
                fieldId: "item",
                line: index,
            })?.toString();
            const newItemRecord = oldToNewItemMapping[oldItemName];
            if (currentSublistItemId && newItemRecord) {
                if (newItemRecord.id === currentSublistItemId) {
                    log.debug({
                        title: "EDI 810 Item (_updateBillItem)",
                        details: `Sublist item is already set to ${newItemRecord.itemName}`
                    });
                } else {
                    log.debug({
                        title: "EDI 810 Item (_updateBillItem)",
                        details: `${oldItemName} => ${newItemRecord.id}:${newItemRecord.itemName}`
                    });
                    this.bill.setSublistValue({
                        sublistId: "item",
                        fieldId: "item",
                        line: index,
                        value: newItemRecord.id,
                    });
                }
            } else if (!newItemRecord) {
                log.debug({
                    title: "EDI 810 Item (_updateBillItem)",
                    details: `${oldItemName} not found on the mapping. Skipping item replacement.`
                })
            }
        }

        /**
         * Compare rate field of the item
         *
         * @param {object} params
         * @param {string} params.itemName Item name
         * @param {number} params.index Line index
         * @param {string} params.qtyFromNetSuite Value from NetSuite
         * @param {string} params.qtyFromVendor Value from vendor
         * @param {{[key:string]: any}} params.matchingVendorItem Item from vendor
         * @returns {void}
         */
        _updateBillQuantity({index, itemName, qtyFromNetSuite, qtyFromVendor, matchingVendorItem}) {
            const isEqual = this._areFieldValuesEqual({
                itemName,
                fieldName: "quantity",
                valueFromNetSuite: qtyFromNetSuite,
                valueFromVendor: qtyFromVendor,
                matchingVendorItem
            });
            if (!isEqual) {
                this.bill.setSublistValue({
                    sublistId: "item",
                    fieldId: "quantity",
                    line: index,
                    value: matchingVendorItem.quantity,
                });
            }
        }

        /**
         * Compare rate field of the item
         *
         * @param {object} params
         * @param {string} params.itemName Item name
         * @param {number} params.index Line index
         * @param {string} params.rateFromNetSuite Value from NetSuite
         * @param {string} params.rateFromVendor Value from vendor
         * @param {{[key:string]: any}} params.matchingVendorItem Item from vendor
         * @returns {void}
         */
        _updateBillRate({index, itemName, rateFromNetSuite, rateFromVendor, matchingVendorItem}) {
            const isEqual = this._areFieldValuesEqual({
                itemName,
                fieldName: "rate",
                valueFromNetSuite: rateFromNetSuite,
                valueFromVendor: rateFromVendor,
                matchingVendorItem
            });
            if (!isEqual) {
                this.bill.setSublistValue({
                    sublistId: "item",
                    fieldId: "rate",
                    line: index,
                    value: matchingVendorItem.rate,
                });
            }
        }

        /**
         * Compare the values retrieved from NetSuite and Vendor
         *
         * @param {object} params
         * @param {string} params.itemName Item name
         * @param {string} params.fieldName Item field name
         * @param {string} params.valueFromNetSuite Value from NetSuite
         * @param {string} params.valueFromVendor Value from vendor
         * @param {{[key:string]: any}} params.matchingVendorItem Item from vendor
         */
        _areFieldValuesEqual({itemName, fieldName, valueFromNetSuite, valueFromVendor}) {
            if (
                numeral(valueFromNetSuite).format("0.00") !=
                numeral(valueFromVendor).format("0.00")
            ) {
                this.logErrors({
                    functionName: "EDI 810 Item (_areFieldValuesEqual)",
                    logMessage: `The ${fieldName} that ${this.vendorName} sent for ${
                        itemName
                    } doesn't match what's in NetSuite.
                       What ${this.vendorName} sent: ${numeral(
                        valueFromVendor
                    ).format("0,0.00")}
                       What we have in NetSuite: ${valueFromNetSuite}`,
                });

                return false;
            }

            return true;
        }

        compareItems() {
            try {
                /** @type {{[key:string]: any}} */
                const oldToNewItemMapping = {};
                query.runSuiteQLPaged({
                    query: `
                        SELECT
                            mapping.name,
                            oldItem.itemid,
                            newItem.itemid,
                            oldItem.id,
                            newItem.id,
                            oldItem.vendorname,
                            newItem.vendorname
                        FROM
                            customrecord_old_new_item_mapping AS mapping
                        INNER JOIN
                            item AS oldItem ON mapping.custrecord_old_item_id = oldItem.id
                        INNER JOIN
                            item AS newItem ON mapping.custrecord_new_item_id = newItem.id
                        WHERE
                            mapping.isinactive = 'F'
                            AND oldItem.vendorname IN (${this.vendorItems.map(item => `'${item.itemName}'`).join(",")})
                    `,
                    pageSize: 1000
                    }).iterator().each((page) => {
                        page.value.data.results.forEach((result) => {
                            const mappingName = result.values[0]?.toString();
                            const oldItemName = result.values[1]?.toString();
                            const newItemName = result.values[2]?.toString();
                            const oldItemId = result.values[3]?.toString();
                            const newItemId = result.values[4]?.toString();
                            const oldItemVendorName = result.values[5]?.toString();
                            const newItemVendorName = result.values[6]?.toString();
                            if (oldItemVendorName && newItemId) {
                                oldToNewItemMapping[oldItemVendorName] = {
                                    itemName: newItemName,
                                    vendorName: newItemVendorName,
                                    id: newItemId,
                                    oldRecord: {
                                        id: oldItemId,
                                        itemName: oldItemName,
                                    },
                                }
                            }
                        });

                        return true;
                    });

                if (!this.vendorItems) {
                    this.logErrors({
                        functionName: "EDI 810 Item (compareItems)",
                        logMessage: "No items in the vendor bill to compare",
                    });
                    throw this.customError?.updateError({
                        errorType: this.customError.ErrorTypes.UNHANDLED_ERROR,
                        summary: "ITEM_NOT_UPDATED",
                        details: "No items in the vendor bill to compare",
                    });
                } else {
                    log.debug({
                        title: "EDI 810 Item (compareItems)",
                        details: JSON.stringify(this.vendorItems)
                    });
                }

                this.list.forEach((netSuiteItem, index) => {
                    const matchingVendorItem = this.vendorItems.find(
                        (vendorItem) =>
                            netSuiteItem.itemName
                                .toUpperCase()
                                .split(".")
                                .join("")
                                .split("-")
                                .join("") ==
                            vendorItem.itemName
                                .toUpperCase()
                                .split(".")
                                .join("")
                                .split("-")
                                .join("")
                    );
                    if (!matchingVendorItem) {
                        netSuiteItem.missingInEdiFile = true;
                        this.logErrors({
                            functionName: "EDI 810 Item (compareItems)",
                            logMessage: `The ${this.vendorName} EDI file that was sent for ${this.referenceNumber} is missing ${netSuiteItem.itemName}.`,
                        });
                    } else {
                        this._updateBillQuantity({
                            index,
                            itemName: netSuiteItem.itemName,
                            qtyFromNetSuite: netSuiteItem.quantity,
                            qtyFromVendor: matchingVendorItem.quantity,
                            matchingVendorItem
                        });
                        this._updateBillRate({
                            index,
                            itemName: netSuiteItem.itemName,
                            rateFromNetSuite: netSuiteItem.rate,
                            rateFromVendor: matchingVendorItem.rate,
                            matchingVendorItem
                        });
                    }
                });
            } catch (e) {
                this.logErrors({
                    functionName: "EDI 810 Item (compareItems)",
                    logMessage: `Error comparing items for ${this.referenceNumber}. Error: ${e}`,
                });
                throw this.customError?.updateError({
                    errorType: this.customError.ErrorTypes.UNHANDLED_ERROR,
                    summary: "ITEM_NOT_UPDATED",
                    details: `Error comparing items for ${this.referenceNumber}. Error: ${e}`,
                });
            }
        }

        removeNetSuiteItemsMissingInEdiFile() {
            try {
                /** @type {string[]} */
                const itemsToBeRemoved = [];
                this.list = this.list.sort((a, b) => b.lineIndex - a.lineIndex); //Descending
                this.list.forEach((netSuiteItem) => {
                    if (netSuiteItem.missingInEdiFile) {
                        this.bill.removeLine({
                            sublistId: "item",
                            line: netSuiteItem.lineIndex,
                        });
                        itemsToBeRemoved.push(netSuiteItem.itemName);
                    }
                });
                log.debug({
                    title: "EDI 810 Item (removeNetSuiteItemsMissingInEdiFile)",
                    details: `Removing the following items: ${JSON.stringify(itemsToBeRemoved)}`
                });
            } catch (error) {
                this.logErrors({
                    functionName: "EDI 810 Item (removeNetSuiteItemsMissingInEdiFile)",
                    logMessage: `Error removing items that are missing on the EDI file from NetSuite bill ${this.referenceNumber}. Error: ${error}`,
                });
            }
        }

        checkNetSuiteForMissingItems() {
            try {
                this.vendorItems.reduce((missingItems, vendorItem) => {
                    const itemInNetSuite = this.list.find(netSuiteItem => netSuiteItem.itemName === vendorItem.itemName);
                    if (!itemInNetSuite) {
                        this.logErrors({
                            functionName: "EDI 810 Item (checkNetSuiteForMissingItems)",
                            logMessage: `${this.referenceNumber} in NetSuite is missing ${vendorItem.itemName}`,
                        });
                        missingItems.push(vendorItem.itemName);
                    }

                    return missingItems;
                }, [])
            } catch (e) {
                this.logErrors({
                    functionName: "EDI 810 Item (checkNetSuiteForMissingItems)",
                    logMessage: `Error checking ${this.vendorName} invoice for missing items ${this.referenceNumber}. Error: ${e}`,
                });
            }
        }

        saveBill() {
            try {
                return this.bill.save();
            } catch (error) {
                this.logErrors({
                    functionName: "EDI 810 Item (saveBill)",
                    logMessage: `New bill for ${this.bill.getValue({fieldId: "transactionnumber"})} not saved. Error: ${error}.`,
                });
                throw this.customError?.updateError({
                    errorType: this.customError.ErrorTypes.UNHANDLED_ERROR,
                    summary: "ITEM_NOT_UPDATED",
                    details: `New bill for ${this.bill.getValue({fieldId: "transactionnumber"})} not saved. Error: ${error}.`,
                });
            }
        }

        validateAndUpdate() {
            log.debug({
                title: "EDI 810 Item (validateAndUpdate)",
                details: `Validating and updating line items from Bill ${this.bill}`
            });
            this.getItems();
            this.compareItems();
            this.removeNetSuiteItemsMissingInEdiFile();
            this.checkNetSuiteForMissingItems();
            this.saveBill();
        }
    }

    exports.EDI810Item = EDI810Item;
});