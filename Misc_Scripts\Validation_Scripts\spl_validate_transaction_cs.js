/**
 * @NApiVersion 2.1
 * @NScriptType ClientScript
 * @NAmdConfig /SuiteScripts/config.json
 */

define([
	"N/log",
	"N/runtime",
	"N/error",
	"GetTransactionAndItemObjsLib",
	"PriceForItemForCustomer",
], function (
	log,
	runtime,
	error,
	getTransactionItemObjsLib,
	PriceForItemForCustomer
) {
	var alertArr = [];
	var isValid = true;

	function getErrorMessageText(e) {
		return `User: ${runtime.getCurrentUser().name}
Role: ${runtime.getCurrentUser().role}
Execution Context: ${runtime.executionContext}
Error: ${e}`;
	}

	var validationFunctions = (function () {
		function displayAlert() {
			if (alertArr.length > 0) {
				var alertText = "";
				alertArr.forEach(function (message) {
					alertText += message + "\n";
				});
				alert(alertText);
			}
		}

		return {
			displayAlert,
		};
	})();

	function pageInit(context) {
		// //HACK: for testing purposes
		// if (runtime.getCurrentUser().id == 3288) {
		// 	/** @type {import("../../Classes/spl_price_for_item_for_customer").PriceForItemForCustomer} */
		// 	// console.log("PriceForItemForCustomer " + PriceForItemForCustomer);
		// 	const priceForItemForCustomer = new PriceForItemForCustomer({
		// 		itemId: 123, // 54940,
		// 		itemName: "PPE1117S",
		// 		customerId: 12323, //13258,
		// 		customerName: "Greenwood Center for Nursing and Rehab LLC",
		// 	});
		// 	priceForItemForCustomer.getPurchaserId();
		// 	priceForItemForCustomer.getPriceValues();
		// 	priceForItemForCustomer.displayNotifications();
		// 	console.log(
		// 		"priceForItemForCustomer " + JSON.stringify(priceForItemForCustomer)
		// 	);
		// }
	}

	function validateLine(context) {
		var transaction = context.currentRecord;
		var transactionType = context.currentRecord.type;
		var transactionIsSalesOrder = transactionType == "salesorder";
		var transactionIsPurchaseOrder = transactionType == "purchaseorder";

		try {
			var subsidiary = transaction.getValue("subsidiary");
			if (subsidiary != 1) {
				//Supplyline
				return true;
			}

			if (context.sublistId == "item") {
				alertArr = [];

				var transactionObj =
					getTransactionItemObjsLib.getTransactionObjForValidateLine(
						transaction
					);

				var itemObj = getTransactionItemObjsLib.getItemObjForValidateLine(
					transaction,
					transactionIsSalesOrder
				);

				if (!itemObj) {
					return true; //This is an empty line - no need to validate
				}

				let hasError = false;

				const {
					messages: itemValidationMessages,
					hasError: itemValidationHasError,
				} = !transactionIsPurchaseOrder //If it's not a purchase order - transaction is quote or sales order
					? getTransactionItemObjsLib.runItemValidations(itemObj)
					: { messages: [], hasError: false };

				const {
					messages: transactionValidationMessages,
					hasError: transactionValidationHasError,
				} = !transactionIsPurchaseOrder //If it's not a purchase order - transaction is quote or sales order
					? getTransactionItemObjsLib.runSalesOrderWithItemValidations(
							transactionObj,
							itemObj
					  )
					: { messages: [], hasError: false };

				const {
					messages: salesOrderItemValidationMessages,
					hasError: salesOrderItemValidationHasError,
				} = transactionIsSalesOrder
					? getTransactionItemObjsLib.runSalesOrderItemValidations(itemObj)
					: { messages: [], hasError: false };

				itemObj.dontUseItemInNewTransactions &&
					salesOrderItemValidationMessages.push(
						`${itemObj.itemName} has been disabled for new transactions. Please use a different item.`
					) &&
					(hasError = true);

				alertArr = alertArr
					.concat(itemValidationMessages)
					.concat(transactionValidationMessages)
					.concat(salesOrderItemValidationMessages);
				validationFunctions.displayAlert();

				return !(
					hasError ||
					itemValidationHasError ||
					transactionValidationHasError ||
					salesOrderItemValidationHasError
				);
			} else {
				//shipgroup for multi line shipping
				return true;
			}
		} catch (e) {
			throw error.create({
				name: "Validate Line Error - " + transaction.id,
				message: getErrorMessageText(e),
			});
		}
	}

	function saveRecord(context) {
		var transaction = context.currentRecord;
		var transactionType = context.currentRecord.type;
		var transactionIsSalesOrder = transactionType == "salesorder";

		try {
			var subsidiary = transaction.getValue("subsidiary");

			if (subsidiary != 1) {
				//Supplyline
				return true;
			}

			alertArr = [];

			var transactionObj = getTransactionItemObjsLib.getTransactionObjForSave(
				transaction,
				transactionIsSalesOrder
			);

			const {
				messages: salesOrderValidationMessages,
				hasError: salesOrderValidationHasError,
			} = transactionIsSalesOrder
				? getTransactionItemObjsLib.runSalesOrderValidations(transactionObj)
				: { messages: [], hasError: false };

			let hasError = salesOrderValidationHasError || false;
			var transactionLineCount = transaction.getLineCount({
				sublistId: "item",
			});

			alertArr = alertArr.concat(salesOrderValidationMessages);

			for (var x = 0; x < transactionLineCount; x++) {
				var itemObj = getTransactionItemObjsLib.getItemObjForSave(
					transaction,
					transactionIsSalesOrder,
					x
				);

				if (!itemObj) {
					return true; //This is an empty line - no need to validate
				}

				const {
					messages: itemValidationMessages,
					hasError: itemValidationHasError,
				} = getTransactionItemObjsLib.runItemValidations(itemObj);

				const {
					messages: transactionValidationMessages,
					hasError: transactionValidationHasError,
				} = getTransactionItemObjsLib.runSalesOrderWithItemValidations(
					transactionObj,
					itemObj
				);

				const {
					messages: salesOrderItemValidationMessages,
					hasError: salesOrderItemValidationHasError,
				} = transactionIsSalesOrder
					? getTransactionItemObjsLib.runSalesOrderItemValidations(itemObj)
					: { messages: [], hasError: false };

				const incrementValidationMessages =
					getTransactionItemObjsLib.runItemIncrementValidations(itemObj) || [];

				transactionIsSalesOrder &&
					(transactionObj.createdFrom || transactionObj.ediControlNumber) &&
					itemObj.dontUseItemInNewTransactions &&
					salesOrderItemValidationMessages.push(
						`${itemObj.itemName} has been disabled for new transactions. Please use a different item.`
					) &&
					(hasError = true);

				alertArr = alertArr
					.concat(itemValidationMessages)
					.concat(transactionValidationMessages)
					.concat(salesOrderItemValidationMessages)
					.concat(incrementValidationMessages);

				hasError =
					hasError ||
					itemValidationHasError ||
					transactionValidationHasError ||
					salesOrderItemValidationHasError ||
					incrementValidationMessages.length > 0;
			}

			validationFunctions.displayAlert();

			return !hasError;
		} catch (e) {
			throw error.create({
				name: "Save Record Error - " + transaction.id,
				message: getErrorMessageText(e),
			});
		}
	}

	function fieldChanged(context) {
		var transaction = context.currentRecord;

		if (context.sublistId !== "item") return;
		if (context.fieldId !== "rate") return;

		let customPriceLevelField = transaction.getCurrentSublistValue({
			sublistId: "item",
			fieldId: "custcol_rate_based_on_price_level",
		});

		if (!customPriceLevelField) return;

		let allowCustomPricing = transaction.getCurrentSublistValue({
			sublistId: "item",
			fieldId: "custcol_allow_custom_pricing",
		});

		if (!allowCustomPricing) {
			transaction.setCurrentSublistValue({
				sublistId: "item",
				fieldId: "rate",
				value: null,
				ignoreFieldChange: true,
			});

			transaction.setCurrentSublistValue({
				sublistId: "item",
				fieldId: "amount",
				value: 0,
				ignoreFieldChange: true,
			});

			alert(
				'Please check off the "Allow Custom Pricing" checkbox to set a custom rate'
			);

			return;
		}

		transaction.setCurrentSublistValue({
			sublistId: "item",
			fieldId: "custcol_rate_based_on_price_level",
			value: "", //Custom price level
			ignoreFieldChange: true,
		});
	}

	function postSourcing(context) {
		var transaction = context.currentRecord;

		if (transaction.getValue("subsidiary") != 1) return; //Supplyline
		if (context.currentRecord.type != "estimate") return;

		let allowCustomPricing = transaction.getCurrentSublistValue({
			sublistId: "item",
			fieldId: "custcol_allow_custom_pricing",
		});

		if (allowCustomPricing === true) return; //Don't override the custom price that the user will set.

		if (context.fieldId == "units") {
			transaction.setCurrentSublistValue({
				sublistId: "item",
				fieldId: "price",
				value: -1, //Custom price level
				ignoreFieldChange: true,
			});

			transaction.setCurrentSublistValue({
				sublistId: "item",
				fieldId: "custcol_rate_based_on_price_level",
				value: "", //Custom price level
				ignoreFieldChange: true,
			});
		}

		if (context.fieldId == "item") {
			/** @type {import("../../Classes/spl_price_for_item_for_customer").PriceForItemForCustomer} */
			const priceForItemForCustomer = new PriceForItemForCustomer(transaction);

			priceForItemForCustomer.getPurchaserId();
			priceForItemForCustomer.getPriceValues();

			priceForItemForCustomer.setPriceValuesBasedOnCustomerPricingCS(
				transaction
			);
			priceForItemForCustomer.displayNotifications();
		}
	}

	return {
		pageInit, //HACK: for testing purposes
		validateLine,
		saveRecord,
		fieldChanged,
		postSourcing,
	};
});
