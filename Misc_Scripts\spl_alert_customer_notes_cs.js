/**
 * @NApiVersion 2.1
 * @NScriptType ClientScript
 * @NAmdConfig /SuiteScripts/config.json
 */

//@ts-ignore
define(["N/log", "N/search"], function (log, search) {
	function alertCustomerNotes(context) {
		try {
			let transaction = context.currentRecord;
			let customerId = transaction.getValue("entity");

			let customerNotesToAlert = search.lookupFields({
				type: search.Type.CUSTOMER,
				id: customerId,
				columns: "custentity_spl_customer_notes_for_so_in",
			}).custentity_spl_customer_notes_for_so_in;

			if (customerNotesToAlert) {
				alert(customerNotesToAlert);
			}
		} catch (e) {
			log.error('Error alerting customer notes', e); 
		}
	}

	function pageInit(context) {
		let pageMode = context.mode;

		if (pageMode != "edit") {
			return;
		}

		alertCustomerNotes(context);
	}

	function saveRecord(context) {
		alertCustomerNotes(context);

		return true;
	}

	return {
		pageInit: pageInit,
		saveRecord: saveRecord,
	};
});
