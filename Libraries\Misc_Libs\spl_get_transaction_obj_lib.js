/**
 * Used in outgoing EDI scripts to get the transaction information needed
 * formatted as a JS obj
 *
 * @NApiVersion 2.1
 */

define([
  "require",
  "TransactionObject",
  "ItemHelperLib",
  "Moment",
  "N/log",
  "N/search",
  "N/query",
], function (require, TransactionObject, itemHelperLib, moment) {
  const log = require("N/log");
  const search = require("N/search");
  const query = require("N/query");

  function getObj(
    netsuiteRecord,
    customerParent,
    documentType,
    purchasingSoftware,
    isInvoice,
    customErrorObject,
    incoming850DataObjParam
  ) {
    const documentInfoHF = (function () {
      /**
       * Return the transaction json string as a Purchase Order Transaction Object
       *
       * @returns {import("../../Classes/spl_transaction_object").TransactionObject|null} Purchase Order Transaction Object
       */
      function getIncoming850Data(incoming850DataObj) {
        //When user passes the data in as a script parameter
        if (incoming850DataObj) {
          return incoming850DataObj;
        }

        try {
          let purchaseOrderJsonString = netsuiteRecord.getValue(
            "custbody_edi_850_in_json_obj"
          );

          if (!purchaseOrderJsonString) {
            return null;
          }

          try {
            var purchaseOrderJson = JSON.parse(purchaseOrderJsonString);
          } catch (err) {
            throw customErrorObject.updateError({
              errorType: customErrorObject.ErrorTypes.INVALID_DATA_TYPE,
              summary: "INVALID_JSON_OBJ",
              details: `This purchase order object isn't in a valid format. Please correct so that the edi file can be sent.\n${purchaseOrderJsonString}`,
            });
          }

          /**
           * Builds up a formatted JS object vs. the JSON obj
           *
           * @type {import("../../Classes/spl_transaction_object").TransactionObject}
           */
          const incoming850DataObj = new TransactionObject(purchaseOrderJson);
          incoming850DataObj.trimStringProperties();
          incoming850DataObj.trimItemsStringProperties();

          return incoming850DataObj;
        } catch (err) {
          throw customErrorObject.updateError({
            errorType: customErrorObject.ErrorTypes.NO_VALUE_RETURNED,
            summary: "PO_OBJ_NOT_GOTTEN",
            details: `Error getting PO object: ${err}`,
          });
        }
      }

      /**
       * Retrieve the control number saved on the EDI integration record for this parent.
       *
       * @returns {number} EDI Control #
       */
      function getControlNumber() {
        try {
          const sqlQuery = `SELECT
					custrecord_document_control_number 
				 FROM
					customrecord_vlmd_edi_integration 
				 WHERE
					BUILTIN.MNFILTER(custrecord_edi_intgrtn_prnt_fclty, 'MN_INCLUDE', '', 'FALSE', NULL, ? ) = 'T' 
					AND custrecord_document_control_number IS NOT NULL`;

          let results = query
            .runSuiteQL({
              query: sqlQuery,
              params: [customerParent.customerId],
            })
            .asMappedResults();

          if (
            results.length <= 0 ||
            !results[0]["custrecord_document_control_number"]
          ) {
            throw customErrorObject.updateError({
              errorType: customErrorObject.ErrorTypes.NO_VALUE_RETURNED,
              summary: "NO_CONTROL_NUMBER_GOTTEN",
              details: `No control number was found for this parent. Query: ${sqlQuery}`,
            });
          }

          const currentControlNumber = parseInt(
            results[0]["custrecord_document_control_number"]
          );

          if (
            !currentControlNumber ||
            currentControlNumber == "NaN" ||
            currentControlNumber < 100000001 ||
            currentControlNumber > 999999998
          ) {
            throw customErrorObject.updateError({
              errorType: customErrorObject.ErrorTypes.INVALID_DATA_TYPE,
              summary: "INVALID_CONTROL_NUMBER",
              details: `Control number, ${currentControlNumber}, is invalid. Please investigate and correct on the EDI integration record if needed.`,
            });
          }

          return currentControlNumber + 1;
        } catch (err) {
          throw customErrorObject.updateError({
            errorType: customErrorObject.ErrorTypes.NO_VALUE_RETURNED,
            summary: "NO_CONTROL_NUMBER_GOTTEN",
            details: `Error getting control number from EDI integration record: ${err}`,
          });
        }
      }

      /**
       * Retrieve Purchase Order document number, if not found return the invoice(810)/sales order (855) doc # instead
       *
       * @param {string} documentNumber Transaction ID
       * @param {number} createdFromId ID of the parent Sales Order
       * @returns {string} Transaction ID or Reference Number
       */
      function getPurchaseOrderNumber(documentNumber, createdFromId) {
        //Get the PO reference number on the invoice
        let purchaseOrderNumber = netsuiteRecord.getText("otherrefnum");

        //See if there's a reference number on the related SO.
        if (!purchaseOrderNumber && isDssiTransaction && createdFromId) {
          purchaseOrderNumber = search.lookupFields({
            type: search.Type.SALES_ORDER,
            id: createdFromId,
            columns: ["otherrefnum"],
          })["otherrefnum"];
        }

        return purchaseOrderNumber ?? documentNumber;
      }

      /**
       * Retrieve Purchase Order Date
       *
       * @param {import("../../Classes/spl_transaction_object").TransactionObject|null} purchaseOrderObj
       * @returns {Date|string} Purchase Order Date
       */
      function getPurchaseOrderDate(purchaseOrderObj) {
        return (
          (purchaseOrderObj instanceof TransactionObject &&
            purchaseOrderObj?.poDate) ||
          ""
        );
      }

      /**
       * Attempt to retrieve the Transaction ID of the Credit Memo's parent transaction
       * If the transaction is not a Credit Memo, default to the supplied document number
       *
       * @param {string} documentNumber Default Transaction ID
       * @returns {string} Parent's Transaction ID or Internal ID
       */
      function getOriginalInvoice(documentNumber, documentType) {
        if (documentType === "CREDIT_MEMO" || documentType === "Credit Memo") {
          const sqlQuery = `SELECT
							T.typebaseddocumentnumber AS document_number,
							BUILTIN.DF( PTLL.previousdoc ) AS invoice_number,
						FROM
							Transaction as PT 
							INNER JOIN
			   				PreviousTransactionLineLink AS PTLL 
			   			ON PT.ID = PTLL.NextDoc 
						INNER JOIN
			  				Transaction AS T 
			   			ON T.ID = PTLL.PreviousDoc 
			   			AND PTLL.previoustype = 'CustInvc' 
		 				WHERE PT.id = ?`;

          let results = query
            .runSuiteQL({
              query: sqlQuery,
              params: [netsuiteRecord.id],
            })
            .asMappedResults();

          //If there is a related invoice for this credit memo, return the invoice #
          if (results.length > 0 && results[0]["document_number"]) {
            return results[0]["invoice_number"];
          }

          //If not found and is for DSSI, throw an error since DSSI requires an original invoice # for CMs
          if (isDssiTransaction) {
            customErrorObject.updateError({
              errorType: customErrorObject.ErrorTypes.NO_VALUE_RETURNED,
              summary: "NO_ORIGINAL_INVOICE_GOTTEN",
              details: `No original invoice was found for this CM, ${documentNumber}`,
            });
          }
        }

        //In all other scenarios, return the invoice #
        return documentNumber;
      }

      /**
       * Retrieve ship date from parent transaction
       *
       * @param {string} createdFromId Internalid of the parent transaction
       * @returns {Date} Ship date, defaults to date today
       */
      function getShipDate(createdFromId) {
        if (createdFromId) {
          const lookupObj = search.lookupFields({
            type: search.Type.SALES_ORDER,
            id: createdFromId,
            columns: ["actualshipdate", "shipdate"],
          });

          return lookupObj.actualshipdate || lookupObj.shipdate;
        }
        return moment().format("YYYYMMDD");
      }

      /**
       * Retrieve the ship date recorded on the transaction
       *
       * @returns {Date} Transaction's ship date, defaults to date today
       */
      function getCurrentScheduledShiptDate() {
        //TODO: When returning today's date, value is formatted as "2024-02-14T19:00:31.148Z", confirm this is formatted later on.
        return netsuiteRecord.getValue("shipdate") || moment();
      }

      return {
        getIncoming850Data,
        getControlNumber,
        getPurchaseOrderNumber,
        getPurchaseOrderDate,
        getOriginalInvoice,
        getShipDate,
        getCurrentScheduledShiptDate,
      };
    })();

    const customerHF = (function () {
      /**
       * Remove the hierarchy and leave only the actual customer name
       *
       * @returns {string} Customer name without hierarchy
       */
      function getCustomerName() {
        try {
          const hierarchyRegex = /^(.*):\s/;
          return netsuiteRecord.getText("entity").replace(hierarchyRegex, "");
        } catch (err) {
          throw customErrorObject.updateError({
            errorType: customErrorObject.ErrorTypes.NO_VALUE_RETURNED,
            summary: "NO_CUSTOMER_NAME_RETURNED",
            details: `Error getting customer name: ${err}`,
          });
        }
      }

      /**
       * Retrieve the Customer's entityid or internalid as the account number
       *
       * @param {number} customerId Customer Internal ID
       * @returns {string|number} Customer Entity ID
       */
      function getAccountNumber(customerId) {
        return search.lookupFields({
          type: search.Type.CUSTOMER,
          id: customerId,
          columns: ["entityid"],
        }).entityid;
      }

      /**
       * Retrieve the customer name as recorded in their own tracking system
       *
       * @param {import("../../Classes/spl_transaction_object").TransactionObject|null} purchaseOrderObj Purchase Order Object
       * @param {string} customerName Customer name
       * @returns {string} Customer name saved in their system
       */
      function getCustomerNameInTheirSystem(purchaseOrderObj, customerName) {
        return (
          (purchaseOrderObj instanceof TransactionObject &&
            purchaseOrderObj?.customerNameInTheirSystem) ||
          customerName
        );
      }

      return {
        getCustomerName,
        getAccountNumber,
        getCustomerNameInTheirSystem,
      };
    })();

    const helperFunctions = (function () {
      /**
       * Retrieve the address (non-mandatory) from the Transaction Object or NetSuite record
       *
       * @param {import("../../Classes/spl_transaction_object").TransactionObject|null} purchaseOrderObj Purchase Order Object
       * @returns {{[key:string]: string|undefined}} Object containing street, city, state and zip of the transaction
       */
      function getAddress(purchaseOrderObj) {
        if (
          purchaseOrderObj instanceof TransactionObject &&
          purchaseOrderObj?.billingAddress
        ) {
          return purchaseOrderObj.getBasicAddress();
        } else {
          const lookupObj = search.lookupFields({
            type: search.Type.INVOICE,
            id: netsuiteRecord.id,
            columns: ["shipaddress1", "shipstate", "shipzip", "shipcity"],
          });

          return {
            streetAddress: lookupObj["shipaddress1"],
            city: lookupObj["shipcity"],
            state: lookupObj["shipstate"],
            zip: lookupObj["shipzip"],
          };
        }
      }

      /**
       * Retrieve the adjusted total tax relative to the transaction type
       *
       * @returns {number} Total tax
       */
      function getTaxAmount() {
        const taxTotal = netsuiteRecord.getValue("taxtotal");
        return isInvoice ? taxTotal : -taxTotal;
      }

      /**
       * Retrieve the adjusted total amount relative to the transaction type
       *
       * @returns {number} Total amount or Amount remaining
       */
      function getTotalAmount() {
        return isInvoice
          ? netsuiteRecord.getValue("total")
          : -netsuiteRecord.getValue("total");
      }

      /**
       * Push the shipping information to the documentObject's sacItems property
       *
       * @returns {void}
       */
      function setShipping() {
        try {
          const altShippingCost = netsuiteRecord.getValue("altshippingcost");

          if (altShippingCost) {
            documentObj.sacItems.push({
              sacType: "D200", //Freight charges to destination (as required for DSSI)
              quantity: "",
              description: "",
              amount: altShippingCost,
            });
          }
        } catch (err) {
          throw customErrorObject.updateError({
            errorType: customErrorObject.ErrorTypes.NO_VALUE_RETURNED,
            summary: "ERROR_SETTING_SHIPPING_VALUES",
            details: `Error getting shipping values: ${err}`,
          });
        }
      }

      /**
       * Push the Items and SAC Items to the documentObj
       *
       * @typedef DocumentObjectItem Object representing an item on a transaction sublist
       * @property {string} name Item name
       * @property {string} description Item description
       * @property {number} quantity Quantity
       * @property {number} rate Item price or rate
       * @property {number} amount Item amount
       * @property {string} itemType Item type
       * @property {number|undefined} lineNumber Line number from the sublist
       * @property {string} [sacType] SAC Type
       * @property {string|null} [uom] Unit of measure
       *
       * @returns {void}
       */
      function setItemsAndSAC(incoming850DataObj) {
        try {
          const itemsLineCount = netsuiteRecord.getLineCount({
            sublistId: "item",
          });

          for (
            let itemLineIndex = 0;
            itemLineIndex < itemsLineCount;
            itemLineIndex++
          ) {
            /** @type DocumentObjectItem */
            let itemName = _getSublistValue("item").replace(/^[^:]+:\s*/, "");

            const item = {
              /*Remove anything before the colon to return only the sub-matrix item 
							name when is a matrix item*/
              name: itemName,
              description: _getSublistValue("description"),
              quantity: _getSublistValue("quantity"),
              rate: _getSublistValue("rate"),
              amount: _getSublistValue("amount"),
              itemType: _getSublistValue("itemtype"),
              lineNumber: _getPurchaseOrderLineNumber(),
            };

            try {
              if (purchasingSoftware == "Agora") {
                //Check if is a matrix item and the order uses the full hierarchal name.
                item.isReplacementFor = incoming850DataObj?.items.find(
                  (obj) => obj?.itemName?.split(":")[1]?.trim() == itemName
                )?.itemName;

                //Check if is a new item for an old/new item.
                if (!item.isReplacementFor) {
                  let itemId = netsuiteRecord.getSublistValue({
                    sublistId: "item",
                    fieldId: "item",
                    line: itemLineIndex,
                  });

                  const sqlQuery = /*sql*/ `
                    SELECT
                      BUILTIN.DF(custrecord_old_item_id)
                    FROM 
                      customrecord_old_new_item_mapping onm
                    WHERE
                      onm.custrecord_new_item_id = ${itemId}`;

                  let results = query.runSuiteQL({
                    query: sqlQuery,
                  });

                  item.isReplacementFor =
                    results.results.length > 0 && results.results[0].values[0];
                }
              }
            } catch (err) {
              throw customErrorObject.updateError({
                errorType: customErrorObject.ErrorTypes.NO_VALUE_RETURNED,
                summary: "ERROR_GETTING_ISREPLACEMENTFOR",
                details: `Error getting isReplacementFor: ${err}`,
              });
            }

            if (item.itemType === "Service") {
              item.sacType = "F050";
              documentObj.sacItems.push(item);
            } else if (item.itemType === "Markup") {
              item.sacType = "B801";
              documentObj.sacItems.push(item);
            } else {
              item.uom = _getUom(itemLineIndex, item);
              documentObj.items.push(item);
            }

            /**
             * Retrieve the value for the specified field
             *
             * @param {string} field NetSuite field name
             * @returns {any} Value from the Item sublist
             */
            function _getSublistValue(field) {
              var sublistValue = netsuiteRecord.getSublistText({
                sublistId: "item",
                fieldId: field,
                line: itemLineIndex,
              });

              sublistValue = sublistValue
                .split("\n")
                .join("")
                .split("\r")
                .join("")
                .trim();

              if (
                (field == "quantity" || field == "amount") &&
                (documentType == "CREDIT_MEMO" || documentType == "Credit Memo")
              ) {
                return -sublistValue;
              } else {
                return sublistValue;
              }
            }

            /**
             * Retrieve the Purchase Order Line Number from the Item sublist
             *
             * @returns {number|undefined} Line number
             */
            function _getPurchaseOrderLineNumber() {
              const lineNumber = _getSublistValue(
                "custcol_spl_hb_po1_line_number"
              );

              if (!lineNumber) {
                /*The SO is created in same order in NS as the PO sent
								 -> the record item line #, plus one (because line #'s in NS are 0 
								 based) is = to the line # on the purchase order*/
                return itemLineIndex + 1;
              }

              return lineNumber;
            }

            /**
             * Return the base uom for the units type of this item
             * (necessary when sending EDI files)
             * Save the retrieved unit of measure to the Item object
             *
             * @param {number} index Line index
             * @param {DocumentObjectItem} item Item object
             * @returns {string|null} Unit of measure
             */
            function _getUom(index, item) {
              let abbreviation = "";

              let itemType = netsuiteRecord.getSublistText({
                sublistId: "item",
                fieldId: "itemtype",
                line: index,
              });

              //The internal id of the sale unit
              item.uom = netsuiteRecord.getSublistValue({
                sublistId: "item",
                fieldId: "units",
                line: index,
              });

              if (!item.uom && itemType !== "Kit") {
                throw customErrorObject.updateError({
                  errorType: customErrorObject.ErrorTypes.NO_VALUE_RETURNED,
                  summary: "NO_UOM_RETURNED",
                  details: `${item.name} is missing a UOM`,
                });
              }

              abbreviation = itemHelperLib.getUomAbbreviationForEdi({
                unit: item.uom,
                type: itemType,
              });

              if (!abbreviation) {
                throw customErrorObject.updateError({
                  errorType: customErrorObject.ErrorTypes.NO_VALUE_RETURNED,
                  summary: "NO_UOM_ABBREVIATION_RETURNED",
                  details: `No UOM gotten for ${item.uom}, ${item.name}`,
                });
              }

              return abbreviation;
            }
          }
        } catch (err) {
          throw customErrorObject.updateError({
            errorType: customErrorObject.ErrorTypes.NO_VALUE_RETURNED,
            summary: "ERROR_GETTING_ITEMS_SAC",
            details: `Error getting items or SAC values: ${JSON.stringify(
              err
            )}`,
          });
        }
      }

      return {
        getAddress,
        getTaxAmount,
        getTotalAmount,
        setShipping,
        setItemsAndSAC,
      };
    })();

    let documentObj = {};

    const isDssiTransaction = purchasingSoftware == "DSSI";

    try {
      const documentNumber = netsuiteRecord.getText("tranid");
      const createdFromId = netsuiteRecord.getValue("createdfrom");
      const customerId = netsuiteRecord.getValue("entity");

      if (!documentNumber || !customerId) {
        throw customErrorObject.updateError({
          errorType: customErrorObject.ErrorTypes.NO_VALUE_RETURNED,
          summary: "MISSING_VALUE_FROM_RECORD",
          details: `Basic record value missing: Document Number: ${documentNumber}, Customer: ${customerId}`,
        });
      }

      const incoming850DataObj = documentInfoHF.getIncoming850Data(
        incoming850DataObjParam
      );
      const customerName = customerHF.getCustomerName();

      documentObj = {
        documentInfo: {
          purchaseOrderObj: incoming850DataObj,
          controlNumber: documentInfoHF.getControlNumber(),
          date: netsuiteRecord.getValue("trandate"),
          documentNumber: documentNumber,
          purchaseOrderNumber: documentInfoHF.getPurchaseOrderNumber(
            documentNumber,
            createdFromId
          ),
          purchaseOrderDate:
            documentInfoHF.getPurchaseOrderDate(incoming850DataObj),
          originalInvoice: documentInfoHF.getOriginalInvoice(
            documentNumber,
            documentType
          ),
          shipDate: documentInfoHF.getShipDate(createdFromId),
          currentScheduledShipDate:
            documentInfoHF.getCurrentScheduledShiptDate(),
          createdFrom: createdFromId,
          purchaseOrderTransactionControlNumber: netsuiteRecord.getValue(
            "custbody_spl_edi_trans_cntrl_num" //Only need this for PO
          ),
        },
        customer: {
          name: customerName,
          id: customerId,
          accountNumber: customerHF.getAccountNumber(customerId),
          customerNameInTheirSystem: customerHF.getCustomerNameInTheirSystem(
            incoming850DataObj,
            customerName
          ),
        },
        address: helperFunctions.getAddress(incoming850DataObj),
        items: [],
        sacItems: [],
        taxType: isDssiTransaction ? "ST" : "GS",
        taxAmount: helperFunctions.getTaxAmount(),
        total: helperFunctions.getTotalAmount(),
        note: netsuiteRecord.getValue("custbody_spl_memo_to_cust_inv") ?? "",
      };

      helperFunctions.setShipping();
      helperFunctions.setItemsAndSAC(incoming850DataObj);

      return documentObj;
    } catch (err) {
      throw customErrorObject.updateError({
        errorType: customErrorObject.ErrorTypes.UNHANDLED_ERROR,
        summary: "ERROR_GETTING_TRANSACTION_OBJ",
        details: `Error getting transaction obj: ${err}`,
      });
    }
  }

  return {
    getObj,
  };
});
