/**
 * @NApiVersion 2.1
 * @NScriptType ClientScript
 * @NAmdConfig /SuiteScripts/config.json
 */
//@ts-ignore
define(["N/search", "N/log"], function (search, log) {
	var pageMode;

	function pageInit(context) {
		pageMode = context.mode;
	}

	function saveRecord(context) {
		var currentRecord = context.currentRecord;
		var total = currentRecord.getValue("total");

		try {
			switch (pageMode) {
				case "edit":
					var billId = currentRecord.id;

					var fileResultsArr = search.lookupFields({
						type: search.Type.TRANSACTION,
						id: billId,
						columns: "file.internalid",
					})["file.internalid"];

					var fileId;
					if (fileResultsArr.length > 0) {
						fileId = fileResultsArr[0].value;
					}

					if (total >= 500 && !fileId) {
						alert("You must attach a document to save this record.");
						return false;
					}

					break;
				case "create":
					if (total >= 500) {
						alert("Please attach a document to save this record.");
					}
			}
		} catch (e) {
			log.error("Error with Valmar attachments!", e);
		}

		return true;
	}
	return {
		pageInit: pageInit,
		saveRecord: saveRecord,
	};
});
