/**
 * @NApiVersion 2.1
 * @NAmdConfig /SuiteScripts/config.json
 */

//@ts-ignore
define(["N/log", "N/search", "N/record", "Moment"], function (
	log,
	search,
	record,
	moment
) {
	function getObj(
		itmfulfillmentRecord,
		customerParent,
		shipmentVals,
		custRecinternalID
	) {
		function _processFail(logMessage) {
			throw logMessage;
		}

		const documentInfoHF = (function () {
			function getPoNumber() {
				try {
					return search.lookupFields({
						type: search.Type.SALES_ORDER,
						id: shipmentVals.salesOrderIntId,
						columns: ["otherrefnum"],
					})["otherrefnum"];
				} catch (e) {
					_processFail(`poNumber value not set correctly. Error: ${e}`);
				}
			}

			function getFacilityNumber() {
				try {
					return search.lookupFields({
						type: search.Type.CUSTOMER,
						id: custRecinternalID,
						columns: "entityid",
					})["entityid"];
				} catch (e) {
					_processFail(`facilityNumber values not set correctly. Error: ${e}`);
				}
			}

			function getControlNumber() {
				const currentControlNumber = search.lookupFields({
					type: search.Type.CUSTOMER,
					id: customerParent.customerId,
					columns: ["custentity_spl_hb_edi_cntrl_nmbr"],
				})["custentity_spl_hb_edi_cntrl_nmbr"];

				if (!currentControlNumber) {
					_processFail(`currentControlNumber not found for parent customer`);
				}

				const newControlNumber = parseInt(currentControlNumber) + 1;

				if (newControlNumber > 999999999) {
					_processFail(
						"Control number is greater than 9 digits, this can not be processed correctly in ISA[13]"
					);
				}

				return newControlNumber;
			}

			function getShipDate() {
				try {
					return moment(
						itmfulfillmentRecord.getValue({ fieldId: "trandate" })
					).format("YYYYMMDD");
				} catch (e) {
					_processFail(`shipDate values not set correctly. Error: ${e}`);
				}
			}

			function getShipmentWeight() {
				try {
					return (
						itmfulfillmentRecord.getValue({
							fieldId: "shipmentweightups",
						}) ?? 0
					);
				} catch (e) {
					_processFail(`shipmentWeight values not set correctly. Error: ${e}`);
				}
			}

			function getTrackingNumber() {
				try {
					var trackingInfoVal = itmfulfillmentRecord.getValue({
						fieldId: "custbody_spl_tracking_information",
					});

					return trackingInfoVal
						? [trackingInfoVal]
						: shipmentVals.trackingNumber.split("<BR>");
				} catch (e) {
					_processFail(`TrackingNumber values not set correctly. Error: ${e}`);
				}
			}

			return {
				getPoNumber,
				getFacilityNumber,
				getControlNumber,
				getShipDate,
				getShipmentWeight,
				getTrackingNumber,
			};
		})();

		const helperFunctions = (function () {
			function getShipAddressObj() {
				try {
					var addressFullValue = itmfulfillmentRecord.getText({
						fieldId: "shipaddress",
					});

					var extractedAddressShipTo = addressFullValue.split("\n")[0];
					var extractedStreetAddress = addressFullValue.split("\n")[1];
					var extractedCityVal = addressFullValue.split("\n")[2].split(" ")[0];

					return {
						addressShipTo: extractedAddressShipTo,
						streetAddress: extractedStreetAddress,
						city: extractedCityVal,
						state: itmfulfillmentRecord.getText({ fieldId: "shipstate" }),
						zip: itmfulfillmentRecord.getText({ fieldId: "shipzip" }),
					};
				} catch (e) {
					_processFail(`Address values not set correctly. Error: ${e}`);
				}
			}

			function getItems() {
				try {
					function getParsedUOM() {
						var uomToParse = soRecObj.getSublistText({
							sublistId: "item",
							fieldId: "units",
							line: i,
						});

						var unitConversionRate = soRecObj.getSublistText({
							sublistId: "item",
							fieldId: "unitconversionrate",
							line: i,
						});

						/*Get last 2 element - when primary units type is Each
                            Example:    5000 EA / CS,   12 EA / DZ
                        Get first 2 elements - when the primary units type is not each
                            PK/200EA,   BX/10EA
                        */

						let uom =
							unitConversionRate == 1 //The primary units type is 'Each'
								? uomToParse.slice(-2) //Get the last 2 elements in the array
								: uomToParse.slice(0, 2); //Get the first 2 elements in the array
						uom = uom.toUpperCase();

						return uom;
					}

					var soRecObj = record.load({
						type: record.Type.SALES_ORDER,
						id: shipmentVals.salesOrderIntId,
					});

					var soItemlineCnt = soRecObj.getLineCount({ sublistId: "item" });
					var ifItemlineCnt = itmfulfillmentRecord.getLineCount({
						sublistId: "item",
					});

					var salesOrderItemsArr = [];
					var itemFulfillmentItemsArr = [];

					//Populate itemFulfillmentItemsArr with IF item ids
					for (var i = 0; i < ifItemlineCnt; i++) {
						var itemId = itmfulfillmentRecord.getSublistValue({
							sublistId: "item",
							fieldId: "item",
							line: i,
						});

						itemFulfillmentItemsArr.push(itemId);
					}

					//Add itemObj to salesOrderItemsArr for each SO item that is found on the IF
					for (var i = 0; i < soItemlineCnt; i++) {
						var po1LineNumFinal = 0;
						var itemId = soRecObj.getSublistValue({
							sublistId: "item",
							fieldId: "item",
							line: i,
						});

						//Check if SO item is found on IF - (item was fulfilled)
						var itemIndex = itemFulfillmentItemsArr.indexOf(itemId);

						if (itemIndex < 0) {
							continue;
						}	 					

						var itemName = soRecObj.getSublistValue({
							sublistId: "item",
							fieldId: "item_display",
							line: i,
						});

						var itmquantity = soRecObj.getSublistValue({
							sublistId: "item",
							fieldId: "quantity",
							line: i,
						});

						var itmDesc = soRecObj.getSublistValue({
							sublistId: "item",
							fieldId: "description",
							line: i,
						});

						var po1LineNum = soRecObj.getSublistValue({
							sublistId: "item",
							fieldId: "custcol_spl_hb_po1_line_number",
							line: i,
						});

						po1LineNumFinal = (po1LineNum != '' ? po1LineNum : 1);

						var itmUom = getParsedUOM();

							salesOrderItemsArr.push({
								itemName,
								itmquantity,
								itmUom,
								itmDesc,
								po1LineNumFinal,
							});
					}
					
					return salesOrderItemsArr;
				} catch (e) {
					_processFail(`Items values not set correctly. Error: ${e}`);
				}
			}

			return {
				getShipAddressObj,
				getItems,
			};
		})();

		let asnObj = {};
		const errorLog = [];

		try {
			asnObj = {
				documentInfo: {
					poNumber: documentInfoHF.getPoNumber(),
					facilityNumber: documentInfoHF.getFacilityNumber(),
					controlNumber: documentInfoHF.getControlNumber(),
					shipmentDate: documentInfoHF.getShipDate(),
					shipmentTime: "000000",
					shipmentWeight: documentInfoHF.getShipmentWeight(),
					shipmentMethod: shipmentVals.shipCarrier,
					trackingNumbers: documentInfoHF.getTrackingNumber(),
				},
				address: helperFunctions.getShipAddressObj(),
				items: helperFunctions.getItems(),
			};
		} catch (e) {
			errorLog.push(`Object values not set correctly:
			${e}`);

			asnObj = {};
		}

		return { asnObj, errorLog };
	}
	return {
		getObj,
	};
});
