/**
 * @description Factory class to handle price changes at a customer level
 *
 * @NApiVersion 2.1
 * <AUTHOR>
 */

define([
  "exports",
  "require",
  "N/log",
  "N/query",
  "N/file",
  "N/record",
  "N/task",
  "../../../Classes/vlmd_custom_error_object",
], (/** @type {any} */ exports, /** @type {any} */ require) => {
  const log = require("N/log");
  const query = require("N/query");
  const file = require("N/file");
  const record = require("N/record");
  const task = require("N/task");

  /** @type {import("../../../Classes/vlmd_custom_error_object").CustomErrorObject} */
  const CustomErrorObject = require("../../../Classes/vlmd_custom_error_object");

  /**
   * Customer Price Changes Class
   *
   * @class
   */
  class CustomerPriceChangesFactory {
    constructor() {
      /** @type {typeof CustomErrorObject} */
      this.customErrorObject = new CustomErrorObject();

      this.queryString = "";

      this.parentCustomerObj = {
        internalId: null,
        entityId: null,
        companyName: null,
        lastModifiedDate: null,
        existingPriceListFileId: null,
      };

      this.existingPricesMap = new Map();

      this.newPriceObjsArr = [];
      this.newPriceListFileId = null;

      this.customerPricingFilesRecordId = null;
      this.taskId = null;
    }

    /**
     * Loads the query string for parent customers that have a lastmodifieddate within the given time period -> which includes if there is a price change
     *
     * @returns {string} Query string}
     */
    getParentsWithPriceChangesQueryString(numberOfDaysBack) {
      try {
        this.queryString = /*sql*/ `
            SELECT 
                c.id,
                c.entityid,
                c.companyName,
                c.lastmodifieddate,
                MAX(cpf.custrecord_current_price_list_file_id) current_price_file_id
            FROM 
                customer c
            JOIN 
                CustomerSubsidiaryRelationship csr ON c.id = csr.entity AND csr.subsidiary = 1
            LEFT JOIN 
                customrecord_customer_pricing_files cpf ON cpf.custrecord_parent_customer = c.id
                AND cpf.custrecord_price_files_last_updated = (
                    SELECT MAX(custrecord_price_files_last_updated)
                    FROM customrecord_customer_pricing_files
                    WHERE custrecord_parent_customer = c.id
                )
            WHERE
                c.custentity_is_active_this_year = 'T'
                AND c.isInactive = 'F'                
                AND c.parent IS NULL
                AND c.lastmodifieddate >= SYSDATE - ${numberOfDaysBack ?? 1} 
            GROUP BY 
                c.id, 
                c.entityid, 
                c.companyname, 
                c.lastmodifieddate
              `;
      } catch (err) {
        throw this.customErrorObject.updateError({
          errorType: this.customErrorObject.ErrorTypes.INVALID_SEARCH,
          summary: "ERROR_LOADING_QUERY_STRING",
          details: `Error loading parent customers with price changes query string: ${err}`,
        });
      }
    }

    /**
     * Sets the class instance values based on the parsed item row
     *
     * @param {any} parsedItemRow
     * @returns {any}
     */
    setParentValuesForIteration(parsedItemRow) {
      this.parentCustomerObj = {
        internalId: parsedItemRow[0],
        entityId: parsedItemRow[1],
        companyName: parsedItemRow[2],
        lastModifiedDate: parsedItemRow[3],
        existingPriceListFileId: parsedItemRow[4],
      };
    }

    /**
     * Create a map of the existing prices for the customer
     *
     * @returns {void}
     */
    createMapObjOfOldPrices() {
      try {
        const fileContents = file
          .load({
            id: this.parentCustomerObj?.existingPriceListFileId,
          })
          .getContents();

        const fileLines = fileContents.split("\n");
        const headers = fileLines[0].split(",");

        for (let i = 1; i < fileLines.length; i++) {
          if (!fileLines[i].trim()) continue; // Skip empty lines

          const fieldValues = fileLines[i].split(",");
          const priceObj = {};

          headers.forEach((header, index) => {
            priceObj[header.trim()] = fieldValues[index]
              ? fieldValues[index].trim()
              : "";
          });

          if (priceObj.item_id) {
            this.existingPricesMap.set(
              parseInt(priceObj.item_id),
              parseFloat(priceObj.price)
            );
          }
        }

        if (this.existingPricesMap.size === 0) {
          throw this.customErrorObject.updateError({
            errorType: this.customErrorObject.ErrorTypes.NO_VALUE_RETURNED,
            summary: "NO_EXISTING_PRICES_FOUND",
            details: `No existing prices found for ${this.parentCustomerObj.entityId}`,
          });
        }

        log.audit(
          `${this.parentCustomerObj.entityId} | Existing Prices - ${this.existingPricesMap.size}`
        );
      } catch (err) {
        throw this.customErrorObject.updateError({
          errorType: this.customErrorObject.ErrorTypes.FILE_NOT_LOADED,
          summary: "ERROR_LOADING_EXISTING_PRICE_LIST_FILE",
          details: `Error loading existing price list file: ${err}`,
        });
      }
    }

    /**
     * Push new price objects to the newPriceObjsArr
     *
     * @returns {void}
     */
    getNewPriceList() {
      log.audit("Getting new price list...");
      try {
        let priceQuery = /*sql*/ `
          SELECT 
              customeritempricing.item AS item_id,
              BUILTIN.DF (customeritempricing.item) AS sku,
              'Item Pricing' AS pricetype,
              customeritempricing.customer AS customer_id,
              customeritempricing.price AS price 
          FROM
              customeritempricing 
          INNER JOIN
              item 
              ON item.id = customeritempricing.item 
              AND item.isinactive = 'F' 
              AND item.isOnline = 'T' 
          WHERE
              customeritempricing.customer = ${this.parentCustomerObj.internalId} 
          
          UNION
          
          SELECT 
              pricingwithcustomers.item AS item_id,
              BUILTIN.DF (pricingwithcustomers.item) AS sku,
              'Group Pricing' AS pricetype,
              pricingwithcustomers.customer AS customer_id,
              pricingwithcustomers.unitprice AS price 
          FROM
              pricingwithcustomers 
          INNER JOIN
              item 
              ON item.id = pricingwithcustomers.item 
              AND item.isinactive = 'F' 
              AND item.isOnline = 'T' 
          WHERE
              pricingwithcustomers.customer = ${this.parentCustomerObj.internalId} 
              AND pricingwithcustomers.assignedpricelevel = 'T'
              AND pricingwithcustomers.unitprice != 0
              `;

        let sqlPageSize = 5000;

        let paginatedRowBegin = 1;
        let paginatedRowEnd = 5000;

        this.newPriceObjsArr = [];
        let moreRecords = true;

        do {
          let paginatedSQL = /*sql*/ `
            SELECT * FROM ( 
                SELECT ROWNUM AS ROWNUMBER, * FROM (  ${priceQuery} ) 
            ) WHERE ( ROWNUMBER BETWEEN ${paginatedRowBegin} AND ${paginatedRowEnd} )`;

          let queryResults = query
            .runSuiteQL({ query: paginatedSQL, params: [] })
            .asMappedResults();

          this.newPriceObjsArr.push(...queryResults);

          if (queryResults.length < sqlPageSize) {
            moreRecords = false;
          }

          paginatedRowBegin += sqlPageSize;
          paginatedRowEnd += sqlPageSize;
        } while (moreRecords);

        if (this.newPriceObjsArr.length === 0) {
          throw this.customErrorObject.updateError({
            errorType: this.customErrorObject.ErrorTypes.NO_VALUE_RETURNED,
            summary: "NO_NEW_PRICES_FOUND",
            details: `No new prices found for ${this.parentCustomerObj.entityId}`,
          });
        }

        log.audit(
          `${this.parentCustomerObj.entityId} | ${this.newPriceObjsArr.length} New Price(s)`
        );
      } catch (err) {
        throw this.customErrorObject.updateError({
          errorType: this.customErrorObject.ErrorTypes.INVALID_SEARCH,
          summary: "ERROR_GETTING_NEW_PRICES",
          details: `Error getting new price objs arr: ${err}`,
        });
      }
    }

    /**
     * Convert new price list to csv, specify which items are the deltas.
     *
     * @returns {void}
     */
    generatePriceListFile() {
      try {
        let newPricesCounter = 0;

        const csvContent = this.newPriceObjsArr
          .map((itemObj) => {
            const itemId = itemObj.item_id;
            const newPrice = parseFloat(itemObj.price);

            if (this.existingPricesMap.size === 0) {
              itemObj.isPriceChange = true;
            } else {
              const existingPrice = this.existingPricesMap.get(itemId);

              itemObj.isPriceChange =
                existingPrice === undefined || existingPrice !== newPrice;
            }

            itemObj.isPriceChange && newPricesCounter++;

            return Object.values(itemObj).join(",");
          })
          .join("\n");

        log.audit(
          `${this.parentCustomerObj.entityId} | ${newPricesCounter} Price Change(s)`
        );

        const headerRow = Object.keys(this.newPriceObjsArr[0]).join(",");
        const fullCsvContent = headerRow + "\n" + csvContent;

        const fileObj = file.create({
          name: `${this.parentCustomerObj.entityId}_Price_List_${new Date()
            .toISOString()
            .slice(0, 16)
            .replace("T", "_")}.csv`,
          fileType: file.Type.CSV,
          contents: fullCsvContent,
          folder: 9513478, //Current_Price_Lists,
        });

        this.newPriceListFileId = fileObj.save();

        log.audit(
          `${this.parentCustomerObj.entityId} | New Price List File Created`,
          `File ID: ${this.newPriceListFileId}\nhttps://5802576.app.netsuite.com/app/common/media/mediaitem.nl?id=${this.newPriceListFileId}`
        );
      } catch (err) {
        throw this.customErrorObject.updateError({
          errorType: this.customErrorObject.ErrorTypes.RECORD_NOT_SAVED,
          summary: "ERROR_SAVING_FILE",
          details: `Error saving new price list file: ${err}`,
        });
      }
    }

    /**
     * Create a new customer pricing files record for the customer
     *
     * @returns {void}
     */
    createCustomerPricingFilesRecord() {
      try {
        const customerPricingFilesRecord = record.create({
          type: "customrecord_customer_pricing_files",
        });

        customerPricingFilesRecord.setValue({
          fieldId: "custrecord_parent_customer",
          value: this.parentCustomerObj.internalId,
        });

        this.newPriceListFileId &&
          customerPricingFilesRecord.setValue({
            fieldId: "custrecord_current_price_list_file_id",
            value: this.newPriceListFileId,
          });

        customerPricingFilesRecord.setValue({
          fieldId: "custrecord_price_files_last_updated",
          value: new Date(),
        });

        this.customerPricingFilesRecordId = customerPricingFilesRecord.save();

        log.audit(
          `${this.parentCustomerObj.entityId} | Customer Pricing Files Record Created`,
          `Record ID: ${this.customerPricingFilesRecordId}\nRecord Link: https://5802576.app.netsuite.com/app/common/custom/custrecordentry.nl?rectype=3223&id=${this.customerPricingFilesRecordId}`
        );
      } catch (err) {
        throw this.customErrorObject.updateError({
          errorType: this.customErrorObject.ErrorTypes.RECORD_NOT_SAVED,
          summary: "ERROR_CREATING_CUSTOMER_PRICING_FILES_RECORD",
          details: `Error creating customer pricing files record: ${err}`,
        });
      }
    }

    /**
     * Call MR script to create the delta sub-records.
     * The script is structured this way so that the delta MR can utilize reduce to iterate over the price file records
     *  which has the highest number of elements within the different lists that we're working with.
     *
     * @returns {void}
     */
    callScriptToCreateSubRecords() {
      try {
        const mrTask = task.create({
          taskType: task.TaskType.MAP_REDUCE,
          scriptId: "customscript_spl_crt_cstmr_lvl_deltas_mr",
          params: {
            custscript_new_price_list_file_id: this.newPriceListFileId,
            custscript_price_file_record_id: this.customerPricingFilesRecordId,
            custscript_parent_customer_id: this.parentCustomerObj.entityId,
          },
        });

        this.taskId = mrTask.submit();

        log.audit(
          `${this.parentCustomerObj.entityId} | Submitted follow-up MR task`,
          `Task ID: ${this.taskId}`
        );

        var taskStatus = task.checkStatus({
          taskId: this.taskId,
        });

        do {
          taskStatus = task.checkStatus({
            taskId: this.taskId,
          });

          if (taskStatus.status == "FAILED") {
            throw this.customErrorObject.updateError({
              errorType: this.customErrorObject.ErrorTypes.REFERENCE_ERROR,
              summary: "FAILED_MAP_REDUCE",
              details: `Map Reduce Creating Creating Delta Records Failed`,
            });
          }
        } while (
          taskStatus.status != "COMPLETE" &&
          taskStatus.status != "FAILED"
        );
      } catch (err) {
        throw this.customErrorObject.updateError({
          errorType: this.customErrorObject.ErrorTypes.RECORD_NOT_SAVED,
          summary: "ERROR_CREATING_CUSTOMER_PRICING_FILES_RECORD",
          details: `Error creating customer pricing files record: ${err}`,
        });
      }
    }

    /**
     * Call MR script to create the delta sub-records.
     * Since no deployment id is specified, the script will automatically call the next available deployment record.
     * The script is structured this way so that the delta MR can utilize reduce to iterate over the price file records
     *  which has the highest number of elements within the different lists that we're working with.
     *
     * @returns {void}
     */
    callScriptToHandleItemLevelPriceChanges(numberOfDaysBack) {
      try {
        const mrTask = task.create({
          taskType: task.TaskType.MAP_REDUCE,
          scriptId: "customscript_spl_hndl_itm_prc_chngs_mr",
          params: {
            custscript_number_of_days_back: numberOfDaysBack,
          },
        });

        this.taskId = mrTask.submit();

        log.audit(
          `Called Script to Handle Price Changes for Items`,
          `Task ID: ${this.taskId}`
        );

        var taskStatus = task.checkStatus({
          taskId: this.taskId,
        });

        do {
          taskStatus = task.checkStatus({
            taskId: this.taskId,
          });

          if (taskStatus.status == "FAILED") {
            throw this.customErrorObject.updateError({
              errorType: this.customErrorObject.ErrorTypes.REFERENCE_ERROR,
              summary: "FAILED_MAP_REDUCE",
              details: `Map Reduce to Handle Price Changes for Items Failed`,
            });
          }
        } while (
          taskStatus.status != "COMPLETE" &&
          taskStatus.status != "FAILED"
        );
      } catch (err) {
        throw this.customErrorObject.updateError({
          errorType: this.customErrorObject.ErrorTypes.UNHANDLED_ERROR,
          summary: "ERROR_CALLING_MR",
          details: `Error calling MR script: ${err}`,
        });
      }
    }
  }

  exports.CustomerPriceChangesFactory = CustomerPriceChangesFactory;
});
