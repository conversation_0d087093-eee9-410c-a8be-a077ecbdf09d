/**
 * @NApiVersion 2.1
 * @NScriptType scheduledscript
 * @NAmdConfig /SuiteScripts/config.json
 */

define([
	"require",
	"N/sftp",
	"N/file",
	"N/record",
	"N/search",
	"GetEdiPartnerValuesLib",
	"WriteOutgoing850Lib",
	"ProcessOutgoing850EmailLib",
	"PushEdiEmailInfoToDBLib",
	"../../../EDI/Libraries/Vendor_Integration_Libraries/Outgoing_850_Libs/spl_create_purchase_order_obj_lib",
	"../../../Classes/vlmd_custom_error_object",
], function (
	require,
	sftp,
	file,
	record,
	search,
	getEdiPartnerValuesLib,
	writePoLib,
	processEnd,
	pushEdiEmailInfoToDBLib
) {
	function execute(context) {
		const createPurchaseOrderObjLib = require("../../../EDI/Libraries/Vendor_Integration_Libraries/Outgoing_850_Libs/spl_create_purchase_order_obj_lib");
		/** @type {import("../../../Classes/vlmd_custom_error_object").CustomErrorObject} */
		const CustomErrorObject = require("../../../Classes/vlmd_custom_error_object");
		const customErrorObject = new CustomErrorObject();

		var vendorData = {
			prodGUID: "fa7bed89f8de4a17834a22c15de88fd8",
			sandboxGUID: "********************************",
			vendorInternalId: 728,
			integrationStartDate: "3/11/2021 12:00 am",
			testDirectory: "/users/Drive/TEST/OUT/850",
			prodDirectory: "/users/Drive/OUT/850",
			ediRefDirectory: "/EDI Reference Files/Drive/Purchase_Orders",
			vendorName: "Drive",
			getShippingAccount: false,
			documentTypeId: 5,
			pushEmailToDB: true,
		};

		var processingLog = [];
		var transactionsProcessedSuccessfully = [];
		var processedSuccesfully = true;

		try {
			var vendorConnection = getVendorConnection();
			var supplylineConnection = getSupplylineConnection();
			var partnerValues = getPartnerValues();

			var poIds = getInternalIds();

			if (poIds.length <= 0) {
				return true;
			}

			processPurchaseOrders();
			if (customErrorObject.summary) {
				processedSuccesfully = false;
			}

			try {
				var sentEmailObj = processEnd.processEnd(
					processingLog,
					transactionsProcessedSuccessfully,
					processedSuccesfully,
					vendorData.vendorName
				);

				if (vendorData.pushEmailToDB) {
					pushEdiEmailInfoToDBLib.pushEdiEmailInfoToDB(
						vendorData,
						sentEmailObj
					);
				}
			} catch (err) {
				throw {
					name: "ERROR_SENDING_PUSHING_EDI_EMAIL",
					message: err.message,
				};
			}

			if (customErrorObject.summary) {
				throw customErrorObject;
			}

			function getVendorConnection() {
				try {
					var hostKey =
						"AAAAB3NzaC1yc2EAAAABIwAAAIEA2YIvZPcsWA3jP6yq5J" +
						"5XYunNySNspUZUhtzxXoePDZEhxJFIF/am9o+15CqO4hlyEDni0m+VW4" +
						"LVSoiUnj31xJis91EcYrUeCewIcSwof4Xs05ghth/2SKn0cITsR5hOeST" +
						"l4jFsMf9oK0+b2gr4jdV4fria4E/eF2u0qQArWuc=";

					return sftp.createConnection({
						username: "FTPadmin",
						passwordGuid: vendorData.prodGUID, //production account
						//passwordGuid: vendorData.sandboxGUID, //sandbox account
						url: "************",
						hostKey: hostKey,
						//directory: vendorData.testDirectory
						directory: vendorData.prodDirectory,
					});
				} catch (err) {
					throw {
						name: `ERROR_GETTING_DRIVE_CONNECTION`,
						message: err.message,
					};
				}
			}

			function getSupplylineConnection() {
				try {
					var hostKey =
						"AAAAB3NzaC1yc2EAAAABIwAAAIEA2YIvZPcsWA3jP6yq5J" +
						"5XYunNySNspUZUhtzxXoePDZEhxJFIF/am9o+15CqO4hlyEDni0m+VW4" +
						"LVSoiUnj31xJis91EcYrUeCewIcSwof4Xs05ghth/2SKn0cITsR5hOeST" +
						"l4jFsMf9oK0+b2gr4jdV4fria4E/eF2u0qQArWuc=";

					return sftp.createConnection({
						username: "FTPadmin",
						passwordGuid: vendorData.prodGUID, //production account
						//passwordGuid: vendorData.sandboxGUID, //sandbox account
						url: "************",
						hostKey: hostKey,
						directory: vendorData.ediRefDirectory,
					});
				} catch (err) {
					throw {
						name: `ERROR_GETTING_SUPPLYLINE_CONNECTION`,
						message: err,
					};
				}
			}

			function getPartnerValues() {
				try {
					return getEdiPartnerValuesLib.getDriveValues();
				} catch (err) {
					throw {
						name: "ERROR_GETTING_DRIVE_VALUES",
						message: err.message,
					};
				}
			}

			function getInternalIds() {
				try {
					var poSearch = search.create({
						type: "purchaseorder",
						filters: [
							["type", "anyof", "PurchOrd"],
							"AND",
							["name", "anyof", vendorData.vendorInternalId],
							"AND",
							["custbody_spl_po_edi_trans_cntrl_number", "isempty", ""],
							"AND",
							["mainline", "is", "T"],
							"AND",
							["datecreated", "onorafter", vendorData.integrationStartDate],
							"AND",
							["status", "anyof", "PurchOrd:B"],
						],
					});

					var results = [];

					poSearch.run().each((result) => {
						results.push(result.id);
						return true;
					});

					return results;
				} catch (err) {
					throw {
						name: "ERROR_GETTING_INTERNAL_IDS",
						message: err.message,
					};
				}
			}

			function processPurchaseOrders() {
				poIds.forEach((id) => {
					try {
						processedSuccesfully = true;
						var purchaseOrderObj = getPurchaseOrderObj();

						var purchaseOrderNumber = purchaseOrderObj.number;
						if (!purchaseOrderObj.continueProcessing) {
							processedSuccesfully = false;
							return;
						}
						var ediFile = writePurchaseOrderAsEdiFile();

						if (processedSuccesfully) {
							var fileToUpload = createFile();
							uploadFileToDrive();
							uploadFileToSupplyline();
							addControlNumberToPurchaseOrder();
							transactionsProcessedSuccessfully.push(purchaseOrderNumber);
						}

						//*******Process Purchase Order Helper Functions*******
						function getPurchaseOrderObj() {
							var obj = createPurchaseOrderObjLib.getPurchaseOrderObj(
								id,
								vendorData.getShippingAccount,
								customErrorObject
							);
							processingLog = processingLog.concat(obj.errorLog);

							return obj;
						}

						function writePurchaseOrderAsEdiFile() {
							var fileObj = writePoLib.getPurchaseOrderAsEDI(
								partnerValues,
								purchaseOrderObj
							);
							if (fileObj.success) {
								return fileObj.value;
							} else {
								throw {
									name: "ERROR_WRITING_EDI_FILE",
									message: `${purchaseOrderNumber}: ${fileObj.error}`,
								};
							}
						}

						function createFile() {
							try {
								return file.create({
									name: purchaseOrderObj.controlNumber + ".edi",
									fileType: file.Type.PLAINTEXT,
									contents: ediFile,
								});
							} catch (err) {
								throw {
									name: "ERROR_CREATING_FILE",
									message: `${purchaseOrderNumber}: ${err.message}`,
								};
							}
						}

						function uploadFileToDrive() {
							try {
								vendorConnection.upload({
									file: fileToUpload,
									replaceExisting: true,
								});
							} catch (err) {
								throw {
									name: "ERROR_UPLOADING_FILE",
									message: `File for ${purchaseOrderNumber} not uploaded to DRIVE folder. 
								${err.message}`,
								};
							}
						}

						function uploadFileToSupplyline() {
							try {
								supplylineConnection.upload({
									file: fileToUpload,
									replaceExisting: true,
								});
							} catch (err) {
								throw {
									name: "ERROR_UPLOADING_FILE",
									message: `File for ${purchaseOrderNumber} not uploaded to SUPPLYLINE folder. 
								${err.message}`,
								};
							}
						}

						function addControlNumberToPurchaseOrder() {
							try {
								if (!purchaseOrderObj.controlNumber) {
									throw {
										name: `MISSING_VALUE`,
										message: `${purchaseOrderNumber} is missing a control number to save to the purchase order record`,
									};
								}

								record.submitFields({
									type: record.Type.PURCHASE_ORDER,
									id: id,
									values: {
										custbody_spl_po_edi_trans_cntrl_number:
											purchaseOrderObj.controlNumber,
									},
									options: {
										ignoreMandatoryFields: true,
									},
								});
							} catch (err) {
								throw {
									name: "ERROR_SAVING_CONTROL_NUMBER",
									message: `${purchaseOrderNumber} - ${purchaseOrderObj.controlNumber}: ${err.message}`,
								};
							}
						}
					} catch (err) {
						customErrorObject.updateError({
							errorType: customErrorObject.ErrorTypes.UNHANDLED_ERROR,
							summary: "ERROR_PROCESSING_SPECIFIC_PO",
							details: `Purchase order (internal id: ${id}) not processed successfully.`,
						});

						processingLog.push(`${customErrorObject.summary}

${customErrorObject.details}`);
					}
				});
			}
		} catch (err) {
			customErrorObject.throwError({
				summaryText: `ERROR_PROCESSING_DRIVE_850`,
				error: err,
			});
		}
	}

	return {
		execute,
	};
});