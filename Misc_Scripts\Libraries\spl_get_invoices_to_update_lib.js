//@ts-ignore
define(["N/log", "N/error", "N/query", "N/search"], function (
	log,
	error,
	query,
	search
) {
	function getInvoicesToUpdate() {
		try {
			const invoicesFromBillsCreatedOrEditedToday =
				getInvoicesFromBillsCreatedOrEditedToday();

			const invoicesFromCreditMemoCreatedToday =
				getInvoicesFromCreditMemosCreatedToday();

			const newUnprocessedInvoices = getNewUnprocessedInvoices();

			return [
				...invoicesFromBillsCreatedOrEditedToday,
				...invoicesFromCreditMemoCreatedToday,
				...newUnprocessedInvoices,
			];
		} catch (e) {
			throw { name: "Error getting invoices", message: e };
		}
	}

	function getInvoicesFromBillsCreatedOrEditedToday() {
		//Order of the query affects the invoice objects in mapToInvObj(). Do not edit the first 2 selects.
		const sqlQuery = `SELECT
		InvLink.nextdoc AS 'invoice id',
		BUILTIN.DF(InvLink.nextdoc) AS 'invoice name',
		SO.status AS 'sales order status',
	FROM
		transaction AS BILL
		LEFT OUTER JOIN transactionLine ON transactionLine.transaction = BILL.id
		AND transactionLine.id = (
			SELECT
				id
			FROM
				transactionLine
			WHERE
				transactionLine.transaction = BILL.id
				AND mainline = 'T'
		)
		JOIN PreviousTransactionLink AS PoBillLink ON BILL.id = PoBillLink.nextdoc
		JOIN transaction AS PO ON PO.id = PoBillLink.previousdoc
		JOIN PreviousTransactionLink AS DropShipLink ON PO.id = DropShipLink.nextdoc
		JOIN transaction AS SO ON SO.id = DropShipLink.previousdoc
		LEFT OUTER JOIN NextTransactionLink AS InvLink ON SO.id = InvLink.previousdoc
		AND InvLink.nextdoc = (
			SELECT
				MAX(nextdoc)
			FROM
				NextTransactionLink
			WHERE
				SO.id = NextTransactionLink.previousdoc
		)
	WHERE
		transactionline.subsidiary = 1
		AND BILL.type = 'VendBill'
		AND PoBillLink.linktype = 'OrdBill'
		AND InvLink.linktype = 'OrdBill'
		AND (
			TO_CHAR (SYSDATE, 'MM/DD/YYYY') = TO_CHAR (BILL.createddate, 'MM/DD/YYYY')
			OR TO_CHAR (SYSDATE, 'MM/DD/YYYY') = TO_CHAR (BILL.lastmodifieddate, 'MM/DD/YYYY')
		)
		`;
		const sqlResults = helperFunctions.runSqlQuery(sqlQuery);

		if (!sqlResults) {
			return [];
		}

		const parsedResultsArr = helperFunctions.parseSql(sqlResults);

		let invoicesToUpdate =
			helperFunctions.removeInvoicesForSOsNotFullyBilled(parsedResultsArr);

		invoicesToUpdate = helperFunctions.mapToInvObj(invoicesToUpdate);

		return invoicesToUpdate;
	}

	function getInvoicesFromCreditMemosCreatedToday() {
		//Order of the query affects the invoice objects in mapToInvObj(). Do not edit the first 2 selects.

		const sqlQuery = `SELECT
		previousdoc,
		BUILTIN.DF(previousdoc)
	FROM
		transaction CREDITMEMO
		LEFT OUTER JOIN transactionLine ON transactionLine.transaction = CREDITMEMO.id
		AND transactionLine.id = (
			SELECT
				id
			FROM
				transactionLine
			WHERE
				transactionLine.transaction = CREDITMEMO.id
				AND mainline = 'T'
		)
		JOIN nexttransactionlink LINKINGINVOICE ON CREDITMEMO.id = LINKINGINVOICE.nextdoc
	WHERE
		type = 'CustCred'
		AND linktype = 'SaleRet'
		AND transactionline.subsidiary = 1
		AND TO_CHAR(lastModifiedDate, 'MM/DD/YYYY') IN (
			SELECT
				TO_CHAR (SYSDATE, 'MM/DD/YYYY')
			FROM
				Dual
		)`;

		const sqlResults = helperFunctions.runSqlQuery(sqlQuery);

		if (!sqlResults) {
			return [];
		}

		const parsedResultsArr = helperFunctions.parseSql(sqlResults);
		const invoicesToUpdateFromCreditMemoCreatedToday =
			helperFunctions.mapToInvObj(parsedResultsArr);

		return invoicesToUpdateFromCreditMemoCreatedToday;
	}

	function getNewUnprocessedInvoices() {
		//Order of the query affects the invoice objects in mapToInvObj(). Do not edit the first 2 selects.

		const sqlQuery = `SELECT
        inv.id,
        inv.tranid,
     FROM
        transaction inv 
        LEFT OUTER JOIN
           transactionline 
           ON transactionline.transaction = inv.id 
           AND transactionline.id = 
           (
              SELECT
                 id 
              FROM
                 transactionline 
              WHERE
                 transactionline.transaction = inv.id 
                 AND mainline = 'T' 
           )
     WHERE
        inv.type = 'CustInvc' 
        AND transactionline.subsidiary = 1 
        AND to_char(inv.createddate, 'MM/DD/YYYY') IN 
        (
           SELECT
              to_char (sysdate, 'MM/DD/YYYY') 
           FROM
              DUAL 
        )
        AND inv.custbody_spl_gp_invoice_gross_profit IS NULL`;

		const sqlResults = helperFunctions.runSqlQuery(sqlQuery);
		const parsedResultsArr = helperFunctions.parseSql(sqlResults);
		const newUnprocessedInvoices =
			helperFunctions.mapToInvObj(parsedResultsArr);

		return newUnprocessedInvoices;
	}

	var helperFunctions = (function () {
		function runSqlQuery(sqlQuery) {
			const sqlResults = query.runSuiteQL({
				query: sqlQuery,
			});

			if (sqlResults.results.length <= 0) {
				log.debug("No  SQL results found.");
				return null;
			}

			return sqlResults;
		}

		function parseSql(sqlResults) {
			const parsedResultsArr = sqlResults.results.map(
				(result) => result.values
			);

			return parsedResultsArr;
		}

		function removeInvoicesForSOsNotFullyBilled(invoiceObjs) {
			//SuiteQL query was not able to filter out the status so we are filtering it in the script.

			const invoicesToUpdate = invoiceObjs.filter(
				(invoiceObj) => invoiceObj.includes("G") //Sales order status - fully billed.
			);

			return invoicesToUpdate;
		}

		function mapToInvObj(invObjsArr) {
			const mappedInvObjsArr = invObjsArr.map((result) => {
				return {
					invoiceInternalId: result[0],
					invoiceName: result[1],
				};
			});

			return mappedInvObjsArr;
		}

		return {
			runSqlQuery,
			parseSql,
			removeInvoicesForSOsNotFullyBilled,
			mapToInvObj,
		};
	})();

	return {
		getInvoicesToUpdate,
		getInvoicesFromBillsCreatedOrEditedToday,
		getInvoicesFromCreditMemosCreatedToday,
		getNewUnprocessedInvoices,
	};
});
