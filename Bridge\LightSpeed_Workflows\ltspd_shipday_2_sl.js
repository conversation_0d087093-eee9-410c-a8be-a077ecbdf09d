/**
 * @description SL that gets triggered if the delivery address custom field is OTHER
 *
 * </br><b>Entry Points:</b> onRequest
 *
 * @NApiVersion 2.1
 * @NScriptType Suitelet
 *
 * <AUTHOR>
 * @module ltspd_shipday
 */

define(["require", "N/log"], function (/** @type {any} */ require) {
  const log = require("N/log");

  /**
   * Return the object for LightSpeed containing the action message of requiring new delivery custom fields
   *
   * @param {import("N/types").EntryPoints.Suitelet.onRequestContext} context Suitelet onRequest context
   */
  function onRequest(context) {
    if (context.request.method === "POST") {
      try {
        const data = context.request.body;
        const jsonObj = JSON.parse(data);
        const username = jsonObj.user["username"];

        if (!jsonObj.customer?.id) return;
        // if(username !=  "Chaya Drillick" && username != "<PERSON>") return;

        const customFields = jsonObj.sale["custom_fields"];

        if (customFields.length < 1) return;
        const deliveryAddressField = customFields.find(
          (field) => field.name === "delivery_address"
        );
        if (!deliveryAddressField || !deliveryAddressField.string_value) return;
        let deliveryAddress;
        try {
          deliveryAddress = JSON.parse(deliveryAddressField.string_value);
        } catch (parseError) {
          log.error("Error parsing delivery address JSON", {
            error: parseError,
            value: deliveryAddressField.string_value,
          });
          return;
        }

        log.audit("Custom Delivery Address Field Value", deliveryAddress);

        if (!deliveryAddress || deliveryAddress.address1 !== "Other") return;

        const actionToReturn = {
          actions: [
            {
              type: "require_custom_fields",
              title: "New Address",
              message: "Please insert a new delivery address:",
              entity: "sale",
              required_custom_fields: [
                {
                  name: "address1",
                },
                //Leaving out address2 for now because if it would be in, it would be required for every address. They will handle splitting it if necessary.
                {
                  name: "city",
                },
                {
                  name: "state",
                },
                {
                  name: "zip",
                },
              ],
            },
          ],
        };

        try {
          //Validating that the user entered an address
          const hasAddress1 = customFields.some(
            (field) =>
              field.name === "address1" &&
              field.string_value &&
              field.string_value.trim() !== ""
          );
          if (!hasAddress1)
            context.response.write(JSON.stringify(actionToReturn));
        } catch (e) {
          log.debug("error", e);
        }
      } catch (/** @type {any} */ err) {
        log.error(err.name, err.message);
      }
    }
  }

  return {
    onRequest: onRequest,
  };
});
