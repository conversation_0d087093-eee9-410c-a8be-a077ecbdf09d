/**
 * Interface and type definitions for Bridge transaction objects
 *
 * <AUTHOR> Prospero
 */

import {Type} from "N/record";

export type BridgeTransactionLinesObject {
	recordType: Type;
	indices: number[];
	linkedTransaction?: string;
}

export type BridgeTransactionsObject {
	[key:string]: BridgeTransactionLinesObject;
}

export type BridgeSubsidiaryTransactionsObject {
	transactionsObj: BridgeTransactionsObject;
	casesCount: number;
}

export type BridgeSubsidiariesObject {
	[key:string]: BridgeSubsidiaryTransactionsObject;
}

export type BridgeRoyalDiscountQueryResult {
	transactionId: string,
	sublistLineIndex: number,
	additionalCases: number,
	subsidiaryId: string,
	isCase: boolean,
	linkedTransaction: string
}

export interface BridgeRoyalDiscount {
	subsidiariesObj: BridgeSubsidiariesObject,
	transactionObj: BridgeRoyalDiscountQueryResult,
	recordType: Type;
	setCurrentTransactionObject: (transactionObj) => void;
	doesSubsidiaryExist: () => boolean;
	doesIndicesArrayExist: () => boolean;
	initializeSubsidiary: () => void;
	addSublistIndex: () => void;
	incrementCasesCount: () => void;
	setLinkedTransaction: () => void;
	getSubsidiariesObject: () => BridgeSubsidiariesObject;
	new(recordType): BridgeRoyalDiscount
}