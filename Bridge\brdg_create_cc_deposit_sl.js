/**
 * @description SL to create a deposit in NS with LS CC payments
 
 * @NApiVersion 2.1
 * @NScriptType Suitelet
 * @NAmdConfig /SuiteScripts/config.json
 *
 * @param {import ("N/types")}
 * <AUTHOR>
 * @module brdg_create_cc_deposit_sl
 */

define([
	"require",
	"N/log",
	"N/ui/serverWidget",
	"N/url",
	"N/https",
	"N/record",
	"N/email",
	"N/runtime",
	"N/query",
	"N/file",
	"../Libraries/External_Libraries/vlmd_moment_lib",
	"../Classes/vlmd_custom_error_object",
	"../Helper_Libraries/vlmd_record_module_helper_lib",
], function (require) {
	const log = require("N/log");
	const serverWidget = require("N/ui/serverWidget");
	const url = require("N/url");
	const https = require("N/https");
	const record = require("N/record");
	const email = require("N/email");
	const runtime = require("N/runtime");
	const query = require("N/query");
	const file = require("N/file");

	const moment = require("../Libraries/External_Libraries/vlmd_moment_lib");

	const CustomErrorObject = require("../Classes/vlmd_custom_error_object");
	const customErrorObject = new CustomErrorObject();

	const recordHelperLib = require("../Helper_Libraries/vlmd_record_module_helper_lib");
	var ccPaymentTypeId = "167";
	var unprocessedPayments = [];
	var ccProcessingFeeTotal = 0;

	var helperFunctions = (function () {
		/**
		 * Creates form for the get request of the SL
		 */
		function createGetForm(form) {
			try {
				// Create the main form

				// Add fields to the main form body
				let accountField = (form.addField({
					id: "custpage_account",
					label: "Account",
					type: serverWidget.FieldType.SELECT,
					source: "account",
					layoutType: serverWidget.FieldLayoutType.OUTSIDEBELOW,
				}).isMandatory = true);

				let dateField = (form.addField({
					id: "custpage_date",
					label: "Date",
					type: serverWidget.FieldType.DATE,
					layoutType: serverWidget.FieldLayoutType.OUTSIDEBELOW,
				}).isMandatory = true);

				let subsidiaryField = (form.addField({
					id: "custpage_subsidiary",
					label: "Subsidiary",
					type: serverWidget.FieldType.SELECT,
					source: "subsidiary",
					layoutType: serverWidget.FieldLayoutType.OUTSIDEBELOW,
				}).isMandatory = true);

				let fileField = (form.addField({
					id: "custpage_file",
					label: "Payout CSV File",
					type: serverWidget.FieldType.FILE,
					layoutType: serverWidget.FieldLayoutType.OUTSIDEBELOW,
				}).isMandatory = true);

				// Add a submit button
				let submitButton = form.addSubmitButton({
					label: "Create Deposit",
					style: "success",
				});
			} catch (e) {
				customErrorObject.throwError({
					summaryText: "ERROR_IN_GET",
					error: e,
				});
			}
		}

		/**
		 * Creates form for the post request of the SL
		 */
		function createPostForm(form, paramObj, resultsObj) {
			var suiteletUrl = url.resolveScript({
				scriptId: runtime.getCurrentScript().id,
				deploymentId: runtime.getCurrentScript().deploymentId,
				returnExternalURL: false,
			});

			form.addField({
				id: "custpage_message",
				label: "Message",
				type: serverWidget.FieldType.INLINEHTML,
			}).defaultValue = `
      <h1>Create Deposit Results:</h1>
      <p style = "font-size:16px">Date: ${paramObj.date}<br/> Account: ${
				paramObj.account
			} <br/> Subsidiary: ${paramObj.subsidiary}<br/> <br/> ${
				resultsObj.error != null
					? `Error creating deposit! ${resultsObj.error} `
					: `Deposit created sucessfully.`
			}
      <br/><br/>See email for more details
       ${
					resultsObj.unprocessedPayments &&
					resultsObj.unprocessedPayments.length > 0
						? `and for a list of unprocessed payments.`
						: "."
				}</p>
      \n
      ${
				ccProcessingFeeTotal > 0
					? `<p style="font-size:16px">The total credit card processing fee from this payout is: ${new Intl.NumberFormat(
							"en-US",
							{ style: "currency", currency: "USD" }
					  ).format(ccProcessingFeeTotal)}</p>`
					: ""
			}
      \n
        
      ${
				resultsObj.depositId != null
					? `<input type="button" style="width: 200px; height: 60px; font-size: 16px; background-color: #0492C2; color: #FFFFFF; border: none; border-radius: 4px; cursor: pointer;" onclick="window.location.href='/app/accounting/transactions/deposit.nl?id=${resultsObj.depositId}';" value="Go to deposit">`
					: ""
			}
      <input type="button" style="width: 200px; height: 60px; font-size: 16px; background-color: #0492C2; color: #FFFFFF; border: none; border-radius: 4px; cursor: pointer;" onclick="window.location.href='${suiteletUrl}';" value="Create another deposit">
`;
		}

		/**
		 * Gets an array of all NS sale ids corresponding to the uploaded LS invoice numbers
		 *
		 * @param {array} invoiceNumbers
		 * @returns array of NS sale ids
		 */
		function getAllNsSaleIdsArr(invoiceNumbers) {
			const quotedInvoiceNumbers = invoiceNumbers.map((num) => "'" + num + "'");

			const sqlQuery = /*sql*/ `
      SELECT 
        id, 
        custbody_in8_vend_number,
        status,
      FROM 
        transaction 
      WHERE 
          paymentmethod = 14 
        AND
          custbody_in8_vend_number in (${quotedInvoiceNumbers})`;

			return query
				.runSuiteQL({
					query: sqlQuery,
				})
				.asMappedResults();
		}

		/**
		 * Gets all LS transactions that were made by CC within the date range
		 * //Note: this function is not used now, instead we upload a CSV file
		 *
		 * @param {object} paramObj {subsidiary, date}
		 * @returns {Array<invoice_number, paymentinfo>}
		 */
		function getListOfCCSales(paramObj) {
			try {
				const storesObjArr = [
					{
						name: "thevineyardsouth",
						subsidiaryId: 24,
						token: "lsxs_pt_1dIAHjr5zWJVig7fRMJVuyaGRX3b2ehI",
					},
					{
						name: "thevineyardmadison",
						subsidiaryId: 31,
						token: "lsxs_pt_RD5JY4e2A8AUIF7OXerflq4aF5Z3grrl",
					},
					{
						name: "thevineyardexpress",
						subsidiaryId: 30,
						token: "lsxs_pt_FmH7n1LeAcSYxcy03j7N1zy4kNVOBPbZ",
					},
					{
						name: "thevineyardbergenfield",
						subsidiaryId: 33,
						token: "lsxs_pt_N3ob7fzftAE9u18xiyjEOSvztcBkJP4r",
					},
					{
						name: "thevineyardwestgate",
						subsidiaryId: 32,
						token: "lsxs_pt_pgZxkIQQBaxZAUAwFsl7NSKz8Lz7XAKQ",
					},
				];

				const storeObj = storesObjArr.find(
					(store) => store.subsidiaryId == paramObj.subsidiary
				);

				const dateUtc = moment.utc(paramObj.date).format();

				const dayOfWeek = moment.utc(paramObj.date).format("dddd");
				const fromDate =
					dayOfWeek == "Monday"
						? moment(paramObj.date).subtract(3, "days").format()
						: moment(paramObj.date).subtract(1, "days").format();
				const dateEnd = moment(paramObj.date).add(1, "days").format();

				const oneDayAgo = moment(paramObj.date).subtract(1, "days").format();

				var header = {
					accept: "application/json",
					authorization: `Bearer ${storeObj.token}`,
				};

				//API call to retrieve all sales from the chosen store between the chosen date range
				var apiURL = `https://${storeObj.name}.retail.lightspeed.app/api/2.0/search?type=sales&&status=CLOSED&date_from=${fromDate}&date_to=${dateUtc}`;

				var responseObj = https.request({
					method: https.Method.GET,
					url: apiURL,
					headers: header,
				});

				const bodyData = JSON.parse(responseObj.body);
				const orderArr = bodyData.data;

				//Filter all the sales to return an obj with payment method of CC and the invoice number
				const ccPaymentsArr = orderArr.flatMap((sale) =>
					sale.payments
						.filter((payment) => payment.payment_type_id === ccPaymentTypeId)
						.map((payment) => ({
							invoiceNum: sale.invoice_number,
							amount: payment.amount,
						}))
				);

				return ccPaymentsArr;
			} catch (e) {
				throw customErrorObject.updateError({
					errorType: customErrorObject.ErrorTypes.NO_VALUE_RETURNED,
					summary: "ERROR_GETTING_VOIDED_ORDERS",
					details: `Error getting voided orders via the LightSpeed API: ${e}`,
				});
			}
		}

		/**
		 *
		 * @param {object} file
		 * @returns array of LS invoice numbers from CSV file
		 */
		function getListOfCCSalesFromFile(file) {
			const lines = file.trim().split("\n");
			const headers = lines[0].split(",");

			const [orderIdIndex, amountIndex, lineTypeIndex] = [
				"order id",
				"amount",
				"line type",
			].map((header) =>
				headers.findIndex((h) => h.toLowerCase().trim() === header)
			);

			if (orderIdIndex === -1 || amountIndex === -1 || lineTypeIndex === -1) {
				throw new Error(
					'CSV file does not contain "Order ID" and "Amount" and "Line Type" columns'
				);
			}

			const ccPaymentsArr = [];

			for (let i = 1; i < lines.length; i++) {
				const line = lines[i].split(",");
				const orderId = line[orderIdIndex].trim();
				const amountValue = line[amountIndex];
				const amount = parseFloat(amountValue);
				const lineType = line[lineTypeIndex].trim();

				if (!isNaN(amount) && amount < 0 && lineType === "FEE") {
					ccProcessingFeeTotal += Math.abs(amount);
				}

				if (!isNaN(amount) && amount > 0 && orderId) {
					ccPaymentsArr.push({ invoiceNum: orderId, amount: amount });
				}
			}

			return ccPaymentsArr;
		}

		/**
		 *
		 * @param {array} ccPaymentsArr
		 * @returns new array with both IDs
		 */
		function getFullArrayWithBothIds(ccPaymentsArr) {
			const invoiceNums = ccPaymentsArr.map(({ invoiceNum }) => invoiceNum);
			const arrayOfNsSaleIds = getAllNsSaleIdsArr(invoiceNums);
			const ccPaymentsWithNsIdArr = [];

			ccPaymentsArr.forEach((ccPaymentObj) => {
				const match = arrayOfNsSaleIds.find(
					(item) => item.custbody_in8_vend_number === ccPaymentObj.invoiceNum
				);

				switch (true) {
					case match && match.status === "B":
						// Not deposited
						ccPaymentsWithNsIdArr.push({ ...ccPaymentObj, id: match.id });

						break;

					case match && match.status !== "B":
						unprocessedPayments.push({
							...ccPaymentObj,
							error: "This transaction is already deposited.",
						});

						break;

					default:
						unprocessedPayments.push({
							...ccPaymentObj,
							error: "Cannot find matching NS payment!",
						});
				}
			});

			return ccPaymentsWithNsIdArr;
		}

		/**
		 *
		 * @param {object} paramObj
		 * @param {Array<{invoiceNum, paymentAmount}>} ccPaymentsArr
		 * @returns
		 */
		function createDepositRecord(paramObj, ccPaymentsArr) {
			try {
				const nsDepositRecord = record.create({
					type: record.Type.DEPOSIT,
					defaultValues: {
						disablepaymentfilters: true,
					},
					isDynamic: true,
				});
				const depositValuesObj = {
					account: paramObj.account,
					subsidiary: paramObj.subsidiary,
					date: paramObj.date,
					trandate: new Date(paramObj.date),
				};

				recordHelperLib.setBodyValues(depositValuesObj, nsDepositRecord);

				var totalPaymentAmount = 0;

				ccPaymentsArr.forEach((ccPaymentObj, index) => {
					if (typeof ccPaymentObj == "object") {
						try {
							const lineNumber = index + 1;

							nsDepositRecord.selectLine({
								sublistId: "payment",
								line: lineNumber,
							});

							nsDepositRecord.setCurrentSublistValue({
								sublistId: "payment",
								fieldId: "id",
								value: ccPaymentObj.id,
							});

							const paymentAmount = ccPaymentObj.amount;

							nsDepositRecord.setCurrentSublistValue({
								sublistId: "payment",
								fieldId: "paymentamount",
								value: paymentAmount,
							});

							nsDepositRecord.setCurrentSublistValue({
								sublistId: "payment",
								fieldId: "deposit",
								value: true,
							});

							nsDepositRecord.commitLine({
								sublistId: "payment",
							});
							totalPaymentAmount += paymentAmount;
						} catch (e) {
							unprocessedPayments.push({
								error: e.message,
								lsPayment: ccPaymentObj.invoiceNum,
							});
						}
					} else {
						unprocessedPayments.push({
							error: "Error with cc payment obj",
							lsPayment: ccPaymentObj,
						});
					}
				});

				nsDepositRecord.setValue({
					fieldId: "total",
					value: totalPaymentAmount,
				});

				var depositId = nsDepositRecord.save({
					ignoreMandatoryFields: true,
				});
			} catch (e) {
				customErrorObject.updateError({
					errorType: customErrorObject.ErrorTypes.UNHANDLED_ERROR,
					summary: "ERROR_CREATING_DEPOSIT_RECORD",
					details: `Error creating deposit record: ${e}`,
				});

				var error =
					e.code == "DUP_RCRD_LINK"
						? "The same exact record exists already."
						: e.message ==
						  "You must enter at least one line item for this transaction."
						? "Could not find any payments to enter on this deposit record."
						: e.message;
			}

			return {
				depositId: depositId ?? null,
				unprocessedPayments:
					unprocessedPayments.length > 0 ? unprocessedPayments : null,
				error: error ?? null,
			};
		}

		/**
		 *
		 * @param {object} paramObj
		 * @param {object} resultsObj
		 */
		function sendResultsEmail(paramObj, resultsObj) {
			try {
				var recipientEmail = runtime.getCurrentUser().id;
				var subject = `BRDG - Create Deposit Results`;
				var body = `Date: ${paramObj.date}, Account ID: ${paramObj.account}, Subsidiary ID: ${paramObj.subsidiary}<br/><br/>`;

				if (resultsObj.error) {
					body += `Errors: ${
						resultsObj.error ==
						"You must enter at least one line item for this transaction."
							? "No matching payments found."
							: resultsObj.error
					}`;
				}

				if (resultsObj.depositId) {
					body += `Deposit successfully created! <br/> Deposit Record ID: ${resultsObj.depositId} 
          <br/> <br/> <a href='/app/accounting/transactions/deposit.nl?id=${resultsObj.depositId}'> Click here to go to deposit record.</a>`;
				}

				body +=
					ccProcessingFeeTotal > 0
						? `<br/>The total credit card processing fee from this payout is: ${new Intl.NumberFormat(
								"en-US",
								{ style: "currency", currency: "USD" }
						  ).format(Math.abs(ccProcessingFeeTotal))}`
						: "";

				if (
					resultsObj.unprocessedPayments &&
					resultsObj.unprocessedPayments.length > 0
				) {
					body += `<br/> <br/> Unprocessed Payments:\n`;
					resultsObj.unprocessedPayments.forEach((paymentResultObj) => {
						body += `<br/> Invoice Number: ${
							paymentResultObj.lsPayment ?? paymentResultObj.invoiceNum
						} Error: ${paymentResultObj.error}`;
					});
				}

				email.send({
					author: 223244, //Requests user
					recipients: recipientEmail,
					subject: subject,
					body: body,
				});
			} catch (error) {
				customErrorObject.updateError({
					errorType: customErrorObject.ErrorTypes.UNHANDLED_ERROR,
					summary: "ERROR_SENDING_EMAIL",
					details: `Error sending results email: ${e}`,
				});
			}
		}

		return {
			createGetForm,
			createPostForm,
			getListOfCCSales,
			getListOfCCSalesFromFile,
			getFullArrayWithBothIds,
			createDepositRecord,
			sendResultsEmail,
		};
	})();

	return {
		onRequest: function (context) {
			const form = serverWidget.createForm({
				title: "Deposit for LightSpeed Payments",
			});

			try {
				if (context.request.method === "GET") {
					helperFunctions.createGetForm(form);
				} else {
					//POST Request
					const paramObj = {
						date: context.request.parameters.custpage_date,
						account: context.request.parameters.custpage_account,
						subsidiary: context.request.parameters.custpage_subsidiary,
					};

					log.audit("Process Payout", paramObj);

					var lsPayoutFile = context.request.files.custpage_file;

					if (!lsPayoutFile) {
						throw customErrorObject.updateError({
							errorType: customErrorObject.ErrorTypes.MISSING_PARAM,
							summary: "NO_FILE_PASSED_IN",
							details: `No LightSpeed payout file was passed in`,
						});
					}

					var fileObj = lsPayoutFile;
					fileObj.folder = 8345325; // LightSpeed Payouts
					fileObj.save();

					var fileContents = fileObj.getContents();

					const ccPaymentsArr =
						helperFunctions.getListOfCCSalesFromFile(fileContents);

					var ccPaymentsWithNsIdArr =
						ccPaymentsArr &&
						ccPaymentsArr.length > 0 &&
						helperFunctions.getFullArrayWithBothIds(ccPaymentsArr);

					const resultsObj =
						ccPaymentsWithNsIdArr && ccPaymentsWithNsIdArr.length > 0
							? helperFunctions.createDepositRecord(
									paramObj,
									ccPaymentsWithNsIdArr
							  )
							: {
									depositId: null,
									unprocessedPayments: null,
									error:
										"No cc payments found to be deposited for these dates.",
							  };

					helperFunctions.sendResultsEmail(paramObj, resultsObj);
					helperFunctions.createPostForm(form, paramObj, resultsObj);
				}
				context.response.writePage(form);
			} catch (e) {
				customErrorObject.throwError({
					summaryText: "ERROR_CREATING_FORM",
					error: e,
				});
			}
		},
	};
});
