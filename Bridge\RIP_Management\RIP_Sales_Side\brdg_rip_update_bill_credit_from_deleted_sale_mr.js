/**
 * @description Takes in array of bill credits that have to be updated because a sale was deleted
 *  
 * </br><b>Schedule:</b> Gets called by the brdg_rip_deleted_sale_ue
 *  
 * @NApiVersion 2.1
 * @NScriptType MapReduceScript
 * 
 * <AUTHOR>
 * @module brdg_rip_deleted_sale_ue
 */
define([
    "require",
    "N/record",
    "N/runtime",
    "N/email",
    "../../Classes/vlmd_custom_error_object",
  ], (/** @type {any} */ require) => {
    const record = require("N/record");
    const runtime = require("N/runtime");
    const email = require("N/email");
  
    /** @type {import("../../Classes/vlmd_custom_error_object").CustomErrorObject} */
  
    const CustomErrorObject = require("../../Classes/vlmd_custom_error_object");
    const customErrorObject = new CustomErrorObject();
  
    function getInputData(context) {
      try {
        const transactionsToUpdate = JSON.parse(
          runtime.getCurrentScript().getParameter({
            name: "custscript_bc_transactions_to_update",
          })
        );
  
        if (!transactionsToUpdate || transactionsToUpdate.length < 0) {
          customErrorObject.updateError({
            summaryText: "GET_INPUT_DATA_ERROR",
            error: "No transactions to update found!",
          });
        }
  
        return transactionsToUpdate;
      } catch (e) {
        customErrorObject.throwError({
          summaryText: "GET_INPUT_DATA_ERROR",
          error: e,
        });
      }
    }
  
    function map(context) {
      const mapErrorObject = new CustomErrorObject();
      const transactionData = JSON.parse(context.value);

      try {
        if (!transactionData || !transactionData.billCreditId || !transactionData.itemId ||!transactionData.quantityUsed) {
          mapErrorObject.throwError({
            summaryText: "MAP_ERROR",
            error: "Missing transaction data!",
          });
        }
        const transactionRecord = record.load({
          type: record.Type.VENDOR_CREDIT,
          id: transactionData.billCreditId,
          isDynamic: false,
        });
  
        const lineNumber = transactionRecord.findSublistLineWithValue({
          sublistId: "item",
          fieldId: "custcol_rip_item_link",
          value: transactionData.itemId,
        });

        if(lineNumber ==-1){
          context.write({
            key: "Error",
            value: {
              transactionId: transactionData.billCreditId,
              itemId: transactionData.itemId,
              error: 'Cannot find line number for this item on bill credit!',
            },
          });
          return;
        }

        const currentRemainingQuantity = transactionRecord.getSublistValue({
            sublistId: "item",
            fieldId: "custcol_rip_quantity_remaining",
            line: lineNumber,
          });
  
        transactionRecord.setSublistValue({
          sublistId: "item",
          fieldId: "custcol_rip_quantity_remaining",
          line: lineNumber,
          value: currentRemainingQuantity + transactionData.quantityUsed,
        });
  
          transactionRecord.save({
            enableSourcing: true,
            ignoreMandatoryFields: true,
          });
        context.write({
          key: "Success",
          value: {
            transactionId: transactionData.billCreditId,
            itemId: transactionData.itemId,
          },
        });
  
      } catch (e) {
        context.write({
          key: "Error",
          value: {
            transactionId: transactionData.billCreditId,
            itemId: transactionData.itemId,
            error: e.message,
          },
        });
      }
    }
  
    function summarize(context) {
      let successCount = 0;
      let errorCount = 0;
      const successfulTransactions = [];
      const failedTransactions = [];
  
      context.output.iterator().each(function (key, value) {
        const valueObj = JSON.parse(value);
  
        if (key === "Success") {
          successCount++;
          successfulTransactions.push({
            name: valueObj.transactionId,
          });
        } else {
          errorCount++;
          failedTransactions.push({
            id: valueObj.transactionId,
            item: valueObj.itemId,
            error: valueObj.error,
          });
        }
        return true;
      });
  
      log.audit("Bill Credit Link Removal Summary", {
        totalProcessed: successCount + errorCount,
        successCount: successCount,
        errorCount: errorCount,
        successfulTransactions: successfulTransactions,
        failedTransactions: failedTransactions,
      });

      failedTransactions.length > 0 &&
        email.send({
          author: runtime.getCurrentUser().id,
          recipients: ['<EMAIL>'],
          subject: "Failed to remove Bill Credit Link",
          body: JSON.stringify(failedTransactions),
        });
    }
  
    return {
      getInputData,
      map,
      summarize,
    };
  });