/**
 * @description Adds a button to the SPL Pricing Group Tier Price Levels record to update pricing based on customer's tier
 *
 * </br><b>Deployed On:</b> customrecord_spl_pricing_group_by_tier
 * </br><b>Execution Context:</b> ALL
 * </br><b>Event Type/Mode:</b> ALL
 * </br><b>Entry Points:</b> BeforeLoad
 *
 * @NApiVersion 2.1
 * @NScriptType UserEventScript
 * @NAmdConfig /SuiteScripts/config.json
 *
 * <AUTHOR>
 * @module spl_update_price_level_by_tier_button_ue
 */
define(["require", "N/query", "N/log"], (/** @type {any} */ require) => {
	const log = require("N/log");

	return {
		beforeLoad: (context) => {
			if (context.type !== "view") {
				//So the user will need to first save changes before updating the pricing. To prevent the old value from getting passed in.
				return;
			}

			const splPriceTierRecord = context.newRecord;
			const priceLevelIdsArr = [
				splPriceTierRecord.getValue("custrecord_tier_level_price_level_1"),
				splPriceTierRecord.getValue("custrecord_tier_level_price_level_2"),
				splPriceTierRecord.getValue("custrecord_tier_level_price_level_3"),
				splPriceTierRecord.getValue("custrecord_tier_level_price_level_web"),
			];
			const priceGroupId = splPriceTierRecord.getValue(
				"custrecord_pricing_group"
			);
			if (priceLevelIdsArr.some(Boolean) && priceGroupId) {
				//If there is a pricing group and the arr of all price levels has at least one value
				try {
					context.form.addButton({
						id: "custpage_update_tier_1_btn",
						label: "Update Tier 1 Customers",
						functionName: `updateCustomers(1, ${priceGroupId}, ${priceLevelIdsArr[0]})`,
					});
					context.form.addButton({
						id: "custpage_update_tier_2_btn",
						label: "Update Tier 2 Customers",
						functionName: `updateCustomers(2, ${priceGroupId}, ${priceLevelIdsArr[1]})`,
					});
					context.form.addButton({
						id: "custpage_update_tier_3_btn",
						label: "Update Tier 3 Customers",
						functionName: `updateCustomers(3, ${priceGroupId}, ${priceLevelIdsArr[2]})`,
					});
					context.form.addButton({
						id: "custpage_update_tier_5_btn",
						label: "Update Web Tier Customers",
						functionName: `updateCustomers(5, ${priceGroupId}, ${priceLevelIdsArr[3]})`,
					});
					context.form.clientScriptFileId = "7793315";
				} catch (e) {
					let err = {
						name: "UNABLE TO UPDATE",
						message: "Error:" + e.message,
					};

					throw `${err.name} - ${err.message}`;
				}
			}
		},
	};
});
