/**
 * @description Class containing functions to post-process 810
 *
 * @NApiVersion 2.1
 * <AUTHOR> <<EMAIL>>
 */

define([
    "exports",
    "require",
    "../edi_summary",
    "../../../../Classes/vlmd_custom_error_object",
], function (/** @type {any} */ exports, /** @type {any} */ require) {
    const { EDISummary } = require("../edi_summary");
    /** @type {CustomErrorObject} */
    const CustomErrorObject = require("../../../../Classes/vlmd_custom_error_object");

    /**
     * 810 Processing Summary Class
     *
     * @class
     * @typedef {import("../../Interfaces/Models/File/edi_file").EDIPostProcessEmail} EDIPostProcessEmail
     * @typedef {import("../../Interfaces/Models/Partner/edi_partner").EDIVendorInterface} EDIVendorInterface
     * @typedef {import("../../../../Classes/vlmd_custom_error_object").CustomErrorObject} CustomErrorObject
     * @extends {EDISummary}
     */
    class EDI810Summary extends EDISummary {
        /** @param {{[key:string]: any}} params */
        constructor (params) {
            super(params);
        }
    }

    exports.EDI810Summary = EDI810Summary;
});