/**
 * @description EDI Incoming Class Interface
 *
 * <AUTHOR> <<EMAIL>>
 */

import { EDIOutgoingInterface } from "./edi_outgoing";
import { File } from "N/file";

export interface EDIIncomingInterface {
    /** Transaction Object */
    transaction: any;
    /** Parse incoming EDI File */
    parse(): void;
    /** Transform an EDI file to a NetSuite record */
    transform(): void;
    /** Move the EDI File to the Reference directory */
    archive({filename: string, source: string, target: string}) : void;
    /** Upload the EDI File content to the Reference directory */
    archive({content: string}) : void;
    /** Retrieve the email object after processing the transaction */
    summarize(): void;
}