/**
 *@NApiVersion 2.x
 *@NScriptType UserEventScript
 */

//@ts-ignore
define(["N/log"], function (log) {
	function beforeLoad(context) {
		if (context.type !== context.UserEventType.CREATE) {
			var customer = context.newRecord;
			// var customerId = customer.id;
			var customerId = customer.getValue("internalid");
			var customerName = customer.getValue("altname");

			context.form.addButton({
				id: "custpage_testbutton",
				label: "View Customer Payments",
				//functionName: '(function(){return nlOpenWindow("/app/site/hosting/scriptlet.nl?script=536&deploy=1&custId=' + customerId + '&custName=' + customerName + '")})()' //Production Account
				functionName:
					'(function(){return nlOpenWindow("/app/site/hosting/scriptlet.nl?script=544&deploy=1&custId=' +
					customerId +
					"&custName=" +
					customerName +
					'")})()', //Sandbox Account
			});
		}
	}

	return {
		beforeLoad: beforeLoad,
	};
});
