/**
 * @description Get all internal ids of 855
 *
 * </br><b>Implemented By:</b> EDI Outgoing 855 Script
 *
 * @NApiVersion 2.1
 * <AUTHOR>
 * @module spl_get_855_internal_ids_lib
 */

//@ts-ignore
define(["N/log", "N/search"], function (log, search) {
	function getInternalIds(
		customerParentId,
		purchasingSoftwareInternalId,
		searchId
	) {
		const internalIdsObj = {
			internalIds: [],
			errorLog: [],
		};

		try {
			let poAckSearch;

			if (searchId) {
				poAckSearch = search.load({
					id: searchId,
				});
			} else {
				const filtersArr = [
					["subsidiary", "anyof", "1"],
					"AND",
					["type", "anyof", "SalesOrd"],
					"AND",
					["mainline", "is", "T"],
					"AND",
					[
						"customer.custentity_spl_purchasing_software",
						"anyof",
						purchasingSoftwareInternalId,
					],
					"AND",
					["custbody_spl_edi_855_cntrl_nmbr", "isempty", ""],
					"AND",
					["customersubof", "anyof", customerParentId],
					"AND",
					["status", "noneof", "SalesOrd:A"],
					"AND",
					["datecreated", "within", "yesterday"],
					"AND",
					["otherrefnum", "isnotempty", ""],
				];

				poAckSearch = search.create({
					type: "transaction",
					filters: filtersArr,
					columns: ["type"],
				});
			}

			poAckSearch.run().each(function (result) {
				//@ts-ignore
				internalIdsObj.internalIds.push(result.id);
				return true;
			});
		} catch (e) {
			internalIdsObj.errorLog.push(
				//@ts-ignore
				`Purchase Order Acknowledgement internal ids not gotten. Error: ${e}`
			);
		}

		return internalIdsObj;
	}

	return {
		getInternalIds,
	};
});
