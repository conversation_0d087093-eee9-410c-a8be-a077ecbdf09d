/**
 * @description Class containing functions specific to processing NetSuite records into an Outgoing 997 EDI File
 * @NApiVersion 2.1
 * <AUTHOR> <<EMAIL>>
 */

define([
    "exports",
    "require",
    "N/file",
    "N/log",
    "N/record",
    "../../Decorators/edi_processor",
], (/** @type {any} */ exports, /** @type {any} */ require) => {
    const file = require("N/file");
    const log = require("N/log");
    const record = require("N/record");
    const { EDIProcessor } = require("../../Decorators/edi_processor"); 

    /**
     * 997 Processor Class
     * 
     * @typedef {import("../../../../Classes/vlmd_custom_error_object").CustomErrorObject} CustomErrorObject
     * @typedef {import("../../Interfaces/Decorators/997/edi_997_processor").EDI997ProcessorInterface} EDI997ProcessorInterface
     * @typedef {import("../../Interfaces/Models/File/edi_outgoing").SuiteQLObjectReference} SuiteQLObjectReference
     * @typedef {import("../../Interfaces/Models/Partner/edi_partner").EDIParsingInformation} EDIParsingInformation
     * @typedef {import("../../Interfaces/Models/Partner/edi_partner").EDITemplate} EDITemplate
     * @implements {EDI997ProcessorInterface}
     * @class
     */
    class EDI997Processor extends EDIProcessor {
        /** @param {{[key:string]: any}} params Constructor params */
        constructor(params){
            super();
            /** @type {CustomErrorObject} */
            this.customError = params.customError;
            /** @type {EDITemplate} */
            this.template = params.template;
            /** @type {string} */
            this.salesOrderControlNumber = params.salesOrderControlNumber.toString();
            /** @type {string} */
            this.documentControlNumber = params.documentControlNumber.toString();
            /** @type {string} */
            this.documentNumber = params.documentNumber;
            /** @type {EDIParsingInformation} */
            this.parsingInformation = params.parsingInformation;
            /** @type {string} */
            this.fileContent = "";
        }

        /**
         * Return the query string or Search object to load the records
         *
         * @param {{[key:string]: any}} [params] Parameters
         * @returns {SuiteQLObjectReference} Query string or Search object to retrieve transaction records
         */
        load(params) {
            log.error({ title: "EDI 997 Processor (load)", details: "Error: There's no need to fetch any transction from NetSuite." });
            return { type: "", query: "", params: [] };
        }

        /**
         * Generate the string to store on the EDI File
         *
         * @returns {string} EDI File as string
         */
        process() {
            try {
                if (this.template.header) {
                    this.fileContent = this.template.header
                        .replace(/SenderQualifier/g, this.parsingInformation.senderInfo[0].value)
                        .replace(/ISASenderId/g, this.parsingInformation.senderInfo[1].value)
                        .replace(/ReceiverQualifier/g, this.parsingInformation.receiverInfo[0].value)
                        .replace(/ISAReceiverId/g, this.parsingInformation.receiverInfo[1].value)
                        .replace(/ISADate/g, this.parsingInformation.isaGsInfo[0].value)
                        .replace(/Time/g, this.parsingInformation.isaGsInfo[2].value)
                        .replace(/EdiVersion/g, this.parsingInformation.isaGsInfo[3].value)
                        .replace(/SalesOrderControlNumber/g, this.salesOrderControlNumber.padStart(9, "0"))
                        .replace(/ControlNumber/g, this.documentControlNumber.padStart(9, "0"))
                        .replace(/GSSenderId/g, this.parsingInformation.senderInfo[2].value)
                        .replace(/GSReceiverId/g, this.parsingInformation.receiverInfo[2].value)
                        .replace(/GSDate/g, this.parsingInformation.isaGsInfo[1].value)
                }

                log.audit('EDI File Content', this.fileContent);

                return this.fileContent || "";
            } catch (/** @type {any} */ err) {
                log.error({ title: err.name, details: err.message });
                throw this.customError.updateError({
                    errorType: this.customError.ErrorTypes.NO_VALUE_RETURNED,
                    summary: "FAILED_TO_GENERATE_EDI_FILE_STRING",
                    details: err.message,
                });
            }   
        }

        /**
         * Create the NetSuite File Record
         *
         * @param {{[key:string]: any}} params Parameters
         * @returns {file.File} EDI File
         */
        create(params) {
            try {
                return file.create({
                    name: `${params.fileName}_${this.documentNumber}.997`,
                    fileType: file.Type.PLAINTEXT,
                    contents: this.fileContent
                });
            } catch (/** @type {any} */ err) {
                throw this.customError?.updateError({
                    errorType: this.customError?.ErrorTypes.FILE_NOT_CREATED,
                    summary: "FAILED_TO_CREATE_OUTGOING_856",
                    details: `Error creating file object: ${err}`,
                });
            }
        }

        /**
         * Create an EDI Transaction Record and Document Control Number for the Acknowledgement
         *
         * @returns {number} Document Control Number record ID
         */
        complete() {
            // Create Document Control Number
            const documentControlNumber = record.create({
                type: "customrecord_edi_dcn_doc_ctrl_num",
                isDynamic: true,
            });
            documentControlNumber.setValue({
                fieldId: "custrecord_edi_dcn_doc_num",
                value: this.documentNumber,
            });
            documentControlNumber.setValue({
                fieldId: "custrecord_edi_dcn_tran_ctrl_num",
                value: this.documentControlNumber,
            });
            const documentControlNumberId = documentControlNumber.save();
            log.audit(`Document Control Number Record for ${this.documentNumber}`, documentControlNumberId);

            return documentControlNumberId;
        }
    }

    exports.EDI997Processor = EDI997Processor;
});