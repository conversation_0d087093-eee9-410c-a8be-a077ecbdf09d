/******************************************************************************************************
	Script Name - AVA_SUT_TwoWayIMSViewBatch.js
	Company -     Avalara Technologies Pvt Ltd.
******************************************************************************************************/

/**
 *@NApiVersion 2.0
 *@NScriptType Suitelet
 */

define(['N/ui/serverWidget', 'N/search', 'N/record', 'N/redirect', 'N/url', 'N/task', './utility/AVA_Library'],
	function(ui, search, record, redirect, url, task, ava_library){
		function onRequest(context){
			try{
				var checkServiceSecurity = ava_library.mainFunction('AVA_CheckService', 'TaxSvc');
				if(checkServiceSecurity == 0){
					checkServiceSecurity = ava_library.mainFunction('AVA_CheckSecurity', 35);
				}

				if(checkServiceSecurity == 0){
					if(context.request.method === 'GET'){
						var avaIMSForm = ui.createForm({
							title: 'View Batch'
						});
						avaIMSForm.clientScriptModulePath = './AVA_CLI_TwoWayIMSViewBatch.js';
						addFormSublist(avaIMSForm);
						setSublistFieldValues(avaIMSForm);
						avaIMSForm.addSubmitButton({
							label: 'Submit'
						});
						avaIMSForm.addButton({
							id: 'custpage_ava_twowayimsviewbatch_ref',
							label: 'Refresh',
							functionName: 'AVA_TwoWayIMSViewBatchRefresh()'
						});
						avaIMSForm.addPageLink({
							title: 'Create Batch',
							type: ui.FormPageLinkType.CROSSLINK,
							url: url.resolveScript({
								scriptId: 'customscript_ava_2wayimscreatebatch_suit',
								deploymentId: 'customdeploy_ava_2wayimscreatebatch_suit'
							})
						});
						context.response.writePage({
							pageObject: avaIMSForm
						});
					}
					else{
						var lineCount = context.request.getLineCount({
							group: 'custpage_twowayimsbatchsublist'
						});
						
						for(var i = 0; i < lineCount; i++){
							var apply = context.request.getSublistValue({
								group: 'custpage_twowayimsbatchsublist',
								line: i,
								name: 'apply'
							});
							
							if(apply == 'T'){
								var batchId = context.request.getSublistValue({
									group: 'custpage_twowayimsbatchsublist',
									line: i,
									name: 'twowayimsbatchid'
								});
								
								record.submitFields({
									type: 'customrecord_avatwowayimsbatch',
									id: batchId,
									values: {
										custrecord_ava_twowayimsstatus: 'Delete'
									},
									options: {
										enableSourcing: false,
										ignoreMandatoryFields: true
									}
								});
							}
						}
						
						var scheduledScript = task.create({
							taskType: task.TaskType.SCHEDULED_SCRIPT
						});
						scheduledScript.scriptId = 'customscript_ava_2wayimsrecorddelete_sch';
						scheduledScript.submit();

						redirect.toTaskLink({
							id: 'CARD_-29'
						});
					}
				}
				else{
					context.response.write({
						output: checkServiceSecurity
					});
				}
			}
			catch(e){
				log.error('ERROR', 'ERROR NAME = ' + e.name + ', ERROR TYPE = ' + e.type + ', ERROR MESSAGE = ' + e.message);
			}
		}

		function setSublistFieldValues(avaIMSForm){
			try{
				var avaIMSSearchObj = search.create({
					type: "customrecord_avatwowayimsbatch",
					filters: [
						["isinactive", "is", "F"],
						"AND",
						["custrecord_ava_twowayimsstatus", "isnot", "Delete"]
					],
					columns: [
						"name",
						"created",
						"custrecord_ava_twowayimsstatus"
					]
				});
				
				var imsSublist = avaIMSForm.getSublist({
					id: 'custpage_twowayimsbatchsublist'
				});
				var lineNum = 0;
				
				avaIMSSearchObj.run().each(function(result){
					var twoWayIMSBatchName = result.getValue({
						name: 'name'
					});
					var twoWayIMSBatchCreatedDate = result.getValue({
						name: 'created'
					});
					var twoWayIMSBatchStatus = result.getValue({
						name: 'custrecord_ava_twowayimsstatus'
					});
					
					imsSublist.setSublistValue({
						id: 'twowayimsbatchid',
						line: lineNum,
						value: result.id
					});
					imsSublist.setSublistValue({
						id: 'twowayimsbatchname',
						line: lineNum,
						value: twoWayIMSBatchName
					});
					imsSublist.setSublistValue({
						id: 'twowayimsbatchcreateddate',
						line: lineNum,
						value: ava_library.mainFunction('AVA_DateFormat', ava_library.mainFunction('AVA_ConvertDate', twoWayIMSBatchCreatedDate))
					});
					imsSublist.setSublistValue({
						id: 'twowayimsbatchstatus',
						line: lineNum,
						value: twoWayIMSBatchStatus
					});
					
					if(twoWayIMSBatchStatus == 'Completed'){
						var imsBatchDetailsURL = url.resolveScript({
							scriptId: 'customscript_ava_2wayimsbatchdetails_sui',
							deploymentId: 'customdeploy_ava_2wayimsbatchdetails_sui'
						});
						
						imsBatchDetailsURL = imsBatchDetailsURL + '&twowayimsbatchid=' + result.id;
						var FinalURL = '<a href="' + imsBatchDetailsURL + '" target="_blank">View Details</a>';
						
						imsSublist.setSublistValue({
							id: 'twowayimsbatchviewdetails',
							line: lineNum,
							value: FinalURL
						});
					}
					
					lineNum++;
					return true;
				});
				
			}
			catch(e){
				log.error('setSublistFieldValues', 'ERROR NAME = ' + e.name + ', ERROR TYPE = ' + e.type + ', ERROR MESSAGE = ' + e.message);
			}
		}

		function addFormSublist(avaIMSForm){
			try{
				var imsSublist = avaIMSForm.addSublist({
					id: 'custpage_twowayimsbatchsublist',
					label: 'Select Batches',
					type: ui.SublistType.LIST
				});
				
				var batchId = imsSublist.addField({
					id: 'twowayimsbatchid',
					label: 'Batch ID',
					type: ui.FieldType.TEXT
				});
				batchId.updateDisplayType({
					displayType: ui.FieldDisplayType.HIDDEN
				});
				imsSublist.addField({
					id: 'apply',
					label: 'Delete',
					type: ui.FieldType.CHECKBOX
				});
				imsSublist.addMarkAllButtons();
				imsSublist.addField({
					id: 'twowayimsbatchname',
					label: 'Batch Name',
					type: ui.FieldType.TEXT
				});
				imsSublist.addField({
					id: 'twowayimsbatchcreateddate',
					label: 'Created On',
					type: ui.FieldType.DATE
				});
				imsSublist.addField({
					id: 'twowayimsbatchstatus',
					label: 'Status',
					type: ui.FieldType.TEXT
				});
				imsSublist.addField({
					id: 'twowayimsbatchviewdetails',
					label: 'Details',
					type: ui.FieldType.TEXT
				});
				return avaIMSForm;
			}
			catch(e){
				log.error('addFormSublist', 'ERROR NAME = ' + e.name + ', ERROR TYPE = ' + e.type + ', ERROR MESSAGE = ' + e.message);
			}
		}
		
		return{
			onRequest: onRequest
		};
	}
);