/**
 * @description Class containing functions specific to processing Item Fulfillment to Outgoing 856 EDI Files
 *
 * @NApiVersion 2.1
 * <AUTHOR> <<EMAIL>>
 */

define([
    "exports",
    "require",
    "./edi_856_processor",
    "../../Models/File/edi_file",
    "N/file",
    "N/log",
    "N/record"
], (/** @type {any} */ exports, /** @type {any} */ require) => {
    const { EDI856Processor } =  require("./edi_856_processor");
    const { DocumentType } = require("../../Models/File/edi_file");
    const file = require("N/file");
    const log = require("N/log");
    const record = require("N/record");

    /**
     * 856 ItemFulfillment Processor Class
     *
     * @typedef {import("../../Models/Transaction/edi_item_fulfillment").EDIItemFulfillment} EDIItemFulfillment
     * @typedef {import("../../../../Classes/vlmd_custom_error_object").CustomErrorObject} CustomErrorObject
     * @typedef {import("../../Interfaces/Models/File/edi_outgoing").SuiteQLObjectReference} SuiteQLObjectReference
     * @typedef {import("../../Interfaces/Models/Partner/edi_partner").EDIParsingInformation} EDIParsingInformation
     * @typedef {import("../../Interfaces/Models/Partner/edi_partner").EDITemplate} EDITemplate
     * @typedef {import("../../Interfaces/Decorators/856/edi_856_processor").EDI856ProcessorInterface} EDI856ProcessorInterface
     * @implements {EDI856ProcessorInterface}
     */
    class EDI856ItemFulfillmentProcessor extends EDI856Processor {
        /** @param {{[key:string]: any}} params Constructor params */
        constructor(params){
            super();
            /** @type {CustomErrorObject} */
            this.customError = params.customError;
            /** @type {EDIItemFulfillment} */
            this.itemFulfillment = params.itemFulfillment;
            /** @type {EDIParsingInformation} */
            this.parsingInformation = params.parsingInformation;
            /** @type {EDITemplate} */
            this.template = params.template;
            /** @type {{base: number, item: number; hierarchy: number}} */
            this.segmentCount = params.template?.segmentCount;
            /** @type {string} */
            this.fileContent = "";
            /** @type {number} */
            this.purchasingSoftwareId = params.purchasingSoftwareId;
        }

        /**
         * Return the query string or Search object to load the NetSuite Item Fulfillments
         *
         * @param {object} [params] Parameters
         * @param {number} [params.searchId] Saved search ID
         * @param {number} [params.customerParent] Customer parent ID
         * @param {Date} [params.startDate] Start date
         * @param {Date} [params.endDate] End date
         * @param {string[]} [params.documentNumbers] ItemFulfillment Document Numbers
         * @returns {SuiteQLObjectReference} Query string or Search object to retrieve transaction records
         */
        load(params) {
            try {
                if (!params || !params.customerParent) {
                    throw this.customError.updateError({
                        errorType: this.customError.ErrorTypes.MISSING_PARAM,
                        summary: "NO_PARAMETERS_SUPPLIED",
                        details: `Parameters are required for EDI 856 Processor load function. params: ${params}`,
                    });
                }

                return {
                    type: "suiteql",
                    query: `
                        SELECT
                            ROW_NUMBER() OVER (ORDER BY item_fulfillment.id) AS result_index,
                            item_fulfillment.id,
                            item_fulfillment.type,
                            sales_order.otherrefnum,
                            item_fulfillment.tranid,
                            ptll.previousdoc,
                            item_fulfillment.trandate,
                            item_fulfillment.custbody_scac_code,
                            item_fulfillment.custbody_walmart_bol_case_qnty,
                            item_fulfillment.custbody_bol_fullfilment_weight,
                            item_fulfillment.custbody_spl_tracking_information,
                            sales_order.linkedtrackingnumberlist,
                            tsa.addr1,
                            tsa.city,
                            tsa.state,
                            tsa.zip,
                            c.fullname,
                            BUILTIN_RESULT.TYPE_INTEGER(edi.custrecord_document_control_number)
                        FROM
                            transaction item_fulfillment
                        JOIN
                            customer c ON item_fulfillment.entity = c.id
                        JOIN
                            customer p ON c.parent = p.id
                        JOIN
                            customrecord_vlmd_edi_integration edi ON p.custentity_spl_edi_integration_record = edi.id
                        JOIN
			                transactionshipment tship ON tship.doc = item_fulfillment.id
                        LEFT JOIN
                            previoustransactionlinelink ptll ON item_fulfillment.id = ptll.nextdoc
                        LEFT JOIN
			                transaction sales_order ON ptll.previousdoc = sales_order.id
                        LEFT JOIN
			                transactionshippingaddress tsa ON tsa.nkey = item_fulfillment.shippingaddress
                        WHERE
                            item_fulfillment.type = 'ItemShip'
                            AND ptll.linktype = 'ShipRcpt'
                            AND item_fulfillment.status = 'C'
                            AND item_fulfillment.custbody_spl_edi_856_cntrl_nmbr IS NULL
                            AND c.custentity_spl_exclude_from_edi_intgrtn = 'F'
                            AND (p.id = ${params.customerParent} OR c.id = ${params.customerParent})
                            ${params.startDate && params.endDate
                                ? "AND TO_DATE( item_fulfillment.createddate, 'MM/DD/YYYY' ) BETWEEN TO_DATE('" +
                                    params.startDate +
                                    "', 'MM/DD/YYYY' ) AND TO_DATE('" +
                                    params.endDate +
                                    "', 'MM/DD/YYYY')"
                                : ""
                            }
                            AND item_fulfillment.createddate >= edi.custrecord_edi_intgrtn_start_date
                            AND (
                                SELECT
                                    COUNT (*)
                                FROM
                                    customrecord_edi_dcn_doc_ctrl_num
                                WHERE
                                    customrecord_edi_dcn_doc_ctrl_num.custrecord_edi_dcn_doc_num = item_fulfillment.tranid
                                    AND customrecord_edi_dcn_doc_ctrl_num.custrecord_edi_dcn_doc_num IS NOT NULL
                                    AND customrecord_edi_dcn_doc_ctrl_num.isinactive = 'F'
                            ) = 0
                    `,
                    params: [],
                };
            } catch (/** @type {any} */err) {
                throw this.customError.updateError({
                    errorType: this.customError.ErrorTypes.NO_VALUE_RETURNED,
                    summary: "FAILED_TO_GENERATE_SEARCH_OR_QUERY",
                    details: err.message,
                });
            }
        }

        /**
         * Load the template and fill out values from the ItemFulfillment or Credit Memo record
         *
         * @returns {string} EDI file content
         */
        process() {
            try {
                const purchaseOrderNumber = this.itemFulfillment.getPurchaseOrderNumber();
                let refCount = 0;
                let itemSegmentCount = 0;
                this.itemFulfillment.controlNumber += this.itemFulfillment.index;

                if (this.template.header) {
                    this.fileContent = this.template.header
                        .replace(/SenderQualifier/g, this.parsingInformation.senderInfo[0].value)
                        .replace(/ISASenderId/g, this.parsingInformation.senderInfo[1].value)
                        .replace(/ReceiverQualifier/g, this.parsingInformation.receiverInfo[0].value)
                        .replace(/ISAReceiverId/g, this.parsingInformation.receiverInfo[1].value)
                        .replace(/ISADate/g, this.parsingInformation.isaGsInfo[0].value)
                        .replace(/Time/g, this.parsingInformation.isaGsInfo[2].value)
                        .replace(/EdiVersion/g, this.parsingInformation.isaGsInfo[3].value)
                        .replace(/ControlNumber/g, this.itemFulfillment.controlNumber.toString().padStart(9, "0"))
                        .replace(/GSSenderId/g, this.parsingInformation.senderInfo[2].value)
                        .replace(/GSReceiverId/g, this.parsingInformation.receiverInfo[2].value)
                        .replace(/GSDate/g, this.parsingInformation.isaGsInfo[1].value)
                        .replace(/ShipWeight/g, (this.itemFulfillment.shipWeight || '').toString())
                        .replace(/LadingQuantity/g, (this.itemFulfillment.ladingQuantity || '').toString())
                        .replace(/ShipCarrier/g, this.itemFulfillment.shipCarrier)
                        .replace(/DocumentDate/g, this.itemFulfillment.transactionDateFormatted)
                        .replace(/DocumentNumber/g, this.itemFulfillment.documentNumber)
                        .replace(/PoNumber/g, purchaseOrderNumber)
                        .replace(/SupplierName/g, this.itemFulfillment.supplierName)
                        .replace(/SupplierStreet/g, this.itemFulfillment.supplierAddress.street)
                        .replace(/SupplierCity/g, this.itemFulfillment.supplierAddress.city)
                        .replace(/SupplierState/g, this.itemFulfillment.supplierAddress.state)
                        .replace(/SupplierZip/g, this.itemFulfillment.supplierAddress.zip)
                        .replace(/CustomerName/g, this.itemFulfillment.customerName)
                        .replace(/CustomerGLN/g, this.itemFulfillment.customerGLN)
                        .replace(/CustomerStreet/g, this.itemFulfillment.address.street)
                        .replace(/CustomerCity/g, this.itemFulfillment.address.city)
                        .replace(/CustomerState/g, this.itemFulfillment.address.state)
                        .replace(/CustomerZip/g, this.itemFulfillment.address.zip);
                }

                if (this.template.tracking) {
                    this.itemFulfillment.trackingNumbers.forEach((trackingNumber) => {
                        this.fileContent += this.template.tracking
                            .replace(/TrackingNumber/g, trackingNumber);
                        refCount++;
                    });
                }

                if (this.template.item) {
                    this.itemFulfillment.getLineItems();
                    itemSegmentCount = this.itemFulfillment.lineCount * this.segmentCount.item;
                    for (let i = 0; i < this.itemFulfillment.lineCount; i++) {
                        this.fileContent += this.template.item
                            .replace(/ItemHierarchy/g, (i + 1 + this.segmentCount.hierarchy).toString())
                            .replace(/LineNumber/g, (i + 1).toString())
                            .replace(/Quantity/g, this.itemFulfillment.lineItems[i].quantity.toString())
                            .replace(/UOM/g, this.itemFulfillment.lineItems[i].units || "EA")
                            .replace(/ItemName14/g, this.itemFulfillment.lineItems[i].name.padStart(14, "0"))
                            .replace(/ItemName12/g, this.itemFulfillment.lineItems[i].name.padStart(12, "0"))
                            .replace(/ItemName/g, this.itemFulfillment.lineItems[i].name)
                            .replace(/PO1LineNumber/g, this.itemFulfillment.lineItems[i].po1LineNumber.toString())
                            .replace(/ItemDescription/g, this.itemFulfillment.lineItems[i].description);
                    }
                }
                
                if (this.template.summary) {
                    this.fileContent += this.template.summary
                    .replace(/LineCount/g, this.itemFulfillment.lineCount.toString())
                    .replace(/SegmentCount/g, (itemSegmentCount + refCount + this.segmentCount.base).toString())
                    .replace(/ControlNumber/g, this.itemFulfillment.controlNumber.toString().padStart(9, "0"));
                }
                log.audit('EDI File Content', this.fileContent);

                return this.fileContent;
            } catch (/** @type {any} */ err) {
                log.error({ title: err.name, details: err.message });
                throw this.customError.updateError({
                    errorType: this.customError.ErrorTypes.NO_VALUE_RETURNED,
                    summary: "FAILED_TO_GENERATE_EDI_FILE_STRING",
                    details: err.message,
                });
            }   
        }

        /**
         * Create the NetSuite File Record
         *
         * @param {{[key:string]: any}} params Parameters
         * @returns {file.File} EDI File
         */
        create(params) {
            try {
                return file.create({
                    name: `${params.fileName}_${this.itemFulfillment.documentNumber}.856`,
                    fileType: file.Type.PLAINTEXT,
                    contents: this.fileContent
                });
            } catch (/** @type {any} */ err) {
                throw this.customError?.updateError({
                    errorType: this.customError?.ErrorTypes.FILE_NOT_CREATED,
                    summary: "FAILED_TO_CREATE_OUTGOING_856",
                    details: `Error creating file object: ${err}`,
                });
            }
        }

        /**
         * Create an EDI Transaction Record and Document Control Number for the ItemFulfillment
         *
         * @returns {number} Document Control Number record ID
         */
        complete() {
            // Create Document Control Number
            const documentControlNumber = record.create({
                type: "customrecord_edi_dcn_doc_ctrl_num",
                isDynamic: true,
            });
            documentControlNumber.setValue({
                fieldId: "custrecord_edi_dcn_doc_num",
                value: this.itemFulfillment.documentNumber,
            });
            documentControlNumber.setValue({
                fieldId: "custrecord_edi_dcn_tran_ctrl_num",
                value: this.itemFulfillment.controlNumber,
            });
            const documentControlNumberId = documentControlNumber.save();
            log.audit(`Document Control Number Record for ${this.itemFulfillment.documentNumber}`, documentControlNumberId);

            return documentControlNumberId;
        }
    }

    exports.EDI856ItemFulfillmentProcessor = EDI856ItemFulfillmentProcessor;
});