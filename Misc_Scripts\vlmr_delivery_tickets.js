var legalPageWords ='ASSIGNMENTOFBENEFIT'; 

for (var p=this.numPages-1; p>=0; p--) {
    var firstWordsOnPage=''; 
    for (var n=22; n<=24; n++) {
        console.println(this.getPageNthWord(p, n)+n)
        firstWordsOnPage+=this.getPageNthWord(p, n);         
    }
    if (legalPageWords===firstWordsOnPage) {
        this.deletePages(p);
    }
}

// for (var p=this.numPages-1; p>=0; p--) {
//     for (var n=0; n<this.getPageNumWords(p); n++) {
//         if (this.getPageNthWord(p, n) == "TheWord") {
//             this.deletePages(p);
//             break;
//         }
//     }
// }
// Would this work better
// Where “theWord” would be ASSIGNEMENT 
