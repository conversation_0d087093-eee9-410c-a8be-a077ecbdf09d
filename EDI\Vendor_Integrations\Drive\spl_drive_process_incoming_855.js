/**
 * @NApiVersion 2.1
 * @NScriptType scheduledscript
 * @NAmdConfig /SuiteScripts/config.json
 */

define([
	"GetEdiFileContents",
	"GetEdiPartnerValuesLib",
	"ParseIncoming855Lib",
	"ProcessIncoming855Lib",
], function (
	getEdiFileContentsLib,
	getEdiPartnerValuesLib,
	parsePoAcknowledgmentLib,
	processPOAcknowledgmentLib
) {
	function execute(context) {
		var vendorData = {
			prodGuidBool: true, //Toggle for Sandbox or Prod
			prodDirectoryBool: true, //Toggle for Sandbox or Prod
			decodeContent: true, //Toggle depending on edi file format type
			prodGUID: "01c62ab421a04b10a936f566b36eb398",
			sandboxGUID: "********************************",
			vendorInternalId: 728,
			integrationStartDate: "3/11/2021 12:00 am",
			testDirectory: "/users/Drive/TEST/IN/855",
			prodDirectory: "/users/Drive/IN/855",
			referenceDirectory: "/EDI Reference Files/Drive/IN/855",
			vendorName: "Drive",
			documentType: "Purchase Order Acknowledgment",
			documentTypeId: 2,
			pushEmailToDB: true,
		};

		var fileContents =
			getEdiFileContentsLib.getEdiFileContents(vendorData).fileContents;
		if (fileContents.length > 0) {
			var partnerValues = getEdiPartnerValuesLib.getDriveValues();
			fileContents.forEach((ediFile) => {
				var parsedPoAckObj = parsePoAcknowledgmentLib.parse855(
					ediFile.content,
					partnerValues
				);

				if (parsedPoAckObj.errorLog.length <= 0) {
					processPOAcknowledgmentLib.processPoAck(
						vendorData,
						parsedPoAckObj.poAckObj,
						ediFile.fileName
					);
				}
			});
		} else {
			log.debug("Script ran, no results found");
		}
	}

	return {
		execute: execute,
	};
});
