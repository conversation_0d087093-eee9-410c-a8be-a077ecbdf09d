/**
 * EDI Transaction class - represents the base class for EDI transctions
 *
 * @NApiVersion 2.1
 * <AUTHOR>
 * @module EdiTransaction
 */

define([
	"exports",
	"require",
	"Moment",
	"../Libraries/Misc_Libs/spl_get_edi_file_contents_lib",
	"./vlmd_custom_error_object",
	"N/file",
	"N/email",
], (/** @type {any} */ exports, require, moment) => {
	const file = require("N/file");
	const email = require("N/email");
	const CustomErrorObject = require("./vlmd_custom_error_object");

	/**
	 * EDI Transaction
	 *
	 * @class
	 * @type {import("../Libraries/Misc_Libs/spl_get_edi_file_contents_lib")}
	 */
	const getEdiFileContentsLib = require("../Libraries/Misc_Libs/spl_get_edi_file_contents_lib");

	/**
	 * EDI Transaction
	 *
	 * @class
	 * @type {import("./edi_transaction").EdiTransaction}
	 */
	class EdiTransaction {
		constructor() {
			this.customErrorObject = new CustomErrorObject();
			this.fileObj;
			this.folderIdToSaveFilesTo = 6511263;
			this.fileId;
			this.emailSubject = "Requsted EDI File";
		}

		resetCustomErrorObject() {
			this.customErrorObject = new CustomErrorObject();
		}

		createFileObj(fileName, fileContent) {
			try {
				this.fileObj = file.create({
					name: `${fileName}_${moment().format("YYYYMMDDhhmmss")}.csv`,
					fileType: file.Type.CSV,
					contents: fileContent,
				});
			} catch (err) {
				throw this.customErrorObject.updateError({
					errorType: this.customErrorObject.ErrorTypes.UNAHNDLED_ERROR,
					summary: "FILE_OBJ_NOT_CREATED",
					details: `Error creating file object: ${err}`,
				});
			}
		}

		uploadFileToPartnersFolder(dataObj) {
			try {
				const connection = getEdiFileContentsLib.createConnection(dataObj);

				connection.upload({
					file: this.fileObj,
					directory: "",
					replaceExisting: true,
				});
			} catch (err) {
				throw this.customErrorObject.updateError({
					errorType: this.customErrorObject.ErrorTypes.UNAHNDLED_ERROR,
					summary: "FILE_NOT_UPLOADED_TO_PARTNER",
					details: `Error uploading file to partner's foldere: ${err}`,
				});
			}
		}

		uploadFileToReferenceFolder(dataObj) {
			try {
				const supplyLineConnection = getEdiFileContentsLib.createConnection(
					dataObj,
					"",
					dataObj.referenceDirectory
				);
				supplyLineConnection.upload({
					file: this.fileObj,
					replaceExisting: true,
				});
			} catch (err) {
				throw this.customErrorObject.updateError({
					errorType: this.customErrorObject.ErrorTypes.UNAHNDLED_ERROR,
					summary: "FILE_NOT_UPLOADED_TO_REF_FOLDER",
					details: `Error uploading file to EDI Reference folder: ${err}`,
				});
			}
		}

		saveFileToFolder() {
			try {
				this.fileObj.folder = this.folderIdToSaveFilesTo;
				this.fileId = this.fileObj.save();
			} catch (err) {
				throw this.customErrorObject.updateError({
					errorType: this.customErrorObject.ErrorTypes.UNAHNDLED_ERROR,
					summary: "FILE_NOT_SAVED_TO_FOLDER",
					details: `Error saving file to NetSuite folder: ${err}`,
				});
			}
		}

		emailFile() {
			try {
				email.send({
					author: 262579,
					recipients: 3288,
					subject: this.emailSubject,
					body: `<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
					<html dir="ltr" xmlns="http://www.w3.org/1999/xhtml" xmlns:o="urn:schemas-microsoft-com:office:office">
					
					<head>
						<meta charset="UTF-8">
						<meta content="width=device-width, initial-scale=1" name="viewport">
						<meta name="x-apple-disable-message-reformatting">
						<meta http-equiv="X-UA-Compatible" content="IE=edge">
						<meta content="telephone=no" name="format-detection">
						<title></title>
						<!--[if (mso 16)]>
						<style type="text/css">
						a {text-decoration: none;}
						</style>
						<![endif]-->
						<!--[if gte mso 9]><style>sup { font-size: 100% !important; }</style><![endif]-->
						<!--[if gte mso 9]>
					<xml>
						<o:OfficeDocumentSettings>
						<o:AllowPNG></o:AllowPNG>
						<o:PixelsPerInch>96</o:PixelsPerInch>
						</o:OfficeDocumentSettings>
					</xml>
					<![endif]-->
					</head>
					
					<body data-new-gr-c-s-loaded="14.1165.0">
						<div dir="ltr" class="es-wrapper-color">
							<!--[if gte mso 9]>
								<v:background xmlns:v="urn:schemas-microsoft-com:vml" fill="t">
									<v:fill type="tile" color="#fafafa"></v:fill>
								</v:background>
							<![endif]-->
							<table class="es-wrapper" width="100%" cellspacing="0" cellpadding="0">
								<tbody>
									<tr>
										<td class="esd-email-paddings" valign="top">
											<table cellpadding="0" cellspacing="0" class="es-content esd-header-popover" align="center">
												<tbody>
													<tr>
														<td class="esd-stripe" align="center">
															<table bgcolor="#ffffff" class="es-content-body" align="center" cellpadding="0" cellspacing="0" width="600">
																<tbody>
																	<tr>
																		<td class="esd-structure es-p15t es-p20r es-p20l" align="left">
																			<table cellpadding="0" cellspacing="0" width="100%">
																				<tbody>
																					<tr>
																						<td width="560" class="esd-container-frame" align="center" valign="top">
																							<table cellpadding="0" cellspacing="0" width="100%">
																								<tbody>
																									<tr>
																										<td align="center" class="esd-block-text es-p10b es-m-txt-c">
																											<h1>Your file is ready</h1>
																										</td>
																									</tr>
																								</tbody>
																							</table>
																						</td>
																					</tr>
																				</tbody>
																			</table>
																		</td>
																	</tr>
																</tbody>
															</table>
														</td>
													</tr>
												</tbody>
											</table>
											<table cellpadding="0" cellspacing="0" class="es-content esd-footer-popover" align="center">
												<tbody>
													<tr>
														<td class="esd-stripe" align="center">
															<table bgcolor="#ffffff" class="es-content-body" align="center" cellpadding="0" cellspacing="0" width="600">
																<tbody>
																	<tr>
																		<td class="esd-structure es-p20t es-p10b es-p20r es-p20l" align="left">
																			<table cellpadding="0" cellspacing="0" width="100%">
																				<tbody>
																					<tr>
																						<td width="560" class="esd-container-frame" align="center" valign="top">
																							<table cellpadding="0" cellspacing="0" width="100%">
																								<tbody>
																									<tr>
																										<td align="center" class="esd-block-text es-p5t es-p5b es-p40r es-p40l es-m-p0r es-m-p0l">
																										<br/><br/>	
																										<p>Please find your requested file attached.</p>
																										<br/><br/>	
																										</td>
																									</tr>
																								</tbody>
																							</table>
																						</td>
																					</tr>
																				</tbody>
																			</table>
																		</td>
																	</tr>
																	<tr>
																		<td class="esd-structure es-p15t es-p10b es-p20r es-p20l" align="left">
																			<table cellpadding="0" cellspacing="0" width="100%">
																				<tbody>
																					<tr>
																						<td width="560" align="left" class="esd-container-frame">
																							<table cellpadding="0" cellspacing="0" width="100%">
																								<tbody>
																									<tr>
																										<td align="center" class="esd-block-text es-p10t es-p10b">
																											<p>Have a question? Reach out to <a href="mailto:<EMAIL>"><EMAIL></a></p>
																										</td>
																									</tr>
																								</tbody>
																							</table>
																						</td>
																					</tr>
																				</tbody>
																			</table>
																		</td>
																	</tr>
																</tbody>
															</table>
														</td>
													</tr>
												</tbody>
											</table>
										</td>
									</tr>
								</tbody>
							</table>
						</div>
					</body>
					
					</html>`,
					attachments: [this.fileObj],
					// relatedRecords: {
					// 	entityId: recipientId,
					// 	customRecord:{
					// 		id:recordId,
					// 		recordType: recordTypeId   //an integer value
					// 	}
					// }
				});
			} catch (err) {
				throw this.customErrorObject.updateError({
					errorType: this.customErrorObject.ErrorTypes.UNAHNDLED_ERROR,
					summary: "ERROR_EMAILING_FILE",
					details: `Error emailing file: ${err}`,
				});
			}
		}
	}

	exports.EdiTransaction = EdiTransaction;

	return EdiTransaction;
});
