/**
 * @description EDI Outgoing Class Interface
 *
 * <AUTHOR> <<EMAIL>>
 */

import { EDIServerInterface } from "../Server/edi_server";
import { File } from "N/file";

/** Object returned by get input data when a SuiteQL string will be used for map */
export type SuiteQLObjectReference = {
    type: string;
    query: string;
    params: any[];
};

export interface EDIOutgoingInterface {
    /** EDI Server Instance */
    server: EDIServerInterface | null
    /** NetSuite File record created from NetSuite records */
    file: File | null;
    /** File Name */
    fileName: string;
    /** File ID in File Cabinet */
    fileId: number;
    /** Folder ID in File Cabinet */
    folderId: number;
    /** Email Subject */
    emailSubject: string;
    /** Create the NetSuite File Record */
    create(params?: {[key:string]: any}): void;
    /** Return the query string or Search object to load the records */
    load(params?: {[key:string]: any}): any;
    /** Process the record object using the decorator processor */
    process(params?: {[key:string]: any}): string;
    /** Save the NetSuite File record to File Cabinet */
    save(params?: {[key:string]: any}): void;
    /** Send the EDI File as an E-mail attachment */
    email(params?: {[key:string]: any}): void;
    /** Upload the EDI File to the Partner's SFTP Server */
    upload(params?: {[key:string]: any}): void;\
    /** Mark the transaction as processed */
    complete(params?): number;
}