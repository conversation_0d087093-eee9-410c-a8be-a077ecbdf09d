/**
 * @description EDI class for the 810 template
 *
 * @NApiVersion 2.1
 * <AUTHOR> <<EMAIL>>
 */

define(["exports", "require", "./edi_template"], (
  /** @type {any} */ exports,
  /** @type {any} */ require
) => {
  const { EDITemplate } = require("./edi_template");

  /**
   * EDI 810 Template Class
   *
   * @class
   * @typedef {import("../../Interfaces/Models/Template/edi_template").EDITemplateParams} EDITemplateParams
   */
  class EDI810Template extends EDITemplate {
    /** @param {EDITemplateParams} params */
    constructor(params) {
      super(params);
    }

    /**
     * ISA Segment
     * Pad the Authorization and Security Information with spaces to match the segment length requirement
     *
     * @returns {string}
     */
    createISASegment() {
      return (
        `ISA` +
        `${this.fd}00` +
        `${this.fd}          ` +
        `${this.fd}00` +
        `${this.fd}          ` +
        `${this.fd}SenderQualifier` +
        `${this.fd}ISASenderId` +
        `${this.fd}ReceiverQualifier` +
        `${this.fd}ISAReceiverId` +
        `${this.fd}ISADate` +
        `${this.fd}Time` +
        `${this.fd}U` +
        `${this.fd}EdiVersion` +
        `${this.fd}ControlNumber` +
        `${this.fd}0` +
        `${this.fd}P` +
        `${this.fd}>${this.sd}\n`
      );
    }

    /**
     * GS Segment
     * Date and Time are calculated based on the partner
     *
     * @returns {string}
     */
    createGSSegment() {
      return (
        `GS` +
        `${this.fd}IN` +
        `${this.fd}GSSenderId` +
        `${this.fd}GSReceiverId` +
        `${this.fd}GSDate` +
        `${this.fd}Time` +
        `${this.fd}ControlNumber` +
        `${this.fd}X` +
        `${this.fd}EdiVersion0` +
        `${this.sd}\n`
      );
    }

    /**
     * ST Segment
     * Transaction Set Header
     * Control number assigned is the same as the one in SE
     *
     * @returns {string}
     */
    createSTSegment() {
      return `ST` + `${this.fd}810` + `${this.fd}0001` + `${this.sd}\n`;
    }

    /**
     * BIG Segment
     * Beginning Segment for Invoice
     *
     * @returns {string}
     */
    createBIGSegment() {
      return (
        `BIG` +
        `${this.fd}DocumentDate` +
        `${this.fd}DocumentNumber` +
        `${this.fd}PoDate` +
        `${this.fd}PoNumber` +
        `${this.sd}\n`
      );
    }

    /**
     * REF Segment
     * Reference Information
     *
     * @returns {string}
     */
    createREFSegment() {
      return `REF` + `${this.fd}IA` + `${this.fd}084047631` + `${this.sd}\n`;
    }

    /**
     * Supplier N1, N3 and N4 Segments
     * Party identification and location
     *
     * @returns {string}
     */
    createNSupplierSegment() {
      return (
        `N1` +
        `${this.fd}SU` +
        `${this.fd}SupplierName` +
        `${this.fd}UL` +
        `${this.fd}SupplierGLN` +
        `${this.sd}\n` +
        `N3` +
        `${this.fd}SupplierStreet` +
        `${this.sd}\n` +
        `N4` +
        `${this.fd}SupplierCity` +
        `${this.fd}SupplierState` +
        `${this.fd}SupplierZip` +
        `${this.sd}\n`
      );
    }

    /**
     * Customer/Ship-to N1, N3 and N4 Segments
     * Party identification and location
     * GLN is available from the partner class
     *
     * @returns {string}
     */
    createNCustomerSegment() {
      return (
        `N1` +
        `${this.fd}ST` +
        `${this.fd}CustomerName` +
        `${this.fd}UL` +
        `${this.fd}CustomerGLN` +
        `${this.sd}\n` +
        `N3` +
        `${this.fd}CustomerStreet` +
        `${this.sd}\n` +
        `N4` +
        `${this.fd}CustomerCity` +
        `${this.fd}CustomerState` +
        `${this.fd}CustomerZip` +
        `${this.sd}\n`
      );
    }

    /**
     * ITD Segment
     * Terms of Sale
     *
     * @returns {string}
     */
    createITDSegment() {
      return (
        `ITD` +
        `${this.fd}08` +
        `${this.fd}3` +
        `${this.fd}2` +
        `${this.fd}` +
        `${this.fd}30` +
        `${this.fd}` +
        `${this.fd}35` +
        `${this.sd}\n`
      );
    }

    /**
     * DTM Segment
     * Date & Time Reference
     * Type of date and/or time is 011 (Shipped)
     *
     * @returns {string}
     */
    createDTMSegment() {
      return (
        `DTM` + `${this.fd}011` + `${this.fd}DocumentDate` + `${this.sd}\n`
      );
    }

    /**
     * FOB Segment
     * Method of Payment is CC (Collect)
     *
     * @returns {string}
     */
    createFOBSegment() {
      return `FOB` + `${this.fd}CC` + `${this.sd}\n`;
    }

    /**
     * IT1 and PID Segment
     * Baseline Item Data and Description
     *
     * @returns {string}
     */
    createItemSegment() {
      return (
        `IT1` +
        `${this.fd}` +
        `${this.fd}Quantity` +
        `${this.fd}UOM` +
        `${this.fd}Rate` +
        `${this.fd}` +
        `${this.fd}IN` +
        `${this.fd}ItemName` +
        `${this.fd}UP` +
        `${this.fd}UPC` +
        `${this.fd}UK` +
        `${this.fd}GTIN` +
        `${this.sd}\n` +
        `PID` +
        `${this.fd}F` +
        `${this.fd}` +
        `${this.fd}` +
        `${this.fd}` +
        `${this.fd}ItemDescription` +
        `${this.sd}\n`
      );
    }

    /**
     * Service, Allowance and Charge Segment
     *
     * @returns {string} SAC Segment
     */
    createSACSegment() {
      return (
        `SAC` +
        `${this.fd}` +
        "A" +
        `${this.fd}` +
        "ChargeType" +
        `${this.fd}` +
        `${this.fd}` +
        `${this.fd}` +
        "Amount" +
        `${this.fd}` +
        `${this.fd}` +
        `${this.fd}` +
        `${this.fd}` +
        `${this.fd}` +
        `${this.fd}` +
        `${this.fd}` +
        "02" +
        `${this.sd}\n`
      );
    }
    /**
     * TDS Segment
     * Total Monetary Value
     * DO not use . or ,
     *
     * @returns {string}
     */
    createTDSSegment() {
      return `TDS` + `${this.fd}AmountDigits` + `${this.sd}\n`;
    }

    /**
     * CTT Segment
     * Transaction totals
     * Provide the number of IT1 segment count
     *
     * @returns {string}
     */
    createCTTSegment() {
      return `CTT` + `${this.fd}LineCount` + `${this.sd}\n`;
    }

    /**
     * SE Segment
     * Transaction Set Trailer
     * Control number assigned is the same as the one in ST
     *
     * @returns {string}
     */
    createSESegment() {
      return (
        `SE` + `${this.fd}SegmentCount` + `${this.fd}0001` + `${this.sd}\n`
      );
    }

    /**
     * GE Segment
     * Functional Group Trailer
     * Number of transaction sets is always 1
     *
     * @returns {string}
     */
    createGESegment() {
      return `GE` + `${this.fd}1` + `${this.fd}ControlNumber` + `${this.sd}\n`;
    }

    /**
     * IEA Segment
     * Interchange control trailer
     * Number of functional groups is always 1
     *
     * @returns {string}
     */
    createIEASegment() {
      return `IEA` + `${this.fd}1` + `${this.fd}ControlNumber` + `${this.sd}\n`;
    }
  }

  exports.EDI810Template = EDI810Template;
});
