/**
 * @description Validates that all tier levels on a tier group are incremented in ASC order based on the quantity of the tier level
 * (Not necessary to execute when created via script since the import script already does this validation.)
 * 
 * </br><b>Deployed On:</b> BRDG Rebate Tier Group
 * </br><b>Execution Context:</b> USEREVENT
 * </br><b>Event Type/Mode:</b> ALL
 * </br><b>Entry Points:</b> beforeSubmit
 * 
 * @NApiVersion 2.1
 * @NScriptType UserEventScript
 * @NAmdConfig /SuiteScripts/config.json
 * 
 * <AUTHOR>
 * @module brdg_rip_validate_tier_group_ue
 */

define(["require", "LoDash", "N/log", "N/query", "N/error", "N/runtime"], (require, _) => {
	const log = require("N/log");
	const query = require("N/query");
	const error = require("N/error");
	const runtime = require("N/runtime");

	function runQuantitySql(tierLevelIdsArr) {
		const sqlQuery = `select custrecord_tier_quantity
		from customrecord_rebate_tier_level where id in (${tierLevelIdsArr})
		order by instr('${tierLevelIdsArr}',id)
		`;

		//Sql statement is ORDERING by the IN clause. It's taking the array and making it into a long string,
		// and ordering the ID (result of sql statement) by that string
		//INSTR returns the position of the first occurence of a string in another string
		//EX: '123, 111, 124' : if 123 results in quantity of 1 - it will order by finding where 123 is and putting 1 in that position

		const sqlResults = query.runSuiteQL({
			query: sqlQuery,
		}).results;

		const tierQuantitysArr = new Array();
		sqlResults.forEach((value) => {
			tierQuantitysArr.push(value.values[0]);
		});

		return tierQuantitysArr;
	}

	function beforeSubmit(context) {
		if (runtime.executionContext !== runtime.ContextType.USEREVENT) {
			return;
		}
		
		const tierGroupRecord = context.newRecord;
		const allTierLevelIdsArr = new Array();
		allTierLevelIdsArr.push(
			tierGroupRecord.getValue("custrecord_tier_level_1"),
			tierGroupRecord.getValue("custrecord_tier_level_2"),
			tierGroupRecord.getValue("custrecord_tier_level_3"),
			tierGroupRecord.getValue("custrecord_tier_level_4"),
			tierGroupRecord.getValue("custrecord_tier_level_5")
		);

		const tierLevelIdsArr = allTierLevelIdsArr.filter((element) => {
			return element !== null && element != "";
		});

		const tierQuantitysArr = runQuantitySql(tierLevelIdsArr);

		const checkIfArrIsSorted = tierQuantitysArr.every(
			(firstParameter, i, tierQuantitysArr) =>
				i < tierQuantitysArr.length - 1
					? tierQuantitysArr[i] < tierQuantitysArr[i + 1]
					: tierQuantitysArr[i]
		);

		//TRUE: [1, 5, 7, 8] : FALSE: [1, 3, 2, 6]
		//Runs the function on each element in the array - checks if each element is smaller then the next element
		//The every() method returns true if the function returns true for all elements and false if it's false for even one element

		if (!checkIfArrIsSorted) {
			throw error.create(
				"Error! Please make sure the tier levels are in correct order!"
			);
		}
	}

	return {
		beforeSubmit,
	};
});
