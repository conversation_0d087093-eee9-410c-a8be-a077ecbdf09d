import { EDI856ItemFulfillmentProcessor } from "../../../../../EDI/Classes/Decorators/856/edi_856_item_fulfillment_processor";
import { EDIWalmart } from "../../../../../EDI/Classes/Models/Partner/Customer/edi_walmart";
import { EDIItemFulfillment } from "../../../../../EDI/Classes/Models/Transaction/edi_item_fulfillment";
import { EDISalesTransaction } from "../../../../../EDI/Classes/Models/Transaction/edi_sales_transaction";
import * as record from "N/record";

const mockDate = new Date(2025, 0, 1);

beforeAll(() => {
    jest.spyOn(global, "Date").mockImplementation(() => mockDate);
});

afterAll(() => {
    jest.restoreAllMocks();
});

describe("EDI856ItemFulfillmentProcessor", () => {
    beforeAll(() => {
        jest.spyOn(EDISalesTransaction.prototype, "formatToYYYYMMDD").mockReturnValue("transactionDateFormatted");
        jest.spyOn(EDIItemFulfillment.prototype, "getPurchaseOrderNumber").mockReturnValue("purchaseOrderNumber");
        jest.spyOn(EDIItemFulfillment.prototype, "getLineItems").mockImplementation(function(){
            this.lineCount = 2;
            this.lineItems = [
                {
                    quantity: "quantity",
                    rate: "rate",
                    name: "name",
                    description: "description",
                    po1LineNumber: 1,
                },
                {
                    quantity: "quantity",
                    rate: "rate",
                    name: "name",
                    description: "description",
                    po1LineNumber: 2,
                }
            ];
            this.subtotal = "sub.total";
        });
    });
    describe("process", (() => {
        const walmart = new EDIWalmart({
            direction: "out",
            transactionType: "810",
        });
        let /** @type {EDI856ItemFulfillmentProcessor} */ processor;
        beforeEach(() => {
            processor = new EDI856ItemFulfillmentProcessor({
                customError: null,
                itemFulfillment: new EDIItemFulfillment({
                    customError: null,
                    index: 1,
                    id: "id",
                    type: record.Type.ITEM_FULFILLMENT,
                    customerParent: 8454,
                    otherrefnum: null,
                    tranid: "documentNumber",
                    createdfrom: null,
                    trandate: null,
                    shipCarrier: "shipCarrier",
                    ladingQuantity: "ladingQuantity",
                    shipWeight: "shipWeight",
                    trackingInformation: null,
                    trackingNumbers: "1,2,3",
                    street: "street",
                    city: "city",
                    state: "state",
                    zip: "zip",
                    customerFullName: "customer: Name",
                    customerEntityId: walmart.code,
                    customerGLN: walmart.gln,
                    controlNumber: 0,
                    supplierName: "supplierName",
                    supplierStreet: "supplierStreet",
                    supplierCity: "supplierCity",
                    supplierState: "supplierState",
                    supplierZip: "supplierZip",
                }),
                parsingInformation: walmart.getParsingInformation({
                    delimiters: walmart.delimiters,
                    direction: "out",
                }),
                template: walmart.template.OUT[856],
                purchasingSoftwareId: walmart.purchasingSoftwareId,
            });
            processor.process();
        });
        it("formats transaction date", () => {
            expect(EDISalesTransaction.prototype.formatToYYYYMMDD).toHaveBeenCalledWith(mockDate)
        });
        it("retrieves the otherrefnum value linked to the item fulfillment", () => {
            expect(EDIItemFulfillment.prototype.getPurchaseOrderNumber).toHaveBeenCalled()
        });
        it("parses the item sublist of the sales order linked to the item fulfillment", () => {
            expect(EDIItemFulfillment.prototype.getLineItems).toHaveBeenCalled()
        });
        it('generates file content', () => {
            const expectedFileContent =
`ISA*00*          *00*          *ZZ*7188210570     *08*925485US00     *250101*0000*U*00501*000000001*0*P*>~
GS*SH*7188210570*925485US00*20250101*0000*000000001*X*005010~
ST*856*0001~
BSN*00*documentNumber*20250101*0000~
HL*1**S~
TD1*CAS*ladingQuantity****G*shipWeight*LB~
TD5**2*shipCarrier*M~
DTM*011*transactionDateFormatted~
FOB*CC~
N1*SF*supplierName~
N3*supplierStreet~
N4*supplierCity*supplierState*supplierZip~
N1*ST*Name*UL*0078742028286~
N3*street~
N4*city*state*zip~
HL*2*1*O~
PRF*purchaseOrderNumber*20250101~
REF*BM*1~
REF*BM*2~
REF*BM*3~
HL*3*2*I~
LIN**VN*name~
SN1**quantity*EA~
PID*F****description~
HL*4*2*I~
LIN**VN*name~
SN1**quantity*EA~
PID*F****description~
CTT*2~
SE*28*0001~
GE*1*000000001~
IEA*1*000000001~
`
            expect(processor.fileContent).toEqual(expectedFileContent);
        });
    }));
});