/**
 * Transaction Record class
 * containing custom and searched data
 * that represents a NetSuite Item Fulfillment
 *
 * @NApiVersion 2.1
 * <AUTHOR>
 */

define(["N/search"], (search) => {

	/**
	 * Item Fulfillment Transaction Record class
	 *
	 * @class
	 * @type {import("./spl_transaction_record").ItemFulfillmentTransactionRecord}
	 */
	class ItemFulfillmentTransactionRecord {
		constructor(transaction) {
			this.Column = {
				customerIsShipByTruck: "custentity_spl_ship_by_truck"
			};
			this.Field = {
				entity: "entity"
			};
			this.customerId = transaction.getValue({
				fieldId: this.Field.entity
			});
			this.customerName = transaction.getText({
				fieldId: this.Field.entity
			});

			const customerLookup = search.lookupFields({
				type: search.Type.CUSTOMER,
				id: this.customerId,
				columns: [this.Column.customerIsShipByTruck]
			});

			this.customerIsShipByTruck = customerLookup && customerLookup[this.Column.customerIsShipByTruck];
		}
	}

	return ItemFulfillmentTransactionRecord;
});