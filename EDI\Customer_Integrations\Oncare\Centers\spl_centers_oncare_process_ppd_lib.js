/**
 * @NApiVersion 2.x
 */

define(['N/search'],
    function (search) {
        function processPPD(purchaseOrder) {
            var facilityIsVent = checkIfFacilityIsVent();
            if (facilityIsVent) {
                purchaseOrder.isVentFacility = true;
                checkForPPD();
            }
            return purchaseOrder;

            function checkIfFacilityIsVent() {
                var ventFacilities = [
                    '2179', //Richmond Center 
                    '2186', //Triboro Center
                    '2171', //Northern Manor
                    '2151' //Concord Nursing Home
                ];

                return ventFacilities.some(function (ventFac) {
                    return ventFac === purchaseOrder.customerId
                });
            }

            function checkForPPD() {
                var itemsArr = purchaseOrder.items;

                itemsArr.forEach(function (itemToCheck) {
                    if (itemToCheck.isPPD == '1') {
                        log.debug(itemToCheck + ' is PPD'); 
                        var ppdItem = itemToCheck;
                        checkForNonPpdItem();
                        setPPDRateAndAmount();

                        function checkForNonPpdItem() {
                            for (var x = 0; x < itemsArr.length; x++) {
                                var checkIfNonPpdItem = itemsArr[x];

                                if ((ppdItem.itemName === checkIfNonPpdItem.itemName) && checkIfNonPpdItem.isPPD != '1') {
                                    var nonPpdItem = checkIfNonPpdItem;

                                    log.debug({
                                        title: 'Also has Non-PPD Item',
                                        details: nonPpdItem
                                    })
                                    function setPPDQuantityAndRemoveNonPPD() {
                                        ppdItem = ppdItem;
                                        nonPpdItem = nonPpdItem;
                                        var ppdQuantity = parseInt(ppdItem.quantity);
                                        var nonPpdQuantity = parseInt(nonPpdItem.quantity);
                                        ppdItem.quantity = ppdQuantity + nonPpdQuantity;

                                        itemsArr.splice(x, 1);
                                    };

                                    setPPDQuantityAndRemoveNonPPD(ppdItem, nonPpdItem, x);
                                }
                            }
                        }

                        function setPPDRateAndAmount() {
                            ppdItem = ppdItem;
                            if (ppdItem.quantity > 1) {
                                function getRate() {
                                    var rateSearch = search.create({
                                        type: "noninventoryitem",
                                        filters:
                                            [
                                                ["type", "anyof", "NonInvtPart"],
                                                "AND",
                                                ["name", "haskeywords", ppdItem.itemName],
                                                "AND",
                                                ["pricing.pricelevel", "anyof", "6"]
                                            ],
                                        columns:
                                            [
                                                search.createColumn({
                                                    name: "unitprice",
                                                    join: "pricing",
                                                    label: "Unit Price"
                                                })
                                            ]
                                    });
                                    var resultSet = rateSearch.run();
                                    var result = resultSet.getRange({
                                        start: 0,
                                        end: 1
                                    });
                                    return result[0].getValue(resultSet.columns[0]);
                                }

                                var rate = getRate();
                                ppdItem.rate = rate * .5;
                            } else {
                                ppdItem.rate = .00;
                            };
                            ppdItem.amount = ppdItem.rate * ppdItem.quantity;
                        }
                    }
                });

                purchaseOrder.items = itemsArr;
            }

        };

        return {
            processPPD: processPPD
        };
    });