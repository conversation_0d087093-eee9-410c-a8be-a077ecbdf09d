/**
 * @description Suitelet that generates the CSV file from the Inventory Count Tool table and send it to a specific recipient
 *
 *
 * @NApiVersion 2.1
 * @NScriptType Suitelet
 * @NAmdConfig /SuiteScripts/config.json
 *
 * <AUTHOR>
 * @module brdg_send_csv_file_sl
 */

define([
	"require",
	"N/email",
	"N/file",
	"N/log",
	"N/task",
	"N/encode",
], function (require) {
	const email = require("N/email");
	const file = require("N/file");
	const log = require("N/log");
	const task = require("N/task");
	const encode = require("N/encode");
	function generateCSV(rowDataArr) {
		let newDate = new Date();
		let csvFile = file.create({
			name: `brdg_inv_count_${formatDate(newDate)}.csv`,
			contents:
				"Date,Subsidiary ID,Subsidiary,Item Internal ID,UPC Code,Description,Quantity in Store,UOM ID,UOM,Quantity in NS,Last Purchase Price,Item ID,Discrepancy,Reverse Discrepancy,Is Component Of\n",
			folder: 6521151,
			fileType: "CSV",
		});

		let itemInfoArr = JSON.parse(rowDataArr);

		itemInfoArr.forEach((itemInfo) => {
			let rowValue = "";
			itemInfo.forEach(function (item, itemIndex) {
				let itemVal = item.toString() || "";
				itemVal = itemVal.replace(/,/g, " ");
				if (itemIndex != itemInfo.length - 1) {
					rowValue += itemVal + ",";
				} else {
					rowValue += itemVal;
				}
			});
			csvFile.appendLine({
				value: rowValue,
			});
		});

		let csvId = csvFile.save();

		return csvId;
	}
	function formatDate(date) {
		var month = date.getMonth() + 1;
		var day = date.getDate();
		var year = date.getFullYear();
		var hours = date.getHours();
		var minutes = date.getMinutes();

		// Add leading zeros if necessary
		month = (month < 10 ? "0" : "") + month;
		day = (day < 10 ? "0" : "") + day;
		hours = (hours < 10 ? "0" : "") + hours;
		minutes = (minutes < 10 ? "0" : "") + minutes;

		// Return the formatted date string
		return month + "/" + day + "/" + year + " " + hours + ":" + minutes;
	}
	return {
		onRequest: function (context) {
			if (context.request.method === "POST") {
				let requestBody = context.request.body;
				let params = requestBody.split("&");

				let keyValuePairs = {};

				params.forEach(function (param) {
					let pair = param.split("=");
					let key = pair[0];
					let value = pair[1];
					keyValuePairs[key] = value;
				});

				// let data = JSON.parse(requestBody);
				let senderId = keyValuePairs.senderId;
				let rowDataArr = decodeURIComponent(keyValuePairs.rowDataArr);
				let mrRowData = decodeURIComponent(keyValuePairs.mrRowData);
				let subsidiaryId = keyValuePairs.subsidiaryId;
				let mrTask = task.create({
					taskType: task.TaskType.MAP_REDUCE,
					scriptId: "customscript_brdg_inv_cnt_indvdl_mr",
					params: {
						custscript  : JSON.stringify(mrRowData),
					},
				});

				let mrTaskId = mrTask.submit();

				let taskStatus = task.checkStatus(mrTaskId);
				if (taskStatus.status === "FAILED") {
					const authorId = -5;
					const recipientEmail = "<EMAIL>";
					email.send({
						author: authorId,
						recipients: recipientEmail,
						subject: "Failure executing map/reduce job!",
						body: "Map reduce task: " + mapReduceScriptId + " has failed.",
					});
				}
				let csvId = generateCSV(rowDataArr);
				let csvFile = file.load({
					id: csvId,
				});
				let dateToday = new Date();
				email.send({
					author: senderId,
					body: "Please find the results of the inventory count attached.",
					recipients: [3046, 43398],
					subject: `Inventory Count : ${subsidiaryId} : ${formatDate(
						dateToday
					)}`,
					attachments: [csvFile],
				});
			}
		},
	};
});
