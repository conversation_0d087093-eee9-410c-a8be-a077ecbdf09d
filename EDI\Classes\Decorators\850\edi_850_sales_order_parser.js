/**
 * @description Class containing functions specific to parsing Sales Order from incoming file
 *
 * @NApiVersion 2.1
 * <AUTHOR> <<EMAIL>>
 */

define([
    "exports",
    "require",
    "N/log",
    "./edi_850_parsed_transaction",
    "./edi_850_sales_order",
    "./edi_850_parser",
    "../edi_summary",
    "../../Models/File/edi_file",
    "../../../../Classes/vlmd_custom_error_object"
], function (/** @type {any} */ exports, /** @type {any} */ require) {
    const log = require("N/log");
    const { EDI850ParsedTransaction } = require("./edi_850_parsed_transaction");
    const { EDI850SalesOrder } = require("./edi_850_sales_order");
    const { EDI850Parser } = require("./edi_850_parser");
    const { EDISummary } = require("../edi_summary");
    const { DocumentType } = require("../../Models/File/edi_file");
    /** @type {CustomErrorObject} */
    const CustomErrorObject = require("../../../../Classes/vlmd_custom_error_object");

    /**
     * 850 Parser Class
     *
     * @class
     * @typedef {import("../../Interfaces/Models/Partner/edi_partner").EDICustomerInterface} EDICustomerInterface
     * @typedef {import("../../Interfaces/Decorators/850/edi_850_parser").EDI850ParserInterface} EDI850ParserInterface
     * @typedef {import("../../../../Classes/vlmd_custom_error_object").CustomErrorObject} CustomErrorObject
     * @extends {EDI850Parser}
     * @implements {EDI850ParserInterface}
     */
    class EDI850SalesOrderParser extends EDI850Parser {
        /** @param {{[key:string]: any}} params */
        constructor(params) {
            super(params);
            /** @type {EDICustomerInterface} */
            this.customer = params.customer;
            /** @type {CustomErrorObject} */
            this.customError = new CustomErrorObject();
            /** @type {string} */
            this.ediFileContent = params.ediFileContent;
            /** @type {EDI850ParsedTransaction} */
            this.salesOrder;
            /** @type {number} */
            this.soId;
            /** @type {any[]} */
            this.errors = [];
        }

        /**
         * Update the transaction property with parsed EDI File
         *  while consolidating errors in the errors property
         *
         * @returns {void}
         */
        parse() {
            try {
                this.salesOrder = new EDI850ParsedTransaction({
                    content: this.ediFileContent,
                    segmentDelimiter: this.customer.delimiters.segmentDelimiter,
                    fieldDelimiter: this.customer.delimiters.fieldDelimiter
                });
            } catch (/** @type {any} */ err) {
                throw this.customError.updateError({
                    errorType: this.customError.ErrorTypes.UNHANDLED_ERROR,
                    summary: "EDI850SalesOrderParser_parse",
                    details: err,
                });
            }
        }

        /**
         * Create a NetSuite Sales Order from the Purchase Order object from the EDI File
         *
         * @returns {void}
         */
        transform() {
            const edi850SalesOrder = new EDI850SalesOrder({
                customError: this.customError,
                parsedTransaction: this.salesOrder,
                customer: this.customer,
                customerQueryString: this.customer.generateQueryString(this.salesOrder.customer.identifier),
            });
            try {
                edi850SalesOrder.create();
                edi850SalesOrder.setValues();
                const itemsAdded = edi850SalesOrder.setItems();
        
                if (itemsAdded) {
                    this.soId = edi850SalesOrder.save();
                    log.audit({
                        title: "EDI850SalesOrderParser (transform): Sales Order ID",
                        details: this.soId
                    });
                    this.errors = this.errors.concat(edi850SalesOrder.errors);
                } else {
                    this.errors = this.errors.concat([...edi850SalesOrder.errors, "No item was added to the Sales Order. Transaction not created."]);
                }
            } catch (/** @type {any} */ err) {
                this.errors = this.errors.concat(edi850SalesOrder.errors);
                log.error({
                    title: "EDI850SalesOrderParser (transform)",
                    details: err,
                });
            }
        }

        /**
         * Retrieve the email object after transforming the file contents into a Sales Order
         *
         * @returns {void}
         */
        summarize() {
            const summary = new EDISummary({
                errors: this.errors,
                documentType: DocumentType.INCOMING_850,
                filename: this.filename,
                transactionType: "Sales Order",
                transactionId: this.soId,
                transactionNumber: this.salesOrder.number,
                controlNumber: this.salesOrder.controlNumber,
                referenceNumber: this.salesOrder.number,
                partner: this.customer,
            });
            summary.createPostProcessEmail();
            summary.pushEdiEmailInfoToDB();

            this.email = summary.data;
        }
    }

    exports.EDI850SalesOrderParser = EDI850SalesOrderParser;
});