/**
 * @description Client Script for the Inventory Count Tool for Individual Item Suitelet
 *
 * On click of start new count - create a new inventory count record
 * On click of complete count (only available on list view - call send CSV file MR which creates the file,
 *  sends it and calls the MR script to create the inventory count line subrecord)
 * Creates a new sessions record on every 10th scan
 *
 * @NApiVersion 2.1
 * @NScriptType ClientScript
 * @NAmdConfig /SuiteScripts/config.json
 *
 * @module brdg_inv_count_indvdl_cs
 */

define([
	"require",
	"N/query",
	"N/url",
	"N/runtime",
	"N/record",
	"N/https",
], function (require) {
	const query = require("N/query");
	const url = require("N/url");
	const runtime = require("N/runtime");
	const record = require("N/record");
	const https = require("N/https");

	let arrItemsScanned = [];
	let isUnique = false;
	let itemSequence = 0;

	//Client script syntax requires at least one entry point
	function pageInit(context) {}

	const helperFunctions = (function () {
		function clearForm() {
			jQuery("#fldUpcCode").prop("disabled", false);
			jQuery("#fldUpcCode").val("");
			jQuery("#fldItemObject").val("");
			jQuery("#fldItemName").val("");
			jQuery("#fldUom").val("");
			jQuery("#fldQtyNs").val("");
			jQuery("#fldQtyStore").val("");
			jQuery("#fldDisc").val("");
		}

		function generateCsvFile() {
			let subsidiaryId = jQuery("#fldSubsidiary").find(":selected").text();
			let currentUserId = runtime.getCurrentUser().id;
			let tableContents = [];

			for (let i = 0; i < arrItemsScanned.length; i++) {
				let rowContents = [];
				let getQty = "";
				if (arrItemsScanned[i].itemType == "Kit") {
					getQty = arrItemsScanned[i].compQty;
				} else {
					getQty = arrItemsScanned[i].stockAvailable;
				}
				rowContents.push(jQuery("#datepicker").val());
				rowContents.push(jQuery("#fldSubsidiary").find(":selected").val());
				rowContents.push(jQuery("#fldSubsidiary").find(":selected").text());
				rowContents.push(arrItemsScanned[i].itemId?.toString());
				rowContents.push(arrItemsScanned[i].upcCode?.toString());
				rowContents.push(arrItemsScanned[i].itemDesc?.toString());
				rowContents.push(arrItemsScanned[i].quantityEntered?.toString());
				rowContents.push(arrItemsScanned[i].uomId?.toString());
				rowContents.push(arrItemsScanned[i].uomValue?.toString());
				rowContents.push(getQty?.toString());
				rowContents.push(arrItemsScanned[i].stockPurchPrice?.toString());
				rowContents.push(arrItemsScanned[i].itemNumber?.toString());
				rowContents.push(arrItemsScanned[i].discrepancy?.toString() || "0");
				rowContents.push(
					arrItemsScanned[i].reverseDiscrepacy?.toString() || "0"
				);
				rowContents.push(
					arrItemsScanned[i].parentItem
						?.toString()
						.replace(/(\r\n|\n|\r)/gm, "") || ""
				);
				tableContents.push(rowContents);
			}

			console.log(`arrItemsScanned: ${JSON.stringify(arrItemsScanned)}`);

			let suiteletURL = url.resolveScript({
				scriptId: "customscript_brdg_send_csv_inv_count_sl",
				deploymentId: "customdeploy_brdg_send_csv_inv_count_sl",
			});

			console.log(
				"remaining usage before ajax post " +
					runtime.getCurrentScript().getRemainingUsage()
			);

			jQuery.ajax({
				type: "POST",
				contentType: "application/json; charset=utf-8",
				url: suiteletURL,
				data: {
					senderId: currentUserId,
					mrRowData: JSON.stringify(arrItemsScanned),
					rowDataArr: JSON.stringify(tableContents),
					subsidiaryId: subsidiaryId,
				},
				success: function (response) {
					alert(
						"Your count has been received and will be emailed to the back office."
					);
					jQuery("#tableViewDiv").hide();
					jQuery("#enterItemDiv").show();
					jQuery("#fldUpcCode").focus();
					helperFunctions.clearForm();
				},
				error: function (response) {
					console.log("error: " + JSON.stringify(response));
				},
			});

			//TESTING - will this work?
			https.requestSuitelet({
				scriptId: "customscript_brdg_indvdl_itm_cnt_tool_sl",
				deploymentId: "customdeploy_brdg_indvdl_itm_cnt_tool_sl",
				method: "POST",
			});
		}

		function formatDate(date) {
			var month = date.getMonth() + 1;
			var day = date.getDate();
			var year = date.getFullYear();
			var hours = date.getHours();
			var minutes = date.getMinutes();

			// Add leading zeros if necessary
			month = (month < 10 ? "0" : "") + month;
			day = (day < 10 ? "0" : "") + day;
			hours = (hours < 10 ? "0" : "") + hours;
			minutes = (minutes < 10 ? "0" : "") + minutes;

			// Return the formatted date string
			return month + "/" + day + "/" + year + " " + hours + ":" + minutes;
		}

		return {
			clearForm,
			generateCsvFile,
			formatDate,
		};
	})();

	const sqlHelperFunctions = (function () {
		function getItemByItemId(upcCode, subsidiaryId) {
			const itemTypeQuery = `
                SELECT 
                item.itemtype AS itemtype
                FROM
                item
                LEFT OUTER JOIN
                    inventoryItemLocations 
                    ON inventoryItemLocations.item = item.id 
                LEFT OUTER JOIN
                    Location 
                    ON inventoryItemLocations.location = Location.id 
                LEFT OUTER JOIN
                    LocationSubsidiaryMap 
                    ON Location.id = LocationSubsidiaryMap.location 
                WHERE
                    item.upccode = '${upcCode}'
                    AND item.isinactive = 'F' `;
			let resultsItemType = query
				.runSuiteQL({
					query: itemTypeQuery,
				})
				.asMappedResults();
			let itemType = resultsItemType[0].itemtype;
			const lppFieldId = `
                SELECT
                custrecord_last_purchase_price_by_store 
                FROM
                customrecord_bridge_store 
                WHERE
                custrecord_brdg_store_subsidiary = ${subsidiaryId}`;
			let resultsLppFieldId = query
				.runSuiteQL({
					query: lppFieldId,
				})
				.asMappedResults();

			if (itemType == "Kit") {
				const sqlQuery = `
                SELECT
                item.itemid AS itemnumber,
                item.displayname AS itemname,
                item.description AS itemdescription,
                item.id AS itemid,
                item.itemtype AS itemtype,
                member.upccode AS memberupc,
                member.itemid AS memberitemnumber,
                member.displayname AS memberitemname,
                member.description AS memberitemdescription,
                member.id AS memberitemid,
                KitItemMember.quantity AS componentqty,
                member.stockunit AS stockunitid,
                BUILTIN.DF(member.stockunit) AS stockunit,
                BUILTIN.DF(inventoryItemLocations.location) inventory_location,
                inventoryItemLocations.quantityavailable available,
                inventoryItemLocations.quantityavailable / stockunit.conversionrate stock_unit_available,
                (
                    member.${resultsLppFieldId[0]["custrecord_last_purchase_price_by_store"]} / saleunit.conversionrate 
                )
                * stockunit.conversionrate stock_price_per_unit,
                inventoryItemLocations.lastpurchasepricemli,
                BUILTIN.DF(Location.subsidiary) subsidiary,
                FROM
                item
                INNER JOIN KitItemMember 
                    ON ( KitItemMember.ParentItem = item.ID )
                INNER JOIN Item AS member
                    ON ( member.ID = KitItemMember.Item )
                LEFT OUTER JOIN
                    inventoryItemLocations 
                    ON inventoryItemLocations.item = member.id 
                LEFT OUTER JOIN
                    Location 
                    ON inventoryItemLocations.location = Location.id 
                LEFT OUTER JOIN
                    LocationSubsidiaryMap 
                    ON Location.id = LocationSubsidiaryMap.location 
                INNER JOIN
                    unitstypeuom purchaseunit 
                    ON purchaseunit.internalid = member.purchaseunit 
                INNER JOIN
                    unitstypeuom stockunit 
                    ON stockunit.internalid = member.stockunit 
                INNER JOIN
                    unitstypeuom saleUnit 
                    ON saleunit.internalid = member.saleunit
                WHERE
                LocationSubsidiaryMap.subsidiary = ${subsidiaryId}
                AND item.upccode = '${upcCode}'
                AND item.isinactive = 'F'`;
				let results = query
					.runSuiteQL({
						query: sqlQuery,
					})
					.asMappedResults();

				return results;
			} else {
				const sqlQuery = `
                SELECT
                item.itemid AS itemnumber,
                item.displayname AS itemname,
                item.description AS itemdescription,
                item.id AS itemid,
                item.stockunit AS stockunitid,
                BUILTIN.DF(item.stockunit) AS stockunit,
                BUILTIN.DF(inventoryItemLocations.location) inventory_location,
                inventoryItemLocations.quantityavailable available,
                inventoryItemLocations.quantityavailable / stockunit.conversionrate stock_unit_available,
                (
                    item.${resultsLppFieldId[0]["custrecord_last_purchase_price_by_store"]} / saleunit.conversionrate 
                )
                * stockunit.conversionrate stock_price_per_unit,
                inventoryItemLocations.lastpurchasepricemli,
                BUILTIN.DF(Location.subsidiary) subsidiary, --Field not available in criteria, only in results
                FROM
                item 
                LEFT OUTER JOIN
                inventoryItemLocations 
                ON inventoryItemLocations.item = item.id 
                LEFT OUTER JOIN
                Location 
                ON inventoryItemLocations.location = Location.id 
                LEFT OUTER JOIN
                LocationSubsidiaryMap 
                ON Location.id = LocationSubsidiaryMap.location 
                INNER JOIN
                unitstypeuom purchaseunit 
                ON purchaseunit.internalid = item.purchaseunit 
                INNER JOIN
                unitstypeuom stockunit 
                ON stockunit.internalid = item.stockunit 
                INNER JOIN
                unitstypeuom saleUnit 
                ON saleunit.internalid = item.saleunit
            
            WHERE
            LocationSubsidiaryMap.subsidiary = ${subsidiaryId}
            AND item.upccode = '${upcCode}'
            AND item.isinactive = 'F' `;

				let results = query
					.runSuiteQL({
						query: sqlQuery,
					})
					.asMappedResults();

				return results;
			}
		}

		function getInventoryCounts() {
			const sqlQuery = `SELECT
            customrecord_brdg_inventory_count.id as id,
            customrecord_brdg_inventory_count.name as name
            FROM
            customrecord_brdg_inventory_count ORDER BY id DESC`;
			let results = query
				.runSuiteQL({
					query: sqlQuery,
				})
				.asMappedResults();
			return results;
		}

		function getSessionsLists() {
			const sqlQuery = `SELECT 
            customrecord_brdg_inv_count_session.id as id,
            customrecord_brdg_inv_count_session.name as name,
            customrecord_brdg_inv_count_session.custrecord_brdg_employee as empId, 
            BUILTIN.DF(customrecord_brdg_inv_count_session.custrecord_brdg_employee) as empName,
            customrecord_brdg_inv_count_session.custrecord_brdg_timestamp as recTimestamp, 
            customrecord_brdg_inv_count_session.custrecord_brdg_table_data as sessionData
        FROM 
            customrecord_brdg_inv_count_session 
        WHERE 
            custrecord_brdg_timestamp >= (CURRENT_DATE - 7)
            AND
            custrecord_brdg_employee = '${runtime.getCurrentUser().id}'
            AND
            customrecord_brdg_inv_count_session.custrecord_inv_count = ${jQuery(
							"#inventoryCountData"
						).val()}`;

			let results = query
				.runSuiteQL({
					query: sqlQuery,
				})
				.asMappedResults();
			return results;
		}

		return {
			getItemByItemId,
			getInventoryCounts,
			getSessionsLists,
		};
	})();

	jQuery(document).ready(function () {
		console.log("ready");
		console.log(
			"remaining usage on ready " +
				runtime.getCurrentScript().getRemainingUsage()
		);

		populateScreenOnLoad();

		jQuery("#inventoryCountData").on("change", function () {
			populateExistingSessions();
		});

		jQuery("#btnStartNewCount").click(function (e) {
			e.preventDefault();
			jQuery("#itemFormDiv").show();
			jQuery("#loadDataDiv").hide();
		});

		jQuery("#btnLoadBack").click(function (e) {
			e.preventDefault();
			jQuery("#itemFormDiv").hide();
			jQuery("#loadDataDiv").show();
		});

		jQuery("#btnSubmit").click(function (e) {
			e.preventDefault();
			if (jQuery("#fldSubsidiary").val() !== "") {
				createNewParentRecord();
				jQuery("#itemFormDiv").hide();
				jQuery("#enterItemDiv").show();
				jQuery("#fldUpcCode").focus();
			} else {
				alert("Please choose a Subsidiary.");
			}
		});

		jQuery("#btnLoadData").click(function (e) {
			e.preventDefault();
			jQuery("#fldParentRecord").val(
				jQuery("#loadDataDiv > #inventoryCountData").val()
			);

			if (jQuery("#loadDataDiv > #fldLoadData").val() !== null) {
				loadRecordSession(jQuery("#fldLoadData").val());
			}

			jQuery("#itemFormDiv").hide();
			jQuery("#enterItemDiv").show();
			jQuery("#loadDataDiv").hide();
			jQuery("#fldUpcCode").focus();
		});

		//Setting the maximum character length accepted by the UPC Input field
		jQuery("#fldUpcCode").on("input", function () {
			let maxLength = 13;
			if (jQuery(this).val().length > maxLength) {
				jQuery(this).val(jQuery(this).val().slice(0, maxLength));
			}
		});

		loadUpcChangeFunctionality();
		loadQuantityChangeFunctionality();
		loadScanNextItemFunctionality();
		loadDeleteRowFunctionality();
	});

	function loadUpcChangeFunctionality() {
		jQuery("#fldUpcCode").on("change", function () {
			console.log(
				"remaining usage on UPC change " +
					runtime.getCurrentScript().getRemainingUsage()
			);
			let upcCode = jQuery(this).val();
			let subsidiaryId = jQuery("#fldSubsidiary").val();
			let itemResults = sqlHelperFunctions.getItemByItemId(
				upcCode,
				subsidiaryId
			);

			isUnique = isInputValueUnique(upcCode);

			if (itemResults.length > 0) {
				let objItemVal = assignItemValues(itemResults, itemSequence, upcCode);
				jQuery(this).prop("disabled", true);
				jQuery("#fldQtyStore").prop("disabled", false);
				jQuery("#fldItemObject").val(JSON.stringify(objItemVal));

				if (Array.isArray(objItemVal)) {
					jQuery("#fldItemName").val(objItemVal[0]["parentItem"]);
					jQuery("#fldQtyNs").val(objItemVal[0]["parentItem"]);
					jQuery("#fldQtyNs").val(objItemVal[0]["stockAvailable"]);
					jQuery("#fldDisc").val(objItemVal[0]["discrepancy"]);
				} else {
					jQuery("#fldItemName").val(objItemVal.itemDesc);
					jQuery("#fldUom").val(objItemVal.uomValue);
					jQuery("#fldQtyNs").val(objItemVal.stockAvailable);
					jQuery("#fldDisc").val(objItemVal.discrepancy);
				}

				jQuery("#fldQtyStore").focus();
			} else {
				alert("No item found!");
				helperFunctions.clearForm();
			}
		});
	}

	function loadQuantityChangeFunctionality() {
		jQuery("#fldQtyStore").on("change", function () {
			let currUpc = jQuery("#fldUpcCode").val();
			let objCurrItemValues = JSON.parse(jQuery("#fldItemObject").val());
			let quantityEntered = Number(jQuery(this).val());
			let currentRowArr = [];

			if (Array.isArray(objCurrItemValues)) {
				objCurrItemValues.forEach(function (currItem) {
					if (!isUnique) {
						for (let i = 0; i < arrItemsScanned.length; i++) {
							if (arrItemsScanned[i].upcCode === currUpc) {
								let currentItemObj = {};
								currentItemObj.stockAvailable = currItem.stockAvailable;
								currentItemObj.quantityEntered = quantityEntered;
								currentItemObj.discrepancy = jQuery("#fldDisc").val();
								currentItemObj.reverseDiscrepancy = currItem.reverseDiscrepacy;
								currentRowArr.push(currentItemObj);
							}
						}

						for (let j = 0; j < currentRowArr.length; j++) {
							if (currentRowArr.length >= 1) {
								if (j == 0) {
									if (!currentRowArr[j].discrepancy) {
										objCurrItemValues.discrepancy =
											Number(currentRowArr[j].stockAvailable) -
											Number(currentRowArr[j].quantityEntered);
										objCurrItemValues.reverseDiscrepacy =
											Number(currentRowArr[j].quantityEntered) -
											Number(currentRowArr[j].stockAvailable);
									} else {
										objCurrItemValues.discrepancy =
											Number(currentRowArr[j].discrepancy) -
											Number(currentRowArr[j].quantityEntered);
										objCurrItemValues.reverseDiscrepacy =
											Number(currentRowArr[j].quantityEntered) -
											Number(currentRowArr[j].discrepancy);
									}

									jQuery("#fldDisc").val(objCurrItemValues.discrepancy);
								} else {
									let previousRowDiscrepancy = currentRowArr[j - 1].discrepancy;
									let previousRowReverseDiscrepancy =
										currentRowArr[j - 1].reverseDiscrepancy;
									objCurrItemValues.discrepancy =
										Number(previousRowDiscrepancy) -
										Number(currentRowArr[j].quantityEntered);
									objCurrItemValues.reverseDiscrepacy =
										Number(currentRowArr[j].quantityEntered) -
										Number(previousRowReverseDiscrepancy);
									jQuery("#fldDisc").val(objCurrItemValues.discrepancy);
								}
							}
						}
					} else {
						let quantityAvailable = 0;

						if (jQuery("#fldQtyNs").val()) {
							quantityAvailable = currItem.compQty;
						}
						currItem.discrepancy = Number(quantityAvailable - quantityEntered);
						currItem.reverseDiscrepacy = Number(
							quantityEntered - quantityAvailable
						);
						jQuery("#fldDisc").val(Number(quantityAvailable - quantityEntered));
					}
					currItem.quantityEntered = jQuery("#fldQtyStore").val();
				});
			} else {
				if (!isUnique) {
					for (let i = 0; i < arrItemsScanned.length; i++) {
						if (arrItemsScanned[i].upcCode === currUpc) {
							let currentItemObj = {};
							currentItemObj.stockAvailable = objCurrItemValues.stockAvailable;
							currentItemObj.quantityEntered = quantityEntered;
							currentItemObj.discrepancy = jQuery("#fldDisc").val();
							currentItemObj.reverseDiscrepancy =
								objCurrItemValues.reverseDiscrepacy;
							currentRowArr.push(currentItemObj);
						}
					}

					for (let j = 0; j < currentRowArr.length; j++) {
						if (currentRowArr.length >= 1) {
							if (j == 0) {
								if (!currentRowArr[j].discrepancy) {
									objCurrItemValues.discrepancy =
										Number(currentRowArr[j].stockAvailable) -
										Number(currentRowArr[j].quantityEntered);
									objCurrItemValues.reverseDiscrepacy =
										Number(currentRowArr[j].quantityEntered) -
										Number(currentRowArr[j].stockAvailable);
								} else {
									objCurrItemValues.discrepancy =
										Number(currentRowArr[j].discrepancy) -
										Number(currentRowArr[j].quantityEntered);
									objCurrItemValues.reverseDiscrepacy =
										Number(currentRowArr[j].quantityEntered) -
										Number(currentRowArr[j].discrepancy);
								}

								jQuery("#fldDisc").val(objCurrItemValues.discrepancy);
							} else {
								let previousRowDiscrepancy = currentRowArr[j - 1].discrepancy;
								let previousRowReverseDiscrepancy =
									currentRowArr[j - 1].reverseDiscrepancy;
								objCurrItemValues.discrepancy =
									Number(previousRowDiscrepancy) -
									Number(currentRowArr[j].quantityEntered);
								objCurrItemValues.reverseDiscrepacy =
									Number(currentRowArr[j].quantityEntered) -
									Number(previousRowReverseDiscrepancy);
								jQuery("#fldDisc").val(objCurrItemValues.discrepancy);
							}
						}
					}
				} else {
					let quantityAvailable = 0;

					if (jQuery("#fldQtyNs").val()) {
						quantityAvailable = jQuery("#fldQtyNs").val();
					}
					objCurrItemValues.discrepancy = Number(
						quantityAvailable - quantityEntered
					);
					objCurrItemValues.reverseDiscrepacy = Number(
						quantityEntered - quantityAvailable
					);
					jQuery("#fldDisc").val(Number(quantityAvailable - quantityEntered));
				}
				objCurrItemValues.quantityEntered = jQuery("#fldQtyStore").val();
			}

			jQuery("#fldItemObject").val(JSON.stringify(objCurrItemValues));
		});
	}

	function loadScanNextItemFunctionality() {
		jQuery("#btnNewItem").click(function (e) {
			if (jQuery("#fldQtyStore").val()) {
				let itemObjValues = JSON.parse(jQuery("#fldItemObject").val());
				if (Array.isArray(itemObjValues)) {
					itemObjValues.forEach(function (item) {
						arrItemsScanned.push(item);
					});
				} else {
					arrItemsScanned.push(itemObjValues);
				}

				itemSequence++;
				helperFunctions.clearForm();
				jQuery("#fldUpcCode").focus();
				if (itemSequence % 10 === 0) {
					createRecordEntrySession();
				}
			} else {
				alert("Please Enter a Quantity.");
			}
		});
	}

	function loadDeleteRowFunctionality() {
		jQuery("#tableViewDiv > #itemTableSublist").on(
			"click",
			".btnDeleteRow",
			function (e) {
				e.preventDefault();
				let rowId = jQuery(this).data("row");

				let currentRowArr = [];
				let tableRowArr = [];
				for (let i = 0; i < arrItemsScanned.length; i++) {
					if (
						jQuery(this)
							.closest("tr")
							.find('td[data-row="' + rowId + '"]:eq(1)')
							.text() === arrItemsScanned[i].upcCode?.toString() &&
						rowId != arrItemsScanned[i].itemSequence
					) {
						let currentItemObj = {};
						currentItemObj.itemSequence = arrItemsScanned[i].itemSequence;
						currentItemObj.upcCode = arrItemsScanned[i].upcCode;
						currentItemObj.itemId = arrItemsScanned[i].itemId;
						currentItemObj.itemDesc = arrItemsScanned[i].itemDesc;
						currentItemObj.uomId = arrItemsScanned[i].uomId;
						currentItemObj.uomValue = arrItemsScanned[i].uomValue;
						currentItemObj.stockAvailable = arrItemsScanned[i].stockAvailable;
						currentItemObj.stockPurchPrice = arrItemsScanned[i].stockPurchPrice;
						currentItemObj.quantityEntered = arrItemsScanned[i].quantityEntered;
						currentItemObj.discrepancy = arrItemsScanned[i].discrepancy;
						currentItemObj.reverseDiscrepancy =
							arrItemsScanned[i].reverseDiscrepacy;
						currentItemObj.itemNumber = arrItemsScanned[i].itemNumber;
						currentRowArr.push(currentItemObj);
					} else if (
						jQuery(this)
							.closest("tr")
							.find('td[data-row="' + rowId + '"]:eq(1)')
							.text() !== arrItemsScanned[i].upcCode?.toString()
					) {
						tableRowArr.push(arrItemsScanned[i]);
					}
				}
				jQuery(this)
					.closest("tr")
					.find('[data-row="' + rowId + '"]')
					.remove();

				for (let i = 0; i < currentRowArr.length; i++) {
					if (currentRowArr.length >= 1) {
						if (i == 0) {
							currentRowArr[i].discrepancy =
								Number(currentRowArr[i].stockAvailable) -
								Number(currentRowArr[i].quantityEntered);
							currentRowArr[i].reverseDiscrepancy =
								Number(currentRowArr[i].quantityEntered) -
								Number(currentRowArr[i].stockAvailable);
						} else {
							let previousRowDiscrepancy = currentRowArr[i - 1].discrepancy;
							let previousRowReverseDiscrepancy =
								currentRowArr[i - 1].reverseDiscrepancy;
							currentRowArr[i].discrepancy =
								Number(previousRowDiscrepancy) -
								Number(currentRowArr[i].quantityEntered);
							currentRowArr[i].reverseDiscrepancy =
								Number(currentRowArr[i].quantityEntered) -
								Number(previousRowReverseDiscrepancy);
						}
					}
					tableRowArr.push(currentRowArr[i]);
				}

				arrItemsScanned = tableRowArr;
				displayResults(arrItemsScanned);
			}
		);
	}

	function assignItemValues(item, itemSeq, currUpc) {
		let objItemValues = {};
		let arrObjItemValues = [];
		let subsidiaryId = jQuery("#fldSubsidiary").find(":selected").val();

		if (item[0]["itemtype"] == "Kit") {
			for (let i = 0; i < item.length; i++) {
				objItemValues.itemSequence = itemSeq;
				objItemValues.parentRecord = jQuery("#fldParentRecord").val();

				objItemValues.parentItem = item[i]["itemid"];
				objItemValues.itemType = item[i]["itemtype"];
				objItemValues.itemId = item[i]["memberitemid"];
				objItemValues.upcCode = currUpc;
				objItemValues.compUpcCode = item[i]["upcCode"];
				objItemValues.subsidiaryId = subsidiaryId;
				objItemValues.itemDesc = item[i]["memberitemdescription"];
				objItemValues.compQty = item[i]["componentqty"];
				objItemValues.uomId = item[i]["stockunitid"];
				objItemValues.uomValue = item[i]["stockunit"];
				if (!isUnique) {
					for (let j = 0; j < arrItemsScanned.length; j++) {
						if (arrItemsScanned[i].upcCode === currUpc) {
							objItemValues.stockAvailable = item[i]["componentqty"];
							objItemValues.discrepancy = arrItemsScanned[j].discrepancy;
						}
					}
				} else {
					objItemValues.stockAvailable = item[i]["componentqty"];
				}
				if (item[0]["stock_price_per_unit"]) {
					objItemValues.stockPurchPrice =
						item[i]["stock_price_per_unit"].toFixed(2);
				} else {
					objItemValues.stockPurchPrice = 0.0;
				}
				objItemValues.itemNumber = item[i]["memberitemnumber"];
				objItemValues.compOf = item[i]["itemid"];
				arrObjItemValues.push(objItemValues);
				objItemValues = {};
			}
			return arrObjItemValues;
		} else {
			objItemValues.itemSequence = itemSeq;
			objItemValues.itemType = item[0]["itemtype"];
			objItemValues.itemId = item[0]["itemid"];
			objItemValues.parentRecord = jQuery("#fldParentRecord").val();
			objItemValues.upcCode = currUpc;
			objItemValues.subsidiaryId = subsidiaryId;
			objItemValues.itemDesc = item[0]["itemdescription"];
			objItemValues.uomId = item[0]["stockunitid"];
			objItemValues.uomValue = item[0]["stockunit"];
			if (!isUnique) {
				for (let i = 0; i < arrItemsScanned.length; i++) {
					if (arrItemsScanned[i].upcCode === currUpc) {
						objItemValues.stockAvailable = item[0]["stock_unit_available"];
						objItemValues.discrepancy = arrItemsScanned[i].discrepancy;
					}
				}
			} else {
				objItemValues.stockAvailable = item[0]["stock_unit_available"];
			}

			if (item[0]["stock_price_per_unit"]) {
				objItemValues.stockPurchPrice =
					item[0]["stock_price_per_unit"].toFixed(2);
			} else {
				objItemValues.stockPurchPrice = 0.0;
			}
			objItemValues.itemNumber = item[0]["itemnumber"];
			return objItemValues;
		}
	}

	function displayStartPage() {
		jQuery("#itemFormDiv").append(
			`<h1><span class="label label-primary">Store:</span></h1><br /><select id="fldSubsidiary" name="subsidiary" class="form-control select2 select2-hidden-accessible">`
		);
		let subsidiaries = getSubsidiaryLists();
		let blankOption = jQuery("<option selected></option>")
			.attr("value", "")
			.text("");
		jQuery("#fldSubsidiary").append(blankOption);
		subsidiaries.forEach(function (subsidiary) {
			let option = "";

			if (subsidiary.id == 16) {
				option = jQuery("<option selected></option>")
					.attr("value", subsidiary.id)
					.text(subsidiary.name);
			} else {
				option = jQuery("<option></option>")
					.attr("value", subsidiary.id)
					.text(subsidiary.name);
			}
			jQuery("#fldSubsidiary").append(option);
		});
		jQuery("#itemFormDiv").append(
			`<h1><span class="label label-primary">Date:</span></h1><br /><input type="date" id="datepicker" class="form-control">`
		);
		let today = new Date().toISOString().split("T")[0];
		jQuery("#datepicker").val(today);
		jQuery("#itemFormDiv").append(
			`<br/><br/><button class="btn btn-secondary" id='btnLoadBack'>Back</button>`
		);
		jQuery("#itemFormDiv").append(
			`<button class="btn btn-primary" id='btnSubmit'>Start Count</button><br/><br/>`
		);
	}

	function displayMainPage() {
		//enterItemDiv
		jQuery("#enterItemDiv").append(
			'<h1><span class="label label-primary">UPC Code:</span></h1><br /><input class="form-control" type="number" id="fldUpcCode" style="font-weight: bold;" required/><br />'
		);
		jQuery("#enterItemDiv")
			.append(`<input type="text" class="form-control" placeholder="Parent Record" id="fldParentRecord" hidden>
        <textarea type="text" class="form-control" placeholder="Item Object" aria-label="Item Object" id="fldItemObject" hidden>`);
		jQuery("#enterItemDiv").append(`
        <div class="row">
            <div class="col">
                <h1><span class="label label-primary">Item Name :</span></h1><br /><input type="text" class="form-control" placeholder="Item Name" aria-label="Item Name" id="fldItemName" readonly>
            </div>
        </div>
        <div class="row>
            <div class="col">
                <h1><span class="label label-primary">Quantity in NS :</span></h1><br /><input type="text" class="form-control" placeholder="Quantity in NS" aria-label="Quantity in NS" id="fldQtyNs" readonly>   
            </div>
        </div>
        `);
		jQuery("#enterItemDiv").append(`
        <div class="row">
            <div class="col">
            <h1><span class="label label-primary">Item UOM :</span></h1><br /><input type="text" class="form-control" placeholder="Item UOM" aria-label="Item UOM" id="fldUom" readonly>
            </div>
        </div>
        <div class="row">
            <div class="col">
            <h1><span class="label label-primary">Discrepancy:</span></h1><br /><input type="text" class="form-control" placeholder="Discrepancy" aria-label="Discrepancy" id="fldDisc" readonly>
            </div>
        </div>
        `);
		jQuery("#enterItemDiv").append(
			'<h1><span class="label label-primary">Quantity in Store:</span></h1><br /><input class="form-control" type="number" id="fldQtyStore" required/><br />'
		);
		jQuery("#enterItemDiv").append(`
        <div class="row">
            <div class="col">
                <input type="button" class="btn btn-secondary" id="btnTable" value="Add and View List">
                <input type="button" class="btn btn-success" id="btnNewItem" value="Add and Scan Another">
            </div>                
        </div>
        `);
		jQuery("#fldQtyStore").prop("disabled", true);
	}

	function displayTableView() {
		jQuery("#tableViewDiv").append(addItemTableCss());
		jQuery("#tableViewDiv").append(createItemTable());
		jQuery("#tableViewDiv > #itemTableSublist").addClass("customTable");

		jQuery("#btnTable").click(function (e) {
			e.preventDefault();
			jQuery("#enterItemDiv").hide();
			jQuery("#tableViewDiv").show();
			if (jQuery("#fldUpcCode").val()) {
				if (jQuery("#fldQtyStore").val()) {
					let itemObjValues = JSON.parse(jQuery("#fldItemObject").val());

					if (Array.isArray(itemObjValues)) {
						itemObjValues.forEach(function (item) {
							arrItemsScanned.push(item);
						});
					} else {
						arrItemsScanned.push(itemObjValues);
					}
					itemSequence++;
					helperFunctions.clearForm();
					if (itemSequence % 10 === 0) {
						createRecordEntrySession();
					}
					displayResults(arrItemsScanned);
				} else {
					alert("Please Enter a Quantity.");
				}
			} else {
				displayResults(arrItemsScanned);
			}
		});
		jQuery("#btnBack").click(function (e) {
			e.preventDefault();
			jQuery("#tableViewDiv").hide();
			jQuery("#enterItemDiv").show();
			jQuery("#fldUpcCode").focus();
			helperFunctions.clearForm();
		});

		jQuery("#btnCompleteCount").click(function (e) {
			console.log(
				"remaining usage on complete count button click " +
					runtime.getCurrentScript().getRemainingUsage()
			);

			e.preventDefault();
			helperFunctions.generateCsvFile();
		});
	}

	function populateExistingInventoryCounts() {
		let inventoryCounts = sqlHelperFunctions.getInventoryCounts();

		let inventoryCountOption = "";
		inventoryCountOption = jQuery("<option selected></option>")
			.attr("value", " ")
			.text(" ");
		jQuery("#loadDataDiv > #inventoryCountData").append(inventoryCountOption);

		inventoryCounts.forEach(function (parentRec) {
			inventoryCountOption = jQuery("<option></option>")
				.attr("value", parentRec.id)
				.text(parentRec.name);

			jQuery("#loadDataDiv > #inventoryCountData").append(inventoryCountOption);
		});
	}

	function populateScreenOnLoad() {
		jQuery("#enterItemDiv").hide();
		jQuery("#tableViewDiv").hide();
		jQuery("#itemFormDiv").hide();
		displayStartPage();
		displayMainPage();
		displayTableView();
		populateExistingInventoryCounts();
	}

	function populateExistingSessions() {
		let sessionRecords = sqlHelperFunctions.getSessionsLists();

		jQuery("#loadDataDiv > #fldLoadData option").remove();

		sessionRecords.forEach(function (sessionRec) {
			let option = "";

			option = jQuery("<option></option>")
				.attr("value", sessionRec.id)
				.text(sessionRec.name);

			jQuery("#loadDataDiv > #fldLoadData").append(option);
		});
	}

	/**
	 *
	 * @description This function adds the CSS on the HTML Item Table.
	 * @returns {String}
	 */
	function addItemTableCss() {
		let cssValue = "";

		cssValue += `
		<style>
		/* Define styles in a separate style block */
		.customTable {
			border-collapse: collapse;
			width: 100%;
		}

		.customTable th, .customTable td {
			border: 1px solid #dddddd;
			text-align: center;
			font-size:20pt;
		}

		.customTable th {
			background-color: #f2f2f2;
		}
		
		</style>`;

		return cssValue;
	}

	/**

	 *
	 * @description This function adds the HTML Item Table on the <div> inside the Inline HTML field.
	 * @returns {String}
	 */

	function createItemTable() {
		let htmlTable = "";

		htmlTable += `
        <input type="button" class="btn btn-secondary" id="btnBack" value="Back">
        <input type="button" class="btn btn-success" id="btnCompleteCount" value="Complete Count"><br/><br/>
        <table id='itemTableSublist'>
		<thead>
		<tr>
		<th colspan='9'>Line #</th>
		<th colspan='9'>UPC Code</th>
		<th colspan='9'>Description</th>
		<th colspan='9'>Quantity in Store</th>
		<th colspan='9'>Item UOM</th>
		<th>Quantity in NS</th>
		<th hidden>Last Purchase Price</th>
		<th hidden>Item ID</th>
		<th>Discrepancy</th>
		<th>Action</th>
		</tr>
		</thead>
		<tbody>
		</tbody>
		</table>
		`;

		return htmlTable;
	}
	/**
	 *
	 * @description This function pulls the list of Subsidiaries/stores that is eligible for Inventory Count.
	 * @returns {Object} SQL mapped results pulled from the Subsidiary list.
	 */
	function getSubsidiaryLists() {
		const sqlQuery = `SELECT
		subsidiary.id AS id,
		subsidiary.name AS name FROM
		subsidiary JOIN
		subsidiary parent ON subsidiary.parent = parent.id where
		(
		subsidiary.parent = 16 or parent.parent = 16
		)
		and subsidiary.id not in (
		26,
		27,
		22
		)`;

		let results = query
			.runSuiteQL({
				query: sqlQuery,
			})
			.asMappedResults();
		return results;
	}

	/**
	 *
	 * @description This function runs a query to pull an Item by UPC code.
	 * @param {Number}
	 * @returns {Object} SQL mapped results of the pulled Item
	 */

	function createNewParentRecord() {
		let custRecParent = record.create({
			type: "customrecord_brdg_inventory_count",
			isDynamic: false,
		});
		let dateToday = new Date();
		let formattedDate = helperFunctions.formatDate(dateToday);
		custRecParent.setValue({
			fieldId: "name",
			value: runtime.getCurrentUser().name + ": " + formattedDate,
		});
		custRecParent.setValue({
			fieldId: "custrecord_inv_count_parent_sub",
			value: jQuery("#fldSubsidiary").find(":selected").val(),
		});
		custRecParent.setValue({
			fieldId: "custrecord_inv_count_parent_date",
			value: dateToday,
		});

		let newRecId = custRecParent.save();
		jQuery("#fldParentRecord").val(newRecId);
	}
	function createRecordEntrySession() {
		let custRecSession = record.create({
			type: "customrecord_brdg_inv_count_session",
			isDynamic: true,
		});
		let dateToday = new Date();
		let formattedDate = helperFunctions.formatDate(dateToday);
		custRecSession.setValue({
			fieldId: "name",
			value: runtime.getCurrentUser().name + ": " + formattedDate,
		});
		custRecSession.setValue({
			fieldId: "custrecord_brdg_employee",
			value: runtime.getCurrentUser().id,
		});
		custRecSession.setValue({
			fieldId: "custrecord_brdg_timestamp",
			value: dateToday,
		});
		custRecSession.setValue({
			fieldId: "custrecord_brdg_table_data",
			value: JSON.stringify(arrItemsScanned),
		});

		custRecSession.setValue({
			fieldId: "custrecord_inv_count",
			value: jQuery("#fldParentRecord").val(),
		});

		custRecSession.save();
	}
	function loadRecordSession(sessionId) {
		let custRecSession = record.load({
			type: "customrecord_brdg_inv_count_session",
			id: sessionId,
			isDynamic: false,
		});
		loadResults(
			custRecSession.getValue({ fieldId: "custrecord_brdg_table_data" })
		);
		jQuery("#fldSubsidiary").val(arrItemsScanned[0].subsidiaryId);
		jQuery("#datepicker").val(
			custRecSession.getValue({ fieldId: "custrecord_brdg_timestamp" })
		);
	}
	function loadResults(tableData) {
		let arrItemLoadResults = JSON.parse(tableData);
		arrItemsScanned = arrItemLoadResults;

		let tbody = jQuery("#tableViewDiv > #itemTableSublist tbody");
		tbody.empty();
		for (let i = 0; i < arrItemLoadResults.length; i++) {
			let getQty = "";
			if (arrItemLoadResults[i].itemType == "Kit") {
				getQty = arrItemLoadResults[i].compQty;
			} else {
				getQty = arrItemLoadResults[i].stockAvailable;
			}
			let row = jQuery(`<tr>`)
				.append(
					jQuery(
						'<td data-row="' + arrItemLoadResults[i].itemSequence + '" hidden>'
					).text(arrItemLoadResults[i].itemId)
				)
				.append(
					jQuery(
						'<td data-row="' +
							arrItemLoadResults[i].itemSequence +
							'" colspan="9">'
					).text(arrItemLoadResults[i].upcCode)
				)
				.append(
					jQuery(
						'<td data-row="' +
							arrItemLoadResults[i].itemSequence +
							'"  colspan="9">'
					).text(arrItemLoadResults[i].itemDesc)
				)
				.append(
					jQuery(
						'<td data-row="' +
							arrItemLoadResults[i].itemSequence +
							'"  colspan="9">'
					).text(arrItemLoadResults[i].quantityEntered)
				)
				.append(
					jQuery(
						'<td data-row="' + arrItemLoadResults[i].itemSequence + '" hidden>'
					).text(arrItemLoadResults[i].uomId)
				)
				.append(
					jQuery(
						'<td data-row="' +
							arrItemLoadResults[i].itemSequence +
							'" colspan="9">'
					).text(arrItemLoadResults[i].uomValue)
				)
				.append(
					jQuery(
						'<td data-row="' + arrItemLoadResults[i].itemSequence + '">'
					).text(getQty)
				)
				.append(
					jQuery(
						'<td data-row="' + arrItemLoadResults[i].itemSequence + '" hidden>'
					).text(arrItemLoadResults[i].stockPurchPrice)
				)
				.append(
					jQuery(
						'<td data-row="' + arrItemLoadResults[i].itemSequence + '" hidden>'
					).text(arrItemLoadResults[i].itemNumber)
				)
				.append(
					jQuery(
						'<td data-row="' + arrItemLoadResults[i].itemSequence + '">'
					).text(arrItemLoadResults[i].discrepancy)
				)
				.append(
					jQuery(
						'<td data-row="' + arrItemLoadResults[i].itemSequence + '"  hidden>'
					).text(arrItemLoadResults[i].reverseDiscrepacy)
				)
				.append(
					jQuery(
						'<td data-row="' + arrItemLoadResults[i].itemSequence + '"  hidden>'
					).text(arrItemLoadResults[i].parentItem)
				)
				.append(
					`<td align="center"><button type="button" data-row="${arrItemLoadResults[i].itemSequence}" class='btnDeleteRow'> Delete </button></td>`
				)
				.append(`</tr>`);

			tbody.append(row);
		}
	}

	/**

	 *
	 * @description This function checks if the UPC code is already existing in the Item table.
	 * @returns {String}
	 */

	function isInputValueUnique(value) {
		let isUnique = true;

		let result = jQuery.grep(arrItemsScanned, function (obj) {
			return obj.upcCode === value;
		});
		if (result.length > 0) {
			isUnique = false;
		}

		return isUnique;
	}

	function displayResults(arrItemsScanned, newRowId) {
		jQuery("#tableViewDiv > #itemTableSublist tbody").empty();
		for (let i = 0; i < arrItemsScanned.length; i++) {
			let getQty = "";
			if (arrItemsScanned[i].itemType == "Kit") {
				getQty = arrItemsScanned[i].compQty;
			} else {
				getQty = arrItemsScanned[i].stockAvailable;
			}
			jQuery("#tableViewDiv > #itemTableSublist tbody")
				.append(`<tr>`)
				.append(
					jQuery(
						'<td data-row="' + arrItemsScanned[i].itemSequence + '" hidden>'
					).text(arrItemsScanned[i].itemId)
				)
				.append(
					jQuery(
						'<td data-row="' +
							arrItemsScanned[i].itemSequence +
							'" colspan="9">'
					).text((i + 1).toString())
				)
				.append(
					jQuery(
						'<td data-row="' +
							arrItemsScanned[i].itemSequence +
							'" colspan="9">'
					).text(arrItemsScanned[i].upcCode)
				)
				.append(
					jQuery(
						'<td data-row="' +
							arrItemsScanned[i].itemSequence +
							'"  colspan="9">'
					).text(arrItemsScanned[i].itemDesc)
				)
				.append(
					jQuery(
						'<td data-row="' +
							arrItemsScanned[i].itemSequence +
							'"  colspan="9">'
					).text(arrItemsScanned[i].quantityEntered)
				)
				.append(
					jQuery(
						'<td data-row="' + arrItemsScanned[i].itemSequence + '" hidden>'
					).text(arrItemsScanned[i].uomId)
				)
				.append(
					jQuery(
						'<td data-row="' +
							arrItemsScanned[i].itemSequence +
							'" colspan="9">'
					).text(arrItemsScanned[i].uomValue)
				)
				.append(
					jQuery(
						'<td data-row="' + arrItemsScanned[i].itemSequence + '">'
					).text(getQty)
				)
				.append(
					jQuery(
						'<td data-row="' + arrItemsScanned[i].itemSequence + '" hidden>'
					).text(arrItemsScanned[i].stockPurchPrice)
				)
				.append(
					jQuery(
						'<td data-row="' + arrItemsScanned[i].itemSequence + '" hidden>'
					).text(arrItemsScanned[i].itemNumber)
				)
				.append(
					jQuery(
						'<td data-row="' + arrItemsScanned[i].itemSequence + '">'
					).text(arrItemsScanned[i].discrepancy)
				)
				.append(
					jQuery(
						'<td data-row="' + arrItemsScanned[i].itemSequence + '"  hidden>'
					).text(arrItemsScanned[i].reverseDiscrepacy)
				)
				.append(
					jQuery(
						'<td data-row="' + arrItemsScanned[i].itemSequence + '"  hidden>'
					).text(arrItemsScanned[i].parentItem)
				)
				.append(
					jQuery(
						'<td data-row="' + arrItemsScanned[i].itemSequence + '"  hidden>'
					).text(arrItemsScanned[i].compQty)
				)
				.append(
					`<td align="center"><button type="button" data-row="${arrItemsScanned[i].itemSequence}" class='btnDeleteRow'> Delete </button></td>`
				)
				.append(`</tr>`);
		}
	}
	/**
	 *
	 * @description This function executes the backend Suitelet (File:brdg_send_csv_file_sl.js) that generates the CSV file and email notification.
	 */

	return { pageInit };
});
