/**
 * @NApiVersion 2.1
 */

//@ts-ignore
define(["N/log", "Moment", "Numeral"], function (log, moment, numeral) {
	function getPoAckAsEDI(partnerValues, purchaseOrderObj, salesOrderObj) {
		var ediFile = `ISA^00^          ^00^          ^SenderQualifier^ISASenderId^ReceiverQualifier^ISAReceiverId^ISADate^Time^U^EdiVersion^ControlNumber^0^P^|~
GS^PR^GSSenderId^GSReceiverId^GSDate^Time^ControlNumber^X^EdiVersion0~
ST^855^0001~
BAK^AcknowledgementCode^AcknowledgementType^PoNumber^PoDate^^^^SoNumber^AckDate~
`;
		// N1^ST^CustomerName^91^CustomerAccountNumber~
		// N3^StreetAddress~
		// N4^City^State^Zip~`;
		var itemText = `PO1^LineNumber^Quantity^UOM^Rate^^VN^ItemName~
ACK^ItemStatus^^^068^CurrentScheduledShipDate~
`;
		var endOfFile = `CTT^TotalLineItems~
AMT^TT^TtlAmount~
SE^NumberOfSegments^0001~
GE^1^ControlNumber~
IEA^1^ControlNumber~`;

		formatDateTimeEdiVersion();
		formatSender();
		formatReceiver();
		formatBak();
		//formatNameAddress();
		formatItems();
		validateNoUndefinedOrInvalid();

		return ediFile;

		function formatFormatting() {
			//called at the end of formatItems
			var formattingInfo = partnerValues.formattingInfo;

			formattingInfo.forEach(function (x) {
				ediFile = ediFile.split(x.templateValue).join(x.partnerValue);
			});

			formatSegmantNumber();
		}

		function formatDateTimeEdiVersion() {
			var dateTimeInfo = partnerValues.isaGsInfo;

			dateTimeInfo.forEach(function (x) {
				ediFile = ediFile.split(x.name).join(x.value);
			});
		}

		function formatSender() {
			var senderInfo = partnerValues.senderInfo;

			senderInfo.forEach(function (x) {
				ediFile = ediFile.split(x.name).join(x.value);
			});
		}

		function formatReceiver() {
			var receiverInfo = partnerValues.receiverInfo;

			receiverInfo.forEach(function (x) {
				ediFile = ediFile.split(x.name).join(x.value);
			});
		}

		function formatBak() {
			var bakInfo = [
				{
					name: "AcknowledgementCode",
					value: purchaseOrderObj.acknowledgementCode,
				},
				{
					name: "AcknowledgementType",
					value: "AC", //DSSI always wants this as the Ack type
				},
				{
					name: "PoNumber",
					value: purchaseOrderObj.poNumber,
				},
				{
					name: "PoDate",
					value: moment(purchaseOrderObj.poDate.trim()).format("YYYYMMDD"),
				},
				{
					name: "SoNumber",
					value: salesOrderObj.documentInfo.documentNumber,
				},
				{
					name: "AckDate",
					value: moment().format("YYYYMMDD"),
				},
			];

			bakInfo.forEach(function (x) {
				ediFile = ediFile.replace(x.name, x.value);
			});
		}

		// function formatNameAddress() {
		// 	var nameAddressInfo = [
		// 		{
		// 			name: "NameQualifier",
		// 			value: "ST",
		// 		},
		// 		{
		// 			name: "CustomerName",
		// 			value: salesOrderObj.customer.name,
		// 		},
		// 		{
		// 			name: "CustomerAccountNumber",
		// 			value: salesOrderObj.customer.accountNumber,
		// 		},
		// 		{
		// 			name: "StreetAddress",
		// 			value: salesOrderObj.address.streetAddress,
		// 		},
		// 		{
		// 			name: "City",
		// 			value: salesOrderObj.address.city,
		// 		},
		// 		{
		// 			name: "State",
		// 			value: salesOrderObj.address.state,
		// 		},
		// 		{
		// 			name: "Zip",
		// 			value: salesOrderObj.address.zip,
		// 		},
		// 	];

		// 	nameAddressInfo.forEach(function (x) {
		// 		ediFile = ediFile.replace(x.name, x.value);
		// 	});
		// }

		function formatItems() {
			purchaseOrderObj.items.forEach(function (item) {
				var text = itemText;
				var itemInfo = [
					{
						name: "LineNumber",
						value: item.lineNumber,
					},
					{
						name: "Quantity",
						value: numeral(item.quantity).format("0,0"),
					},
					{
						name: "UOM",
						value: item.uom.toUpperCase(),
					},
					{
						name: "Rate",
						value: numeral(item.rate).format("0.00"),
					},
					{
						name: "ItemName",
						value: item.itemName,
					},
					{
						name: "ItemStatus",
						value: item.status,
					},
					{
						name: "QtyAcknowledged",
						value: item.quantityAcknowledged,
					},
					{
						name: "CurrentScheduledShipDate",
						value: moment(
							salesOrderObj.documentInfo.currentScheduledShipDate
						).format("YYYYMMDD"),
					},
				];

				itemInfo.forEach(function (x) {
					text = text.split(x.name).join(x.value);
				});

				ediFile += text;
			});
			formatEnd();
			formatFormatting();
			formatControlNumber();
		}

		function formatSegmantNumber() {
			var segments = ediFile.match(/[\n]/gm);
			if (!segments) {
				throw "Error matching segments, no segments returned.";
			}
			var numberOfSegments = segments.length - 3;
			ediFile = ediFile.replace(
				"NumberOfSegments",
				numberOfSegments.toString()
			);
		}

		function formatControlNumber() {
			//called at the end of formatItems
			ediFile = ediFile
				.split("ControlNumber")
				.join(salesOrderObj.documentInfo.controlNumber);
		}

		function formatEnd() {
			var endInfo = [
				{
					name: "TotalLineItems",
					value: purchaseOrderObj.items.length,
				},
				{
					name: "TtlAmount",
					value: purchaseOrderObj.totalAmount,
				},
			];

			endInfo.forEach(function (x) {
				endOfFile = endOfFile.replace(x.name, x.value);
			});
			ediFile += endOfFile;
		}

		function validateNoUndefinedOrInvalid() {
			if (
				ediFile.search("undefined") != -1 ||
				ediFile.search("Invalid") != -1
			) {
				//Search returned the result found instead of -1 when no match was found
				ediFile = ""; //Returning ediFile as an empty string so that the value is false
			}
		}
	}

	return {
		getPoAckAsEDI: getPoAckAsEDI,
	};
});
