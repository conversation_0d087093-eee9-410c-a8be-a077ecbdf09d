/**
 * @NApiVersion 2.x
 */

define([
    'N/record',
    'N/search',
    'Numeral',
    'Lodash'
],
    function (
        record,
        search,
        numeral,
        _,
    ) {
        function processItem(itemObj, fileName) {

            var processItem = (function () {

                function _processFail(logMessage, continueProcessing = false) {
                    processingLog.push(logMessage);
                    continueProcessingBool = continueProcessing;
                }

                function getItemInternalId() {
                    var searchObj = search.create({
                        type: "item",
                        filters:
                            [
                                ["othervendor", "anyof", "742"],
                                "AND",
                                ["isinactive", "is", "F"],
                                "AND",
                                ["name", "is", itemObj.name]
                            ]
                    });

                    var internalIdObj = searchObj.run().getRange({
                        start: 0,
                        end: 1000
                    })[0];
                    if (!internalIdObj) {
                        return false;
                    };

                    return internalIdObj.id;
                }

                function createItemRecord() {
                    return record.create({
                        type: record.Type.INVENTORY_ITEM
                    });
                }

                function setItemValues() {
                    itemRecord.setValue({
                        fieldId: 'itemid',
                        value: itemObj.name
                    });

                    itemRecord.setValue({
                        fieldId: 'displayname',
                        value: itemObj.description
                    });

                    itemRecord.setValue({
                        fieldId: 'vendorname',
                        value: itemObj.description
                    });

                    itemRecord.setText({
                        fieldId: 'purchaseunit',
                        value: itemObj.uom //FIGURE OUT HOW TO DO THIS
                    });

                    itemRecord.setText({
                        fieldId: 'saleunit',
                        value: itemObj.uom  //FIGURE OUT HOW TO DO THIS
                    });

                    itemRecord.setValue({
                        fieldId: 'class',
                        value: 32 //FIGURE OUT HOW TO DO THIS
                    });

                    itemRecord.setValue({
                        fieldId: 'subsidiary',
                        value: 1 //FIGURE OUT HOW TO DO THIS
                    });

                    itemRecord.setValue({
                        fieldId: 'pricinggroup',
                        value: 10 //FIGURE OUT HOW TO DO THIS
                    });

                    itemRecord.setText({
                        fieldId: 'taxschedule',
                        value: 'Tax Schedule 1' //FIGURE OUT HOW TO DO THIS
                    });

                    //itemRecord.setValue({
                    //    fieldId: 'incomeaccount',
                    //    value: '1' //ACTUALLY SET
                    //});

                    //itemRecord.setValue({
                    //    fieldId: 'expenseaccount',
                    //    value: '2' //ACTUALLY SET
                    //});

                }

                function saveBill() {
                    //try {
                    return itemRecord.save({
                        enableSourcing: true,
                        ignoreMandatoryFields: true
                    });
                    //} catch (error) {
                    //    _processFail('New Item ' + itemObj.name + ' not saved. Error: ' + error);
                    //}            
                }


                return {
                    getItemInternalId,
                    createItemRecord,
                    setItemValues,
                    saveBill
                }
            })();

            var processingLog = [];
            var continueProcessingBool;
            var itemInternalId = processItem.getItemInternalId();
            if (!itemInternalId) { //Item doesn't exist in NetSuite => add item
                var itemRecord = processItem.createItemRecord();
                processItem.setItemValues();
                processItem.saveBill();
            } else {
                //processInvEndLib.processEnd(processingLog, itemObj, fileName);
            }
        }
        return {
            processItem: processItem
        };
    });