//@ts-ignore
define(["N/log", "GetShippingCogsLib", "GetSqlResultsObjLib"], function (
	log,
	getShippingCogsLib,
	getSqlResultsObjLib
) {
	function getShippingValuesObj(invoice, invoiceInternalId, recordType) {
		let shippingCOGS = 0;
		let shippingRevenue = 0;
		let shipNotesArr = [];

		try {
			const { shippingType } = getShippingCogsLib.getShippingMethodObj(invoice);

			switch (true) {
				case shippingType == "upsStandard":
				case shippingType == "upsNoMarkup":
				case shippingType == "dropShip":
				case shippingType == "freight":
				case getShippingCogsLib.checkIfDropShipWorkAround(invoiceInternalId):
					/*Since we don't have the actual shipping cost when the invoice is created, 
                    get the consolidated amounts via sql query and set on last invoice for an order, 
                    all previous invoice values are set to 0. */
					const freeFreight = invoice.getValue("custbody_spl_free_freight");

					if (recordType != "CREDIT_MEMO") {
						const consolidatedShipInfoObj =
							getShippingCogsLib.getConsolidatedShippingInfoObj(
								invoiceInternalId,
								shippingType,
								freeFreight
							);

						if (
							consolidatedShipInfoObj &&
							consolidatedShipInfoObj.errorLog.length > 0
						) {
							throw {
								name: "GET_GROSS_PROFIT_COST_ERROR",
								message: consolidatedShipInfoObj.errorLog[0],
							};
						}

						if (consolidatedShipInfoObj.shipNotesArr.length > 0) {
							shipNotesArr = [
								...shipNotesArr,
								...consolidatedShipInfoObj.shipNotesArr,
							];
						}

						//Consolidated amount if #last inv, else 0
						shippingRevenue = consolidatedShipInfoObj.consolidatedRevenue;
						shippingCOGS = consolidatedShipInfoObj.consolidatedCOGS;
						invoice.getValue("subtotal");
					}
					break;
				case shippingType == "customerPickUp":
					break;
				default:
					shipNotesArr.push(4);
				//"No valid ship type was determined -> shipping cost not set",
			}

			if (!shippingRevenue) {
				//Revenue not set as consolidated value -> use the ship cost value on the transaction
				shippingRevenue = invoice.getValue("shippingcost") ?? 0;
			}

			if (shipNotesArr.length > 0) {
				let shipAmountsObj = getShippingCogsLib.setShipAmountsForEdgeCases(
					shipNotesArr,
					shippingCOGS,
					shippingRevenue
				);

				shippingCOGS = shipAmountsObj.shippingCOGS;
				shippingRevenue = shipAmountsObj.shippingRevenue;
			}
		} catch (e) {
			throw {
				name: "GET_GROSS_PROFIT_COST_ERROR",
				message: `Error getting shiping values: ${JSON.stringify(e)}`,
			};
		}
		return { shippingCOGS, shippingRevenue, shipNotesArr };
	}

	function getPurchasingSoftwareFee(invoiceInternalId, itemsSubtotal) {
		try {
			const resultsObj =
				getSqlResultsObjLib.getPurchasingSoftwareFeeForInvoice(
					invoiceInternalId
				);

			if (!resultsObj) {
				//No purchasing software fees for this invoice.
				return 0;
			}

			//A result line was returned but the value querying for is null.
			if (resultsObj.values[0] == null) {
				throw {
					name: "GET_GROSS_PROFIT_COST_ERROR",
					message: `No contracted rate returned for this purchasing software.`,
				};
			}

			const purchasingSoftwareFeePercentage = resultsObj.values[0];
			return itemsSubtotal * purchasingSoftwareFeePercentage ?? 0;
		} catch (e) {
			throw {
				name: "GET_GROSS_PROFIT_COST_ERROR",
				message: `Error getting purchasing software fee: ${e}`,
			};
		}
	}

	function getGPOReferralFee(invoiceInternalId, itemsSubtotal) {
		try {
			const resultsObj =
				getSqlResultsObjLib.getGPOReferralFeeResForInvoice(invoiceInternalId);

			if (!resultsObj) {
				//No GPO referral fees for this invoice.
				return 0;
			}

			//A result line was returned but the value returned is incorrect.
			if (typeof resultsObj.values[0] != "number") {
				throw {
					name: "GET_GROSS_PROFIT_COST_ERROR",
					message: "Wrong result type returned for GPO referral fee",
				};
			}

			const gpoReferralFeePercentage = resultsObj.values[0];
			return itemsSubtotal * gpoReferralFeePercentage;
		} catch (e) {
			throw {
				name: "GET_GROSS_PROFIT_COST_ERROR",
				message: `Error getting GPO referral fee: ${e}`,
			};
		}
	}

	return {
		getShippingValuesObj,
		getPurchasingSoftwareFee,
		getGPOReferralFee,
	};
});
