/**
 * @NApiVersion 2.1
 * @NScriptType MapReduceScript
 * @NAmdConfig /SuiteScripts/config.json
 * @param {import ("N/types")}
 * <AUTHOR>
 * @description MR to update pricing based on price tier
 * @module spl_update_pricing_tier_mr
 */

define([
  "require",
  "MapReduceSummaryStageHandling",
  "N/log",
  "N/runtime",
  "N/record",
], function (require, MapReduceSummaryStageHandling) {
  const log = require("N/log");
  const runtime = require("N/runtime");
  const record = require("N/record");

  function getInputData() {
    try {
      const currentScript = runtime.getCurrentScript();

      const tierLevelId = currentScript.getParameter({
        name: "custscript_tier_level_id",
      });

      const sqlQuery = `select id from customer where custentity_spl_price_tier = ?`;
     
      return {
        type: "suiteql",
        query: sqlQuery,
        params: [tierLevelId],
      };
    } catch (error) {
      log.error("Error getting customers in get input data!");
    }
  }

  function reduce(context) {
    let result = JSON.parse(context.values);
    const currentScript = runtime.getCurrentScript();
    const productCategoryId = currentScript.getParameter({
      name: "custscript_product_category_id",
    });
    const priceLevelId = currentScript.getParameter({
      name: "custscript_price_level_id",
    });
    let customer = result.values;
    const customerId = customer[0];
    let customerRecord = record.load({
      type: record.Type.CUSTOMER,
      id: customerId,
      isDynamic: true,
    });
    const customerName = customerRecord.getText("companyname");
    try {
      var lineNumber = customerRecord.findSublistLineWithValue({
        sublistId: "grouppricing",
        fieldId: "group",
        value: productCategoryId,
      });
      if (lineNumber >= 0) {
        //If there is an existing line number then update it, otherwise add a new line
        customerRecord.selectLine({
          sublistId: "grouppricing",
          line: lineNumber,
        });

        customerRecord.setCurrentSublistValue({
          sublistId: "grouppricing",
          fieldId: "level",
          value: priceLevelId,
          ignoreFieldChange: true,
        });
        customerRecord.commitLine({ sublistId: "grouppricing" });
      } else {
        customerRecord.selectNewLine({ sublistId: "grouppricing" });
        customerRecord.setCurrentSublistValue({
          sublistId: "grouppricing",
          fieldId: "group",
          value: productCategoryId,
        });
        customerRecord.setCurrentSublistValue({
          sublistId: "grouppricing",
          fieldId: "level",
          value: priceLevelId,
        });
        customerRecord.commitLine({ sublistId: "grouppricing" });
      }

      customerRecord.save({
        ignoreMandatoryFields: true,
        enableSourcing: false,
      });
    } catch (e) {
      log.error(
        `error with adding/updating this price level ${
          priceLevelId ?? ""
        } for this product category ${productCategoryId ?? ""}!`,
        e
      );
      throw error.create({
        name: "Error updating price level!",
        message: `${priceLevelId ?? ""}, ${productCategoryId ?? ""}, ${e}`,
      });
    }
    context.write({
      key: "Customer Updated",
      value: customerName,
    });
  }
  function summarize(context) {
    const stageHandling = new MapReduceSummaryStageHandling(context);
    stageHandling.printRecordsProcessed();
    stageHandling.printErrors();
  }
  return {
    getInputData,
    reduce,
    summarize,
  };
});
