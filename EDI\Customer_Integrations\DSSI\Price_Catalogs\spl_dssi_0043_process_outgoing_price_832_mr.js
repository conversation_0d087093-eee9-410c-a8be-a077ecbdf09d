/**
 * @NApiVersion 2.1
 * @NScriptType MapReduceScript
 * @NAmdConfig /SuiteScripts/config.json
 */

//@ts-ignore
define([
	"require",
	"N/log",
	"../../../../EDI/Libraries/Process_EDI_File/spl_process_outgoing_832_price_lib",
], function (require, log) {
	/**@type {import ('../../../../EDI/Libraries/Process_EDI_File/spl_process_outgoing_832_price_lib')} */
	const process832PriceLib = require("../../../../EDI/Libraries/Process_EDI_File/spl_process_outgoing_832_price_lib");

	const customerObj = {
		accountNumber: "0043",
		customerName: "Crown",
		internalId: 452,
	};

	const dataObj = {
		prodGuidBool: true,
		prodDirectoryBool: true,
		decodeContent: true,
		prodGUID: "a750b612a8e845668ce6216c0f5d2037",
		sandboxGUID: "",
		prodDirectory: "/users/DSSI/OUT/832",
		testDirectory: "/users/DSSI/Test",
		transactionType: "Price Catalog",
		purchasingSoftware: "DSSI",
		customerName: customerObj.customerName,
	};

	function getInputData(context) {
		return process832PriceLib.getPriceList(customerObj.internalId);
	}

	function map(context) {
		try {
			const parsedItemRow = JSON.parse(context.value);

			const itemObj = process832PriceLib.getItemObj(
				parsedItemRow,
				customerObj.accountNumber
			);

			const itemString = process832PriceLib.getItemAsString(itemObj);

			context.write(customerObj.accountNumber, itemString);
		} catch (e) {
			log.error("Error Mapping Script", e);
		}
	}

	function summarize(summary) {
		try {
			process832PriceLib.processEnd(
				summary,
				customerObj.accountNumber,
				dataObj,
				customerObj.customerName
			);
		} catch (e) {
			log.error("Error Processing Summarize", e);
		}
	}

	return {
		getInputData: getInputData,
		map: map,
		summarize: summarize,
	};
});
