/**
 * @description Helper lib to validates the LPP and Vineyard Executive price levels
 *
 * </br><b>Implemented By:</b> BRDG Validate LPP and Sales Price
 *
 * @NApiVersion 2.1
 * @NAmdConfig /SuiteScripts/config.json
 *
 * <AUTHOR>
 * @module brdg_update_item_lib
 */

define(["require", "exports", "N/log", "N/query"], function (
  /** @type {any} */ require,
  /** @type {any} */ exports
) {
  const log = require("N/log");
  const query = require("N/query");

  /**
   * @typedef PriceObject
   * @property {string | number | boolean | null} purchasePricePerUnit Purchase price
   * @property {number} locationId Location internal id
   *
   * @typedef LastPurchasePriceObj
   * @property {number} lastPurchasePriceGgs GGS
   * @property {number} lastPurchasePriceLol LOL
   * @property {number} lastPurchasePriceGgn GGN
   * @property {number} lastPurchasePriceVyb VYB
   * @property {number} lastPurchasePriceExp EXP
   * @property {number} lastPurchasePriceVwg VWG
   */

  /**
   * Query for inventory item's last purchase price
   *
   * @param {number} itemId Item internal ID
   * @returns {LastPurchasePriceObj} Last purchase price object
   */
  function _getLastPurchasePriceInventoryItem(itemId) {
    const unitConversionRate = `
			SELECT
    			purchaseunit.conversionrate / saleunit.conversionrate
			FROM
    			item
    		INNER JOIN unitstypeuom purchaseunit ON purchaseunit.internalid = item.purchaseunit
    		INNER JOIN unitstypeuom saleunit ON saleunit.internalid = item.saleunit
			WHERE
    			id = ${itemId}`;

    const sqlQuery = `
			SELECT
				ROUND(lastpurchasepricemli / (${unitConversionRate}),2) AS 'Purchase Price per Unit', --pulls from the item receipt
				al.location
			FROM
				aggregateitemlocation al
				JOIN item i ON i.id = al.item
			WHERE
				al.location IN (24, 27, 29, 37, 31, 42)
				AND al.item = '${itemId}'
			ORDER BY
				al.location
		`;

    //Make sure to order by location to get the right results
    //24-GGS, 27-LOL, 29-GGN, 37-VYB, 31-EXP, 42-VWG

    const resultIterator = query.runSuiteQL({
      query: sqlQuery,
    });

    const /** @type {PriceObject[]} */ parsedResultsArr = [];
    resultIterator.results.forEach((resultsObj) => {
      parsedResultsArr.push({
        purchasePricePerUnit: resultsObj.values[0],
        locationId: Number(resultsObj.values[1]),
      });
    });
    let lastPurchasePriceGgs = parsedResultsArr
      .filter((item) => item.locationId === 24)
      .map((item) => Number(item.purchasePricePerUnit))[0];
    let lastPurchasePriceLol = parsedResultsArr
      .filter((item) => item.locationId === 27)
      .map((item) => Number(item.purchasePricePerUnit))[0];
    let lastPurchasePriceGgn = parsedResultsArr
      .filter((item) => item.locationId === 29)
      .map((item) => Number(item.purchasePricePerUnit))[0];
    let lastPurchasePriceVyb = parsedResultsArr
      .filter((item) => item.locationId === 37)
      .map((item) => Number(item.purchasePricePerUnit))[0];
    let lastPurchasePriceExp = parsedResultsArr
      .filter((item) => item.locationId === 31)
      .map((item) => Number(item.purchasePricePerUnit))[0];
    let lastPurchasePriceVwg = parsedResultsArr
      .filter((item) => item.locationId === 42)
      .map((item) => Number(item.purchasePricePerUnit))[0];
    return {
      lastPurchasePriceGgs,
      lastPurchasePriceLol,
      lastPurchasePriceGgn,
      lastPurchasePriceVyb,
      lastPurchasePriceExp,
      lastPurchasePriceVwg,
    };
  }

  function _getLastPurchasePriceComponentItem(itemObj){
    const unitConversionRate = `
			SELECT
    			purchaseunit.conversionrate
			FROM
    			item
    		INNER JOIN unitstypeuom purchaseunit ON purchaseunit.internalid = item.purchaseunit
			WHERE
    			id = ${itemObj.itemId}`;

    const sqlQuery = `
			SELECT
				ROUND(lastpurchasepricemli / (${unitConversionRate}),2) * ${itemObj.itemQuantity} AS 'Purchase Price per Unit',
				al.location
			FROM
				aggregateitemlocation al
				JOIN item i ON i.id = al.item
			WHERE
				al.location IN (24, 27, 29, 37, 31, 42)
				AND al.item = '${itemObj.itemId}'
			ORDER BY
				al.location
		`;

    //Make sure to order by location to get the right results
    //24-GGS, 27-LOL, 29-GGN, 37-VYB, 31-EXP, 42-VWG

    const resultIterator = query.runSuiteQL({
      query: sqlQuery,
    });

    const /** @type {PriceObject[]} */ parsedResultsArr = [];
    resultIterator.results.forEach((resultsObj) => {
      parsedResultsArr.push({
        purchasePricePerUnit: resultsObj.values[0],
        locationId: Number(resultsObj.values[1]),
      });
    });
    let lastPurchasePriceGgs = parsedResultsArr
      .filter((item) => item.locationId === 24)
      .map((item) => Number(item.purchasePricePerUnit))[0];
    let lastPurchasePriceLol = parsedResultsArr
      .filter((item) => item.locationId === 27)
      .map((item) => Number(item.purchasePricePerUnit))[0];
    let lastPurchasePriceGgn = parsedResultsArr
      .filter((item) => item.locationId === 29)
      .map((item) => Number(item.purchasePricePerUnit))[0];
    let lastPurchasePriceVyb = parsedResultsArr
      .filter((item) => item.locationId === 37)
      .map((item) => Number(item.purchasePricePerUnit))[0];
    let lastPurchasePriceExp = parsedResultsArr
      .filter((item) => item.locationId === 31)
      .map((item) => Number(item.purchasePricePerUnit))[0];
    let lastPurchasePriceVwg = parsedResultsArr
      .filter((item) => item.locationId === 42)
      .map((item) => Number(item.purchasePricePerUnit))[0];
    return {
      lastPurchasePriceGgs,
      lastPurchasePriceLol,
      lastPurchasePriceGgn,
      lastPurchasePriceVyb,
      lastPurchasePriceExp,
      lastPurchasePriceVwg,
    };

  }

  /**
   * Query for kit item's last purchase price
   *
   * @param {number} itemId Item internal ID
   * @returns {LastPurchasePriceObj} Last purchase price object
   */
  function _getLastPurchasePriceKitItem(itemId) {
    const itemIdsArr = _getKitItemComponents(itemId);
    var lastPurchasePriceGgs = 0;
    var lastPurchasePriceLol = 0;
    var lastPurchasePriceGgn = 0;
    var lastPurchasePriceVyb = 0;
    var lastPurchasePriceExp = 0;
    var lastPurchasePriceVwg = 0;

    itemIdsArr.forEach((itemObj) => {
      const lppObjItem = _getLastPurchasePriceComponentItem(itemObj);
      lastPurchasePriceGgs = lppObjItem.lastPurchasePriceGgs
        ? lastPurchasePriceGgs + lppObjItem.lastPurchasePriceGgs
        : lastPurchasePriceGgs;
      lastPurchasePriceLol = lppObjItem.lastPurchasePriceLol
        ? lastPurchasePriceLol + lppObjItem.lastPurchasePriceLol
        : lastPurchasePriceLol;
      lastPurchasePriceGgn = lppObjItem.lastPurchasePriceGgn
        ? lastPurchasePriceGgn + lppObjItem.lastPurchasePriceGgn
        : lastPurchasePriceGgn;
      lastPurchasePriceVyb = lppObjItem.lastPurchasePriceVyb
        ? lastPurchasePriceVyb + lppObjItem.lastPurchasePriceVyb
        : lastPurchasePriceVyb;
      lastPurchasePriceExp = lppObjItem.lastPurchasePriceExp
        ? lastPurchasePriceExp + lppObjItem.lastPurchasePriceExp
        : lastPurchasePriceExp;
      lastPurchasePriceVwg = lppObjItem.lastPurchasePriceVwg
        ? lastPurchasePriceVwg + lppObjItem.lastPurchasePriceVwg
        : lastPurchasePriceVwg;
    });
    return {
      lastPurchasePriceGgs,
      lastPurchasePriceLol,
      lastPurchasePriceGgn,
      lastPurchasePriceVyb,
      lastPurchasePriceExp,
      lastPurchasePriceVwg,
    };
  }

  /**
   * Retrieve the last purchase price from Kit or Inventory Item
   *
   * @param {number} itemId Item internal ID
   * @param {string} itemType Item type
   * @returns {LastPurchasePriceObj} Last purchase price object
   */
  exports.getLastPurchasePrice = function (itemId, itemType) {
    const lppObj = ["KIT", "Kit", "kititem", "KIT_ITEM"].includes(itemType)
      ? _getLastPurchasePriceKitItem(itemId)
      : _getLastPurchasePriceInventoryItem(itemId);
    return lppObj;
  };

  /**
   * Set value for Last Purchase Price fields
   *
   * @param {import("N/record").Record | import("N/record").ClientCurrentRecord} itemRecord NetSuite item record
   * @param {LastPurchasePriceObj} lppObj Object containing last purchase price per location
   * @returns {void}
   */
  exports.setLastPurchasePricesFields = function (itemRecord, lppObj) {
    itemRecord.setValue(
      "custitem_vend_last_price_vnggs",
      lppObj.lastPurchasePriceGgs > 0 ? lppObj.lastPurchasePriceGgs : ""
    );
    itemRecord.setValue(
      "custitem_vend_last_price_lol",
      lppObj.lastPurchasePriceLol > 0 ? lppObj.lastPurchasePriceLol : ""
    );
    itemRecord.setValue(
      "custitem_vend_last_price_vnggn",
      lppObj.lastPurchasePriceGgn > 0 ? lppObj.lastPurchasePriceGgn : ""
    );
    itemRecord.setValue(
      "custitem_vend_last_price_vyb",
      lppObj.lastPurchasePriceVyb > 0 ? lppObj.lastPurchasePriceVyb : ""
    );
    itemRecord.setValue(
      "custitem_vend_last_price_vexp",
      lppObj.lastPurchasePriceExp > 0 ? lppObj.lastPurchasePriceExp : ""
    );
    itemRecord.setValue(
      "custitem_vend_last_price_vnyrd_wstgt",
      lppObj.lastPurchasePriceVwg > 0 ? lppObj.lastPurchasePriceVwg : ""
    );
  };

  /**
   * Validating that the sales price is not less then the last purchase price
   * Only used for the CS script, otherwise there's a saved search they run
   *
   * @param {{[key:string]:string | number | boolean | null}} lppObj Object containing last purchase price per location
   * @param {import("N/record").Record | import("N/record").ClientCurrentRecord} itemRecord NetSuite item record
   * @returns {string} Validation result
   */
  exports.validateSalesPrice = function (lppObj, itemRecord) {
    const currentLolSalesPrice = itemRecord.getValue(
      "custitem_vend_last_price_lol"
    );
    const currentGgnSalesPrice = itemRecord.getValue(
      "custitem_vend_last_price_vnggn"
    );
    const currentGgsSalesPrice = itemRecord.getValue(
      "custitem_vend_last_price_vnggs"
    );
    const currentVybSalesPrice = itemRecord.getValue(
      "custitem_vend_last_price_vyb"
    );
    const currentExpSalesPrice = itemRecord.getValue(
      "custitem_vend_last_price_vexp"
    );
    const currentVwgSalesPrice = itemRecord.getValue(
      "custitem_vend_last_price_vnyrd_wstgt"
    );

    var message = [];

    if (
      currentGgsSalesPrice &&
      lppObj.lastPurchasePriceGgs &&
      currentGgsSalesPrice < lppObj.lastPurchasePriceGgs
    ) {
      message.push("GGS");
    }
    if (
      currentLolSalesPrice &&
      lppObj.lastPurchasePriceLol &&
      currentLolSalesPrice < lppObj.lastPurchasePriceLol
    ) {
      message.push("LOL");
    }
    if (
      currentGgnSalesPrice &&
      lppObj.lastPurchasePriceGgn &&
      currentGgnSalesPrice < lppObj.lastPurchasePriceGgn
    ) {
      message.push("GGN");
    }
    if (
      currentVybSalesPrice &&
      lppObj.lastPurchasePriceVyb &&
      currentVybSalesPrice < lppObj.lastPurchasePriceVyb
    ) {
      message.push("VYB");
    }
    if (
      currentExpSalesPrice &&
      lppObj.lastPurchasePriceExp &&
      currentExpSalesPrice < lppObj.lastPurchasePriceExp
    ) {
      message.push("EXP");
    }
    if (
      currentVwgSalesPrice &&
      lppObj.lastPurchasePriceVwg &&
      currentVwgSalesPrice < lppObj.lastPurchasePriceVwg
    ) {
      message.push("WG");
    }

    return message.toString();
  };

  exports.checkForQuantityPricing = function (itemRecord) {
    let hasSecondQuantity = false;
    //Loop through price list and see if any price level has quantity pricing
    let priceLineCount = itemRecord.getLineCount("price");
    for (let i = 0; i < priceLineCount; i++) {
      if (
        itemRecord.getSublistValue({
          sublistId: "price",
          fieldId: "price_2_",
          line: i,
        })
      ) {
        hasSecondQuantity = true;
        break;
      }
    }
    return hasSecondQuantity;
  };

  /**
   *
   * @param {LastPurchasePriceObj} lppObj Object containing last purchase price per location
   * @param {import("N/record").Record | import("N/record").ClientCurrentRecord} itemRecord NetSuite item record
   * @returns {void}
   */
  exports.addExecutivePricingPerStore = function (
    itemRecord,
    lppObj,
    setSecondQuantity
  ) {
    const pricingObjArr = [
      {
        // madisonExecutiveLine
        priceLevelIndex: itemRecord.findSublistLineWithValue({
          sublistId: "price",
          fieldId: "pricelevel",
          value: 35,
        }),
        price:
          lppObj.lastPurchasePriceGgn > 0
            ? (lppObj.lastPurchasePriceGgn * 1.05).toFixed(2)
            : "",
      },
      {
        // ggsExecutiveLine
        priceLevelIndex: itemRecord.findSublistLineWithValue({
          sublistId: "price",
          fieldId: "pricelevel",
          value: 37, //SB: 36
        }),
        price:
          lppObj.lastPurchasePriceGgs > 0
            ? (lppObj.lastPurchasePriceGgs * 1.05).toFixed(2)
            : "",
      },
      {
        // westgateExecutiveLine
        priceLevelIndex: itemRecord.findSublistLineWithValue({
          sublistId: "price",
          fieldId: "pricelevel",
          value: 36, //SB:37
        }),
        price:
          lppObj.lastPurchasePriceVwg > 0
            ? (lppObj.lastPurchasePriceVwg * 1.05).toFixed(2)
            : "",
      },
      {
        // bergenfieldExecutiveLine
        priceLevelIndex: itemRecord.findSublistLineWithValue({
          sublistId: "price",
          fieldId: "pricelevel",
          value: 38,
        }),
        price:
          lppObj.lastPurchasePriceVyb > 0
            ? (lppObj.lastPurchasePriceVyb * 1.05).toFixed(2)
            : "",
      },
      {
        // expressExecutiveLine
        priceLevelIndex: itemRecord.findSublistLineWithValue({
          sublistId: "price",
          fieldId: "pricelevel",
          value: 39,
        }),
        price:
          lppObj.lastPurchasePriceExp > 0
            ? (lppObj.lastPurchasePriceExp * 1.05).toFixed(2)
            : "",
      },
      {
        // lolExecutiveLine
        priceLevelIndex: itemRecord.findSublistLineWithValue({
          sublistId: "price",
          fieldId: "pricelevel",
          value: 40,
        }),
        price:
          lppObj.lastPurchasePriceLol > 0
            ? (lppObj.lastPurchasePriceLol * 1.05).toFixed(2)
            : "",
      },
    ];
    try {
      pricingObjArr.forEach((executivePriceObj) => {
        itemRecord.selectLine({
          sublistId: "price",
          line: executivePriceObj.priceLevelIndex,
        });
        itemRecord.setCurrentSublistValue({
          sublistId: "price",
          fieldId: "price_1_",
          value: executivePriceObj.price,
        });
        if (setSecondQuantity) {
          itemRecord.setCurrentSublistValue({
            sublistId: "price",
            fieldId: "price_2_",
            value: executivePriceObj.price,
          });
        }
        itemRecord.commitLine({
          sublistId: "price",
        });
      });
    } catch (e) {
      log.error("Error updating price level!", e);
    }
  };

  /**
   * Add cost price to the Last Purchase Price object
   *
   * @param {LastPurchasePriceObj} lppObj Object containing last purchase price per location
   * @param {import("N/record").Record | import("N/record").ClientCurrentRecord} itemRecord NetSuite item record
   * @returns {void}
   */
  exports.addCostPricePerStore = function (
    itemRecord,
    lppObj,
    setSecondQuantity
  ) {
    const pricingObjArr = [
      {
        // Vineyard Madison Cost Price Line
        priceLevelIndex: itemRecord.findSublistLineWithValue({
          sublistId: "price",
          fieldId: "pricelevel",
          value: 41,
        }),
        price:
          lppObj.lastPurchasePriceGgn > 0
            ? lppObj.lastPurchasePriceGgn.toFixed(2)
            : "",
      },
      {
        // Vineyard South Cost Price
        priceLevelIndex: itemRecord.findSublistLineWithValue({
          sublistId: "price",
          fieldId: "pricelevel",
          value: 42,
        }),
        price:
          lppObj.lastPurchasePriceGgs > 0
            ? lppObj.lastPurchasePriceGgs.toFixed(2)
            : "",
      },
      {
        // Vineyard Westgate Cost Price
        priceLevelIndex: itemRecord.findSublistLineWithValue({
          sublistId: "price",
          fieldId: "pricelevel",
          value: 43,
        }),
        price:
          lppObj.lastPurchasePriceVwg > 0
            ? lppObj.lastPurchasePriceVwg.toFixed(2)
            : "",
      },
      {
        // Vineyard Bergenfield Cost Price
        priceLevelIndex: itemRecord.findSublistLineWithValue({
          sublistId: "price",
          fieldId: "pricelevel",
          value: 44,
        }),
        price:
          lppObj.lastPurchasePriceVyb > 0
            ? lppObj.lastPurchasePriceVyb.toFixed(2)
            : "",
      },
      {
        // Vineyard Express Cost Price
        priceLevelIndex: itemRecord.findSublistLineWithValue({
          sublistId: "price",
          fieldId: "pricelevel",
          value: 45,
        }),
        price:
          lppObj.lastPurchasePriceExp > 0
            ? lppObj.lastPurchasePriceExp.toFixed(2)
            : "",
      },
      {
        // LOL Cost Price
        priceLevelIndex: itemRecord.findSublistLineWithValue({
          sublistId: "price",
          fieldId: "pricelevel",
          value: 46,
        }),
        price:
          lppObj.lastPurchasePriceLol > 0
            ? lppObj.lastPurchasePriceLol.toFixed(2)
            : "",
      },
    ];
    try {
      pricingObjArr.forEach(function (costPriceObj) {
        itemRecord.selectLine({
          sublistId: "price",
          line: costPriceObj.priceLevelIndex,
        });
        itemRecord.setCurrentSublistValue({
          sublistId: "price",
          fieldId: "price_1_",
          value: costPriceObj.price,
        });
        if (setSecondQuantity) {
          itemRecord.setCurrentSublistValue({
            sublistId: "price",
            fieldId: "price_2_",
            value: costPriceObj.price,
          });
        }
        itemRecord.commitLine({
          sublistId: "price",
        });
      });
    } catch (e) {
      log.error("Error updating cost price level!", e);
    }
  };

  /**
   * Retrieve item internal ids of kit item components
   *
   * @param {number} itemId
   * @returns {number[]} array of items part of the kit
   */
  function _getKitItemComponents(itemId) {
    const sqlQuery = `
			SELECT i.id internalid, k.quantity
			FROM KitItemMember k 
			INNER JOIN item i on  k.item = i.id
			WHERE parentitem = ?
		`;
    var sqlResults = query
      .runSuiteQL({
        query: sqlQuery,
        params: [itemId],
      })
      .asMappedResults();

    const /** @type {number[]} */ itemIdsArr = [];

    sqlResults.forEach((itemObj) =>
      itemIdsArr.push({
        itemId: Number(itemObj.internalid),
        itemQuantity: Number(itemObj.quantity)})
    );

    return itemIdsArr;
  }
});
