var CONFIG = {
	authorizedUsers: ["<EMAIL>", "<EMAIL>"], //lowercase
	googleClientSecret: "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",
	googleClientId:
		"404128131177-5fjqtmtpganl3l6l2cdigq3uq3o7ajn6.apps.googleusercontent.com",
	scriptUrl:
		"https://5802576.extforms.netsuite.com/app/site/hosting/scriptlet.nl?script=4268&deploy=1&compid=5802576&ns-at=AAEJ7tMQCcigWzxgsKwNCK293u20mDV8TGr2uFlcxhArqDlqLfI",
	sessionTtl: 86400, //maximum session time in seconds, 300 minimum
};

/**
 * @NApiVersion 2.1
 * @NScriptType Suitelet
 * @NModuleScope Public
 */
define([
	"N/ui/serverWidget",
	"N/cache",
	"N/redirect",
	"N/https",
	"N/crypto/random",
], runSuitelet);

function runSuitelet(ui, cache, redirect, https, random) {
	function execute(context) {
		if (!validateAccessControlWithGoogle(context)) {
			return;
		}

		//##### AUTHORIZED VALID SESSION AFTER HERE ######

		if (context.request.method === "GET") {
			context.response.write(`
			<h1>GET. You are logged in.</h1>
			<a href="javascript:void%20function(){location.href+=%22%26signout=T%22}();">Sign Out</a>
			`);
		} else {
			context.response.write(`POST. You are logged in.`);
		}

		return;

		//###### HELPERS ######
		/**
		 * Validates access control using Google authentication and manages user sessions. Developed by Adolfo Garza at ursuscode.com
		 * @param {Object} context - The context of the request containing parameters and session info.
		 * @returns {boolean} - Returns true if the rest of the program should continue to run, false otherwise.
		 */
		function validateAccessControlWithGoogle(context) {
			const isSignOut = context.request.parameters.signout == "T";
			if (isSignOut) {
				log.debug("validateAccessControlWithGoogle|Signing out...");
				const sessionIdFromCookie = extractCookieValue(
					context.request.headers["Cookie"],
					"session_id"
				);
				clearServerSideSession(sessionIdFromCookie);
				deleteSessionCookie(context);
				redirectToHome(context);
				return false;
			}

			const validSession = isValidSession(context);
			log.debug("execute|validSession", validSession);

			if (!validSession) {
				const authCode = context.request.parameters.code;
				if (authCode) {
					handleAuthCodeFlow(context, authCode);
					return false;
				}
				redirectToGoogleOAuthServer(context);
				return false;
			}

			return true;

			/**
			 * Checks if the current session is valid based on session ID from cookies.
			 * @param {Object} context - The context object containing the request information.
			 * @returns {boolean} - Returns true if the session is valid, false otherwise.
			 */
			function isValidSession(context) {
				const clientIp = context.request.clientIpAddress;
				const sessionIdFromCookie = extractCookieValue(
					context.request.headers["Cookie"],
					"session_id"
				);
				return validateSession(sessionIdFromCookie);

				/**
				 * Validates a session ID by checking the stored session data in the cache.
				 * @param {string} sessionId - The session ID to validate.
				 * @returns {boolean} - Returns true if the session ID is valid and the user is authorized, false otherwise.
				 */
				function validateSession(sessionId) {
					log.debug("validateSession|sessionId", sessionId);
					if (!sessionId) {
						return false;
					}
					const cacheName = sessionId;
					const sessionCache = cache.getCache({
						name: cacheName,
						scope: cache.Scope.PRIVATE,
					});
					const sessionData = JSON.parse(
						sessionCache.get({ key: "sessionData" }) || "{}"
					);
					log.debug("validateSession|sessionData", JSON.stringify(sessionData));
					return isAuthorizedUser(sessionData.emailAddress);
				}
			}

			/**
			 * Clears the server-side session from the cache using the session ID extracted from the cookie.
			 * @param {Object} context - The context object from the Suitelet.
			 */
			function clearServerSideSession(sessionId) {
				if (sessionId) {
					log.debug("clearServerSideSession|sessionId", sessionId);
					const cacheName = sessionId;
					const sessionCache = cache.getCache({
						name: cacheName,
						scope: cache.Scope.PRIVATE,
					});
					sessionCache.remove({ key: "sessionData" });
				}
			}

			/**
			 * Extracts a specific cookie value from the cookie string.
			 * @param {string} cookieString - The string containing all cookies.
			 * @param {string} key - The key of the cookie to extract.
			 * @returns {string|null} - Returns the value of the specified cookie, or null if not found.
			 */
			function extractCookieValue(cookieString, key) {
				return (
					(cookieString || "")
						.split(";")
						.map((cookie) => cookie.split("="))
						.find((parts) => parts[0].trim() === key)?.[1] || null
				);
			}

			/**
			 * Handles the authorization code flow for OAuth by exchanging the code for a token and validating the user.
			 * @param {Object} context - The request context.
			 * @param {string} authCode - The authorization code received from Google.
			 * @returns {boolean} - Returns true if the authentication and session setup are successful, false otherwise.
			 */
			function handleAuthCodeFlow(context, authCode) {
				const clientIp = context.request.clientIpAddress;

				log.debug("handleAuthCodeFlow|authCode", authCode);
				const accessToken = fetchAccessToken(authCode);
				log.debug("handleAuthCodeFlow|accessToken", accessToken);
				if (!accessToken) {
					redirectToGoogleOAuthServer(context);
					return false;
				}
				const emailAddress = fetchEmailAddress(accessToken);
				if (!isAuthorizedUser(emailAddress)) {
					log.error("handleAuthCodeFlow|isAuthorizedUser", false);
					context.response.write(`
						<!DOCTYPE html>
						<html>
						<head>
						  <title>Unauthorized Access</title>
						  <meta http-equiv="refresh" content="10;url=${CONFIG.scriptUrl}">
						</head>
						<body>
						  <h1>Unauthorized User</h1>
						  <p>You are not authorized to access this page. Redirecting you in 10 seconds...</p>
						</body>
						</html>
					`);
					return false;
				}
				const sessionId = random.generateUUID();
				createUserSession(clientIp, accessToken, sessionId, emailAddress);
				setSessionCookie(context, sessionId);
				redirectToHome(context);

				return true;

				/**
				 * Fetches an access token from Google's OAuth server using an authorization code.
				 * @param {string} authCode - The authorization code provided by Google after user consent.
				 * @returns {string|null} - Returns the access token if successfully retrieved, null otherwise.
				 */
				function fetchAccessToken(authCode) {
					log.debug("fetchAccessToken|authCode", authCode);
					const tokenResponse = https.post({
						url: "https://oauth2.googleapis.com/token",
						body: {
							code: authCode,
							client_id: CONFIG.googleClientId,
							client_secret: CONFIG.googleClientSecret,
							redirect_uri: CONFIG.scriptUrl,
							grant_type: "authorization_code",
						},
						headers: { "Content-Type": "application/x-www-form-urlencoded" },
					});

					const tokenData = JSON.parse(tokenResponse.body);
					if (tokenData.error || !tokenData.access_token) {
						log.error("fetchAccessToken|tokenResponse", tokenResponse.body);
						return;
					}

					return tokenData.access_token;
				}

				/**
				 * Retrieves the user's email address from Google using the access token.
				 * @param {string} accessToken - The access token to authenticate the request.
				 * @returns {string|null} - Returns the user's email address if successfully retrieved, null otherwise.
				 */
				function fetchEmailAddress(accessToken) {
					const userInfoResponse = https.get({
						url: "https://www.googleapis.com/oauth2/v1/userinfo?alt=json",
						headers: { Authorization: "Bearer " + accessToken },
					});
					if (userInfoResponse.code === 200) {
						return JSON.parse(userInfoResponse.body).email;
					} else {
						log.error("Failed to retrieve email", userInfoResponse.body);
						return null;
					}
				}

				/**
				 * Creates a user session and stores it in the cache.
				 * @param {string} clientIp - The IP address of the client initiating the session.
				 * @param {string} accessToken - The access token of the user.
				 * @param {string} sessionId - The session ID generated for the new session.
				 * @param {string} emailAddress - The email address of the user.
				 */
				function createUserSession(
					clientIp,
					accessToken,
					sessionId,
					emailAddress
				) {
					log.debug("createUserSession|emailAddress", emailAddress);
					const sessionCache = cache.getCache({
						name: sessionId,
						scope: cache.Scope.PRIVATE,
					});
					sessionCache.put({
						key: "sessionData",
						value: JSON.stringify({
							ipAddress: clientIp,
							token: accessToken,
							sessionId: sessionId,
							emailAddress: emailAddress,
						}),
						ttl: CONFIG.sessionTtl,
					});
				}

				/**
				 * Sets a session cookie in the client's browser.
				 * @param {Object} context - The context object from the Suitelet.
				 * @param {string} sessionId - The session ID to set in the cookie.
				 */
				function setSessionCookie(context, sessionId) {
					log.debug("setSessionCookie|sessionId", sessionId);
					const expirationDate = new Date(
						Date.now() + CONFIG.sessionTtl * 1000
					);

					context.response.setHeader({
						name: "Set-Cookie",
						value: `session_id=${sessionId}; Path=/; HttpOnly; Secure; SameSite=Strict; expires=${expirationDate.toUTCString()}`,
					});
				}
			}

			/**
			 * Checks if the given email address belongs to an authorized user.
			 * @param {string} emailAddress - The email address to check.
			 * @returns {boolean} - Returns true if the user is authorized, false otherwise.
			 */
			function isAuthorizedUser(emailAddress) {
				return CONFIG.authorizedUsers.includes(
					(emailAddress || "").toLowerCase()
				);
			}

			/**
			 * Redirects the user to a "home" URL, wipes the url params clean.
			 * @param {Object} context - The context of the Suitelet script.
			 */
			function redirectToHome(context) {
				log.debug("redirectToHome|Cleaning URL params..."); //Redirect to clean the url params
				let htmlBody = `<!DOCTYPE HTML>
				<html lang="en-US">
					<head>
						<meta charset="UTF-8">
						<meta http-equiv="refresh" content="0; url=${CONFIG.scriptUrl}">
						<script type="text/javascript">
							window.location.href = "${CONFIG.scriptUrl}"
						</script>
						<title>Success</title>
					</head>
					<body>
						If you are not redirected automatically, follow this <a href='${CONFIG.scriptUrl}'>link</a>.
					</body>
				</html>`;

				context.response.write(htmlBody);
				return;
			}

			/**
			 * Deletes the session cookie by setting its expiration date to a past value.
			 * @param {Object} context - The context of the Suitelet script.
			 */
			function deleteSessionCookie(context) {
				log.debug("deleteSessionCookie");
				context.response.setHeader({
					name: "Set-Cookie",
					value: `session_id=; Path=/; HttpOnly; Secure; SameSite=Strict; expires=Sun, 1 Jan 2023 00:00:00 UTC;`,
				});
			}

			/**
			 * Redirects the user to the Google OAuth server to initiate the authentication flow.
			 * @param {Object} context - The context of the Suitelet script.
			 */
			function redirectToGoogleOAuthServer(context) {
				log.debug("redirectToGoogleOAuthServer|Redirecting...");
				const authUrl =
					"https://accounts.google.com/o/oauth2/v2/auth?" +
					"prompt=select_account" +
					"&response_type=code" +
					"&client_id=" +
					encodeURIComponent(CONFIG.googleClientId) +
					"&redirect_uri=" +
					encodeURIComponent(CONFIG.scriptUrl) +
					"&scope=" +
					encodeURIComponent("https://www.googleapis.com/auth/userinfo.email");
				log.debug("authroul", authUrl);
				redirect.redirect({ url: authUrl });
			}
		}
	}

	return { onRequest: execute };
}
