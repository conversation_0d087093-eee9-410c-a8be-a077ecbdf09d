/**
 * @description Class representing a custom error object
 *
 * @NApiVersion 2.1
 * <AUTHOR>
 */

define(["exports"], (/** @type {any} */ exports) => {
	/**
	 * Custom Error Object class
	 *
	 * @type {import("./vlmd_custom_error_object").CustomErrorObject}
	 */
	class CustomErrorObject extends Error {
		/**
		 * Create the custom error object
		 *
		 * @param {string} message
		 */
		constructor(message) {
			super(message);
			this.ERROR_TYPE = "";
			this.summary = "";
			this.details = "";
			this.recordId = "";
			this.recordName = "";
			this.recordType = "";

			/** @type {{[key:string]: string}} */
			this.ErrorTypes = {
				EVAL_ERROR: "EvalError",
				INVALID_DATA_TYPE: "InvalidDataType",
				RANGE_ERROR: "RangeError",
				REFERENCE_ERROR: "ReferenceError",
				SYNTAX_ERROR: "SyntaxError",
				TYPE_ERROR: "TypeError",
				URI_ERROR: "URIError",

				MISSING_USER_INPUT: "MissingUserInput",
				MISSING_PARAM: "MissingParam",
				MISSING_VALUE: "MissingValue",
				MISSING_FIELD: "MissingField",
				VALUE_NOT_SET: "ValueNotSet",
				NO_VALUE_RETURNED: "NoValueReturned",
				RECORD_NOT_LOADED: "RecordNotLoaded",
				RECORD_NOT_CREATED: "RecordNotCreated",
				RECORD_NOT_SAVED: "RecordNotSaved",
				RECORD_NOT_DELETED: "RecordNotDeleted",
				FILE_NOT_LOADED: "FileNotLoaded",
				FILE_NOT_CREATED: "FileNotCreated",
				FILE_NOT_SAVED: "FileNotSaved",
				INVALID_FORM: "InvalidForm",
				INVALID_DATA: "InvalidData",
				SFTP_CONNECTION_FAILURE: "SftpConnectionFailure",
				INVALID_SEARCH: "InvalidSearch"
			};
		}

		/**
		 * Update error properties and return the error type
		 *
		 * @param {{errorType: string, summary: string, details: string}} param Object containing error details
		 * @returns {string} Error type
		 */
		updateError({
			errorType,
			summary: summaryPassedIn,
			details: detailsPassedIn,
		}) {
			/*The error type is passed into the param by using the property key (so that it can pull from the predefined list)
			 -> the property value is what's passed in as the param -> doing a backward search to check if there's a key for this value*/
			this.ERROR_TYPE = Object.keys(this.ErrorTypes).find(
				(key) => this.ErrorTypes[key] === errorType
			);

			this.summary = this.summary
				? `${summaryPassedIn}: ${this.summary}`
				: summaryPassedIn;

			if (detailsPassedIn) {
				this.details = this.details
					? detailsPassedIn + "\n" + this.details + "\n"
					: detailsPassedIn;
			}

			return this.ERROR_TYPE;
		}

		/**
		 * Handle generic/unhandled error
		 *
		 * @param {Error} err Error object
		 */
		handleUnhandledError(err) {
			/*The error type is passed into the param by using the property key (so that it can pull from the predefined list)
			 -> the property value is what's passed in as the param -> doing a backward search to check if there's a key for this value*/
			if (!this.ERROR_TYPE && err && err.name) {
				this.ERROR_TYPE = Object.keys(this.ErrorTypes).find(
					(key) => this.ErrorTypes[key] === err.name
				);
			}

			if (!this.ERROR_TYPE) {
				this.ERROR_TYPE = "UNHANDLED_ERROR";
			}

			if (!this.summary) {
				this.summary = err && err.name ? err.name : "UNHANDLED_ERROR";
			}

			if (!this.details) {
				this.details =
					err && err.message ? err.message : "No error details provided.";
			}
		}

		/**
		 * Throw the error from the custom error object
		 *
		 * @param {{summaryText: string, error: Error}} param
		 */

		throwError({
			error,
			summaryText,
			recordId,
			recordName,
			recordType,
			errorWillBeGrouped,
		}) {
			this.handleUnhandledError(error);

			this.recordId = recordId;
			this.recordName = recordName;
			this.recordType = recordType;

			this.details = errorWillBeGrouped
				? `${this.recordName ? this.recordName + ", " : ""}${
						this.recordId ? this.recordId + ", " : ""
				  }${this.recordType ? this.recordType + ", " : ""}${summaryText}, ${
						this.summary
				  }, ${this.details}`
				: `SUMMARY: ${summaryText + ":" ?? ""}${this.summary}${"\n"}DETAILS: ${
						this.details
				  }${"\n"}`;

			throw {
				name: this.ERROR_TYPE,
				message: this.details,
			};
		}
	}

	exports.CustomErrorObject = CustomErrorObject;

	return CustomErrorObject;
});
