/**
 * @NApiVersion 2.1
 */

define([
  "require",
  "Write810EdiFileLib",
  "GetEdiFileContents",
  "../../../Libraries/Misc_Libs/spl_get_transaction_obj_lib",
  "N/log",
  "N/record",
  "N/file",
], function (require, writeInvLib, getEdiFileContentsLib) {
  function process810(
    transactionObj,
    customer,
    partnerValues,
    dataObj,
    customErrorObject,
    ediTransactionRecordId
  ) {
    const log = require("N/log");
    const record = require("N/record");
    const file = require("N/file");

    const getInvoiceObjLib = require("../../../Libraries/Misc_Libs/spl_get_transaction_obj_lib");
    let { transactionId, transactionType } = transactionObj;
    let { integrationFolder, integrationId } = customer;
    let { purchasingSoftware, isInvoice } = dataObj;
    let documentObj;
    let ediFile;

    try {
      var helperFunctions = (function () {
        function getRecord() {
          try {
            return record.load({
              type: record.Type[isInvoice ? "INVOICE" : "CREDIT_MEMO"],
              id: transactionId,
            });
          } catch (err) {
            throw customErrorObject.updateError({
              errorType: customErrorObject.ErrorTypes.RECORD_NOT_LOADED,
              summary: "ERROR_LOADING_RECORD",
              details: `Invoice/CM record not loaded: ${err.message}`,
            });
          }
        }

        function getDocumentAsEDI() {
          try {
            var ediFile = writeInvLib.getInvoiceAsEDI(
              partnerValues,
              documentObj,
              isInvoice,
              dataObj.purchasingSoftware
            );

            if (
              !ediFile ||
              ediFile.search("undefined") != -1 ||
              ediFile.search("Invalid") != -1
            ) {
              throw customErrorObject.updateError({
                errorType: customErrorObject.ErrorTypes.NO_VALUE_RETURNED,
                summary: "EDI_FILE_NOT_GOTTEN",
                details: `File was converted to EDI file with "Undefined" or "Invalid" in the text.\n${ediFile}`,
              });
            }

            return ediFile;
          } catch (err) {
            throw customErrorObject.updateError({
              errorType: customErrorObject.ErrorTypes.NO_VALUE_RETURNED,
              summary: "NO_EDI_FILE_GOTTEN",
              details: `Error writing EDI file string for this transaction: $${err}`,
            });
          }
        }

        function _getFileToUpload() {
          return file.create({
            name: documentNumber + "_810.edi",
            fileType: file.Type.PLAINTEXT,
            contents: ediFile,
          });
        }

        function uploadFileToTheirServer() {
          try {
            var theirConnection =
              getEdiFileContentsLib.createConnection(dataObj);

            if (!theirConnection) {
              throw customErrorObject.updateError({
                errorType: customErrorObject.ErrorTypes.SFTP_CONNECTION_FAILURE,
                summary: "PURCHASING_SOFTWARE_CONNECTION_NOT_CREATED",
                details: `Purchasing software SFTP server connection not created.`,
              });
            }

            theirConnection.upload({
              file: _getFileToUpload(),
              replaceExisting: true,
            });
          } catch (err) {
            throw customErrorObject.updateError({
              errorType: customErrorObject.ErrorTypes.RECORD_NOT_SAVED,
              summary: "FILE_NOT_SAVED_TO_OUR_SERVER",
              details: `File not saved to our server:\nDataObj: ${JSON.stringify(
                dataObj
              )}\nReference Directory: ${referenceDirectory}\nError: ${
                err.message
              }`,
            });
          }
        }

        function uploadFileToSupplyLineServer() {
          try {
            var referenceDirectory = `/EDI Reference Files/${dataObj.purchasingSoftware}/${integrationFolder}/OUT/810`;

            var supplyLineConnection = getEdiFileContentsLib.createConnection(
              dataObj,
              "",
              referenceDirectory
            );

            if (!supplyLineConnection) {
              throw customErrorObject.updateError({
                errorType: customErrorObject.ErrorTypes.SFTP_CONNECTION_FAILURE,
                summary: "SUPPLYLINE_CONNECTION_NOT_CREATED",
                details: `Supplyline SFTP server connection not created.`,
              });
            }

            supplyLineConnection.upload({
              file: _getFileToUpload(),
              replaceExisting: true,
            });
          } catch (err) {
            throw customErrorObject.updateError({
              errorType: customErrorObject.ErrorTypes.RECORD_NOT_SAVED,
              summary: "FILE_NOT_SAVED_TO_OUR_SERVER",
              details: `File not saved to our server:\nDataObj: ${JSON.stringify(
                dataObj
              )}\nReference Directory: ${referenceDirectory}\nError: ${
                err.message
              }`,
            });
          }
        }

        function updateAndSaveControlNumber(controlNumber, integrationId) {
          try {
            record.submitFields({
              type: "customrecord_vlmd_edi_integration",
              id: integrationId,
              values: {
                custrecord_document_control_number: controlNumber,
              },
            });
          } catch (err) {
            throw customErrorObject.updateError({
              errorType: customErrorObject.ErrorTypes.RECORD_NOT_SAVED,
              summary: "CUSTOMER_NOT_UPDATED",
              details: `Value not saved to "customrecord_vlmd_edi_integration": ${err.message}`,
            });
          }
        }

        function createControlNumberRecord(controlNumber, documentNumber) {
          try {
            if (!controlNumber) {
              throw customErrorObject.updateError({
                errorType: customErrorObject.ErrorTypes.MISSING_PARAM,
                summary: "NO_CONTROL_NUMBER",
                details: `No control number passed in.`,
              });
            }

            //Create new Document Control Number Record
            var controlNumberRecord = record.create({
              type: "customrecord_edi_dcn_doc_ctrl_num",
            });

            controlNumberRecord.setValue(
              "custrecord_edi_dcn_tran_ctrl_num",
              controlNumber
            );

            controlNumberRecord.setValue(
              "custrecord_edi_dcn_doc_num",
              documentNumber
            );

            controlNumberRecord.setValue(
              "custrecord_edi_dcn_edi_tran_rec",
              ediTransactionRecordId
            );

            return controlNumberRecord.save();
          } catch (err) {
            throw customErrorObject.updateError({
              errorType: customErrorObject.ErrorTypes.RECORD_NOT_SAVED,
              summary: "EDI_CNTRL_NMBR_NOT_UPDATED",
              details: `Value not saved for customrecord_edi_dcn_doc_ctrl_num": ${controlNumber} ${documentNumber} ${err}`,
            });
          }
        }

        return {
          getRecord,
          getDocumentAsEDI,
          uploadFileToTheirServer,
          uploadFileToSupplyLineServer,
          updateAndSaveControlNumber,
          createControlNumberRecord,
        };
      })();

      const recordModuleMappingObj = {
        custinvc: "INVOICE",
        custcred: "CREDIT_MEMO",
        creditmemo: "CREDIT_MEMO",
        invoice: "INVOICE",
        CustInvc: "INVOICE",
        CustCred: "CREDIT_MEMO",
        INVOICE: "INVOICE",
        CREDIT_MEMO: "CREDIT_MEMO",
        Invoice: "INVOICE",
        "Credit Memo": "CREDIT_MEMO",
      };

      const mappedTransactionType = recordModuleMappingObj[transactionType];

      if (!mappedTransactionType) {
        throw customErrorObject.updateError({
          errorType: customErrorObject.ErrorTypes.NO_VALUE_RETURNED,
          summary: "MISSING_MAPPED_TRANSACTION_TYPE",
          details: `No mapped transaction found for ${transactionType}`,
        });
      }

      isInvoice = mappedTransactionType == "INVOICE";

      let netsuiteRecord = helperFunctions.getRecord();
      let documentNumber = netsuiteRecord.getText("tranid");

      documentObj = getInvoiceObjLib.getObj(
        netsuiteRecord,
        customer,
        mappedTransactionType,
        purchasingSoftware,
        isInvoice,
        customErrorObject
      );

      ediFile = helperFunctions.getDocumentAsEDI();
      helperFunctions.uploadFileToTheirServer();
      helperFunctions.uploadFileToSupplyLineServer();
      helperFunctions.updateAndSaveControlNumber(
        documentObj.documentInfo.controlNumber,
        integrationId
      );

      let controlNumberRecordId = helperFunctions.createControlNumberRecord(
        documentObj.documentInfo.controlNumber,
        documentNumber
      );

      if (customErrorObject.ERROR_TYPE) {
        throw customErrorObject.updateError({
          errorType: customErrorObject.ErrorTypes.UNHANDLED_ERROR,
          summary: "SOFT_ERROR_PROCESSING_810",
          details: `Transaction was processed but there was a soft error.`,
        });
      }

      return controlNumberRecordId;
    } catch (err) {
      throw customErrorObject.updateError({
        errorType: customErrorObject.ErrorTypes.UNHANDLED_ERROR,
        summary: "ERROR_PROCESSING_810",
        details: `Error in Process Outgoing 810 Lib: ${
          err && JSON.stringify(err)
        }`,
      });
    }
  }

  return {
    process810,
  };
});
