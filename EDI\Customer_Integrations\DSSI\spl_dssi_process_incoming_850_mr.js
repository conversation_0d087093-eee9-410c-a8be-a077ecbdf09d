/**
 * @NApiVersion 2.1
 * @NScriptType MapReduceScript
 * @NAmdConfig /SuiteScripts/config.json
 */

//@ts-ignore
define([
	"require",
	"N/log",
	"GetEdiFileContents",
	"GetEdiPartnerValuesLib",
	"Parse850Lib",
	"ProcessIncoming850Lib",
	"MoveFileLib",
	"../../../Classes/vlmd_custom_error_object",
	"../../../Classes/vlmd_mr_summary_handling",
], function (
	require,
	log,
	getEdiFileContentsLib,
	getEdiPartnerValuesLib,
	getParsedPurchaseOrderLib,
	processIncoming850Lib,
	moveFileLib
) {
	/** @type {import("../../../Classes/vlmd_custom_error_object").CustomErrorObject} */
	const CustomErrorObject = require("../../../Classes/vlmd_custom_error_object");

	const dataObj = {
		prodGuidBool: true,
		prodDirectoryBool: true,
		decodeContent: true,
		prodGUID: "49d25f1e76df4ca9a4fab7b6e94fd1ea",
		sandboxGUID: "",
		prodDirectory: `/users/DSSI/IN/850`,
		testDirectory: "/users/DSSI/Test",
		documentType: "Sales Order",
		documentTypeId: 1,
		purchasingSoftware: "DSSI",
		purchasingSoftwareId: 2,
		pushEmailToDB: true,
	};

	function getInputData(context) {
		//All DSSI files get placed into one shared folder
		//	-> no need to iterate over integration folders
		return getEdiFileContentsLib.getEdiFileContents(dataObj, "DSSI Customer")
			.fileContents;
	}

	function map(context) {
		const ediFile = JSON.parse(context.value);

		/*Since DSSI uses one folder for all integrations we don't have the
		 parent name/account number available at this point in the script 
		 -> using the general purchasing software instead.*/
		const purchasingSoftware = dataObj.purchasingSoftware;

		try {
			const partnerValues = getEdiPartnerValuesLib.getDssiDelimiterValues();

			if (!partnerValues) {
				log.error(
					"Error Getting Partner Values",
					`Partner values not gotten correctly for ${purchasingSoftware} ${ediFile.fileName}.`
				);

				throw {
					folder: "Incoming_850_Processed_With_Errors",
				};
			}

			const parsedObj = getParsedPurchaseOrderLib.parse850(
				ediFile.content,
				partnerValues,
				purchasingSoftware
			);

			if (parsedObj.is997) {
				throw {
					folder: "997",
				};
			}

			if (parsedObj.errorLog.length > 0) {
				log.error(
					"Error Parsing PO",
					`Purchase order not parsed correctly for ${purchasingSoftware} ${ediFile.fileName}.
					${parsedObj.errorLog}`
				);

				throw {
					folder: "Incoming_850_Processed_With_Errors",
				};
			}

			const errorsProcessingEnd = processIncoming850Lib.processIncoming850(
				dataObj,
				parsedObj.purchaseOrderObj,
				ediFile.fileName
			);

			if (errorsProcessingEnd.length > 0) {
				log.error("Error Processing End", errorsProcessingEnd);
			} else {
				context.write(purchasingSoftware, ediFile.fileName);
			}
		} catch (e) {
			dataObj.referenceDirectory = `/EDI Reference Files/${purchasingSoftware}/${e.folder}`;

			moveFileLib.moveFile(
				dataObj,
				ediFile.fileName,
				false //Not processed successfully
			);
		}
	}

	function reduce(context) {
		context.write({
			key: context.key,
			value: context.values,
		});
	}

	function summarize(context) {
		const customErrorObject = new CustomErrorObject();
		try {
			const StageHandling = require("../../../Classes/vlmd_mr_summary_handling");
			const stageHandling = new StageHandling(context);

			stageHandling.printScriptProcessingSummary();
			stageHandling.printRecordsProcessed();
			stageHandling.printErrors({
				groupErrors: true,
			});
		} catch (err) {
			customErrorObject.throwError({
				summaryText: `SUMMARIZE_ERROR`,
				error: err,
			});
		}
	}

	return {
		getInputData,
		map,
		reduce,
		summarize,
	};
});
