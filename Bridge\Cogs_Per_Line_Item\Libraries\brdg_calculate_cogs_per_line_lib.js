/**
 * <AUTHOR>
 * @module brdg_calculate_cogs_per_line_lib
 * @description lib to calculate COGS per item, called by 'call Lib on button click'
 */

define(["require", "exports", "N/log", "N/record", "N/query"], (
  require,
  exports
) => {
  const log = require("N/log");
  const record = require("N/record");
  const query = require("N/query");

  const brdg_calculate_cogs_per_line_lib = exports;

  /**
   * Build up and return a query string of transactions to process.
   *
   * @param {Object} paramsObj Object with params as key/values
   * @returns {string} Query string
   */
  exports.getQueryOfTransactionsToProcess = (paramsObj, customErrorObject) => {
    const {
      numberOfDaysBack,
      startDate,
      endDate,
      relativeStartDate,
      relativeEndDate,
      getTransactionsCreated,
      getTransactionsEdited,
      subsidiary,
    } = paramsObj;

    const selectClause = `SELECT DISTINCT
			trnsctn.id,
			trnsctn.type as recordtype,
			trnsctn.tranid`;

    const fromClause = `FROM
			   transaction AS trnsctn`;

    let joinClause = `LEFT OUTER JOIN
				  transactionLine as trnsctnln
				  ON trnsctnln.transaction = trnsctn.id
			    JOIN
				  subsidiary AS sbsdry
				  ON trnsctnln.subsidiary = sbsdry.id
				JOIN
					subsidiary AS prntsbsdry
				  ON sbsdry.parent = prntsbsdry.id
				JOIN
					subsidiary AS grndprntsbsdry 
				  ON prntsbsdry.parent = grndprntsbsdry.id `;

    if (relativeStartDate && relativeEndDate) {
      joinClause += `
		INNER JOIN
    		systemnote sysnote
    	ON 
			trnsctn.id = sysnote.recordid
    	AND 
			sysnote.field = 'TRANDOC.MAMOUNTMAIN'`;
    }

    let whereClause = `WHERE ${
      subsidiary
        ? "trnsctnln.subsidiary = " + subsidiary
        : "(prntsbsdry.id = '16' OR grndprntsbsdry.id = '16')"
    }
				AND sbsdry.id NOT LIKE '26'
				AND sbsdry.id NOT LIKE '27'
				AND (
					trnsctn.type = 'CashSale'
					OR trnsctn.type = 'CustInvc'
					--OR trnsctn.type = 'CustCred'
				)`;

    //Build clause for transactions CREATED within time frame - by either between 2 specific dates or between 2 relative dates
    let transactionsCreatedClause = ``;

    if (getTransactionsCreated && startDate && endDate) {
      transactionsCreatedClause += `
						TO_DATE(trnsctn.createddate, 'MM/DD/YYYY') BETWEEN TO_DATE( '${startDate}', 'MM/DD/YYYY' ) AND TO_DATE( '${endDate}', 'MM/DD/YYYY' )`;
    }

    /*NOTE: When using relative dates there is no need check for transactions 
		created within those dates as they will be automatically included by the transactions edited clause.
		(the MAMOUNTMAIN field is always updated when a transaction is created)
*/
    if (
      getTransactionsCreated &&
      !startDate &&
      !endDate &&
      !relativeStartDate &&
      !relativeEndDate
    ) {
      transactionsCreatedClause += `
				   		TO_CHAR (SYSDATE - ${
                numberOfDaysBack ? numberOfDaysBack : "1"
              } , 'MM/DD/YYYY') = TO_CHAR (trnsctn.createddate, 'MM/DD/YYYY')`;
    }

    //Build clause for transactions EDITED within time frame.
    let transactionsEditedClause = ``;

    if (getTransactionsEdited && startDate && endDate) {
      transactionsEditedClause += `
							TO_DATE( trnsctn.lastmodifieddate, 'MM/DD/YYYY') BETWEEN TO_DATE( '${startDate}', 'MM/DD/YYYY' ) AND TO_DATE( '${endDate}', 'MM/DD/YYYY' ) `;
    }

    if (relativeStartDate && relativeEndDate) {
      //NOTE: This will pull in all transactions edited and the field changed is the MAMOUNTMAIN or created within this time frame.
      transactionsEditedClause += `
						TO_DATE(sysnote.date, 'MM/DD/YYYY') BETWEEN BUILTIN.RELATIVE_RANGES( 'LM', 'START' ) AND BUILTIN.RELATIVE_RANGES( 'LM', 'END' )`;
    }

    if (
      getTransactionsEdited &&
      !startDate &&
      !endDate &&
      !relativeStartDate &&
      !relativeEndDate
    ) {
      transactionsEditedClause += `
							TO_CHAR (SYSDATE - ${
                numberOfDaysBack ? numberOfDaysBack : "1"
              } , 'MM/DD/YYYY') = TO_CHAR (trnsctn.lastmodifieddate, 'MM/DD/YYYY')`;
    }

    //Add edit and create clause to where clause using the correct expressions.
    if (transactionsCreatedClause && transactionsEditedClause) {
      whereClause += `
			AND (${transactionsCreatedClause} OR ${transactionsEditedClause})`;
    } else if (transactionsCreatedClause || transactionsEditedClause) {
      whereClause += `
			AND ${
        transactionsCreatedClause
          ? transactionsCreatedClause
          : transactionsEditedClause
      }`;
    } else {
      throw customErrorObject.updateError({
        errorType: customErrorObject.ErrorTypes.NO_VALUE_RETURNED,
        summary: "MISSING_DATE_CLAUSE",
        details: `Params passed in: ${JSON.stringify(paramsObj)}`,
      });
    }

    const sqlQuery = [selectClause, fromClause, joinClause, whereClause].join(
      "\n"
    );

    return sqlQuery;
  };

  /**
   * Create the COGS query string to pull the amount and account for all transaction accounting lines for this order.
   *
   * @param {string} transactionId Transaction internal ID
   * @returns {string} Query string
   */
  exports.getCogsQuery = (transactionId) => {
    return `
			SELECT
				tal.amount AS amount,
				tl.accountinglinetype as account
			FROM
				transactionaccountingline AS tal 
			INNER JOIN
				transactionline AS tl 
			ON
				tal.transactionline = tl.id 
				AND tal.transaction = tl.transaction 
			WHERE
				tl.transaction = ${transactionId}
				AND
				tl.itemtype != 'Service'
				AND 
				(
				   tl.accountinglinetype = 'INCOME' 
				   OR tl.accountinglinetype = 'COGS' 
				)
			ORDER BY
				tl.linesequencenumber
		`;
  };

  /**
   * Query the Cost of Goods Sold amount and set the value to Actual Items COGS
   *   so it appears on the Item sublist of the transaction
   * Each item line has an Income and COGS line
   * Save the COGS amount for every Income accounting line encountered
   * For Kit Items, accumulate all COGS amount until a new Income line is met
   *
   * @param {string} scriptTypeCallingLib Type of script using the library
   * @param {string} transactionId Transaction internal ID
   * @param {string} recordType Transaction record type
   * @returns {void}
   */
  exports.setAllCogsPerItem = (
    transactionId,
    recordType,
    customErrorObject
  ) => {
    const recordModuleMappingObj = {
      cashsale: "CASH_SALE",
      custinvc: "INVOICE",
      custcred: "CREDIT_MEMO",
      CashSale: "CASH_SALE",
      CustInvc: "INVOICE",
      CustCred: "CREDIT_MEMO",
      invoice: "INVOICE",
    };

    const recordModuleType = recordModuleMappingObj[recordType];

    if (!recordModuleType) {
      throw customErrorObject.updateError({
        errorType: customErrorObject.ErrorTypes.NO_VALUE_RETURNED,
        summary: "RECORD_MODULE_TYPE",
        details: `No record module type gotten for ${recordType}`,
      });
    }

    let transactionRecord = record.load({
      type: record.Type[recordModuleType],
      id: transactionId,
    });

    if (!transactionRecord) {
      throw customErrorObject.updateError({
        errorType: customErrorObject.ErrorTypes.RECORD_NOT_LOADED,
        summary: recordModuleType,
        details: `${recordModuleType} record not loaded for int id ${transactionId}`,
      });
    }

    //Get all accounting lines for this order
    const cogsAmountMappedResults = query
      .runSuiteQL({
        query: brdg_calculate_cogs_per_line_lib.getCogsQuery(transactionId),
      })
      .asMappedResults();

    if (!cogsAmountMappedResults) {
      throw customErrorObject.updateError({
        errorType: customErrorObject.ErrorTypes.NO_VALUE_RETURNED,
        summary: `NO_SUITEQL_COGS_AMOUNT_RESULTS`,
        details: `No cogs amount returned for query: ${brdg_calculate_cogs_per_line_lib.getCogsQuery(
          transactionId
        )}`,
      });
    }

    const cogsArr = [];
    let transactionCogsValue = 0;

    //Iterate over all results - create a new slot for each income line, then add the COGS amount to the slot for all lines related to this income line
    //The pattern that the GL impact follows is a sales line, then a cogs line for each item.
    cogsAmountMappedResults.forEach((result) => {
      switch (result.account) {
        case "INCOME":
          cogsArr.push(0);
          break;
        case "COGS":
          cogsArr[cogsArr.length - 1] += result.amount;
          transactionCogsValue += result.amount;
          break;
        default:
          break;
      }
    });

    if (!transactionCogsValue) {
      //First check if this transaction has at least 1 inventory or kit item.
      const sqlQuery = `SELECT
			TOP 1 id,
		 FROM
			transactionline 
		 WHERE
			(
			   itemtype = 'Kit' 
			   OR itemtype = 'InvtPart' 
			)
			AND transaction = ?			
			`;

      const transactionHasValidItem = query.runSuiteQL({
        query: sqlQuery,
        params: [transactionId],
      }).results[0];

      if (!transactionHasValidItem) {
        //transactionHasValidItem is blank
        //There is no inventory or kit item on this transaction - no need to continue.
        return;
      }

      throw customErrorObject.updateError({
        errorType: customErrorObject.ErrorTypes.MISSING_VALUE,
        summary: "NO_COGS_AMOUNT",
        details: `No cogs amount was found on this transaction.`,
      });
    }

    //Validate forms and correct if necessary
    const recordForm = transactionRecord.getValue("customform");

    if (recordForm != 199 && recordForm != 145 && recordForm != 160) {
      //Invoice, CM, and cash sale form for BRDG
      transactionRecord.setValue(
        "customform",
        recordModuleType == "INVOICE"
          ? 199
          : recordModuleType == "CREDIT_MEMO"
          ? 145
          : 160
      ); //else is cash sale

      transactionRecord.save();

      transactionRecord = record.load({
        type: record.Type[recordModuleType],
        id: transactionId,
      });

      customErrorObject.updateError({
        errorType: customErrorObject.ErrorTypes.INVALID_FORM,
        summary: "INVALID_FORM",
        details: `Incorrect form chosen for record. Although it has been updated, please investigate to prevent in the future.`,
      });
    }

    var columnFieldExists = transactionRecord
      .getSublistFields({
        sublistId: "item",
      })
      .some((columnField) => columnField == "custcol_actual_item_cogs");

    if (!columnFieldExists) {
      throw customErrorObject.updateError({
        errorType: customErrorObject.ErrorTypes.MISSING_FIELD,
        summary: "NO_COGS_Field",
        details: `Custom column field, "custcol_actual_item_cogs", isn't found on this form. Please verify that the correct form is used and correct if needed.`,
      });
    }

    const lineCount = transactionRecord.getLineCount({
      sublistId: "item",
    });

    for (let x = 0; x < lineCount; x++) {
      const itemType = transactionRecord.getSublistValue({
        sublistId: "item",
        fieldId: "itemtype",
        line: x,
      });

      if (itemType !== "InvtPart" && itemType !== "Kit") {
        continue;
      }

      const cogsAmount = cogsArr.shift();

      if (!cogsAmount) {
        const itemName = transactionRecord.getSublistValue({
          sublistId: "item",
          fieldId: "item_display",
          line: x,
        });

        customErrorObject.updateError({
          errorType: customErrorObject.ErrorTypes.NO_VALUE_RETURNED,
          summary: "ITEM_LINE_COGS_AMOUNT",
          details: `No COGS amount gotten for ${itemName} on line ${x + 1}.`,
        });
      }

      transactionRecord.setSublistValue({
        sublistId: "item",
        fieldId: "custcol_actual_item_cogs",
        line: x,
        value: cogsAmount,
      });
    }

    try {
      transactionRecord.save();
    } catch (err) {
      throw customErrorObject.updateError({
        errorType: customErrorObject.ErrorTypes.RECORD_NOT_SAVED,
        summary: "ERROR_SAVING_RECORD",
        details: `Error saving record: ${err.message}`,
      });
    }
  };
});
