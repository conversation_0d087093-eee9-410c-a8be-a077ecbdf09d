/**
 * @NApiVersion 2.x
 */

define(["N/log", "N/query", "CompareAddressLib", "Moment", "LoDash"], function (
  log,
  query,
  compareAddressLib,
  moment,
  _
) {
  function compareValues(
    poAckObj,
    purchaseOrderObj,
    purchaseOrderInternalId,
    purchasingSoftwareId
  ) {
    var logsDirectory = {
      errorLog: [],
      addressDiscrepancy: [],
      itemsAcknowledgedSuccessfully: [],
      itemsOnBackorder: [],
      rejectedItems: [],
      itemValueDiscrepancy: [],
    };

    purchasingSoftwareId != 13 && compareShippingAddresses(); //Medacure doesn't send shipping addresses
    processItems();

    return logsDirectory;

    /**********Validate PO Acknowledgment Values Helper Functions**********/
    function processFail(logMessage, continueProcessing = true) {
      logsDirectory.errorLog.push(logMessage);
      return continueProcessing;
    }

    function compareShippingAddresses() {
      try {
        if (!purchaseOrderObj.shippingAddress || !poAckObj.shippingAddress) {
          processFail(`Shipping address missing`);
          return;
        }

        var addressesMatch = compareAddressLib.compareAddresses(
          purchaseOrderObj.shippingAddress,
          poAckObj.shippingAddress
        );

        if (!addressesMatch) {
          logsDirectory.addressDiscrepancy.push(
            `This shipping address doesn't match what the vendor received.
							What we sent: ${purchaseOrderObj.shippingAddress}
							What the vendor received: ${poAckObj.shippingAddress}`
          );
        }
      } catch (e) {
        processFail(
          `Shipping addresses not compared successfully for ${poAckObj.purchaseOrderNumber} Error: ${e}`
        );
      }
    }

    function processItems() {
      purchaseOrderObj.items.forEach((itemInNetSuite) => {
        var correspondingItemOnAcknowledgment = poAckObj.items.find(
          (itemOnAcknowledgment) =>
            itemOnAcknowledgment.itemName.split("-").join("") ==
            itemInNetSuite.itemName.split("-").join("")
        );

        //Check if the old item number was sent on the PO.
        if (!correspondingItemOnAcknowledgment) {
          const sqlQuery = /*sql*/ `SELECT TOP 1
						BUILTIN.DF(onm.custrecord_old_item_id) as new_item_name
						FROM
							transactionline tl 
							JOIN
							customrecord_old_new_item_mapping onm 
							ON onm.custrecord_new_item_id = '${itemInNetSuite.itemInternalId}' 
						WHERE
							tl.transaction = ${purchaseOrderInternalId}`;

          const queryResults = query
            .runSuiteQL({
              query: sqlQuery,
            })
            .asMappedResults();

          let oldItemName =
            queryResults.length > 0 && queryResults[0]["new_item_name"]
              ? queryResults[0]["new_item_name"]
              : "";

          if (oldItemName) {
            correspondingItemOnAcknowledgment = poAckObj.items.find(
              (itemOnAcknowledgment) => {
                return (
                  itemOnAcknowledgment.itemName.split("-").join("") ==
                  oldItemName.split("-").join("")
                );
              }
            );
          }
        }

        //Check if the vendor responded with the vendor code instead of the NS item name
        if (!correspondingItemOnAcknowledgment) {
          const sqlQuery = /*sql*/ `
					SELECT TOP 1
						item.vendorname 
					FROM
						item 
					JOIN
						transactionline tl 
						ON item.id = tl.item 
					WHERE
						item.id = ${itemInNetSuite.itemInternalId} 
						AND item.vendorname IN 
						(${poAckObj.items.map((item) => "'" + item.itemName + "'").join(",")})
						AND tl.transaction = ${purchaseOrderInternalId}`;

          const vendorItemNameInNetSuite = query
            .runSuiteQL({
              query: sqlQuery,
            })
            .asMappedResults()[0]?.["vendorname"];

          if (vendorItemNameInNetSuite) {
            correspondingItemOnAcknowledgment = poAckObj.items.find(
              (itemOnAcknowledgment) => {
                return (
                  itemOnAcknowledgment.itemName.split("-").join("") ==
                  vendorItemNameInNetSuite.split("-").join("")
                );
              }
            );
          }
        }

        if (!correspondingItemOnAcknowledgment) {
          processFail(
            `${itemInNetSuite.itemName} not found in the PO acknowledgment`
          );

          return;
        }

        if (
          correspondingItemOnAcknowledgment.ack == "AC" ||
          correspondingItemOnAcknowledgment.ack == "IA" ||
          correspondingItemOnAcknowledgment.ack == "AA" ||
          correspondingItemOnAcknowledgment.ack == "IP"
        ) {
          //Item Accepted and Shipped || Item Accepted || Item is Vendor Direct || Item Accepted – Price Changed
          if (correspondingItemOnAcknowledgment.expectedDate) {
            logsDirectory.itemsAcknowledgedSuccessfully.push(
              `The expected ship date for ${
                itemInNetSuite.itemName
              } is ${moment(
                correspondingItemOnAcknowledgment.backorderDate
              ).format("MM/DD/YYYY")}.`
            );
          }
          compareItemValues(itemInNetSuite, correspondingItemOnAcknowledgment);
        } else if (correspondingItemOnAcknowledgment.ack == "IB") {
          //Item on Backorder
          var backorderText = `${itemInNetSuite.itemName}  is on backorder. `;
          if (correspondingItemOnAcknowledgment.expectedDate) {
            backorderText += `The expected ship date is ${moment(
              correspondingItemOnAcknowledgment.expectedDate
            ).format("MM/DD/YYYY")}.`;
          }
          logsDirectory.itemsOnBackorder.push(backorderText);

          compareItemValues(itemInNetSuite, correspondingItemOnAcknowledgment);
        } else if (correspondingItemOnAcknowledgment.ack == "IR") {
          //Item Rejected
          logsDirectory.rejectedItems.push(
            `${itemInNetSuite.itemName}was rejected by the vendor. Please investigate.`
          );
        } else {
          processFail(
            `${itemInNetSuite.itemName} received acknowledgement of ${correspondingItemOnAcknowledgment.ack}. Please investigate.`
          );
        }
      });
    }

    function compareItemValues(poItem, poAckItem) {
      if (poItem.quantity != poAckItem.quantity) {
        logsDirectory.itemValueDiscrepancy.push(
          `The quantity that we sent for ${poItem.itemName} doesn't match what the vendor sent back.
						What we sent: ${poItem.quantity} 
						What the vendor sent back: ${poAckItem.quantity}, Rate - ${poAckItem.rate}`
        );
      }

      if (poItem.rate != poAckItem.rate) {
        logsDirectory.itemValueDiscrepancy.push(
          `The rate that we sent for ${poItem.itemName} doesn't match what the vendor charged us.
						What we sent: " ${poItem.rate} 
						What the vendor sent back: ${poAckItem.rate}`
        );
      }
    }
  }

  return {
    compareValues,
  };
});
