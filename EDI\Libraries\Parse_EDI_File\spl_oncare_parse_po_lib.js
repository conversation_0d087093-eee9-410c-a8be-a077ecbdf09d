/**
 * @NApiVersion 2.1
 */

//@ts-ignore
define(["N/log", "Moment"], function (log, moment) {
	function parse850(ediFile) {
		try {
			var lineRegEx = /\n/;
			var fieldRegEx = /,/;

			var linesFields = getPoFields();
			var purchaseOrderHeaderFields = linesFields[1];
			var purchaseOrder = getPO();

			purchaseOrder.items = addPoItems(linesFields);

			purchaseOrder.memo = getMemo();

			return purchaseOrder;

			function _formatField(field, failIfUndefined) {
				try {
					if (!field && failIfUndefined) {
						throw {
							name: "ERROR_FORMATTING_FIELD",
							message: `Integral field to format is missing`,
						};
					}

					if (field) {
						return field.trim().split('"').join("");
					}
				} catch (err) {
					throw err;
				}
			}

			function getPoFields() {
				try {
					ediFile = ediFile.replace(/\r/g, "");
					let orderLinesArr = ediFile.split(lineRegEx);

					//Remove last line if is empty
					if (orderLinesArr[orderLinesArr.length - 1] === "") {
						orderLinesArr.pop();
					}

					//Split to arr of arr
					const orderLinesArrOfArr = orderLinesArr.map(function (line) {
						return line.split(fieldRegEx);
					});

					if (orderLinesArrOfArr.length < 2) {
						throw {
							message: `This file is missing lines needed. It doesn't contain a header row and at least one line item`,
						};
					}

					return orderLinesArrOfArr;
				} catch (err) {
					throw { name: "ERROR_GETTING_PO_FIELDS", message: err.message };
				}
			}

			function getPO() {
				try {
					var po = {
						transactionControlNumber: _formatField(
							purchaseOrderHeaderFields[0],
							true
						),
						poNumber: _formatField(purchaseOrderHeaderFields[1], true),
						poDate: moment(purchaseOrderHeaderFields[3]).format("YYYYMMDD"),
						customerExternalId: _formatField(
							purchaseOrderHeaderFields[2],
							true
						),
						customerNameInTheirSystem: _formatField(
							purchaseOrderHeaderFields[10]
						),
						items: [],
						streetAddress: _formatField(purchaseOrderHeaderFields[11], true),
						address: getShipToAddress(),
						communicationObj: {},
					};

					return po;
				} catch (err) {
					throw { name: "ERROR_GETTING_PO_OBJ", message: err.message };
				}
			}

			function getShipToAddress() {
				try {
					return {
						streetAddress: _formatField(purchaseOrderHeaderFields[11], true),
						city: _formatField(purchaseOrderHeaderFields[13]),
						state: _formatField(purchaseOrderHeaderFields[14]),
						zip: _formatField(purchaseOrderHeaderFields[15]),
					};
				} catch (err) {
					throw { name: "ERROR_GETTING_SHIP_ADDRESS", message: err.message };
				}
			}

			function addPoItems() {
				try {
					var itemsArr = [];
					linesFields.shift(); //Remove header row
					linesFields.forEach(function (lineFields) {
						if (lineFields.length <= 1) {
							return;
						}
						var item = {
							itemName: _formatField(lineFields[5], true).toUpperCase(),
							quantity: _formatField(lineFields[6], true),
							rate: _formatField(lineFields[7], true),
							isPPD: _formatField(lineFields[8]),
							description: _formatField(lineFields[15]),
						};

						itemsArr.push(item);
					});

					return itemsArr;
				} catch (err) {
					throw { name: "ERROR_GETTING_PO_ITEMS", message: err.message };
				}
			}

			function getMemo() {
				try {
					if (purchaseOrderHeaderFields.length >= 9) {
						return purchaseOrderHeaderFields[9];
					}
				} catch (err) {
					throw { name: "ERROR_GETTING_MEMO", message: err.message };
				}
			}
		} catch (err) {
			throw { name: `ERROR_PARSING_FILE`, message: err.message };
		}
	}

	return {
		parse850,
	};
});
