/**
 * @description Sets values on Purchase Orders - use custom PO email address field and sets the shipping date to null
 *
 * </br><b>Deployed On:</b> PurchaseOrder
 * </br><b>Execution Context:</b> CLIENTSCRIPT
 * </br><b>Event Type/Mode:</b> EDIT
 * </br><b>Entry Points:</b> pageInit, fieldChanged, saveRecord
 *
 * @NApiVersion 2.1
 * @NScriptType ClientScript
 * @NAmdConfig /SuiteScripts/config.json
 *
 * <AUTHOR>
 * @module spl_set_purchase_order_email_cs
 */

define([
  "require",
  "N/log",
  "GetOrderEmailAddressLib",
  "./../Classes/vlmd_custom_error_object",
], function (require, log, getOrderEmailAddress) {
  /** @type {import("../Classes/vlmd_custom_error_object").CustomErrorObject} */

  const CustomErrorObject = require("../Classes/vlmd_custom_error_object");
  const customErrorObject = new CustomErrorObject();

  function pageInit(context) {
    try {
      const purchaseOrder = context.currentRecord;

      context.mode == "create" && purchaseOrder.setValue("shipdate", null);

      const vendorId = purchaseOrder.getValue("entity");

      if(!vendorId) return;

      let results = getOrderEmailAddress.getPurchaseOrderEmailAddress(vendorId);

      if (results && results.length > 0) {
        results = results.join(";");
        purchaseOrder.setValue("email", results);
      }
    } catch (e) {
      customErrorObject.throwError({
        errorType: customErrorObject.ErrorTypes.VALUE_NOT_SET,
        summary: "ERROR_SETTING_PO_VALUES",
        details: `Page Init: Could not set PO values: ${e.message || e}`,
      });
    }
  }

  function fieldChanged(context) {
    if (context.fieldId != "entity") return;

    try {
      const purchaseOrder = context.currentRecord;

      const vendorId = purchaseOrder.getValue("entity");
      var results = getOrderEmailAddress.getPurchaseOrderEmailAddress(vendorId);

      if (results && results.length > 0) {
        results = results.join(";");
        purchaseOrder.setValue("email", results);
      }
    } catch (e) {
      customErrorObject.throwError({
        errorType: customErrorObject.ErrorTypes.VALUE_NOT_SET,
        summary: "EMAIL_STRING_NOT_FOUND",
        details: `Field Changed: Could not set email field ${e.message || e}`,
      });
    }
  }

  function saveRecord(context) {
    try {
      const purchaseOrder = context.currentRecord;

      const vendorId = purchaseOrder.getValue("entity");
      var results = getOrderEmailAddress.getPurchaseOrderEmailAddress(vendorId);

      if (results && results.length > 0) {
        results = results.join(";");
        purchaseOrder.setValue("email", results);
      }
    } catch (e) {
      customErrorObject.throwError({
        errorType: customErrorObject.ErrorTypes.VALUE_NOT_SET,
        summary: "EMAIL_STRING_NOT_FOUND",
        details: `Save Record: Could not set email field ${e.message || e}`,
      });
    }

    return true;
  }

  return {
    pageInit,
    fieldChanged,
    saveRecord,
  };
});
