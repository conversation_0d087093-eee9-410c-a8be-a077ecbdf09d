/**
 * @description Class containing functions to create 850 Sales Order
 *
 * @NApiVersion 2.1
 * <AUTHOR> <<EMAIL>>
 */
define([
    "exports",
    "require",
    "N/record",
    "N/query",
    "N/log",
    "./edi_850_customer",
], (
    /** @type {any} */ exports,
    /** @type {any} */ require,
) => {
    const record = require("N/record");
    const query = require("N/query");
    const log = require("N/log");
    const { EDI850Customer } = require("./edi_850_customer");
    const { EDI850ParsedTransaction } = require("../../Decorators/850/edi_850_parsed_transaction");

    /**
     * 850 Sales Order Class
     *
     * @class
     * @typedef {import("../../Interfaces/Models/Partner/edi_partner").EDICustomerInterface} EDICustomerInterface
     * @typedef {import("../../Interfaces/Decorators/850/edi_850").EDI850SalesOrderParams} EDI850SalesOrderParams
     * @typedef {import("../../../../Classes/vlmd_custom_error_object").CustomErrorObject} CustomErrorObject
     */
    class EDI850SalesOrder {
        /** @param {EDI850SalesOrderParams} params */
        constructor (params) {
            /** @type {string[]} */
            this.errors = [];
            /** @type {EDI850ParsedTransaction} */
            this.parsedPurchaseOrder = params.parsedTransaction;
            /** @type {EDICustomerInterface} */
            this.customer = params.customer;
            /** @type {import("N/record").Record | null} */
            this.dynamicRecord = null;
            /** @type {string} */
            this.customerQueryString = params.customerQueryString;
            /** @type {CustomErrorObject} */
            this.customError = params.customError;
        }

        /**
         * Create a NetSuite dynamic record for the Sales Order
         *
         * @returns {EDI850SalesOrder|undefined} Sales Order instance
         */
        create() {
            try {
                this.dynamicRecord = record.create({
                    type: record.Type.SALES_ORDER,
                    isDynamic: true,
                });

                return this;
            } catch (/** @type {any} */ err) {
                this.errors.push(err.message);
                log.error({
                    title: "EDI850SalesOrder (create)",
                    details: err,
                });
                throw this.customError.updateError({
                    errorType: this.customError.ErrorTypes.RECORD_NOT_CREATED,
                    summary: "EDI850SalesOrder_create",
                    details: err,
                });
            }
        }

        /**
         * Set header values for the Sales Order
         *
         * @returns {EDI850SalesOrder|undefined} Sales Order instance
         */
        setValues() {
            try {
                if (!this.dynamicRecord) {
                    return;
                }

                const customer = new EDI850Customer({
                    customError: this.customError,
                    customer: this.parsedPurchaseOrder.customer,
                    partner: this.customer
                }).lookup(this.customerQueryString);

                log.debug({
                    title: "EDI850SalesOrder (setValues): customer",
                    details: customer
                });

                this.dynamicRecord.setValue({
                    fieldId: "customform",
                    value: "185", // Walmart Creoh Sales Order
                });

                this.dynamicRecord.setValue({
                    fieldId: "entity",
                    value: customer,
                });

                this.dynamicRecord.setValue({
                    fieldId: "custbody_walmart_gln",
                    value: this.parsedPurchaseOrder.customer.identifier,
                });
    
                this.dynamicRecord.setValue({
                    fieldId: "custbody_walmart_4_digit_po_type",
                    value: this.parsedPurchaseOrder.typeCode,
                });
    
                try {
                    this.parsedPurchaseOrder.department && this.dynamicRecord.setText({
                        fieldId: "custbody_crh_walmart_department",
                        text: this.parsedPurchaseOrder.department.toString(),
                    });
                } catch (/** @type {any} */ err) {
                    this.errors.push(`${err.message}\nDepartment Number from Incoming 850: ${this.parsedPurchaseOrder.department}`);
                    log.error({
                        title: "EDI850SalesOrder (setValues)",
                        details: err,
                    });
                }
    
                this.dynamicRecord.setValue({
                    fieldId: "otherrefnum",
                    value: this.parsedPurchaseOrder.number,
                });
    
                this.dynamicRecord.setValue({
                    fieldId: "trandate",
                    value: this.parsedPurchaseOrder.date,
                });

                this.dynamicRecord.setValue({
                    fieldId: "shipdate",
                    value: this.parsedPurchaseOrder.date,
                });

                try {
                    this.parsedPurchaseOrder.shipPoint && this.dynamicRecord.setText({
                        fieldId: "custbody_crh_walmart_ship_point",
                        text: this.parsedPurchaseOrder.shipPoint,
                    });
                } catch (/** @type {any} */ err) {
                    this.errors.push(`${err.message}\nShip Point from Incoming 850: ${this.parsedPurchaseOrder.shipPoint}, Setting to default: "08404701"`);
                    log.error({
                        title: "EDI850SalesOrder (setValues)",
                        details: err,
                    });
                    this.dynamicRecord.setText({
                        fieldId: "custbody_crh_walmart_ship_point",
                        text: "08404701",
                    });
                }
    
                this.dynamicRecord.setValue({
                    fieldId: "orderstatus",
                    value: "A", // Pending Approval
                });
    
                this.dynamicRecord.setValue({
                    fieldId: "custbody_crh_must_arrive_by_date",
                    value: this.parsedPurchaseOrder.mustArriveBy,
                });
    
                const {street, city, state, zip} = this.parsedPurchaseOrder.customer.address;
                this.dynamicRecord.setValue({
                    fieldId: "shipaddress",
                    value: `${street}, ${city} ${state}, ${zip}`,
                });

                return this;
            } catch (/** @type {any} */ err) {
                this.errors.push("Failed to set header values to the sales order.");
                log.error({
                    title: "EDI850SalesOrder (setValues)",
                    details: err,
                });
                throw this.customError.updateError({
                    errorType: this.customError.ErrorTypes.VALUE_NOT_SET,
                    summary: "EDI850SalesOrder_setValues",
                    details: err,
                });
            }
        }

        /**
         * Set items on the item sublist
         * Use both itemid and vendorname as valid item names
         *
         * @returns {number | undefined}
         */
        setItems() {
            try {
                if (!this.dynamicRecord) {
                    return;
                }
                const itemNames = this.parsedPurchaseOrder.items.map(i => i.name);

                /** @type {{[key:string]: number}} */
                const itemNameIdMapping = {};

                const itemQuery = `
                    SELECT
                        id,
                        itemid,
                        vendorname
                    FROM
                        item
                    WHERE
                        (vendorname IN (${itemNames.map(i => `'${i}'`).join(",")})
                        OR itemid IN (${itemNames.map(i => `'${i}'`).join(",")}))
                        AND isinactive = 'F'
                    `;

                log.debug({
                    title: "EDI850SalesOrder (setItems)",
                    details: JSON.stringify({ itemNames, itemQuery })
                });
                
                query.runSuiteQLPaged({
                    query: itemQuery,
                    pageSize: 1000
                }).iterator().each((page) => {
                    page.value.data.results.forEach((result) => {
                        if (result.values[1]) {
                            itemNameIdMapping[result.values[1].toString()] = Number(result.values[0]);
                        }
                        if (result.values[2]) {
                            itemNameIdMapping[result.values[2].toString()] = Number(result.values[0]);
                        }
                    });
                    return true;
                });

                log.debug({
                    title: "EDI850SalesOrder (setItems): itemNameIdMapping",
                    details: JSON.stringify(itemNameIdMapping)
                });

                const itemsToAdd = Object.values(itemNameIdMapping);

                itemsToAdd.length > 0 && this.parsedPurchaseOrder.items.forEach((item) => {
                    if (!itemNameIdMapping[item.name]) {
                        this.errors.push(`Item ${item.name} from EDI File does not exist in NetSuite.`);
                        return;
                    }

                    this.dynamicRecord?.selectNewLine({
                        sublistId: "item",
                    });

                    this.dynamicRecord?.setCurrentSublistValue({
                        sublistId: "item",
                        fieldId: "item",
                        value: itemNameIdMapping[item.name],
                    });

                    this.dynamicRecord?.setCurrentSublistValue({
                        sublistId: "item",
                        fieldId: "rate",
                        value: item.rate,
                    });

                    this.dynamicRecord?.setCurrentSublistValue({
                        sublistId: "item",
                        fieldId: "quantity",
                        value: item.quantity,
                    });

                    this.dynamicRecord?.commitLine({
                        sublistId: "item"
                    });
                });

                return itemsToAdd.length;
            } catch (/** @type {any} */ err) {
                this.errors.push("Failed to set line item values to the sales order.");
                log.error({
                    title: "EDI850SalesOrder (setItems)",
                    details: err,
                });
                throw this.customError.updateError({
                    errorType: this.customError.ErrorTypes.VALUE_NOT_SET,
                    summary: "EDI850SalesOrder_setItems",
                    details: err,
                });
            }
        }

        /**
         * Save the NetSuite record for the Sales Order
         *
         * @returns {number} Internal ID assigned to the Sales Order
         */
        save() {
            try {
                if (this.dynamicRecord) {

                    log.debug({
                        title: "EDI850SalesOrder (save)",
                        details: "Saving the Sales Order..",
                    });

                    return this.dynamicRecord.save({
                        ignoreMandatoryFields: true,
                    });
                } else {
                    this.errors.push("No NetSuite record to save.");
                    return 0;
                }
            } catch (/** @type {any} */ err) {
                this.errors.push("Failed to save NetSuite record for the sales order.");
                log.error({
                    title: "EDI850SalesOrder (save)",
                    details: err,
                });
                throw this.customError.updateError({
                    errorType: this.customError.ErrorTypes.RECORD_NOT_SAVED,
                    summary: "EDI850SalesOrder_save",
                    details: err,
                });
            }
        }
    }

    exports.EDI850SalesOrder = EDI850SalesOrder;
});