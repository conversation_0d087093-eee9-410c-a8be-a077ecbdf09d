/**
 * @NApiVersion 2.1
 * @NScriptType MapReduceScript
 * @NAmdConfig /SuiteScripts/config.json
 * @description Checks the Medline 856 folder, creates IF records for any new files
 */

//@ts-ignore
define([
	"N/log",
	"N/error",
	"GetEdiFileContents",
	"GetEdiPartnerValuesLib",
	"ProcessIncoming856Lib",
	"MoveFileLib",
	"ProcessIncoming856EndLib",
	"EdiDataObject",
], function (
	log,
	error,
	getEdiFileContentsLib,
	getEdiPartnerValuesLib,
	processAsnLib,
	moveFileLib,
	processEnd,
	EdiDataObject
) {
	/** @typedef {import("../Classes/vlmd_edi_transaction").EdiDataObject} EdiDataObject*/
	const ediDataObj = new EdiDataObject({
		prodGuidBool: true,
		prodDirectoryBool: true,
		decodeContent: true,
		prodGUID: "8487fcffdf4e47a882ad1841889ea130",
		sandboxGUID: "",
		prodDirectory: "/users/medlineprod/IN/856",
		referenceDirectory: "/EDI Reference Files/Medline/IN/856",
		testDirectory: "",
		documentType: "Tracking",
		documentTypeId: 4,
		purchasingSoftware: "Medline",
		purchasingSoftwareId: 9,
	});

	const partnerValues = getEdiPartnerValuesLib.getMedlineValues();

	if (!partnerValues) {
		throw `Partner values not gotten correctly for ${ediDataObj.purchasingSoftware}.`;
	}

	function getInputData() {
		return getEdiFileContentsLib.getEdiFileContents(ediDataObj).fileContents;
	}

	function map(context) {
		const ediFile = JSON.parse(context.value);

		let {
			errorLog,
			processingLog,
			itemFulfillmentInternalId,
			itemFulfillmentName,
			isTrackingForDropShipOrder,
			subsidiary,
			fileMovedSuccessfully,
		} = processAsnLib.processASN(ediDataObj, partnerValues, ediFile);

		if (!fileMovedSuccessfully) {
			const moveErrorLog = moveFileLib.moveFile(
				ediDataObj,
				ediFile.fileName,
				false //Not processed successfully
			);

			if (moveErrorLog.length > 0) {
				log.error("Error Moving File to Error Folder", moveErrorLog);
				errorLog = errorLog.concat(errorLog, moveErrorLog);
			}
		}

		if (errorLog.length > 0) {
			throw error.create({ name: ediFile?.fileName, message: errorLog.join() });
		}

		let contextMessage = `${subsidiary}, ${
			processingLog.length <= 0 ? "Success" : "Created With Errors"
		},${itemFulfillmentInternalId}, ${itemFulfillmentName}, ${
			processingLog.length > 0 ? processingLog.join(", ") : ""
		}`;

		context.write({
			key: isTrackingForDropShipOrder ? "DropShip Order" : "Warehouse Order",
			value: contextMessage,
		});
	}

	function summarize(context) {
		const ediTransactionRecordObj = processEnd.getEdiTransactionObj(
			ediDataObj,
			context
		);

		if (ediTransactionRecordObj) {
			const ediTransactionRecordId = processEnd.createEdiTransactionRecord(
				ediTransactionRecordObj
			);

			log.debug(
				"EDI Transaction Record",
				ediTransactionRecordId
					? '<a href="https://5802576.app.netsuite.com/app/common/custom/custrecordentry.nl?rectype=707&id=' +
							ediTransactionRecordId +
							'&selectedtab=custom592"> Record Link </a>'
					: `No record created`
			);
		}
	}

	return {
		getInputData,
		map,
		summarize,
	};
});
