/**
 * @description Replace IR and PO number texts as links.
 * 
 * </br><b>Parent Page:</b> poReceiving_custPostingComplete
 * </br><b>Action:</b> poReceiving_custPostingComplete_afterLoadClientFunction
 * 
 * <AUTHOR>
 * @module vlmd_wms_receipt_posted_after_load_cs
 */
(function(){
    let clientFunctions = {};
    clientFunctions.addLinksToMessage = function () {
        try {
            let dataRecord = mobile.getRecordFromState();
            
            // Different message is displayed if map reduce is invoked.
            if(dataRecord.scriptParams.isMapReduceScriptInvoked) return;

            let URLPromiseObj = mobile.callRestlet(
                'customscript_vlmd_wms_get_ir_po_urls', // Script_id
                'customdeploy_vlmd_wms_get_ir_po_urls', // Deployment_id
                {
                    purchaseOrderId: dataRecord.scriptParams.impactedRecords.purchaseorder[0],
                    itemReceiptId: dataRecord.scriptParams.impactedRecords.itemreceipt[0]
                }, // Restlet parameters
                'get' // Http method
            );
            
            URLPromiseObj.then(async (UrlsObj) => {
                let { itemReceiptUrlStr, purchaseOrderUrlStr } = await UrlsObj;                
                let successMessageStr = mobile.getValueFromPage('poReceiving_custPostingComplete_successMessage');
                
                let irRegex = /(#IR[0-9])\w+/g;            
                let itemReceiptNumber = successMessageStr.match(irRegex)[0];
                successMessageStr = successMessageStr.replace(irRegex, `<a href=${itemReceiptUrlStr}><b>${itemReceiptNumber}</b></a>`);

                let poRegex = /(PO[0-9])\w+/g;
                let purchaseOrderNumber = successMessageStr.match(poRegex)[0];
                successMessageStr = successMessageStr.replace(poRegex, `<a href=${purchaseOrderUrlStr}><b>${purchaseOrderNumber}</b></a>`);

                mobile.setValueInPage('poReceiving_custPostingComplete_successMessage', successMessageStr);
            });
        } catch (err) {
            alert(`An error occured.\nPlease take a screenshot of this message and file a support ticket.\n\nParent Page: poReceiving_selectItem_1\nAction: poReceiving_selectItem_afterLoadClientFunction\nError Message: ${err.message}`);
        }
    }

    return clientFunctions;
} ());