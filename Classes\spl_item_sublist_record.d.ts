/**
 * Interface and type definitions for Item Sublist Record class
 *
 * <AUTHOR>
 */

import {Record} from "@hitc/netsuite-types/N/record";

export interface ItemFulfillmentItemSublistRecord {
	Column: {[key: string]: string};
	Field: {[key: string]: string};
	Sublist: {[key: string]: string};
	itemId: string;
	itemName: string;
	itemIsShipByTruck: boolean;
}

export interface CustomItemSublistRecord extends ItemSublistRecord {
	Column: {[key: string]: string};
	getMinimumMarkupPercentage: () => string;
	getMinimumMarkupAmount: () => number;

	getMaximumMarkupAmount: () => number;
	dontDropShip?: boolean;
	dontUseItemInNewTransactions?: boolean;
	isFinalSaleItem: boolean;
	itemLookup: object;
	noMinimumMarkupAlert?: boolean;

	noMaximumMarkupAlert?: boolean;
	priceGroup?: string;
	vendorId: string;
}

/** Transaction line item record */
export interface ItemSublistRecord {
	Field: {[key: string]: string};
	Sublist: {[key: string]: string};
	getSublistText: (SublistParameters) => string;
	getSublistValue: (SublistParameters) => any;
	isUsingBasePrice: () => boolean;
	isGloveNotUsingCorrectRate: () => boolean;

	isPpeGloveItem: () => boolean;

	shouldChangeMattressVendorToDrive: () => boolean;
	hasCorrectIncrementsFor400879: () => boolean;
	hasCorrectIncrementsForMDTIU7SEFBLU: () => boolean;
	hasCorrectIncrementsForMDTTB4C22WHIR: () => boolean;
	costEstimateType: string;
	itemCost: number;
	itemId: string;
	itemName: string;
	itemType: string;
	priceLevel: string;
	quantity: number;
	rate: number;
	vendorId: string
}

export interface CustomItemSublistRecord extends ItemSublistRecord {
	Column: {[key: string]: string};
	dontDropShip?: boolean;
	dontUseItemInNewTransactions?: boolean;
	isFinalSaleItem: boolean;
	itemLookup: object;
	noMinimumMarkupAlert?: boolean;
	priceGroup?: string;
}

export interface SalesOrderItemSublistRecord extends CustomItemSublistRecord {
	isDropShipItemNotUsingPurchaseOrderRate: () => boolean;
	isItemNotUsingLastPurchasePrice: () => boolean;
	isNotDropShipAndNoLocation: () => boolean;
	isNotForDropShip: () => boolean;
	isDropShipItemUsingWrongLocation: () => boolean;
	shouldDropShipOverBedTable: () => boolean;
	shouldDropShipLowAirLossMattress: () => boolean;
	isLiftGateAlertRequired: () => boolean;
	shouldChangeShippingToMedlineFreight: () => boolean;
	shouldChangeShippingToMedlineFlatRate: () => boolean;
	shouldDisplayOrderOnlineProcessingAlert: () => boolean;
	createPO: string;
	location: string;
	needsImmediateEtaUpdate: boolean;
	poVendor: string;
	shipAccount: string;
}

export type SublistParameters {
	netsuiteRecord: Record;
	sublistId: string;
	fieldId: string;
	line: number;
}