/**
 * @NApiVersion 2.1
 * @NScriptType MapReduceScript
 * @NAmdConfig /SuiteScripts/config.json
 */

//@ts-ignore
define(["N/log", "N/error", "N/record", "N/file", "N/search"], function (
	log,
	error,
	record,
	file,
	search
) {
	const fileToUploadId = 2976568; //The "UpdateFile.csv" in the "Programming Folder" (2306503) in the documents library.
	//Upload the file that you want to use to this file (overwrite the old one), so that the internal id will be the same.

	function getInputData(context) {
		try {
			const uploadedFile = getFile();
			const fileLinesArr = mapFileLinesToArr();

			return fileLinesArr;

			function getFile() {
				return file.load({
					id: fileToUploadId,
				});
			}

			function mapFileLinesToArr() {
				const fileLinesArr = [];
				var iterator = uploadedFile.lines.iterator();
				iterator.each(function () {
					return false;
				}); //Skips the first line, which is the header

				iterator.each(function (line) {
					fileLinesArr.push(line.value);
					return true;
				});

				return fileLinesArr;
			}
		} catch (e) {
			throw error.create({
				name: `Error at Get Input Data`,
				message: e,
			});
		}
	}
	function map(context) {
		try {
			const parsedResultArr = context.value.split(",");

			var updateObj = {
				transactionName: parsedResultArr[0],
				internalId: parsedResultArr[1],
				itemName: parsedResultArr[2],
				itemRate: parsedResultArr[3],
				vendorCost: parsedResultArr[4],
			};

			const { transactionName, internalId, itemName, itemRate, vendorCost } =
				updateObj;

			const transactionRecord = loadTransaction();
			const lineNumberToUpdate = getLineNumber();
			updateField();
			transactionRecord.save();

			context.write(
				`${transactionName} - ${internalId} - ${itemName}`,
				`Updated Item Rate: ${itemRate}, Updated Vendor Cost: ${vendorCost}, Line Number: ${lineNumberToUpdate}`
			);

			function loadTransaction() {
				return record.load({
					type: record.Type.SALES_ORDER,
					id: internalId,
				});
			}

			function getLineNumber() {
				return transactionRecord.findSublistLineWithValue({
					sublistId: "item",
					fieldId: "item_display",
					value: itemName,
				});
			}

			function updateField() {
				transactionRecord.setSublistValue({
					sublistId: "item",
					fieldId: "rate",
					line: lineNumberToUpdate,
					value: itemRate,
				});

				// transactionRecord.setSublistValue({
				// 	sublistId: "item",
				// 	fieldId: "costestimaterate", //'porate',
				// 	line: lineNumberToUpdate,
				// 	value: vendorCost,
				// });

				// transactionRecord.setSublistValue({
				// 	sublistId: "item",
				// 	fieldId: "costestimatetype",
				// 	line: lineNumberToUpdate,
				// 	value: "CUSTOM",
				// });
			}
		} catch (e) {
			throw error.create({
				name: "Map Error",
				message: e,
			});
		}
	}

	function summarize(context) {
		try {
			logSuccess();
			logErrors();

			function logSuccess() {
				const resultsLog = [];
				context.output.iterator().each(function (key, value) {
					resultsLog.push({ documentInfo: key, valueChanges: value });
					return true;
				});

				if (resultsLog.length > 0) {
					let summaryText = "";
					resultsLog.forEach((result) => {
						var parsedResults = JSON.stringify(result);
						summaryText += `${parsedResults}`;

						return true;
					});

					log.debug("Transactions Processed Successfully", summaryText);
				}
			}

			function logErrors() {
				const errorsLog = [];

				context.mapSummary.errors.iterator().each(function (key, value) {
					errorsLog.push(`Error Processing Updates for ${key} - ${value}`);

					return true;
				});

				if (errorsLog.length > 0) {
					let summaryText = "";
					errorsLog.forEach((result) => {
						var parsedResults = result;
						summaryText += `${parsedResults}`;

						return true;
					});

					log.error("Errors Processing Transactions", summaryText);
				}
			}
		} catch (e) {
			log.error("Error Processing Summarize", e);
		}
	}

	return {
		getInputData: getInputData,
		map: map,
		summarize: summarize,
	};
});
