/**
 * @description Create a delta record for each item that is a price change/new price in the price file
 *
 * </br><b>Schedule:</b> Called by the script that handles price changes on a customer level
 * 
 * @NApiVersion 2.1
 * @NScriptType MapReduceScript
 * @NAmdConfig /SuiteScripts/config.json
 * <AUTHOR>
 */

define([
  "require",
  "N/log",
  "N/runtime",
  "N/record",
  "N/file",
  "../../Classes/vlmd_custom_error_object",
  "../../Classes/vlmd_mr_summary_handling",
], (/** @type {any} */ require) => {
  const log = require("N/log");
  const runtime = require("N/runtime");
  const record = require("N/record");
  const file = require("N/file");

  /** @type {import("../../Classes/vlmd_custom_error_object").CustomErrorObject} */
  const CustomErrorObject = require("../../Classes/vlmd_custom_error_object");

  /**
   * Return all lines in the price list file
   *
   * @returns {string}
   */
  function getInputData() {
    const customError = new CustomErrorObject();
    try {
      log.debug("Creating customer level price deltas...");
      const currentScript = runtime.getCurrentScript();

      const newPriceListFileId = currentScript.getParameter({
        name: "custscript_new_price_list_file_id",
      });

      if (!newPriceListFileId) {
        throw customError.updateError({
          errorType: customError.ErrorTypes.MISSING_PARAMETER,
          summary: "MISSING_FILE_ID",
          details: `No file id passed in from MR parameter`,
        });
      }

      return file.load(newPriceListFileId);
    } catch (err) {
      customError.throwError({
        summaryText: "GET_INPUT_DATA",
        error: err,
      });
    }
  }

  /**
   * Iterate over each row in the price file and create a delta record if the price is a new price.
   *
   * @param {import("N/types").EntryPoints.MapReduce.mapContext} context Map context
   * @returns {void}
   */
  function reduce(context) {
    const customError = new CustomErrorObject();
    const currentScript = runtime.getCurrentScript();

    try {
      const priceFileRecordId = currentScript.getParameter({
        name: "custscript_price_file_record_id",
      });

      const parsedItemRow = context.values[0].split(",");

      let isNewPrice = parsedItemRow[6] === "true";

      if (!isNewPrice) {
        return;
      }

      let rowNumber = parseInt(parsedItemRow[0]);

      if (isNaN(rowNumber) || rowNumber === "rownumber") {
        return; // This is the header row, skip processing
      }

      let itemId = parsedItemRow[1];
      let price = parseFloat(parsedItemRow[5]);
      let priceType = parsedItemRow[3];

      const subRecord = record.create({
        type: "customrecord_dlta_prc_per_itm_per_cstmr",
      });

      subRecord.setValue({
        fieldId: "custrecord_customer_pricing_files_record",
        value: priceFileRecordId,
      });

      subRecord.setValue({
        fieldId: "custrecord_item",
        value: itemId,
      });

      subRecord.setValue({
        fieldId: "custrecord_price",
        value: price,
      });

      subRecord.setValue({
        fieldId: "custrecord_price_type",
        value: priceType,
      });

      let subRecordId = subRecord.save();

      context.write(priceFileRecordId, subRecordId);
    } catch (err) {
      customError.throwError({
        summaryText: "MAP_ERROR",
        error: err,
        recordType: "PRICE",
        errorWillBeGrouped: true,
      });
    }
  }

  /**
   * Log summary results
   *
   * @param {import("N/types").EntryPoints.MapReduce.summarizeContext} context Summarize context
   * @returns {void}
   */
  function summarize(context) {
    /**@typedef {import("../../Classes/vlmd_mr_summary_handling")}*/
    const StageHandling = require("../../Classes/vlmd_mr_summary_handling");
    const stageHandling = new StageHandling(context);

    stageHandling.printErrors({
      groupErrors: true,
    });

    stageHandling.printScriptProcessingSummary();

    const currentScript = runtime.getCurrentScript();

    const priceFileRecordId = currentScript.getParameter({
      name: "custscript_price_file_record_id",
    });

    const parentCustomerId = currentScript.getParameter({
      name: "custscript_parent_customer_id",
    });

    log.audit(
      `${parentCustomerId} | Customer Pricing Files Record Created`,
      `Record ID: ${priceFileRecordId}\nRecord Link: https://5802576.app.netsuite.com/app/common/custom/custrecordentry.nl?rectype=3223&id=${priceFileRecordId}`
    );
  }

  return {
    getInputData,
    reduce,
    summarize,
  };
});
