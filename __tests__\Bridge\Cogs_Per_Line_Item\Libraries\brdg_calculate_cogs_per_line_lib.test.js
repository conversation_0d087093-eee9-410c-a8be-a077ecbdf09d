//@ts-nocheck

import log from "N/log";
import record from "N/record";
import query from "N/query";
import error from "N/error";

import brdg_calculate_cogs_per_line_lib from "../../../../Bridge/Cogs_Per_Line_Item/Libraries/brdg_calculate_cogs_per_line_lib";
import CustomErrorObject from "../../../../Classes/vlmd_custom_error_object";

beforeEach(() => {
	jest.resetModules();
	jest.resetAllMocks();
	jest.clearAllMocks();
});

const mockErrorObject = {
	updateError: () => {},
}

describe("brdg_calculate_cogs_per_line_lib", () => {
	beforeEach(() => {
		jest.spyOn(query, "runSuiteQL").mockReturnValue({
			asMappedResults: () => [
				{ account: "INCOME", amount: 1 },
				{ account: "ASSET", amount: 2 },
				{ account: "COGS", amount: 3 },
				{ account: "INCOME", amount: 1 },
				{ account: "ASSET", amount: 2 },
				{ account: "COGS", amount: 3 },
				{ account: "DISCOUNT", amount: 10 },
				{ account: "INCOME", amount: 1 },
				{ account: "", amount: 100 },
				{ account: "ASSET", amount: 2 },
				{ account: "COGS", amount: 3 },
				{ account: "", amount: 100 },
				{ account: "ASSET", amount: 2 },
				{ account: "COGS", amount: 3 },
				{ account: "INCOME", amount: 1 },
				{ account: "ASSET", amount: 2 },
				{ account: "COGS", amount: 3 },
				{ account: "INCOME", amount: 1 },
				{ account: "ASSET", amount: 2 },
				{ account: "COGS", amount: 3 },
			],
		});
	});
	describe("setAllCogsPerItem", () => {
		const setSublistValue = jest.fn();
		const getSublistValue = ({ fieldId }) => {
			switch (fieldId) {
				case "itemtype":
					return "InvtPart";
				default:
					return "Discount";
			}
		};
		describe("InvPart", () => {
			beforeEach(() => {
				jest.spyOn(record, "load").mockReturnValue({
					getValue: jest.fn().mockReturnValue(199),
					setValue: jest.fn(),
					getLineCount: () => 5,
					getSublistValue,
					save: () => {},
					getSublistFields: jest.fn().mockReturnValue(["custcol_actual_item_cogs"]),
					setSublistValue,
				});
				brdg_calculate_cogs_per_line_lib.setAllCogsPerItem(
					"tranId",
					"cashsale",
					mockErrorObject
				);
			});
			it("loads the transaction", () => {
				expect(record.load).toHaveBeenCalledWith({
					id: "tranId",
					type: "cashsale",
				});
			});
			it("loads the transaction", () => {
				expect(query.runSuiteQL).toHaveBeenCalledWith({
					query: brdg_calculate_cogs_per_line_lib.getCogsQuery("tranId"),
				});
			});
			it("sets the COGS line item field", () => {
				expect(setSublistValue).toHaveBeenNthCalledWith(1, {
					fieldId: "custcol_actual_item_cogs",
					line: 0,
					sublistId: "item",
					value: 3,
				});
				expect(setSublistValue).toHaveBeenNthCalledWith(2, {
					fieldId: "custcol_actual_item_cogs",
					line: 1,
					sublistId: "item",
					value: 3,
				});
				expect(setSublistValue).toHaveBeenNthCalledWith(3, {
					fieldId: "custcol_actual_item_cogs",
					line: 2,
					sublistId: "item",
					value: 6,
				});
				expect(setSublistValue).toHaveBeenNthCalledWith(4, {
					fieldId: "custcol_actual_item_cogs",
					line: 3,
					sublistId: "item",
					value: 3,
				});
				expect(setSublistValue).toHaveBeenNthCalledWith(5, {
					fieldId: "custcol_actual_item_cogs",
					line: 4,
					sublistId: "item",
					value: 3,
				});
			});
		});
		describe("NonInvPart", () => {
			beforeEach(() => {
				jest.spyOn(record, "load").mockReturnValue({
					getValue: jest.fn().mockReturnValue(199),
					getLineCount: () => 5,
					getSublistValue: () => "NonInvPart",
					save: () => {},
					getSublistFields: jest.fn().mockReturnValue(["custcol_actual_item_cogs"]),
					setSublistValue,
				});
				brdg_calculate_cogs_per_line_lib.setAllCogsPerItem(
					"tranId",
					"cashsale",
					mockErrorObject
				);
			});
			it("skips lines that are not InvPart or Kit", () => {
				expect(setSublistValue).not.toHaveBeenCalled();
			});
		});

		describe("Mismatching lines and query results", () => {
			const customErrorObject = new CustomErrorObject();
			const lineCount = 6;
			beforeEach(() => {

				jest.spyOn(log, "error").mockImplementation(() => {});
				jest.spyOn(error, "create").mockImplementation((param) => param);

				jest.spyOn(record, "load").mockReturnValue({
					getValue: jest.fn().mockReturnValue(199),
					getLineCount: () => lineCount, // line count is greater than query result size
					getSublistValue,
					save: () => {},
					getSublistFields: jest.fn().mockReturnValue(["someColumn"]),
					setSublistValue,
				});
			});
			it("throws an error", () => {
				try {
					brdg_calculate_cogs_per_line_lib.setAllCogsPerItem(
						"tranId",
						"cashsale",
						customErrorObject
					);
					throw "NOT_THIS_ERROR"
				} catch (err) {
					expect(err).not.toEqual("NOT_THIS_ERROR");
				}
			});
		});
	});
});
