/**
 * @description EDI File Interface
 *
 * <AUTHOR> <<EMAIL>>
 */

import { CustomErrorObject } from "../../../../../Classes/vlmd_custom_error_object";
import { EDIServerInterface } from "../Server/edi_server";

export interface EDIFileInterface {
    /** Custom Error Object assigned to the instance */
    customError: CustomErrorObject | null;
    /** EDI File Type Text */
    ediType?: string;
    /** Parser class for incoming file containing decorator-specific functions */
    parser: any;
    /** Processor class for outgoing file containing decorator-specific functions */
    processor: any;
    /** Server Instance */
    server: EDIServerInterface | null;
    /** Record Type */
    type: string;
    /** ID assigned to the Record Type */
    typeId: number;
    /** Reset the Custom Error Object */
    resetCustomError?(): void;
    /** Create a NetSuite EDI Transaction record */
    createEDITransactionRecord?(data: EDITransactionData): void;
}

export type EDIPostProcessEmail = {
    /** Email Subject */
    subject: string;
    /** Email Body */
    body: string;
    /** Message Recipients */
    recipients: string[];
    /** Message CC List */
    cc: string[];
    /** Log Title */
    logTitle: string;
    /** Processing Status ID */
    processingStatus: number;
    /** Consolidated Error Messages */
    errorText: string;
    /** Document Control Numbers */
    documentControlNumbers: string[];
};

export type EDITransactionData = {
    /** Email Object */
    email: EDIPostProcessEmail;
    /** Transaction Control Number */
    transactionControlNumber: string;
    /** PO Reference Number */
    purchaseOrderReferenceNumber: string;
    /** Purchasing Software Internal ID */
    purchasingSoftwareId: string;
    /** Vendor Internal ID */
    vendorInternalId: string;

}