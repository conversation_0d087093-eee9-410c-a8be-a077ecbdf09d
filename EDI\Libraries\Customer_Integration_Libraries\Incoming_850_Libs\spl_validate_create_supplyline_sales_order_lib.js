/**
 * @NApiVersion 2.1
 */

define([
	"N/log",
	"N/search",
	"GetEdiIntegrationsLib",
	"AddItemInternalIds",
	"CompareAddressLib",
	"LoDash",
], function (
	log,
	search,
	getEdiIntegrationsLib,
	addItemInternalIdsLib,
	compareAddressesLib,
	_
) {
	function validateDataAndGetMissingInfo(
		purchaseOrderObj,
		purchasingSoftware,
		integrationAccountNumber
	) {
		function processFail(logMessage, programmingError) {
			createSalesOrder = false;
			errorLog.push({ logMessage, programmingError });

			//This information is needed to move the file to the correct folder on the SFTP server
			purchaseOrderObj.parentObj = {
				name: purchasingSoftware,
				externalId: integrationAccountNumber
					? integrationAccountNumber
					: "Incoming_850_Processed_With_Errors",
			};

			throw "Invalid purchase order, don't create sales order";
		}

		const validateCustomerDataFunctions = (function () {
			function validateCustomerIdIsNotNull(customerIdToVerify) {
				if (!customerIdToVerify) {
					processFail(`No customer ID was found for this order.`);
				}
			}

			function validateCustomerIdIsInteger(customerIdToVerify) {
				const parsedIdAsInt = parseInt(customerIdToVerify);

				if (!parsedIdAsInt && parsedIdAsInt != 0) {
					processFail(
						`Error validating customer. The customer's account number, ${customerIdToVerify}, was not sent as an integer. 
						Please reach out to the customer to update the account number.`
					);
				}
			}

			function validateIdIsNotZero(customerIdToVerify) {
				if (customerIdToVerify == 0) {
					processFail(
						"Error validating customer. The customer's account number was sent as zero. Please reach out to the customer to update their account number."
					);
				}
			}

			function validateCustExternalIdSentIsInNetsuite(customerExternalId) {
				const searchObj = search.create({
					type: "customer",
					filters: [
						["subsidiary", "anyof", "1"],
						"AND",
						["entityid", "is", customerExternalId],
					],
				});

				const intForExtIdArr = searchObj.run().getRange({
					start: 0,
					end: 1,
				});

				if (intForExtIdArr.length <= 0) {
					processFail(
						`Customer account number (external ID), ${customerExternalId}, is not found in NetSuite.`
					);
				}

				return intForExtIdArr[0].id;
			}

			function validateCustomersAddress(
				customerInternalId,
				customerIdToVerify,
				streetAddressOnPo
			) {
				if (!streetAddressOnPo) {
					return;
				}

				//Validate customer has address in NetSuite.
				const searchObj = search.create({
					type: "customer",
					filters: [["internalid", "anyof", customerInternalId]],
					columns: ["address1"],
				});

				const addressInNetSuiteArr = [];

				searchObj.run().each(function (result) {
					addressInNetSuiteArr.push(
						result.getValue({
							name: "address1",
						})
					);

					return true;
				});

				if (addressInNetSuiteArr.length <= 0) {
					processFail(
						`No address was found in NetSuite for ${customerIdToVerify}. Please confirm that this is the correct account number (external ID) for this customer.`
					);
				}

				//Validate address sent on PO matches customer's address in NetSuite.
				const addressMatchFound = addressInNetSuiteArr.some(
					(addressInNetSuite) => {
						var addressIsEqual = compareAddressesLib.compareAddresses(
							addressInNetSuite,
							streetAddressOnPo
						);

						return addressIsEqual;
					}
				);

				if (!addressMatchFound) {
					processFail(
						`The customer's address sent doesn't match the address in NetSuite. 
							The address that we have in NetSuite is ${addressInNetSuiteArr}.
							The address that the customer sent is ${streetAddressOnPo}.
							Please confirm that ${customerIdToVerify} is the correct account number (external ID) for this customer.`
					);
				}
			}

			function validateCustomersParentIsEdiIntegrated(parentObj) {
				if (!parentObj.isIntegratedEdiParent) {
					processFail(
						`The parent that was pulled for this customer, ${parentObj.name}, is not an EDI integrated customer. 
						Please confirm that this customer is under the correct parent in NetSuite.`
					);
				}
			}

			return {
				validateCustomerIdIsNotNull,
				validateCustomerIdIsInteger,
				validateIdIsNotZero,
				validateCustExternalIdSentIsInNetsuite,
				validateCustomersAddress,
				validateCustomersParentIsEdiIntegrated,
			};
		})();

		const validateOrderDataFunctions = (function () {
			function validateSalesOrderNotDuplicateForParent(poNumber, parentObj) {
				const searchObj = search.create({
					type: "salesorder",
					filters: [
						["type", "anyof", "SalesOrd"],
						"AND",
						["mainline", "is", "T"],
						"AND",
						["otherrefnum", "equalto", poNumber],
						"AND",
						["customersubof", "anyof", parentObj.internalId],
					],
					columns: [search.createColumn("tranid")],
				});

				const salesOrderArr = searchObj.run().getRange({
					start: 0,
					end: 1,
				});

				if (salesOrderArr.length > 0) {
					const existingSalesOrder = salesOrderArr[0].getValue(
						salesOrderArr[0].columns[0]
					);

					processFail(
						`${existingSalesOrder} has already been created for PO# ${poNumber} under ${parentObj.name}. 
						Please compare the second purchase order submitted with the first purchase order to check for any changes and check with the customer if corrections to the sales order are needed.`
					);
				}
			}

			function validateItemsHaveInternalIds(items) {
				items.forEach((item) => {
					if (!item.internalId) {
						processFail(
							`${item.itemName} is an incorrect item name, is marked "Don't use in new transaction", or is not in NetSuite and was not added to the sales order.`
						);
					}
				});
			}

			function validateAtLeastOneLineItemToAdd(items) {
				const hasItemWithInternalId = items.some(function (item) {
					return item.internalId;
				});

				if (!hasItemWithInternalId) {
					errorLog.push({
						logMessage: "No valid line items to create sales order.",
					});
				}
			}

			return {
				validateSalesOrderNotDuplicateForParent,
				validateItemsHaveInternalIds,
				validateAtLeastOneLineItemToAdd,
			};
		})();

		var getDataHelperFunctions = (function () {
			function getCustomerInternalId(customerExternalId) {
				/*Search for the internal ID using the external ID*/

				try {
					var searchObj = search.create({
						type: "customer",
						filters: [["entityid", "is", customerExternalId]],
					});

					var searchResults = searchObj.run().getRange({
						start: 0,
						end: 10,
					});

					if (!searchResults[0]) {
						throw { message: `No customer internal id found` };
					}

					return searchResults[0].id;
				} catch (e) {
					throw e.message;
				}
			}

			function getParentObj(customerInternalId) {
				try {
					const searchObj = search.lookupFields({
						type: search.Type.CUSTOMER,
						id: customerInternalId,
						columns: ["parent"],
					});

					const parentInternalId = searchObj.parent[0].value;
					const parentName = searchObj.parent[0].text;

					const parentSearchObj = search.lookupFields({
						type: search.Type.CUSTOMER,
						id: parentInternalId,
						columns: ["entityid", "custentity_spl_intgrtd_edi_prnt_cstmr"],
					});

					return {
						internalId: parentInternalId,
						name: parentName,
						externalId: parentSearchObj.entityid,
						isIntegratedEdiParent:
							parentSearchObj["custentity_spl_intgrtd_edi_prnt_cstmr"],
					};
				} catch (e) {
					processFail(`Error Getting Parent Object: ${e}`, true);
				}
			}

			function getIntegrationAccountNumber(parentInternalId) {
				try {
					if (!integrationAccountNumber) {
						integrationAccountNumber =
							getEdiIntegrationsLib.getIntegrationAccountNumberForParent(
								parentInternalId
							);
					}

					return integrationAccountNumber;
				} catch (e) {
					log.error("Error", e);
				}
			}

			function addItemInternalIds(items) {
				try {
					var itemsWithIdsArr = addItemInternalIdsLib.addItemInternalIds(
						items,
						1 //Supplyline
					);
					if (itemsWithIdsArr.length > 0) {
						items = itemsWithIdsArr;
					}
				} catch (e) {
					processFail(`Item internal IDs not gotten.${e}`, true);
				}
			}

			function addItemLocations(items, address, parentInternalId) {
				items
					.filter((item) => item.internalId)
					.forEach((item) => {
						//Set PPE and MR items to Union
						const itemObj = search.lookupFields({
							type: "item",
							id: item.internalId,
							columns: ["type"],
						});

						const isInventoryItem = itemObj.type[0].value == "InvtPart";

						if (
							isInventoryItem &&
							(item.itemName.startsWith("PPE.") ||
								item.itemName.startsWith("MR."))
						) {
							item.location = 9; //Union Warehouse
						}

						//Set gloves to Infinite (Agora) customers in FL as dropship
						if (
							parentInternalId == 4302 &&
							(item.internalId == 4847 ||
								item.internalId == 6756 ||
								item.internalId == 4849 ||
								item.internalId == 6755) &&
							address.state == "FL"
						) {
							//Infinite Care and PPE.066-PPE.069 (Gloves: XL,L,M,S) and shipping to FL
							errorLog.push({
								logMessage:
									"This order contains gloves that should be placed as a drop ship order. Please handle accordingly",
								programmingError: false,
							});

							item.location = 4; //Drop Ship Location
							item.dropShip = true;
							item.vendorId = 9109; //SMX Ventures
						}
					});
			}

			return {
				getCustomerInternalId,
				getParentObj,
				getIntegrationAccountNumber,
				addItemInternalIds,
				addItemLocations,
			};
		})();

		const errorLog = [];
		let createSalesOrder = true;

		try {
			let {
				streetAddress,
				items,
				customerExternalId,
				poNumber,
				address,
			} = purchaseOrderObj;

			const customerIdToVerify = customerExternalId;

			validateCustomerDataFunctions.validateCustomerIdIsNotNull(
				customerIdToVerify
			);

			validateCustomerDataFunctions.validateCustomerIdIsInteger(
				customerIdToVerify
			);

			validateCustomerDataFunctions.validateIdIsNotZero(customerIdToVerify);

			let customerInternalId =
				validateCustomerDataFunctions.validateCustExternalIdSentIsInNetsuite(
					purchaseOrderObj.customerExternalId
				);

			purchaseOrderObj.customerInternalId = customerInternalId;

			//If the wrong account # for this customer was sent but the # is a valid ID in NS,
			//the validations would pass and an order would be created for the wrong
			//customer -> validate that the address sent matches the customer.
			validateCustomerDataFunctions.validateCustomersAddress(
				customerInternalId,
				customerIdToVerify,
				streetAddress
			);

			purchaseOrderObj.parentObj =
				getDataHelperFunctions.getParentObj(customerInternalId);

			//Get the integration account # if missing so that can move the file to the
			//correct EDI reference file folder
			integrationAccountNumber =
				getDataHelperFunctions.getIntegrationAccountNumber(
					purchaseOrderObj.parentObj.internalId
				);

			validateCustomerDataFunctions.validateCustomersParentIsEdiIntegrated(
				purchaseOrderObj.parentObj
			);

			validateOrderDataFunctions.validateSalesOrderNotDuplicateForParent(
				poNumber,
				purchaseOrderObj.parentObj
			);

			getDataHelperFunctions.addItemInternalIds(items);

			validateOrderDataFunctions.validateItemsHaveInternalIds(items);
			validateOrderDataFunctions.validateAtLeastOneLineItemToAdd(items);

			getDataHelperFunctions.addItemLocations(
				items,
				address,
				purchaseOrderObj.parentObj.internalId
			);
		} catch (e) {
			if (e != "Invalid purchase order, don't create sales order") {
				//Is an error processing script.
				processFail(e, true);
			}
		}

		return {
			errorLog,
			createSalesOrder,
			purchaseOrderObj,
			integrationAccountNumber,
		};
	}

	return {
		validateDataAndGetMissingInfo,
	};
});
