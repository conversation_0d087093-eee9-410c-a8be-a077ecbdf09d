/**
 * Interface and type definitions for Transaction Object class
 *
 * <AUTHOR>
 */

/** Contains the generic properties of an address */
export interface Address {
    address1: string|null
    address2: string|null
    addressee: string|null
    city: string|null
    state: string|null
    streetAddress: string|null
    zip: string|null
}

/** Contains the contact information and communication methodology */
export interface CommunicationObject {
    communicationContact: string|null
    communicationMethod: string|null
    contactNumber: string|null
}

/** Contains properties for customer */
export interface Customer {
    customerAccountNumber: number|null;
    customerInternalId: number|null;
    customerName: string|null
    customerNameInTheirSystem: string|null
}

/** Represents the item object that is specified in the transaction documents */
export interface Item {
    color: string|null;
    internalId: string|null;
    itemName: string|null;
    pack: string|null;
    quantity: number|null;
    size: number|null;
    uom: string|null;
}

/** Generic name object */
export interface Name {
    firstName: string|null
    lastName: string|null
    middleName: string|null
}

/** For tracking the parent of the transaction */
export interface ParentObject {
    internalId: number|null;
    isIntegratedEdiParent: boolean;
    name: string|null
}

/** Revival-specific integration's patient information */
export interface RevivalPatient {
    address: Address;
    name: Name;
    phoneNumber: string|null
    revivalId: string|null
}

/** Represents the transaction parsed from received documents */
export interface TransactionObject extends Customer {
    acknowledgementCode: string|null
    billingAddress: Address|null;
    communicationObj: CommunicationObject|null;
    gln: string|null
    items: Item[]|null;
    memo: string|null
    mustArriveByDate: Date|null;
    orderDate: Date|null;
    parentObj: ParentObject|null;
    poDate: Date|null;
    poNumber: number|null;
    shipOnDate: Date|null;
    shippingAddress: string|null
    shippingAddressObj: Address|null;
    streetAddress: string|null
    totalAmount: number|null;
    transactionControlNumber: number|null;
    getBasicAddress: () => {[key: string]: string|undefined};
    trimItemsStringProperties: () => void;
    trimStringProperties: () => void;
}

/** Walmart-specific integration's item properties */
export interface WalmartItem extends Item {
    walmartGtin: string|null
    walmartItemName: string|null
    walmartRate: number|null;
    walmartTotal: number|null;
}