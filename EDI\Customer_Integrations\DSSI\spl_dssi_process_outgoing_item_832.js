/**
 * @NApiVersion 2.1
 * @NScriptType scheduledscript
 * @NAmdConfig /SuiteScripts/config.json
 * @param {Array} results
 * @param {Array} slice - for paginated search results
 * @param {Number}resultRowNumber
 * @HBoxer
 */

//@ts-ignore
define([
	"require",
	"N/log",
	"../../../EDI/Libraries/Process_EDI_File/spl_process_outgoing_832_item_lib",
], function (require) {
	function execute(context) {
		/**@type {import ('../../../EDI/Libraries/Process_EDI_File/spl_process_outgoing_832_item_lib')} */
		const processCatalogLib = require("../../../EDI/Libraries/Process_EDI_File/spl_process_outgoing_832_item_lib");

		try {
			const dataObj = {
				prodGuidBool: true,
				prodDirectoryBool: true,
				prodGUID: "43524fe6d98d419d8b293e05e1a831c5",
				sandboxGUID: "********************************",
				prodDirectory: "/users/DSSI/OUT/832",
				testDirectory: "/users/DSSI/Test",
				transactionType: "Item Catalog",
				purchasingSoftware: "DSSI",
			};

			processCatalogLib.processCatalog(dataObj, 1474); //DSSI Item Catalog
		} catch (e) {
			throw e;
		}
	}

	return {
		execute,
	};
});
