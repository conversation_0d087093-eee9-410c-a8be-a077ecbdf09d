/**
 * @NApiVersion 2.1
 */

define(["N/log", "N/search", "N/query"], function (log, search, query) {
  function getInternalIds(
    customerParentId,
    searchId,
    startDate,
    endDate,
    documentNumbers,
    customErrorObject
  ) {
    var internalIdsArr = [];

    try {
      if (searchId) {
        const invoiceSearch = search.load({
          id: searchId,
        });

        const pagedData = invoiceSearch.runPaged({
          pageSize: 1000,
        });

        pagedData.pageRanges.forEach((pageRange) => {
          const pageResults = pagedData.fetch({
            index: pageRange.index,
          });

          pageResults.data.forEach((result) => {
            internalIdsArr.push({
              transactionId: result.id,
              transactionType: result.getText("type"),
            });
          });
        });

        log.audit(
          "Get 810 Internal Ids: Saved Search",
          `Search ID: ${searchId}`
        );
      } else if (documentNumbers) {
        const transactionQuery = `SELECT
				inv.ID,
					BUILTIN.DF( inv.Type ) AS Type,
				FROM
					Transaction inv 
				JOIN
					customer c 
					ON inv.entity = c.id
        JOIN
				   customer p 
				   ON c.parent = p.id 
				JOIN
				   customrecord_vlmd_edi_integration edi 
				   ON p.custentity_spl_edi_integration_record = edi.id 
				WHERE
				inv.TranID IN 
					(
					(${documentNumbers})
					)
				AND
          (p.id = ${customerParentId} OR c.id = ${customerParentId} or edi.custrecord_spl_prchsng_fclty = ${customerParentId})
					`;
        try {
          const resultIterator = query
            .runSuiteQL({
              query: transactionQuery,
            })
            .asMappedResults();

          resultIterator.forEach(function (result) {
            internalIdsArr.push({
              transactionId: result.id,
              transactionType: result.type,
            });

            return true;
          });

          log.audit(
            "Get 810 Internal Ids: Document Numbers",
            `Document Numbers: ${documentNumbers}\n\nQuery: ${transactionQuery}`
          );
        } catch (e) {
          throw `${e}: ${transactionQuery}`;
        }
      } else {
        const transactionQuery = `SELECT
				inv.id,
				inv.type 
			 FROM
				transaction inv 
				JOIN
				   customer c 
				   ON inv.entity = c.id 
				JOIN
				   customer p 
				   ON c.parent = p.id 
				JOIN
				   customrecord_vlmd_edi_integration edi 
				   ON p.custentity_spl_edi_integration_record = edi.id 
			 WHERE
				(
				   inv.type = 'CustInvc' 
				   OR inv.type = 'CustCred' 
				)
				AND inv.custbody_spl_inv_edi_tran_cntrl_nmbr IS NULL 
				AND c.custentity_spl_exclude_from_edi_intgrtn = 'F' 
				AND (p.id = ${customerParentId} OR c.id = ${customerParentId} or edi.custrecord_spl_prchsng_fclty = ${customerParentId})
				AND ${
          startDate && endDate
            ? "TO_DATE( inv.createddate, 'MM/DD/YYYY' ) BETWEEN TO_DATE('" +
              startDate +
              "', 'MM/DD/YYYY' ) AND TO_DATE('" +
              endDate +
              "', 'MM/DD/YYYY')"
            : "TO_DATE (inv.createddate , 'MM/DD/YYYY') >= TO_DATE ('01/01/2025', 'MM/DD/YYYY')"
        } 
				AND inv.createddate > = edi.custrecord_edi_intgrtn_start_date
				AND inv.createddate < SYSDATE
				AND NOT EXISTS (
          SELECT 1 
           FROM customrecord_edi_dcn_doc_ctrl_num dcn
          WHERE dcn.custrecord_edi_dcn_doc_num = inv.tranid 
            AND dcn.custrecord_edi_dcn_doc_num IS NOT NULL
            AND dcn.isinactive = 'F'
        )`;

        const resultIterator = query
          .runSuiteQL({
            query: transactionQuery,
          })
          .asMappedResults();

        resultIterator.forEach(function (result) {
          internalIdsArr.push({
            transactionId: result.id,
            transactionType: result.type,
          });

          return true;
        });

        log.audit(
          "Get 810 Internal Ids: Default Query",
          `Params: customerParentId - ${customerParentId}, startDate - ${startDate}, endDate - ${endDate}\n\nQuery: ${transactionQuery}`
        );
      }
    } catch (err) {
      throw customErrorObject.updateError({
        errorType: customErrorObject.ErrorTypes.NO_VALUE_RETURNED,
        summary: "INTERNAL_IDS_NOT_GOTTEN",
        details: `Error getting Internal IDs: ${err.message}`,
      });
    }
    return internalIdsArr;
  }

  return {
    getInternalIds,
  };
});
