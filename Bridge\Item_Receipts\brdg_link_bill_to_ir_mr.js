/**
 * @description Loops through bill items and adds item receipt link for any lines missing a link
 *
 *
 * @NApiVersion 2.1
 * @NScriptType MapReduceScript
 * @NAmdConfig /SuiteScripts/config.json
 *
 * <AUTHOR>
 * @module brdg_update_bill_ir_line_mr
 */

define([
	"require",
	"N/log",
	"N/record",
	"N/runtime",
	"N/format",
	"N/query",
	"N/task",
	"N/email",
	"N/file",
	"../../Classes/vlmd_custom_error_object",
], (/** @type {any} */ require) => {
	const log = require("N/log");
	const record = require("N/record");
	const runtime = require("N/runtime");
	const format = require("N/format");
	const query = require("N/query");
	const task = require("N/task");
	const email = require("N/email");
	const file = require("N/file");

	/**@type {import ("../../Classes/vlmd_custom_error_object").CustomErrorObject}} */
	const CustomErrorObject = require("../../Classes/vlmd_custom_error_object");
	const customErrorObject = new CustomErrorObject();

	/**
	 * @description This function gets the script parameters from the script deployment
	 * @param {void}
	 * @returns {Object} Start Date, End Date, Subsidiary, Bill ID Override
	 */
	function getScriptParameters() {
		const currentScript = runtime.getCurrentScript();

		let startDate = currentScript.getParameter({
			name: "custscript_bill_start_date",
		});

		startDate =
			startDate && startDate != "null"
				? format.format({
						value: new Date(startDate),
						type: format.Type.DATE,
				  })
				: null;

		let endDate = currentScript.getParameter({
			name: "custscript_bill_end_date",
		});

		endDate =
			endDate && endDate != "null"
				? format.format({
						value: new Date(endDate),
						type: format.Type.DATE,
				  })
				: null;

		const subsidiary = currentScript.getParameter({
			name: "custscript_bill_subsidiary",
		});

		const billIdOverride = currentScript.getParameter({
			name: "custscript_bill_id_override",
		});

		log.audit(
			"Script Params",
			`Start Date: ${startDate} End Date: ${endDate} Subsidiary: ${subsidiary} Bill ID: ${billIdOverride}`
		);

		return {
			startDate,
			endDate,
			subsidiary,
			billIdOverride,
		};
	}

	/**
	 *
	 * @description This function runs a query to pull any duplicate items on a specific vendor bill.
	 * @param {Number}
	 * @returns {Object} SQL mapped results of the duplicate items
	 */
	function getDuplicateItemsOnBill(billId) {
		const duplicateItemsQuery = `SELECT  
			a.linesequencenumber,
			a.uniquekey,
			a.item,
			FROM transactionLine a
			JOIN (SELECT units, item,  COUNT(*) 
			FROM transactionLine 
			WHERE transaction = ?   
			GROUP BY units, item 
			HAVING COUNT(*) > 1 ) b
			ON a.units = b.units
			AND a.item = b.item
			WHERE transaction = ? 
			ORDER BY a.item`;

		return query
			.runSuiteQL({
				query: duplicateItemsQuery,
				params: [billId, billId],
			})
			.asMappedResults();
	}

	/**
	 *
	 * @description This function runs a query to pull the linked item receipt info for a specific line on a specific bill.
	 * @param {Number} billId
	 * @param {Number} currentLineItem
	 * @param {Number} currentLineUOM
	 * @returns {Object} SQL mapped results of the duplicate items
	 */
	function getLinkedItemReceipt(billId, currentLineItem, currentLineUOM) {
		const getLinkedItemReceiptQuery = `
    SELECT DISTINCT
	BILLLINE.uniquekey,
  --  BUILTIN.DF(POLINK.previousdoc) PO,
  IRLINK.nextdoc as IRinternalid,
    BUILTIN.DF(IRLINK.nextdoc) IR,
    --BUILTIN.DF(BILLLINE.transaction) BILL,
   -- BILLLINE.transaction 'BILL ID',
   -- BILLLINE.item 'ITEM ID',
    BILLLINE.memo as ITEMNAME,
    --BILLLINE.units,
 FROM
    transactionline BILLLINE 
    JOIN
       previousTransactionLineLink AS POLINK 
       ON BILLLINE.transaction = POLINK.nextdoc 
       AND BILLLINE.linesequencenumber = POLINK.nextline 
    JOIN
       nextTransactionLineLink IRLINK 
       ON POLINK.previousline = IRLINK.previousline 
       AND POLINK.previousdoc = IRLINK.previousdoc 
 WHERE
    BILLLINE.transaction = ?
    AND BILLLINE.item IS NOT NULL 
    AND POLINK.previoustype = 'PurchOrd' 
    AND IRLINK.nexttype = 'ItemRcpt' 	
   AND BILLLINE.ITEM = ? 
   ${currentLineUOM ? `AND BILLLINE.units = ?` : ``}`;

		return query
			.runSuiteQL({
				query: getLinkedItemReceiptQuery,
				params: currentLineUOM
					? [billId, currentLineItem, currentLineUOM]
					: [billId, currentLineItem],
			})
			.asMappedResults();
	}

	/**
	 *
	 * @description This function goes through the formatted results of what was updated and creates a CSV file to be emailed to the users.
	 * @param {Object} resultsLogToPrint 1 object containing 3 arrays of the results
	 * @returns {File} resultsFile
	 */
	function createCSVResultFile(resultsLogToPrint) {
		const { billsUpdatedSuccessfully, billsPartiallyUpdated, billsWithErrors } =
			resultsLogToPrint;

		const resultsFile = file.create({
			name: `Link_Item_Receipt_Results ${new Date().toLocaleDateString()}.csv`,
			fileType: file.Type.CSV,
			contents: "BILL ID,BILL NAME,BILL LINE WAS UPDATED,ERRORS\n", //Main file headers
		});

		function addLinesToFile(results) {
			results.forEach((result) => {
				let formattedResult = `${result.billId},${result.billName},${result.billLineUpdated},`; //The values for the cells

				if (result.errorsForBill.length > 0) {
					formattedResult += `LINE,ITEM,ERROR`; //If this bill has error(s), include the error headers on the main bill line.
				}

				resultsFile.appendLine({
					value: formattedResult,
				});

				if (result.errorsForBill.length > 0) {
					//If there are errors for this bill,
					let errors = Object.values(result.errorsForBill);
					errors = JSON.parse(JSON.stringify(errors));

					errors.forEach((error) => {
						//Create a new line for each error for user readability
						error = `,,,${Object.values(error)}`;
						resultsFile.appendLine({
							value: error,
						});
					});
				}
			});
		}

		addLinesToFile(billsUpdatedSuccessfully);
		addLinesToFile(billsPartiallyUpdated);
		addLinesToFile(billsWithErrors);

		return resultsFile;
	}

	/**
	 *
	 * @description This function sends an email of the results to the Bridge team.
	 * @returns {void}
	 */
	function sendResultsEmail(parameters, resultsLogToPrint, resultsFile) {
		const { startDate, endDate, subsidiary, billIdOverride } = parameters;

		const { billsUpdatedSuccessfully, billsPartiallyUpdated, billsWithErrors } =
			resultsLogToPrint;

		let emailText = `Please see the attached results from the BRDG Update Line IR Link MR script.<br /><br /> 
			Script Parameters: Start Date: ${startDate} End Date: ${endDate} Subsidiary: ${subsidiary} Bill ID: ${billIdOverride}<br /><br />`;

		emailText += `${billsUpdatedSuccessfully.length} bills were updated successfully.<br />
		${billsPartiallyUpdated.length} bills were updated with errors.<br />
		${billsWithErrors.length} bills have errors and were not updated.<br /><br />`;

		if (billsWithErrors.length > 0 || billsPartiallyUpdated.length > 0) {
			//Show the list of bill with errors on the email
			emailText += `Bills with errors:<br />`;
			billsPartiallyUpdated.forEach(
				(bill) => (emailText += `${bill.billName}<br />`)
			);
			billsWithErrors.forEach(
				(bill) => (emailText += `${bill.billName}<br />`)
			);
		}

		email.send({
			author: 223244, //<EMAIL>
			recipients: 3046, //<EMAIL>
			cc: [43398, 3288], //<EMAIL>, <EMAIL>
			body: emailText,
			subject: `Results from Linking Item Receipts MR ${new Date().toLocaleDateString()}`,
			attachments: [resultsFile],
		});
	}

	/**
	 *
	 * @description Build up and return a query object to pull bill IDs based on params
	 * @param {Object} context
	 * @returns {Object} SQL query object
	 */
	function getInputData(context) {
		try {
			const { startDate, endDate, subsidiary, billIdOverride } =
				getScriptParameters();

			/**Get bills with the following criteria:
			 * Subsidiary: Bridge
			 * Status: Approved (Open or Paid)
			 * Checkbox: All lines not linked yet
			 * Contains items (not just expenses)
			 */

			const selectClause = `SELECT DISTINCT BILL.id, BILL.tranid, BILL.createddate `; //Need ID for finding, name for results, date for ordering by

			const fromClause = `FROM transaction AS BILL `;

			const joinClause = `LEFT OUTER JOIN
			transactionLine 
			ON transactionLine.transaction = BILL.id 
		 JOIN
			subsidiary AS billSub 
			ON transactionLine.subsidiary = billSub.id 
		 JOIN
			subsidiary AS parent 
			ON billSub.parent = parent.id 
			LEFT JOIN
					 SystemNote 
					 ON bill.id = SystemNote.recordid `;

			const whereClause =
				`WHERE
			BILL.type = 'VendBill' 
			AND 
			(
			   BUILTIN.CF( BILL.Status ) = 'VendBill:A' 
			   OR 
				  BUILTIN.CF( BILL.Status ) = 'VendBill:B' 
			)
			AND 
			(
			  BILL.custbody_bill_lines_linked_to_ir IS NULL 
			  OR BILL.custbody_bill_lines_linked_to_ir = 'F'
			)
			AND BILL.ID IN 
			(
			   SELECT
				  transaction 
			   FROM
				  transactionline 
			   WHERE
				  itemtype IS NOT NULL
			) ` +
				(billIdOverride && billIdOverride != "null"
					? `AND BILL.ID IN(${billIdOverride}) ` //If bill ID was passed in as parameter, don't filter the subsidiary or dates.
					: (startDate && endDate
							? `AND (
						TO_DATE(BILL.createddate, 'MM/DD/YYYY' ) BETWEEN TO_DATE('${startDate}', 'MM/DD/YYYY') AND TO_DATE('${endDate}', 'MM/DD/YYYY')
						OR
						(
						TO_DATE(SystemNote.date, 'MM/DD/YYYY' ) BETWEEN TO_DATE('${startDate}', 'MM/DD/YYYY') AND TO_DATE('${endDate}', 'MM/DD/YYYY')
						AND SystemNote.field = 'TRANDOC.MAMOUNTMAIN'
						)
			) `
							: `AND
					(
					   TO_DATE (SYSDATE -1, 'MM/DD/YYYY') = TO_DATE (BILL.createddate, 'MM/DD/YYYY')
					   OR 
					   (TO_DATE (SYSDATE -1, 'MM/DD/YYYY') = TO_DATE (SystemNote.date, 'MM/DD/YYYY')
					   AND SystemNote.field = 'TRANDOC.MAMOUNTMAIN' )
					) `) +
					  (subsidiary && subsidiary != "null"
							? `AND transactionLine.subsidiary = ${subsidiary} `
							: `AND 
			(
			   parent.id = '16' 
			   OR parent.parent = '16' 
			) `));

			const orderyByClause = ` ORDER BY BILL.createddate DESC `;

			const billsToUpdateQuery = `${selectClause}${fromClause}${joinClause}${whereClause}${orderyByClause}`;

			log.audit("SQL Query: billsToUpdateQuery", billsToUpdateQuery);

			return {
				type: "suiteql",
				query: billsToUpdateQuery,
				params: [],
			};
		} catch (e) {
			customErrorObject.throwError({
				summaryText: `GET_INPUT_DATA_ERROR`,
				error: e,
				errorWillBeGrouped: true,
			});
		}
	}

	/**
	 *
	 * @description For each NS bill record, loop through the items, check for duplicates and update the receipt link on that line if doesn't exist.
	 * @param {Object}
	 * @returns {void}
	 */
	function map(context) {
		const billId = JSON.parse(context.value).values[0]; //Pull bill ID from SQL results ([2]] is the createddate, don't need it)
		const billName = JSON.parse(context.value).values[1];

		try {
			let allItemsLinesLinked = true; //Starting off as true, will update to false if anything stops the script from linking item receipts
			var billLineUpdated = false; //Will change to true once a line has been updated by the script
			const errorsForBill = [];

			//Get duplicate items and their ifo on this bill, if there are duplicates, they will be handled on the line level
			const duplicateItemObjects = getDuplicateItemsOnBill(billId);

			//Load bill record
			const billRecord = record.load({
				type: record.Type.VENDOR_BILL,
				id: billId,
				isDynamic: false, //Must be standard mode or else need to use select line and commit line
			});

			//Get count of all items on bill
			const itemLineCount = billRecord.getLineCount({
				sublistId: "item",
			});

			//Loop through items
			billItemsLoop: for (
				let currentLineNumber = 0;
				currentLineNumber < itemLineCount;
				currentLineNumber++
			) {
				try {
					//First check if there is already an item receipt linked

					const currentLineItemReceiptLink = billRecord.getSublistValue({
						sublistId: "item",
						fieldId: "billreceipts",
						line: currentLineNumber,
					})[0]; //This is returned as a multi-select list, so only pulling the first value

					if (currentLineItemReceiptLink) {
						//If there is already a link, skip this iteration and continue to the next line.
						continue billItemsLoop;
					}

					//Then check if this line's item type is valid
					const currentLineItemType = billRecord.getSublistValue({
						sublistId: "item",
						fieldId: "itemtype",
						line: currentLineNumber,
					});

					const validItemTypes = ["InvtPart", "NonInvtPart"];

					//Check if this is a valid item type
					const itemIsValidItemType =
						Object.values(validItemTypes).includes(currentLineItemType);

					if (!itemIsValidItemType) {
						//If this is an item group, subtotal, discount, service, markup etc. it doesn't need an item receipt linked
						//So skip this iteration and continue to the next line.
						continue billItemsLoop;
					}

					//If this item does not yet have a link and is a valid item type :

					//Get bill record info needed to find the matching item receipt and check for duplicates
					const currentLineItem = billRecord.getSublistValue({
						sublistId: "item",
						fieldId: "item",
						line: currentLineNumber,
					});

					const currentLineItemName = billRecord.getSublistText({
						sublistId: "item",
						fieldId: "item",
						line: currentLineNumber,
					});

					const currentLineUOM = billRecord.getSublistValue({
						sublistId: "item",
						fieldId: "units",
						line: currentLineNumber,
					});

					//Get the linked item receipt info
					let itemReceiptLinkObj = getLinkedItemReceipt(
						billId,
						currentLineItem,
						currentLineUOM
					);

					itemReceiptLinkObj = itemReceiptLinkObj[0]; //Wasn't working to pull index of 0 when creating variable, so assigning it here.

					//If this item does not have a link:
					if (!itemReceiptLinkObj) {
						/**
						 * If linking object is null or empty, no item receipt was found -> this item wasn't yet received.
						 * Bill status is "open" and PO status is "partially received"
						 **/

						allItemsLinesLinked = false;
						//The bill can continue to get updated but the unfulfilled lines will still have to get updated once received

						errorsForBill.push({
							line: currentLineNumber + 1, //Off by one
							item: currentLineItemName,
							error:
								"No item receipt found for item. Confirm item was not yet received.",
						});

						//Since there is no item receipt to link, skip this iteration and continue to the next line.
						continue billItemsLoop;
					}

					//If there is an IR to link, check if the unique key for this bill line matches any of the duplicate items unique keys
					//If yes, that means that this item is a duplicate
					if (
						duplicateItemObjects.some(
							(duplicate) => duplicate.uniquekey == itemReceiptLinkObj.uniquekey
						)
					) {
						allItemsLinesLinked = false;
						//The bill can continue to get updated but this line will still have to get updated once duplicates are corrected

						errorsForBill.push({
							line: currentLineNumber + 1, //Off by one
							item: currentLineItemName,
							error: "Duplicate Item",
						});

						//Since this is a duplicate item and we cannot update it, skip this iteration and continue to the next line.
						continue billItemsLoop;
					}

					//Set the item receipt link on the bill line
					billRecord.setSublistValue({
						sublistId: "item",
						fieldId: "billreceipts",
						line: currentLineNumber,
						value: itemReceiptLinkObj.irinternalid,
					});

					//Only update this boolean once the item receipt has been linked on the bill line
					billLineUpdated = true;
				} catch (e) {
					//If there was any error, don't check off that all lines are linked
					allItemsLinesLinked = false;

					errorsForBill.push({
						line: currentLineNumber + 1, //Off by one
						error: `Error updating item: ${e}`,
					});

					//There was an error with one line item, so continue to iterate over the next line.
					continue;
				}
			}

			//Once looped through all items, save the bill
			billRecord.save({
				ignoreMandatoryFields: true, //Ignore mandatory fields like location, inventory detail etc.
				disableTriggers: true, //Block UE scripts and workflows from being triggered on save
			});

			/**
			 * If all items have an item receipt linked (the allItemsLinesLinked value was never changed) -> "Bill Saved Successfully"
			 * If some items do not have an item receipt linked (the allItemsLinesLinked value was set to false) -> "Bill Partially Updated"
			 */
			context.write({
				key: allItemsLinesLinked
					? "Bill Saved Successfully"
					: "Bill Partially Updated",
				value: {
					billId: billId,
					billName: billName,
					billLineUpdated: billLineUpdated, //At least one bill line was updated via the script
					errorsForBill: errorsForBill, //Array of errors for this bill
				},
			});

			//Only check off the checkbox if all items are linked
			if (allItemsLinesLinked) {
				record.submitFields({
					type: record.Type.VENDOR_BILL,
					id: billId,
					values: {
						custbody_bill_lines_linked_to_ir: "T",
					},
				});
			}
		} catch (e) {
			context.write({
				key: "Bill Not Updated",
				value: { bill: billId, billName: billName, error: e.message },
			});
		}
	}

	function summarize(context) {
		try {
			const billsUpdatedSuccessfully = [];
			const billsPartiallyUpdated = [];
			const billsWithErrors = [];

			context.output.iterator().each(function (key, value) {
				switch (key) {
					case "Bill Saved Successfully":
						billsUpdatedSuccessfully.push(JSON.parse(value));
						break;

					case "Bill Partially Updated":
						billsPartiallyUpdated.push(JSON.parse(value));
						break;

					case "Bill Not Updated":
						billsWithErrors.push(JSON.parse(value));
						break;
				}
				return true; //To continue iterating the context.output
			});

			log.audit(
				`${billsUpdatedSuccessfully.length} bills updated successfully`,
				billsUpdatedSuccessfully
			);
			log.audit(
				`${billsPartiallyUpdated.length} bills partially updated`,
				billsPartiallyUpdated
			);
			log.audit(`${billsWithErrors.length} bills with errors`, billsWithErrors);

			const parameters = getScriptParameters();
			const { startDate, endDate, subsidiary, billIdOverride } = parameters;

			const updateItemReceipts = task.create({
				taskType: task.TaskType.MAP_REDUCE,
				scriptId: "customscript_brdg_update_item_receipt_mr",
				deploymentId: "customdeploy_brdg_update_item_receipt_mr",
				params: {
					custscript_update_ir_start_date: startDate,
					custscript_update_ir_end_date: endDate,
					custscript_update_ir_subsidiary: subsidiary,
					custscript_brdg_bill_id_override: billIdOverride,
				},
			});

			updateItemReceipts.submit();

			const resultsToPrint = {
				billsUpdatedSuccessfully,
				billsPartiallyUpdated,
				billsWithErrors,
			};

			try {
				const resultsFile = createCSVResultFile(resultsToPrint);
				sendResultsEmail(parameters, resultsToPrint, resultsFile);
			} catch (error) {
				log.error("Error creating/sending file", error);
			}
		} catch (e) {
			customErrorObject.throwError({
				summaryText: `SUMMARIZE_ERROR`,
				error: e,
				errorWillBeGrouped: true,
			});
		}
	}

	return {
		getInputData,
		map,
		summarize,
	};
});
