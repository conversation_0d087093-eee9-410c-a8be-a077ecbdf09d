/**
 * Item Record class representing a SPL item record
 *
 * @NAmdConfig /SuiteScripts/config.json
 * @NApiVersion 2.1
 * <AUTHOR> <PERSON>
 * @module SplItemRecord
 */

define([
	"require",
	"exports",
	"../Classes/vlmd_custom_error_object",
	"N/log",
	"N/error",
	"N/query",
], (/** @type {any} */ require, /** @type {any} */ exports) => {
	/** @type {import("../Classes/vlmd_custom_error_object").CustomErrorObject} */
	const CustomErrorObject = require("../Classes/vlmd_custom_error_object");

	const log = require("N/log");
	const error = require("N/error");
	const query = require("N/query");

	/**
	 * SPL Item Record class
	 *
	 * @class
	 * @type {import("./spl_item_record").SplItemRecord}
	 *
	 */
	class SplItemRecord {
		/**
		 * Constructor
		 *
		 * @param {import("N/record").Record | import("N/record").ClientCurrentRecord} itemRecord Item record
		 */
		constructor(itemRecord) {
			this.currentRecord = itemRecord;
			this.id = itemRecord.id;
			this.type = itemRecord.type;
			this.subsidiaries = itemRecord.getValue({
				fieldId: "subsidiary",
			});
			this.categoryId = itemRecord.getValue({
				fieldId: "class",
			});
			this.itemNumber = itemRecord.getValue({
				fieldId: "itemid",
			});
			this.vendorCode = itemRecord.getValue({
				fieldId: "vendorname",
			});
			this.dontAutomateItemName = itemRecord.getValue({
				fieldId: "custitem_override_item_name",
			});
			this.displayComponents = itemRecord.getValue({ fieldId: "printitems" });
		}

		getParentCategoryId(categoryId) {
			return query
				.runSuiteQL({
					query: `SELECT
		  CASE
			  WHEN basicCategory.fullname LIKE '%:%:%' THEN parentsCategory.parent
			  WHEN basicCategory.parent IS NULL THEN basicCategory.id
			  ELSE basicCategory.parent
		  END AS toplevelparent,
		  FROM
		  classification basicCategory
		  LEFT OUTER JOIN classification parentsCategory ON parentsCategory.id = basicCategory.parent
		  WHERE
		  basicCategory.id = ${categoryId}`,
				})
				.asMappedResults()[0].toplevelparent;
		}

		/**
		 * Validates there is no duplicate vendor code
		 *
		 * @returns {void}
		 */
		validateVendorCode() {
			const sqlQuery = `SELECT id, displayname from ITEM where vendorname = '${this.vendorCode}'
			  and BUILTIN.MNFILTER(
				  item.subsidiary, 'MN_INCLUDE', '', 'FALSE', NULL, '1'
				  ) = 'T' and isinactive != 'T'
			  `;

			let sqlResults = query
				.runSuiteQL({
					query: sqlQuery,
				})
				.asMappedResults()
				.filter((result) => this.id != result.id);

			if (sqlResults.length > 0) {
				const itemsArr = sqlResults.map((a) => a.displayname);
				throw error.create({
					name: "Error with this item id",
					message: `This vendor code already exists for: ${itemsArr.join(
						", "
					)}`,
				});
			}
		}

		/**
		 * Validates that kit items are checked off for "Display Components on Transactions" (Field ID: printitems)
		 * This is needed so that the picking ticket (printed from SO) and packing slip (printed from IF) will show the items - if not checked off, they will be blank.
		 *
		 * @returns {void}
		 */
		validateDisplayComponents() {
			if (this.type == "kititem" && !this.displayComponents) {
				//If this is a kit item and it's not checked off
				throw error.create({
					name: "Error",
					message: `Please check off "Display Components on Transactions" under the Purchasing/Inventory tab.`,
				});
			}
		}

		/**
		 * Generates item name based on product category
		 *
		 * @returns {object}
		 */
		generateItemName() {
			const customError = new CustomErrorObject();

			try {
				if (!this.categoryId) {
					//Category field was empty
					return;
				}
				const parentCategoryId = this.categoryId
					? this.getParentCategoryId(this.categoryId)
					: null;

				const sqlQuery = `SELECT custrecord_spl_last_item_number, custrecord_vlmd_category_abbreviation  
				  FROM classification WHERE id = ${parentCategoryId}`;

				const sqlResults = query
					.runSuiteQL({
						query: sqlQuery,
					})
					.asMappedResults()[0];

				let lastItemNumber = sqlResults.custrecord_spl_last_item_number;

				const categoryAbbreviation =
					sqlResults.custrecord_vlmd_category_abbreviation;

				if (!categoryAbbreviation) {
					//No product category abreviation assigned to the parent category
					throw customError.updateError({
						errorType: customError.ErrorTypes.MISSING_VALUE,
						summary: "MISSING_PRODUCT_CATEGORY_ABBREVIATION",
						details: `The top level product category assigned (ID: ${parentCategoryId}) has no abbreviation set. Please assign a product category abbreviation before saving the item. <a href='/app/common/otherlists/classtype.nl?id=${parentCategoryId}'>Click here to update product category.</a>`,
					});
				}

				if (!lastItemNumber) {
					//No last item number assigned to the parent category
					throw customError.updateError({
						errorType: customError.ErrorTypes.MISSING_VALUE,
						summary: "MISSING_LAST_ITEM_NUMBER",
						details: `The top level product category assigned (ID: ${parentCategoryId}) does not have a "last item number". Please add the last item number for the category before saving the item. <a href='/app/common/otherlists/classtype.nl?id=${parentCategoryId}'>Click here to update product category.</a>`,
					});
				}

				const itemNumber = getNextAvailableItemNumber(
					lastItemNumber,
					categoryAbbreviation
				);

				const itemName = categoryAbbreviation + itemNumber;
				return { parentCategoryId, itemNumber, itemName };
			} catch (e) {
				customError.throwError({
					summaryText: `GENERATE_ITEM_NAME`,
					error: e,
					errorWillBeGrouped: true,
				});
			}
		}
	}

	exports.SplItemRecord = SplItemRecord;

	function getNextAvailableItemNumber(itemNumber, categoryAbbreviation) {
		var increment = 0;

		function runSql() {
			const sqlQuery = `select itemid from item where itemid = '${
				categoryAbbreviation + (itemNumber + increment)
			}'`;
			const results = query
				.runSuiteQL({
					query: sqlQuery,
				})
				.asMappedResults()[0];
			if (results) {
				increment++;
				runSql();
			} else {
				return;
			}
		}

		runSql();
		return itemNumber + increment;
	}

	return SplItemRecord;
});
