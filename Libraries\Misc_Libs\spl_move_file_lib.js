//@ts-ignore
define(["N/log", "GetEdiFileContents"], function (log, getEdiFileContents) {
	function moveFile(dataObj, fileName, processedSuccessfully) {
		var connection = getEdiFileContents.createConnection(
			dataObj,
			false, //appendToDirectory
			false, //referenceDirectory
			true //So that the "createConnectionToRoot" param will be true
		);

		var destinationFolder =
			"Processed_" + (processedSuccessfully ? "Successfully" : "With_Errors");

		var sourcePath = `${
			dataObj.prodDirectoryBool ? dataObj.prodDirectory : dataObj.testDirectory
		}/${fileName}`;

		var destinationPath = `${dataObj.referenceDirectory}/${destinationFolder}/${fileName}`;

		try {
			connection.move({
				from: sourcePath,
				to: destinationPath,
			});
		} catch (e) {
			if (e.name == "FTP_INVALID_MOVE") {
				return [
					`File was not moved. Please verify the file paths are correct. 
            Source path: ${sourcePath}
            Destination path: ${destinationPath}`,
				];
			} else {
				return [e];
			}
		}
		return [];
	}
	return {
		moveFile: moveFile,
	};
});
