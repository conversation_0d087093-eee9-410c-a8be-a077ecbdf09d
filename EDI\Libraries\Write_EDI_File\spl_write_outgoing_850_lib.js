/**
 * @NApiVersion 2.1
 */

define(["Moment", "Numeral"], function (moment, numeral) {
  function getPurchaseOrderAsEDI(partnerValues, po) {
    try {
      var receiverIDVal = partnerValues.receiverInfo[1].value;
      var ediFile =
        "ISA*00*          *00*          *SenderQualifier*ISASenderId*ReceiverQualifier*ISAReceiverId*ISADate*Time*U*EdiVersion*ControlNumber*0*P*>~" +
        "\n" +
        "GS*PO*GSSenderId*GSReceiverId*GSDate*Time*ControlNumber*X*EdiVersion0~" +
        "\n" +
        "ST*850*0001~" +
        "\n" +
        "BEG*00*OrderType*PoNumber**PoDate~" +
        "\n" +
        "N1*ST*CustomerNameShipToAccount~" +
        "\n" +
        "N3*Primary Secondary~" +
        "\n" +
        "N4*City*Stte*Zip~" +
        "\n" +
        "N1*SE**92*GSReceiverId~";

      var deliveryNotesText = "\n" + "REF*ZZ*DeliveryNotes~";

      var liftgateFlagText = "\n" + "REF*L1*Liftgate Required~";

      var itemText =
        "\n" +
        "PO1*LineNumber*Quantity*UOM*Rate**VN*ItemName*IN*ItemName~" +
        "\n" +
        "PID*F****ItemDescription~";

      var endOfFile =
        "\n" +
        "CTT*TotalLineItems~" +
        "\n" +
        "SE*NumberOfSegments*0001~" +
        "\n" +
        "GE*1*ControlNumber~" +
        "\n" +
        "IEA*1*ControlNumber~";

      formatISAGS();
      formatSender();
      formatReceiver();
      formatPO();
      formatNameAndShippingAccount();
      formatAddress();
      formatDeliveryDetails();
      formatItems();

      function formatFormatting() {
        //called at the end of formatItems
        var formattingInfo = partnerValues.formattingInfo;

        formattingInfo.forEach(function (x) {
          ediFile = ediFile.split(x.templateValue).join(x.partnerValue);
        });

        formatSegmantNumber();
      }

      function formatISAGS() {
        var isaGsInfo = partnerValues.isaGsInfo;

        isaGsInfo.forEach(function (x) {
          ediFile = ediFile.split(x.name).join(x.value);
        });
      }

      function formatSender() {
        var senderInfo = partnerValues.senderInfo;

        senderInfo.forEach(function (x) {
          ediFile = ediFile.split(x.name).join(x.value);
        });
      }

      function formatReceiver() {
        var receiverInfo = partnerValues.receiverInfo;

        receiverInfo.forEach(function (x) {
          ediFile = ediFile.split(x.name).join(x.value);
        });
      }

      function formatPO() {
        var poInfo = [
          {
            name: "PoNumber",
            value: po.number,
          },
          {
            name: "PoDate",
            value: moment(po.date).format("YYYYMMDD"),
          },
          {
            name: "OrderType",
            value: po.orderType, //SA - stand alone order , DS - drop ship, CF - confirmation, NE - New Order
          },
        ];

        poInfo.forEach(function (x) {
          ediFile = ediFile.replace(x.name, x.value);
        });
      }

      function formatNameAndShippingAccount() {
        ediFile = ediFile.replace("CustomerName", po.customer);
        if (po.shipToAccount) {
          ediFile = ediFile.replace(
            "ShipToAccount",
            "* 91 *" + po.shipToAccount
          );
        } else {
          ediFile = ediFile.replace("ShipToAccount", "");
        }
      }

      function formatAddress() {
        var addressInfo = [
          {
            name: "Primary",
            value: po.address.address1,
          },
          {
            name: "Secondary",
            value: po.address.address2,
          },
          {
            name: "City",
            value: po.address.city,
          },
          {
            name: "Stte",
            value: po.address.state,
          },
          {
            name: "Zip",
            value: po.address.zip,
          },
        ];

        addressInfo.forEach(function (x) {
          ediFile = ediFile.replace(x.name, x.value);
        });
        if (po.address.address2 === "") {
          ediFile = ediFile.replace(" Secondary", "");
        }
      }

      function formatDeliveryDetails() {
        // Delivery Detail Requirement Logic For Drive
        if (receiverIDVal == 10057) {
          //Drive receiverID
          if (po.address.deliveryDetails.deliveryInstructions) {
            var text = deliveryNotesText;
            var deliveryDetailsInfo = [
              {
                name: "DeliveryNotes",
                value: po.address.deliveryDetails.deliveryInstructions,
              },
            ];

            deliveryDetailsInfo.forEach(function (x) {
              text = text.split(x.name).join(x.value);
            });

            ediFile += text;
          }

          if (po.address.deliveryDetails.liftgateFlag === 1) {
            ediFile += liftgateFlagText;
          }
        }
      }

      function formatItems() {
        po.items.forEach(function (item) {
          var text = itemText;
          var itemInfo = [
            {
              name: "LineNumber",
              value: item.lineNumber,
            },
            {
              name: "Quantity",
              value: item.quantity,
            },
            {
              name: "UOM",
              value: item.uom,
            },
            {
              name: "Rate",
              value: numeral(item.rate).format("0.00"),
            },
            {
              name: "ItemName",
              value: item.name,
            },
            {
              name: "ItemDescription",
              value: item.description.replace(/[^a-zA-Z0-9/ -,]/g, ""),
              //Replace function removes special characters on item.description.
              //The only non alphanumeric characters allowed are space, '-', and ','.
            },
          ];

          itemInfo.forEach(function (x) {
            text = text.split(x.name).join(x.value);
          });

          ediFile += text;
        });
        formatEnd();
        formatFormatting();
        formatControlNumber();
      }

      function formatSegmantNumber() {
        //called at the end of formatFormatting
        var segments = ediFile.match(/[\n]/gm);
        var numberOfSegments = segments.length - 3;
        ediFile = ediFile.replace("NumberOfSegments", numberOfSegments);
      }

      function formatControlNumber() {
        //called at the end of formatItems
        ediFile = ediFile.split("ControlNumber").join(po.controlNumber);
      }

      function formatEnd() {
        //called at the end of formatItems
        var endInfo = [
          {
            name: "TotalLineItems",
            value: po.items.length,
          },
        ];

        endInfo.forEach(function (x) {
          endOfFile = endOfFile.replace(x.name, x.value);
        });
        ediFile += endOfFile;
      }

      if (ediFile.search(/undefined|invalid/i) != -1) {
        throw `EDI file contains 'undefined' or 'invalid'\n${ediFile}`;
      }

      var ediFile = {
        success: true,
        value: ediFile,
      };

      return ediFile;
    } catch (error) {
      var ediFile = {
        success: false,
        error: error,
      };

      return ediFile;
    }
  }

  return {
    getPurchaseOrderAsEDI,
  };
});
