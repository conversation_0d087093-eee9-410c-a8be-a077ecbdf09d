/**
 * @description Send an email for each applicable scenario and return an obj with the data
 * @NApiVersion 2.x
 */

define(["N/log", "N/email"], function (log, email) {
	function processEmail(
		logDirectory,
		errorLog,
		vendorName,
		purchaseOrderObj,
		fileName
	) {
		let { purchaseOrderName, purchaseOrderInternalId, createdBy, subsidiary } =
			purchaseOrderObj;

		let isSupplylinePurchaseOrder = subsidiary == 1;

		let emailDataObj = {};

		if (logDirectory.addressDiscrepancy.length > 0) {
			emailDataObj = addEmailToBucketHandlerData();
			sendEmail("emailToBucketHandler");
		}

		if (
			logDirectory.itemsOnBackorder.length > 0 ||
			logDirectory.rejectedItems.length > 0 ||
			logDirectory.itemValueDiscrepancy.length > 0
		) {
			emailDataObj = addEmailToOwnerData();
			sendEmail("emailToOwner");
		}

		if (logDirectory.itemsAcknowledgedSuccessfully.length > 0) {
			if (isSupplylinePurchaseOrder) {
				emailDataObj = addEmailToTracking();
				sendEmail("emailToTracking");
			}
		}

		if (errorLog.length > 0) {
			emailDataObj = addEmailToDevData();
			sendEmail("emailToDev");
		}

		return emailDataObj;

		/**********Send Email Helper Functions**********/
		function sendEmail(emailType) {
			try {
				email.send({
					author: 262579, //EDI
					recipients: emailDataObj.recipients,
					cc: emailDataObj.cc,
					subject: `${emailDataObj.subject} - PO Acknowledgment for ${vendorName} ${purchaseOrderName}`,
					body: emailDataObj.body,
					relatedRecords: {
						transactionId: purchaseOrderInternalId,
					},
				});
			} catch (e) {
				throw `${addEmailToDevData} email not sent for ${purchaseOrderName}. Error: ${e}`;
			}
		}

		function addEmailToBucketHandlerData() {
			return {
				recipients: [
					isSupplylinePurchaseOrder
						? "<EMAIL>"
						: "<EMAIL>",
				],
				cc: [
					isSupplylinePurchaseOrder ? "<EMAIL>" : "",
				],
				subject: "Address Discrepancy",
				body: _getBody(logDirectory.addressDiscrepancy),
				processingStatus: 2, //Processed With Errors
			};
		}

		function addEmailToOwnerData() {
			var subject = "";
			var body = "";
			var processingStatus;

			if (logDirectory.itemsOnBackorder.length > 0) {
				subject += "Backorder, ";
				logDirectory.itemsOnBackorder.forEach((log) => (body += "\n\n" + log));
			}

			if (logDirectory.rejectedItems.length > 0) {
				subject += "Items Rejected, ";
				logDirectory.rejectedItems.forEach((log) => (body += "\n\n" + log));
			}

			if (logDirectory.itemValueDiscrepancy.length > 0) {
				subject += "Rate Discrepancy";
				logDirectory.itemValueDiscrepancy.forEach(
					(log) => (body += "\n\n" + log)
				);
			}

			return {
				recipients: [
					isSupplylinePurchaseOrder
						? createdBy
						: "<EMAIL>",
				],
				cc: [],
				subject: subject,
				body: body,
				processingStatus: 6, // Processed With No Errors - Attention Needed
			};
		}

		function addEmailToTracking() {
			return {
				recipients: ["<EMAIL>"],
				subject: "Successful Acknowledgment",
				body: `The items below were successfully acknowledged for ${purchaseOrderName}. \n\n${_getBody(
					logDirectory.itemsAcknowledgedSuccessfully
				)}`,
				processingStatus: 1, //Processed With No Errors
			};
		}

		function addEmailToDevData() {
			return {
				recipients: ["<EMAIL>"],
				cc: [
					"<EMAIL>",
					isSupplylinePurchaseOrder
						? "<EMAIL>"
						: "<EMAIL>",
				], //Supplyline
				subject: "Processing Errors",
				body: _getBody(errorLog),
				processingStatus: 3, //Programming Error
			};
		}

		function _getBody(logArr) {
			var text = "";
			logArr.forEach((log) => (text += log + "\n"));
			text += "\n\n" + "EDI Transaction Control # " + fileName;
			return text;
		}
	}

	return {
		processEmail,
	};
});
