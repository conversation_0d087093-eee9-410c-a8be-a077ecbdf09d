/******************************************************************************************************
	Script Name - AVA_SUT_DeleteOldRecordCreateBatch.js
	Company -     Avalara Technologies Pvt Ltd.
******************************************************************************************************/

/**
 *@NApiVersion 2.0
 *@NScriptType Suitelet
 */

define(['N/ui/serverWidget', 'N/url', 'N/search', 'N/task', 'N/record', 'N/redirect', './utility/AVA_Library'],
	function(ui, url, search, task, record, redirect, ava_library){
		function onRequest(context){
			try{
				if(ava_library.mainFunction('AVA_CheckSecurity', 37) == 0){
					if(context.request.method === 'GET'){
						var avaDeleteOldRecordForm = ui.createForm({
							title: 'Create Batch'
						});
						avaDeleteOldRecordForm.clientScriptModulePath = './AVA_CLI_DeleteOldRecordCreateBatch.js';
						addFormFields(avaDeleteOldRecordForm);
						avaDeleteOldRecordForm.addSubmitButton({
							label: 'Submit'
						});
						avaDeleteOldRecordForm.addResetButton({
							label: 'Reset'
						});
						avaDeleteOldRecordForm.addPageLink({
							title: 'View Batch',
							type: ui.FormPageLinkType.CROSSLINK,
							url: url.resolveScript({
								scriptId: 'customscript_ava_deleteoldrecordvb_suit',
								deploymentId: 'customdeploy_ava_deleteoldrecordvb_suit'
							})
						});
						context.response.writePage({
							pageObject: avaDeleteOldRecordForm
						});
					}
					else{
						var deleteOldRecordBatchId = createDeleteOldRecordBatch(context);
						
						if(deleteOldRecordBatchId){
							var deleteOldRecordBatchData = {
								batchname: context.request.parameters.ava_batchname,
								startdate: context.request.parameters.ava_datefrom,
								enddate: context.request.parameters.ava_dateto,
								recordtype: context.request.parameters.ava_recordtype,
								newbatchid: deleteOldRecordBatchId
							};

							var callMapReduce = task.create({
								taskType: task.TaskType.MAP_REDUCE
							});
							callMapReduce.scriptId = 'customscript_ava_deleteoldrecord_map';
							callMapReduce.params = {
								custscript_ava_deleteoldrecorddetails: deleteOldRecordBatchData
							};
							callMapReduce.submit();

							context.response.sendRedirect("SUITELET", "customscript_ava_deleteoldrecordvb_suit", "customdeploy_ava_deleteoldrecordvb_suit", false);
						}
					}
				}
				else{
					context.response.write({
						output: checkServiceSecurity
					});
				}
			}
			catch(e){
				log.error('onRequest', 'ERROR NAME = ' + e.name + ', ERROR TYPE = ' + e.type + ', ERROR MESSAGE = ' + e.message);
			}
		}

		function addFormFields(avaDeleteOldRecordForm){
			try{
				avaDeleteOldRecordForm.addFieldGroup({
					id: 'ava_deleteoldrecordbatchdata',
					label: '<b>Batch Information</b>'
				});
				var avaBatchName = avaDeleteOldRecordForm.addField({
					id: 'ava_batchname',
					label: 'Batch Name',
					type: ui.FieldType.TEXT,
					container: 'ava_deleteoldrecordbatchdata'
				});
				avaBatchName.isMandatory = true;
				avaBatchName.updateDisplaySize({
					width: 40,
					height: 0
				});
				var avaRecordType = avaDeleteOldRecordForm.addField({
					id: 'ava_recordtype',
					label: 'Record Type',
					type: ui.FieldType.SELECT,
					container: 'ava_deleteoldrecordbatchdata'
				});
				avaRecordType.addSelectOption({
					value: 'customrecord_avatransactionlogs',
					text: 'AVATRANSACTIONLOGS'
				});
				avaRecordType.isMandatory = true;
				avaDeleteOldRecordForm.addFieldGroup({
					id: 'ava_createddate',
					label: '<b>Record Created Date</b>'
				});
				var avaStartDate = avaDeleteOldRecordForm.addField({
					id: 'ava_datefrom',
					label: 'Start Date',
					type: ui.FieldType.DATE,
					container: 'ava_createddate'
				});
				avaStartDate.isMandatory = true;
				var avaEndDate = avaDeleteOldRecordForm.addField({
					id: 'ava_dateto',
					label: 'End Date',
					type: ui.FieldType.DATE,
					container: 'ava_createddate'
				});
				avaEndDate.isMandatory = true;
			}
			catch(e){
				log.error('addFormFields', 'ERROR NAME = ' + e.name + ', ERROR TYPE = ' + e.type + ', ERROR MESSAGE = ' + e.message);
			}
		}

		function createDeleteOldRecordBatch(context){
			try{
				var deleteOldRecordBatchObj = record.create({
					type: 'customrecord_avadeleteoldrecord'
				});
				deleteOldRecordBatchObj.setValue({
					fieldId: 'name',
					value: context.request.parameters.ava_batchname
				});
				deleteOldRecordBatchObj.setValue({
					fieldId: 'custrecord_ava_deleteoldrecordbatchname',
					value: context.request.parameters.ava_batchname
				});
				deleteOldRecordBatchObj.setValue({
					fieldId: 'custrecord_ava_deleteoldrecordtype',
					value: context.request.parameters.inpt_ava_recordtype
				});
				deleteOldRecordBatchObj.setValue({
					fieldId: 'custrecord_ava_deleteoldrecordstartdate',
					value: ava_library.AVA_FormatDate(context.request.parameters.ava_datefrom)
				});
				deleteOldRecordBatchObj.setValue({
					fieldId: 'custrecord_ava_deleteoldrecordenddate',
					value: ava_library.AVA_FormatDate(context.request.parameters.ava_dateto)
				});
				deleteOldRecordBatchObj.setValue({
					fieldId: 'custrecord_ava_deleteoldrecordstatus',
					value: 'In Queue'
				});
				var deleteOldRecordBatchId = deleteOldRecordBatchObj.save({
					enableSourcing: true,
					ignoreMandatoryFields: true
				});
				return deleteOldRecordBatchId;
			}
			catch(e){
				log.error('createDeleteOldRecordBatch', 'ERROR NAME = ' + e.name + ', ERROR TYPE = ' + e.type + ', ERROR MESSAGE = ' + e.message);
			}
		}

		return{
			onRequest: onRequest
		};
	}
);