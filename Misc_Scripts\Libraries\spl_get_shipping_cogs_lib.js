//@ts-ignore
define(["N/log", "GetSqlResultsObjLib"], function (log, getSqlResultsObjLib) {
	function getShippingMethodObj(invoice) {
		const shippingInternalId = invoice.getValue("shipmethod");
		const shippingType = _getShippingType(shippingInternalId);

		return {
			internalId: shippingInternalId,
			shippingType: shippingType,
			name: invoice.getText("shipmethod"),
		};
	}

	function _getShippingType(shippingInternalId) {
		switch (shippingInternalId) {
			case "4195": //Drop Ship
			case "49096": //Drop Ship 2
			case "49097": //Drop Ship 3
			case "49098": //Drop Ship 4
				return "dropShip";
			case "8609": //UPS Ground
				return "upsStandard";
			case "7856": //Overnight
			case "8723": //UPS Next Day Air®
			case "8721": //UPS Ground Discount
				return "upsNoMarkup";
			case "5567":
				return "customerPickUp";
			case "2011":
				return "freight";
			default:
				return "nonRecognizedShipType";
		}
	}

	function getConsolidatedShippingInfoObj(
		invoiceInternalId,
		shippingType,
		freeFreight
	) {
		var helperFunctions = (function () {
			function getConsolidatedShippingRevenueObj(invoiceInternalId) {
				const resultsObj =
					getSqlResultsObjLib.getCnsldtdShipRevenueForInvoice(
						invoiceInternalId
					);

				return {
					numberOfInvoices: resultsObj.values[0] ?? 0,
					lastInvoiceId: resultsObj.values[1],
					salesOrderStatus: resultsObj.values[2],
					consolidatedRevenue: resultsObj.values[3] ?? 0,
				};
			}

			function getConsolidatedShippingCOGSForDropShip(invoiceInternalId) {
				let shipNotesArr = [];

				const consolidatedCostResultsObj =
					getSqlResultsObjLib.getRelatedBillForInvoice(invoiceInternalId);

				if (!consolidatedCostResultsObj) {
					throw `No PO status/shipping cost returned for this dropship order.`;
				}

				const purchaseOrderStatus = consolidatedCostResultsObj.values[0] ?? "";
				let consolidatedCOGS = consolidatedCostResultsObj.values[1] ?? 0;

				return {
					consolidatedCOGS,
					purchaseOrderStatus,
					shipNotesArr,
				};
			}

			function getConsolidatedShippingCOGSForDropShipWorkAround(
				invoiceInternalId
			) {
				/*Shipping costs are pulled from the order's item fulfillments
				Either shipping cost or custField "Our Shipping Cost"*/

				let shipNotesArr = [1]; //Shipping method is drop ship workaround

				const consolidatedShipCogsObj =
					getSqlResultsObjLib.getCnsldtdLtlShipCogsForDropShipWorkAroundInvoice(
						invoiceInternalId
					);

				if (!consolidatedShipCogsObj) {
					throw `No related shipping costs found for this dropship order.`;
				}

				const ourShippingCost = consolidatedShipCogsObj.values[0] ?? 0;
				const shippingCost = consolidatedShipCogsObj.values[1] ?? 0;

				let consolidatedCOGS = ourShippingCost ? ourShippingCost : shippingCost;

				return {
					consolidatedCOGS,
					shipNotesArr,
				};
			}

			function getConsolidatedShippingCOGSForLTL(invoiceInternalId) {
				let consolidatedCOGS = 0;
				let shipNotesArr = [];

				//Query the consolidated bill rate based on the "Associated SO" field on bills
				const resultsObj =
					getSqlResultsObjLib.getCnsldtdLtlShipCogsForInvoice(
						invoiceInternalId
					);

				//Comment: It was reading NULL as a result in the results object so consolidatedCOGS equaled = null;

				if (resultsObj.values[0]) {
					consolidatedCOGS = resultsObj.values[0];
				}

				//If no vendor bill is returned whose "Associated Sales Order" field matches the sales order of this invoice.
				if (!resultsObj.values[0]) {
					consolidatedCOGS = 0;
				}

				return { consolidatedCOGS, shipNotesArr };
			}

			function getConsolidatedShippingCOGSForUps(
				invoiceInternalId,
				shippingType
			) {
				let consolidatedCOGS = 0;
				let shipNotesArr = [];

				//Query to get the consolidated amount we paid for UPS from the corresponding bill
				const resultsObj =
					getSqlResultsObjLib.getCnsldtBillLinesForInvoice(invoiceInternalId);

				if (resultsObj.values[0]) {
					consolidatedCOGS = resultsObj.values[0];
				}

				//Add a 10% Safeco fee markup to the UPS ship cost
				if (
					(shippingType === "upsStandard" || shippingType === "upsNoMarkup") &&
					consolidatedCOGS
				) {
					const safeCoMarkup = consolidatedCOGS * 0.1;

					consolidatedCOGS = consolidatedCOGS + safeCoMarkup;
				}

				return { consolidatedCOGS, shipNotesArr };
			}

			function handleEdgeCases(
				salesOrderStatus,
				numberOfInvoices,
				lastInvoiceId,
				consolidatedRevenue,
				purchaseOrderStatus,
				consolidatedCOGS,
				shipNotesArr,
				freeFreight
			) {
				const edgeCasesObj = {
					shipNotesArr,
					consolidatedCOGS,
					consolidatedRevenue,
				};

				if (numberOfInvoices > 1 && invoiceInternalId != lastInvoiceId) {
					//There is more than one invoice for this order and this is not the final invoice
					edgeCasesObj.shipNotesArr.push(2);
					//X final invoice for consolidated order, ship values x set, are set on last inv
					return edgeCasesObj;
				}

				if (numberOfInvoices > 1 && invoiceInternalId == lastInvoiceId) {
					//This is the final invoice for an order with multiple invoices
					edgeCasesObj.shipNotesArr.push(8);
					//This is the final invoice for an order with multiple invoices
				}

				if (salesOrderStatus !== "G") {
					//!(Sales Order : Billed)
					edgeCasesObj.shipNotesArr.push(5); //X completely billed, ship values x set,
					//will be last inv once completely invoiced
				}

				if (shippingType == "dropShip" && !purchaseOrderStatus) {
					//X PO status returned, ship values x set, handle errror
					errorLog.push("No PO status returned.");
				}

				if (
					shippingType == "dropShip" &&
					purchaseOrderStatus &&
					purchaseOrderStatus !== "G" //!(Purchase Order : Fully Billed)
				) {
					//Drop ship order but the PO hasn't been fully billed yet.
					edgeCasesObj.shipNotesArr.push(6);
					//PO X fully billed, ship values x set, will be when completely billed
				}

				if (purchaseOrderStatus == "G" && !consolidatedCOGS) {
					//There is a bill for this PO but there are not shipping expenses on it.
					edgeCasesObj.shipNotesArr.push(3);
					//X related shipping expense for this dropship order, ship revenue still set
				}

				if (
					(shippingType == "upsNoMarkup" || shippingType == "upsStandard") &&
					!consolidatedCOGS &&
					!freeFreight
				) {
					//No related shipping expense found on bills corresponding to the item fulfillment for this order
					edgeCasesObj.shipNotesArr.push(9);
					//X related shipping expense on bill for this orders,
				}

				return edgeCasesObj;
			}

			return {
				getConsolidatedShippingRevenueObj,
				getConsolidatedShippingCOGSForDropShip,
				getConsolidatedShippingCOGSForDropShipWorkAround,
				getConsolidatedShippingCOGSForLTL,
				getConsolidatedShippingCOGSForUps,
				handleEdgeCases,
			};
		})();

		let consolidatedRevenue = 0;
		let consolidatedCOGS = 0;

		const errorLog = [];
		let shipNotesArr = [];

		try {
			let consolidatedRevenueObj =
				helperFunctions.getConsolidatedShippingRevenueObj(invoiceInternalId);

			let { numberOfInvoices, lastInvoiceId, salesOrderStatus } =
				consolidatedRevenueObj;

			consolidatedRevenue = consolidatedRevenueObj.consolidatedRevenue;

			let consolidatedShippingCogsObj = {};

			switch (shippingType) {
				case "dropShip":
					consolidatedShippingCogsObj =
						helperFunctions.getConsolidatedShippingCOGSForDropShip(
							invoiceInternalId
						);
					break;
				case "nonRecognizedShipType":
				case "customerPickUp":
					consolidatedShippingCogsObj =
						helperFunctions.getConsolidatedShippingCOGSForDropShipWorkAround(
							invoiceInternalId
						);
					break;
				case "freight":
					consolidatedShippingCogsObj =
						helperFunctions.getConsolidatedShippingCOGSForLTL(
							invoiceInternalId
						);
					break;
				case "upsStandard":
				case "upsNoMarkup":
					//Not putting in the free freight part here, bec. in the library it only comes here if it's free freight
					consolidatedShippingCogsObj =
						helperFunctions.getConsolidatedShippingCOGSForUps(
							invoiceInternalId,
							shippingType,
							freeFreight
						);
					break;
				default:
					throw `Consolidated ship method not recognized.`;
			}

			let purchaseOrderStatus = consolidatedShippingCogsObj.purchaseOrderStatus;
			consolidatedCOGS = consolidatedShippingCogsObj.consolidatedCOGS;

			if (consolidatedShippingCogsObj.shipNotesArr.length > 0) {
				shipNotesArr = [
					...shipNotesArr,
					...consolidatedShippingCogsObj.shipNotesArr,
				];
			}

			let edgeCaseObj = helperFunctions.handleEdgeCases(
				salesOrderStatus,
				numberOfInvoices,
				lastInvoiceId,
				consolidatedRevenue,
				purchaseOrderStatus,
				consolidatedCOGS,
				shipNotesArr,
				freeFreight
			);

			shipNotesArr = [...shipNotesArr, ...edgeCaseObj.shipNotesArr];
			consolidatedRevenue = edgeCaseObj.consolidatedRevenue;
			consolidatedCOGS = edgeCaseObj.consolidatedCOGS;
		} catch (e) {
			errorLog.push(e);
		}

		return {
			consolidatedRevenue,
			consolidatedCOGS,
			shipNotesArr,
			errorLog,
		};
	}

	function checkIfDropShipWorkAround(invoiceInternalId) {
		const resultsObj =
			getSqlResultsObjLib.getInvoiceShipMethodIsDropShipWorkAround(
				invoiceInternalId
			);

		if (resultsObj && resultsObj.values[0] != null) {
			return true;
		}

		return false;
	}

	function setShipAmountsForEdgeCases(
		shipNotesArr,
		shippingCOGS,
		shippingRevenue
	) {
		if (shipNotesArr.some((id) => id == 3)) {
			//No related shipping expense for this dropship order
			shippingCOGS = 0;
		}

		var idsToSetAmountsToZero = [2, 4, 5, 6];

		if (shipNotesArr.some((noteId) => idsToSetAmountsToZero.includes(noteId))) {
			shippingCOGS = 0;
			shippingRevenue = 0;
		}

		return { shippingCOGS, shippingRevenue };
	}

	return {
		getShippingMethodObj,
		getConsolidatedShippingInfoObj,
		checkIfDropShipWorkAround,
		setShipAmountsForEdgeCases,
	};
});
