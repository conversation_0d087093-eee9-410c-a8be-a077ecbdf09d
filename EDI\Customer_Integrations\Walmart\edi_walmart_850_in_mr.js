/**
 * @description Parse the Incoming 850 File (Sales Order) from Walmart
 *
 * @NApiVersion 2.1
 * @NScriptType MapReduceScript
 * @NAmdConfig /SuiteScripts/config.json
 * <AUTHOR> <<EMAIL>>
 */

define([
    "require",
    "N/log",
    "N/record",
    "N/search",
    "../../Classes/Models/File/edi_file",
    "../../Classes/Models/File/edi_incoming",
    "../../Classes/Models/File/edi_outgoing",
    "../../Classes/Models/Partner/Customer/edi_walmart",
    "../../Classes/Models/Server/edi_mft_server",
    "../../Classes/Decorators/850/edi_850",
    "../../Classes/Decorators/997/edi_997",
    "../../Classes/Decorators/997/edi_997_summary",
    "../../Classes/Decorators/850/edi_850_sales_order_parser",
    "../../Classes/Decorators/997/edi_997_processor",
    "../../../Classes/vlmd_custom_error_object",
], (/** @type {any} */ require) => {
    const log = require("N/log");
    const record = require("N/record");
    const search = require("N/search");
    const { DocumentType } = require("../../Classes/Models/File/edi_file");
    const { EDIIncoming } = require("../../Classes/Models/File/edi_incoming");
    const { EDIOutgoing } = require("../../Classes/Models/File/edi_outgoing");
    const { EDIWalmart } = require("../../Classes/Models/Partner/Customer/edi_walmart");
    const { EDIMFTServer } = require("../../Classes/Models/Server/edi_mft_server");
    const { EDI850 } = require("../../Classes/Decorators/850/edi_850");
    const { EDI997 } = require("../../Classes/Decorators/997/edi_997");
    const { EDI997Summary } = require("../../Classes/Decorators/997/edi_997_summary");
    const { EDI850SalesOrderParser } = require("../../Classes/Decorators/850/edi_850_sales_order_parser");
    const { EDI997Processor } = require("../../Classes/Decorators/997/edi_997_processor");
    /** @type {import("../../../Classes/vlmd_custom_error_object").CustomErrorObject} */
    const CustomErrorObject = require("../../../Classes/vlmd_custom_error_object");
    const customError = new CustomErrorObject();
    const WALMART_INTEGRATION_RECORD_ID = 1076;

    /**
     * Walmart sent multiple POs under a single file instead of individual EDI files
     * 1. Download all files from the MFT server
     * 2. For every file, break it down into multiple EDI strings delimited by ST segments
     * 3. Prefix each string with the ISA and GS segments found at the start of each file
     *
     * @returns {{filename: string, mftFileContent:string, documentControlNumber:number}[]|undefined} Attachment URLs
     */
    function getInputData() {
        try {
            const mftServer = new EDIMFTServer({
                partner: new EDIWalmart({
                    direction: "in",
                    transactionType: "850",
                }),
                customError
            });

            const lastControlNumber = search.lookupFields({
                type: "customrecord_vlmd_edi_integration",
                id: WALMART_INTEGRATION_RECORD_ID,
                columns: ["custrecord_document_control_number"]
            })?.["custrecord_document_control_number"];

            const walmart = new EDIWalmart({
                direction: "in",
                transactionType: "850",
            });

            const /** @type {{[key:string]: string}[]} */ purchaseOrders = [];
            const incoming850s = mftServer.authorize().list().filter(file => file.name.includes("850~OUT"));
            incoming850s.forEach(incoming850 => {
                const content = mftServer.authorize().download(incoming850.url, { decode: true });
                const segments = content.split(walmart.delimiters.segmentDelimiter);
                const isaGsSegment = `${segments.shift()}${walmart.delimiters.segmentDelimiter}${segments.shift()}`;
                let currentPurchaseOrder = "";
                let poCount = 1;
                segments.forEach(segment => {
                    if (segment.startsWith("ST*")) {
                        if (currentPurchaseOrder !== "") {
                            purchaseOrders.push({
                                name: `${incoming850.name}_${poCount}`,
                                content: `${isaGsSegment}${walmart.delimiters.segmentDelimiter}${currentPurchaseOrder}${walmart.delimiters.segmentDelimiter}`
                            });
                            poCount++;
                        }
                        currentPurchaseOrder = `${segment}`; // New ST segment
                    } else if (segment) {
                        currentPurchaseOrder = `${currentPurchaseOrder}${walmart.delimiters.segmentDelimiter}${segment}`
                    }
                });
                currentPurchaseOrder !== null && purchaseOrders.push({
                    name: `${incoming850.name}_${poCount}`,
                    content: `${isaGsSegment}${walmart.delimiters.segmentDelimiter}${currentPurchaseOrder}${walmart.delimiters.segmentDelimiter}`
                });
            });

            log.debug({
                title: "EDI Incoming 850 Walmart (getInputData): Purchase Order EDI Files",
                details: purchaseOrders
            });

            return purchaseOrders.map((purchaseOrder, index) => ({
                filename: purchaseOrder.name,
                mftFileContent: purchaseOrder.content,
                documentControlNumber: Number(lastControlNumber) + index + 1
            }));
        } catch (/** @type {any} */ err) {
            customError.throwError({
                summaryText: "GET_INPUT_DATA",
                error: err,
                recordId: null,
                recordName: null,
                recordType: null,
                errorWillBeGrouped: false,
            });
        }
    }

    /**
     * Parse and transform the relevant Purchase Order to a Sales Order
     *
     * @param {import("N/types").EntryPoints.MapReduce.mapContext} context Map context
     * @returns {void}
     */
    function map(context) {
        try {
            const { filename, mftFileContent, documentControlNumber } = JSON.parse(context.value);
            log.debug({
                title: "EDI Incoming 850 Walmart (Map)",
                details: JSON.stringify({ filename, mftFileContent, documentControlNumber })
            });
            const walmart = new EDIWalmart({
                direction: "in",
                transactionType: "850",
            });
            const ediFile = new EDIIncoming();
            ediFile.server = new EDIMFTServer({
                prodGUID: "0897309265eb4d3c937733ec311f0627",
                partner: walmart,
                customError,
            });

            if (mftFileContent) {
                // Override the MFT username since we're connecting to the SFTP for archiving
                ediFile.server.username = "FTPAdmin";
                new EDI850({
                    parser: new EDI850SalesOrderParser({
                        ediFileContent: mftFileContent,
                        customer: walmart,
                        filename
                    }),
                    type: "Sales Order",
                    typeId: DocumentType.INCOMING_850,
                }).decorate(ediFile);
                ediFile.parse();
                ediFile.transform();
                ediFile.summarize();
                ediFile.archive({ content: mftFileContent });

                // Write the data for the outgoing 997 to reduce context
                const salesOrderControlNumber = ediFile.parser.salesOrder?.controlNumber;
                const documentNumber = ediFile.parser.salesOrder?.number;
                if (salesOrderControlNumber && documentControlNumber && documentNumber) {
                    context.write(filename, JSON.stringify({
                        salesOrderControlNumber, documentControlNumber, documentNumber
                    }));
                }
            }
        } catch (/** @type {any} */ err) {
            log.error({
                title: "EDI Incoming 850 Walmart (MAP)",
                details: err,
            });
            customError.throwError({
                summaryText: "MAP",
                error: err,
                recordId: null,
                recordName: null,
                recordType: null,
                errorWillBeGrouped: false,
            });
        }
    }
    
    /**
     * Send the 997 for every 850
     *
     * @param {import("N/types").EntryPoints.MapReduce.reduceContext} context Reduce context
     * @returns {void}
     */
    function reduce(context) {
        try {
            const { salesOrderControlNumber, documentControlNumber, documentNumber } = JSON.parse(context.values?.[0]);
            log.debug({
                title: "EDI Outgoing 997 Walmart (Reduce)",
                details: JSON.stringify({ salesOrderControlNumber, documentControlNumber, documentNumber })
            });
            const ediFile = new EDIOutgoing();
            const walmart = new EDIWalmart({
                direction: "out",
                transactionType: "997",
            });
            ediFile.server = new EDIMFTServer({
                partner: walmart,
                customError,
            });
            // We're already sending the file to the MFT server
            // Target the ref instead of prod
            ediFile.server.connect({
                directory: walmart.referenceDirectory,
                username: "FTPAdmin",
                passwordGuid: "0897309265eb4d3c937733ec311f0627",
            });
            new EDI997({
                processor: new EDI997Processor({
                    customError,
                    template: walmart.template.OUT[997],
                    salesOrderControlNumber,
                    documentControlNumber,
                    documentNumber,
                    parsingInformation: walmart.getParsingInformation({
                        delimiters: walmart.delimiters,
                        direction: "out",
                    }),
                }),
                type: "Acknowledgement",
                typeId: DocumentType.OUTGOING_997,
            }).decorate(ediFile);
            ediFile.process();
            ediFile.create();
            ediFile.upload({ targetStationId: walmart.targetStationId });
            ediFile.save(); // TODO: Remove save once we're confident with the MFT and SFTP upload process
            const documentControlNumberId = ediFile.complete();

            context.write(documentNumber, { id: documentControlNumberId, controlNumber: documentControlNumber })
        } catch (/** @type {any} */ err) {
            log.error({
                title: "EDI Outgoing 997 Walmart (REDUCE)",
                details: err,
            });
            customError.throwError({
                summaryText: "REDUCE",
                error: err,
                recordId: null,
                recordName: null,
                recordType: null,
                errorWillBeGrouped: false,
            });
        }
    }

    /**
     * Log files that were processed and not processed
     *
     * @param {import("N/types").EntryPoints.MapReduce.summarizeContext} context Summarize context
     * @returns {void}
     */
    function summarize(context) {
        try {
            let filesProcessed = 0;
            let filesNotProcessed = 0;

            // Update new document control number of EDI Integration record for Walmart
            let newDocumentControlNumber = 0;
            context.output.iterator().each((key, controlNumberObj) => {
                newDocumentControlNumber = Math.max(newDocumentControlNumber, Number(JSON.parse(controlNumberObj).controlNumber));
                return true;
            });
            log.audit("New Control Number:", newDocumentControlNumber);
            newDocumentControlNumber && record.submitFields({
                type: "customrecord_vlmd_edi_integration",
                id: WALMART_INTEGRATION_RECORD_ID,
                values: {
                    custrecord_document_control_number: newDocumentControlNumber.toString(),
                },
            });

            // Create EDI Transaction record for all transactions
            const edi997Summary = new EDI997Summary({
                transactionType: "Acknowledgement",
                partner: new EDIWalmart({
                    direction: "out",
                    transactionType: "997",
                }),
                documentType: DocumentType.OUTGOING_997
            });
            edi997Summary.summarizeProcess(context).pushEdiEmailInfoToDB(); 
    
            context.mapSummary.keys.iterator().each((key, executionCount, completionState) => {
                if (completionState === 'COMPLETE'){
                    filesProcessed++;
                } else {
                    filesNotProcessed++;
                }
                return true;
            });
    
            log.audit({
                title: "EDI Incoming 850 Walmart (summarize)",
                details: `# of Files processed: ${filesProcessed}, # of Files skipped: ${filesNotProcessed}`,
            });
        } catch (/** @type {any} */ err) {
            log.error({
                title: "EDI Incoming 850 Walmart (SUMMARIZE)",
                details: err,
            });
            customError.throwError({
                summaryText: "SUMMARIZE",
                error: err,
                recordId: null,
                recordName: null,
                recordType: null,
                errorWillBeGrouped: false,
            });
        }
    }

    return {
        getInputData,
        map,
        reduce,
        summarize,
    }
});