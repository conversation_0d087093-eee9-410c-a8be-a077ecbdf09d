/**
 * @description Suitelet to upload bid sheet file and trigger the MR script to process the bid sheet
 *
 * @NApiVersion 2.1
 * @NScriptType Suitelet
 * @NAmdConfig /SuiteScripts/config.json
 * 
 * <AUTHOR>
 * @module spl_bid_sheet_sl 
 */
define([
    "require",
    "N/ui/serverWidget",
    "N/record",
    "N/task",
    "N/file",
    "N/ui/message",
    "N/runtime",
    "N/search",
    "../../Classes/vlmd_custom_error_object",
], (require) =>{

    const serverWidget = require('N/ui/serverWidget');
    const record = require('N/record');
    const task = require('N/task');
    const file = require('N/file');
    const message = require('N/ui/message');
    const runtime = require('N/runtime');
    const search = require('N/search');
    
    const CustomErrorObject = require("../../Classes/vlmd_custom_error_object");
    const customErrorObject = new CustomErrorObject();

    const renderUI = (context, infoMessage = null) =>  {
        const form = serverWidget.createForm({
            title: 'Bid Sheet Upload'
        });

        if(infoMessage){
            form.addPageInitMessage({
                type: infoMessage.type,
                title: infoMessage.title,
                message: infoMessage.message
            });
        }

        form.addField({
            id: 'custpage_spl_customer',
            type: serverWidget.FieldType.SELECT,
            label: 'Customer',
            source: 'customer'
        }).isMandatory = true;

        form.addField({
            id: 'custpage_spl_bid_sheet_file',
            type: serverWidget.FieldType.FILE,
            label: 'Select Bid Sheet File'
        }).isMandatory = true;

        form.addSubmitButton({
            label: 'Process Bid Sheet'
        });

        context.response.writePage(form);
    }

    const onRequest = (context) => {
        if (context.request.method === 'GET') {
            const form = renderUI(context);
        } else {
            try {
                const customerId = context.request.parameters.custpage_spl_customer;
                const uploadedFile = context.request.files.custpage_spl_bid_sheet_file;

                if(uploadedFile.fileType != file.Type.CSV) {
                    let infoMessage = { 
                        type: message.Type.ERROR,
                        title: 'Invalid File Format',
                        message: 'Invalid file format detected. Please upload a CSV file.'
                    };

                    renderUI(context, infoMessage);

                    return false;
                }

                let customerNameAndNumber = Object.values(search.lookupFields({
                    type: 'customer',
                    id: customerId,
                    columns: ['entityid','companyname']
                }))
                    .join('_')
                    .replace(/\s+/g, '_');

                let folderId = 9922680;
                uploadedFile.name = `${customerNameAndNumber}_${new Date().toISOString().replace(/[:.]/g, '_')}_${uploadedFile.name}`;
                uploadedFile.folder = folderId;
                const uploadedFileId = uploadedFile.save();

                const mapReduceTask = task.create({
                    taskType: task.TaskType.MAP_REDUCE,
                    scriptId: 'customscript_spl_bid_sheet_processing_mr',
                    deploymentId: 'customdeploy_spl_bid_sheet_processing_mr',
                    params: {
                        custscript_spl_bid_sheet_file_id: uploadedFileId,
                        custscript_spl_bid_sheets_customer: customerId
                    }
                });
                mapReduceTask.submit();                
                
                const infoMessage = {
                    type: message.Type.INFORMATION,
                    title: 'Processing',
                    message: 'Processing Bid Sheet. You will receive the processed file via email once it is complete.'
                };

                renderUI(context, infoMessage);

                return false;
            } catch (err) {
                customErrorObject.throwError({
                    summaryText: `ERROR_IN_BID_SHEET_SL`,
                    error: err,
                });
            }
        }
    }

    return { onRequest };
});