/******************************************************************************************************
	Script Name - AVA_SCH_RegenerateAvalaraTkn.js
	Company -     Avalara Technologies Pvt Ltd.
******************************************************************************************************/

/**
 *@NApiVersion 2.0
 *@NScriptType ScheduledScript
*/

define(['N/search', 'N/record', 'N/https', './utility/AVA_Library', 'N/cache'],
	function(search, record, https, ava_library, cache){
		function execute(context){
			try{
				var customrecord_avaconfigSearchObj = search.create({
					type: "customrecord_avaconfig",
					columns: ['custrecord_ava_url', 'custrecord_ava_additionalinfo4']
				});
				var searchresult = customrecord_avaconfigSearchObj.run();
				searchresult = searchresult.getRange({
					start: 0,
					end: 5
				});
				
				if(searchresult != null && searchresult.length > 0){
					var additional_Info4 = searchresult[0].getValue('custrecord_ava_additionalinfo4');
					
					if(additional_Info4 != null && additional_Info4.length > 0){
						var result = calladditionalInfo4(additional_Info4, searchresult[0].id, searchresult[0].getValue('custrecord_ava_url'));

						if(result == 200){
							log.debug('Tkn generated successfully');
							
							var avaConfigCache = cache.getCache({
								name: 'avaConfigCache',
								scope: cache.Scope.PROTECTED
							});
							avaConfigCache.put({
								key: 'avaConfigObjRec',
								value: ava_library.mainFunction('AVA_LoadValuesToGlobals', '')
							});
						}
						else{
							log.debug({title: 'result Error', details: result});
						}
					}
				}
			}
			catch(err){
				log.debug({title: 'execute Error', details: err});
			}
		}

		function calladditionalInfo4(additional_Info4, recordId, sUrl){
			try{
				var details;
				var authorizationUrl;
				
				var header = {};
				header["content-type"] = "application/x-www-form-urlencoded";
				
				if(sUrl == 1){
					details = 'client_id=sbx-netsuite-identity&client_secret=Netsbx123';
					authorizationUrl = 'https://ai-sbx.avlr.sh/connect/token';
				}
				else{
					details = 'client_id=prd-netsuite-identity&client_secret=yD7)o#Uk@!GH%1#PVahs5Lx0';
					authorizationUrl = 'https://identity.avalara.com/connect/token';
				}
				
				var body = 'refresh_token=' + additional_Info4 + '&grant_type=refresh_token&' + details + '&scope=offline_access openid profile avatax_api avatax email';
				
				var response = https.request({
					method: https.Method.POST,
					url: authorizationUrl,
					body: body,
					headers: header
				});
				
				if(response.code == 200){
					var myresponse_body = JSON.parse(response.body);
					
					record.submitFields({
						type: 'customrecord_avaconfig',
						id: recordId,
						values: {
							'custrecord_ava_additionalinfo3': myresponse_body.access_token,
							'custrecord_ava_additionalinfo4': myresponse_body.refresh_token
						}
					});
					
					return response.code;
				}
				else{
					var myresponse_body = JSON.parse(response.body);
					return myresponse_body.error;
				}
			}
			catch(err){
				log.debug({title: 'calladditionalInfo4 Error', details: err.message});
			}
		}
		
		return{
			execute: execute
		};
	}
)
