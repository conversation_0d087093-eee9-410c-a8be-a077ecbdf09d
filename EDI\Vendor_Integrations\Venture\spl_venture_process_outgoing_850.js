
/**
* @NApiVersion 2.1
* @NScriptType scheduledscript
 * @NAmdConfig /SuiteScripts/config.json
*/


//CHECK THAT SAVED SEARCH IS PULLING THE RIGHT PURCHASE ORDERS - CHANGE THE DATE CREATED FILTER
//UPDATE ANY RELEVANT SAVED SEARCHES
//CHANGE THAT VENTURE PO'S AREN'T EMAILED OUT

define([
    'N/sftp',
    'N/record',
    'N/task',
    'N/file',
    'N/search',
    'N/email',
    'Moment',
    'LoDash',
],
    function (
        sftp,
        record,
        task,
        file,
        search,
        email,
        moment,
        _
    ) {
        function execute(context) {
            var processingLog = [];
            var transactionsProcessedSuccessfully = [];
            var processedSuccesfully = true;

            var savedSearchId = 1113 //'Venture Purchase Orders to Send'
            var searchTaskFileId = 528519; //ADD IN FOR SANDBOX

            var ventureConnection = createVentureConnection();
            var supplyLineConnection = createSupplyLineConnection();
            var searchResults = checkIfSearchResults();
            if (searchResults) {
                pushSearchResultsToFile();
                var searchResultsFile = loadFile();
                var ediTransactionControlNumber = 'VNTR_' + moment().format('hhmmMMDDYYYY') + '.csv';
                uploadFileToVenture();
                uploadFileToSupplyLine();
                if (processedSuccesfully) {
                    updatePOsWithTransactionControlNumber();
                }
                processEnd();
            }


            //*******Process Purchase Order Helper Functions*******
            function processFail(logMessage, continueProcessing = false) {
                processedSuccesfully = false;
                processingLog.push(logMessage);
                return continueProcessing;
            }

            function createVentureConnection() {
                var hostKey = "AAAAB3NzaC1yc2EAAAABIwAAAIEA2YIvZPcsWA3jP6yq5J" +
                    "5XYunNySNspUZUhtzxXoePDZEhxJFIF/am9o+15CqO4hlyEDni0m+VW4" +
                    "LVSoiUnj31xJis91EcYrUeCewIcSwof4Xs05ghth/2SKn0cITsR5hOeST" +
                    "l4jFsMf9oK0+b2gr4jdV4fria4E/eF2u0qQArWuc=";

                return sftp.createConnection({
                    username: 'FTPadmin',
                    passwordGuid: 'ac4fde2468944f40a40116e1ad310898', //Production
                    //passwordGuid: '9693b8ef2a06493d8f00669b5a7e4bdc', //Sandbox
                    url: '************',
                    hostKey: hostKey,
                    directory: '/users/Venture/Test/Purchase_Orders' //Take out test when go live

                });
            }

            function createSupplyLineConnection() {
                var hostKey = "AAAAB3NzaC1yc2EAAAABIwAAAIEA2YIvZPcsWA3jP6yq5J" +
                    "5XYunNySNspUZUhtzxXoePDZEhxJFIF/am9o+15CqO4hlyEDni0m+VW4" +
                    "LVSoiUnj31xJis91EcYrUeCewIcSwof4Xs05ghth/2SKn0cITsR5hOeST" +
                    "l4jFsMf9oK0+b2gr4jdV4fria4E/eF2u0qQArWuc=";

                return sftp.createConnection({
                    username: 'FTPadmin',
                    passwordGuid: 'd093ca334e3b4154ad2f7bb172f591e0', //Production
                    //passwordGuid: '9693b8ef2a06493d8f00669b5a7e4bdc', //Sandbox
                    url: '************',
                    hostKey: hostKey,
                    directory: '/EDI Reference Files/Venture/850'
                });
            }

            function checkIfSearchResults() {
                var searchObj = search.load({
                    id: savedSearchId
                });

                var resultSet = searchObj.run();

                var results = resultSet.getRange({
                    start: 0,
                    end: 1000 //CHANGE IF MORE THAN THIS
                });
                return results.length > 0;
            }

            function pushSearchResultsToFile() {
                try {
                    var searchTask = task.create({
                        taskType: task.TaskType.SEARCH
                    });
                    searchTask.savedSearchId = savedSearchId;
                    //searchTask.filePath = 'Venture/Venture_Purchase_Orders.csv'
                    searchTask.fileId = searchTaskFileId;
                    return searchTask.submit();
                } catch (error) {
                    processFail('Search results not pushed to file.' + ' Error: ' + error);
                }
            }

            function loadFile() {
                try {
                    return file.load({
                        id: searchTaskFileId
                    });
                } catch (error) {
                    processFail('Search file not loaded.' + ' Error: ' + error);
                }
            }

            function uploadFileToVenture() {
                try {
                    ventureConnection.upload({
                        file: searchResultsFile,
                        filename: 'VNTR.csv',
                        replaceExisting: true
                    });
                } catch (error) {
                    processingLog.push('File not uploaded to Venture.' + ' Error: ' + error);
                }
            }

            function uploadFileToSupplyLine() {
                try {
                    supplyLineConnection.upload({
                        file: searchResultsFile,
                        filename: 'VNTR.csv',
                        replaceExisting: true
                    });
                } catch (error) {
                    processingLog.push('File not uploaded to SupplyLine.' + ' Error: ' + error);
                }
            }

            function updatePOsWithTransactionControlNumber() {
                var updatedPosInternalIds = getUpdatedPosInternalIds();
                log.debug({
                    title: 'updated pos', 
                    details: updatedPosInternalIds
                })

                updatedPosInternalIds.forEach((internalId) => {
                    var purchaseOrder = loadPurchaseOrder(internalId);
                    purchaseOrder.setValue('custbody_spl_po_edi_trans_cntrl_number', ediTransactionControlNumber);
                    purchaseOrder.save();
                    transactionsProcessedSuccessfully.push(purchaseOrder.getValue('tranid'));
                })


                function getUpdatedPosInternalIds() {
                    var searchObj = search.load({
                        id: savedSearchId
                    });

                    var resultSet = searchObj.run();

                    var results = resultSet.getRange({
                        start: 0,
                        end: 1000 //CHANGE IF MORE THAN THIS
                    });

                    log.debug({
                        title: 'results',
                        details: results
                    });

                    return  results.map(result => result.id); 
                }

                function loadPurchaseOrder(internalId) {
                    try {
                        log.debug('result id ' + internalId)
                        return record.load({
                            type: record.Type.PURCHASE_ORDER,
                            id: internalId
                        });
                    } catch (error) {
                        processFail('Purchase order record not loaded for internal ID ' + internalId.id, false);
                    }
                };
            }

            function processEnd() {
                var resultsData = setResultsData();
                sendEmail();
                logEndResult();

                function setResultsData() {
                    var data = {};
                    var errorText = '';
                    processingLog.forEach(function (error) {
                        errorText += error + '\n\n';
                    });
                    var transactionsProcessed = '';
                    transactionsProcessedSuccessfully.forEach(function (transaction) {
                        transactionsProcessed += transaction + '\n';
                    });

                    if (processedSuccesfully) {
                        processCreatedSuccessfully();
                    } else {
                        if (transactionsProcessedSuccessfully.length > 0) {
                            processCreatedWithErrors();
                        } else {
                            processFailedData();
                        }
                    }

                    return data;

                    function processCreatedSuccessfully() {
                        data.subject = 'Success: Venture EDI Purchase Orders Processed Successfully';
                        data.body = 'The following Venture purchase orders were processed successfully via EDI:\n\n'
                            + transactionsProcessed;
                        data.logTitle = 'Processed Successfully'
                    }

                    function processCreatedWithErrors() {
                        data.subject = 'Errors: Please Review and Correct Venture EDI Purchase Orders';
                        data.body = 'The following Venture purchase orders were processed successfully via EDI:\n\n'
                            + transactionsProcessed +
                            'Please review the errors below.\n\n' + errorText;
                        data.logTitle = 'Created with Errors'
                    };

                    function processFailedData() {
                        data.subject = 'Failure: Venture EDI purchase orders not processed successfully.';
                        data.body = 'Please review the errors below.\n\n' + errorText;
                        data.logTitle = 'Purchased Orders Not Processed';
                    };
                }

                function sendEmail(salesOrderId) {
                    try {
                        email.send({
                            author: 3288,
                            recipients: ['<EMAIL>'],
                            subject: resultsData.subject,
                            body: resultsData.body
                        });
                    } catch (error) {
                        throw 'Venture EDI Purchase Order email not sent.' + ' Error: ' + error;
                    }
                }

                function logEndResult() {
                    log.debug({
                        title: resultsData.logTitle,
                        details: resultsData.logDetails
                    });
                };
            }

        }

        return {
            execute: execute
        }
    });

