/*
How To: 

"Sample Snippet Name": {
	"scope": "javascript,typescript", //The scope to make this snippet available. if ommitted, is available to all scopes
	"prefix": "miscfunc", //The letters that will be used to call this snippet
	"body": [
		"function MiscFunc(${1:placeholder1},${2:placeholder2}){", //Placeholders
		"console.log(${1:placeholder1})", //Placeholder with the same numeric value are connected
		"$1", //Tab stop 1
		"}",
		"$2", //Tab stop 2
		"$0", //Final cursor position
		
	],
	"description": "Sample snippet description here"
}
*/
{
	"Insert Data Obj": {
		"scope": "javascript,typescript",
		"prefix": "data",
		"body": [
			"var dataObj = {",
			"prodGuidBool: false,",
			"prodGUID: $1,",
			"sandboxGUID: $2,",
			"testDirectory: $3,",
			"sandboxGUID:$4,",
			"transactionType: $5,",
			"purchasingSoftware: $6,",
			"};"
		],
		"description": "Insert the integration partners data obj"
	},
	"Insert SQL Query": {
		"scope": "javascript,typescript",
		"prefix": "sql",
		"body": [
			"const sqlQuery = `",
			"",
			"`;",
			"",
			"const sqlResults = query.runSuiteQL({",
			"query: sqlQuery,",
			"params: [],",
			"}).asMappedResults();",
			"",

			"return sqlResults;"
		],
		"description": "Insert template for SQL query"
	},
	"Insert Library Template": {
		"scope": "javascript,typescript",
		"prefix": "lib",
		"body": [
			"//@ts-ignore",
			"define(['N/log','$1', ], function (log, $2, ) {",
			"function ${3:functionName} ($4){",
			"}",
			"return {",
			"$3: $3,",
			"};",
			"});"
		],
		"description": "Insert template for library"
	},
	"Insert Helper Library Template": {
		"scope": "javascript,typescript",
		"prefix": "helperLib",
		"body": [
			"var helperFunctions = (function () {",
			"",
			"return {",
			"",
			"}",
			"})();"
		],
		"description": "Insert a template for a helper library"
	},
	"Insert MapReduce Script Template": {
		"scope": "javascript,typescript",
		"prefix": "mr",
		"body": [
			"//Insert JSDoc File Header",			
			"//@ts-ignore",
			"define(['N/log','$1', ], function (log, $2, ) {",
			"function getInputData(context) {",
			"var ${3:arrToReturn}= [];",
			"return $3;",
			"}",
			"function map(context) {",
			"var parsedResult = JSON.parse(context.value);",
			"var {} = parsedResult;",
			"var processedSuccessfullyObj = '';",
			"if (processedSuccessfullyObj.processedSuccessfully) {",
			"context.write('', '');",
			"} else {",
			"throw error.create({",
			"message: processedSuccessfullyObj.errorLog,",
			"name: 'Map Error',",
			"});",
			"}",
			"}",
			"function reduce(context) {",
			"context.write({",
			"key: context.key,",
			"value: context.values,",
			"});",
			"}",
			"function summarize(context) {}",
			"return {",
			"getInputData,",
			"map,",
			"reduce,",
			"summarize,",
			"};",
			"});"
		],
		"description": "Insert template for a Map Reduce Script"
	},
	"Insert Scheduled Script Template": {
		"scope": "javascript,typescript",
		"prefix": "scsc",
		"body": [
			"//Insert JSDoc File Header",			
			"",
			"//@ts-ignore",
			"define(['N/log','$1', ], function (log, $2, ) {",
			" ",
			"function execute(context) {",
			"try {",
			" ",
			"} catch (e) {",
			"throw e;",
			"}",
			"}",
			"return {",
			"execute,",
			" };",
			" });"
		],
		"description": "Insert template for a Scheduled Script"
	},
	"Insert ClientSide Script Template": {
		"scope": "javascript,typescript",
		"prefix": "cs",
		"body": [
			"//Insert JSDoc File Header",			
			"",
			"//@ts-ignore",
			"define(['N/log','$1', ], function (log, $2, ) {",
			"var alertArr = [];",
			"var isValid = true;",
			"",
			"var validationFunctions = (function () {",
			"  function _setInvalid(message, setIsInValid) {",
			"  if (setIsInValid) {",
			"   isValid = false;",
			"}",
			"alertArr.push(message);",
			"   }",
			"",
			"function displayAlert() {",
			" if (alertArr.length > 0) {",
			"alertText = '';",
			"   alertArr.forEach(function (message) {",
			"   alertText += message + '\n';",
			"   });",
			"alert(alertText);",
			"   }",
			" }",
			" ",
			" return {",
			"   displayAlert: displayAlert,",
			" };",
			"  })();",
			" ",
			"function fieldChanged(context) {",
			"   var transaction = context.currentRecord;",
			" }",
			" ",
			" function lineInit(context) {}",
			" ",
			" function pageInit(context) {}",
			" ",
			" function validateLine(context) {}",
			" ",
			" function saveRecord(context) {}",
			" ",
			" return {",
			" pageInit,",
			" fieldChanged,",
			" lineInit,",
			" validateLine,",
			" saveRecord,",
			" };",
			" });",
			""
		],
		"description": "Insert template for a ClientSide Script"
	},
	"Insert UserEvent Script Template": {
		"scope": "javascript,typescript",
		"prefix": "ue",
		"body": [
			"//Insert JSDoc File Header",			
			"",
			"//@ts-ignore",
			"define(['N/log','$1', ], function (log, $2, ) {",
			"function beforeLoad(context) {",
			"",
			"}",
			"",
			"function beforeSubmit(context) {",
			"",
			"}",
			"",
			"function afterSubmit(context) {",
			"",
			"}",
			"",
			"return {",
			"beforeLoad,",
			"beforeSubmit,",
			"afterSubmit",
			"}",
			"})"
		],
		"description": "Insert template for a UserEvent Script"
	},
	"Insert Suitelet Custom Form and Get ValuesTemplate": {
		"scope": "javascript,typescript",
		"prefix": "slform",
		"body": [
			"//Insert JSDoc File Header",			
			"",
			"//@ts-ignore",
			"define(['N/log', 'N/ui/serverWidget','$1', ], function (log, serverWidget, $2, ) {",
			"return {",
			"onRequest: function (context) {",
			"var request = context.request;",
			"var response = context.response;",

			"if (request.method === 'GET') {",
			"var form = serverWidget.createForm({",
			"title: 'TitleHere',",
			"});",

			"var trackingFileUpload = form.addField({",
			"id: 'custpage_custIdHere',",
			"type: serverWidget.FieldType.FILE,",
			"label: 'FieldLabelHere',",
			"});",

			"form.addSubmitButton({",
			"label: 'SubmitTextHere',",
			"});",

			"response.writePage(form);",
			"} else {",
			"var valueFromField = request.files.custpage_custIdHere;",
			"}",
			"},",
			"};",
			"});"
		],
		"description": "Insert template for a UserEvent Script"
	},
	"Insert JSDoc File Header": {
		"scope": "javascript,typescript",
		"prefix": "jsdocheader",
		"body": [
			"/**",
			" * @description ${1:A short summary of what the script does}",
			" * ",
			" * </br><b>Deployed On:</b> ${2:RecordTypeHereIfUeOrCSScript}",
			" * </br><b>Execution Context:</b> ${3:CopySuiteScriptNamingInAllCaps}",
			" * </br><b>Event Type/Mode:</b> ${4:CopySuiteScriptNamingInAllCaps}",
			" * </br><b>Entry Points:</b> ${5:EntryPointIncamelCase}",
			" *",
			" * @NApiVersion 2.1",
			" * @NScriptType ${6:ScriptTypeHere}",
			" * @NAmdConfig /SuiteScripts/config.json",
			" * ",
			" * <AUTHOR>
			" * @module ${8:FileNameWithoutJsSuffix}",
			" */"
		],
		"description": "Insert template for JSDoc File Header"
	},
	"Insert customErrorObject.updateError": {
		"scope": "javascript,typescript",
		"prefix": "updateError",
		"body": [
			"throw customErrorObject.updateError({",
				"errorType: customErrorObject.ErrorTypes.${1:ERROR_TYPE_FROM_PREDEFINED_LIST},",
				"summary: '${2:SHORT_SUMMARY}',",
				"details: `${3:Error details in paragraph format}`,",
				"});",
		],
		"description": "Insert update error template for customErrorObject"
	},
	"Insert customErrorObject.throwError": {
		"scope": "javascript,typescript",
		"prefix": "throwError",
		"body": [
			"customErrorObject.throwError({",
			"summaryText: `${1:SHORT_SUMMARY}`,",
			"error:${2:errorObjectFromCatch},",
			"recordId:${4: recordId},",
			"recordName:${5: recordName},",
			"recordType:${6: recordType},",
			"errorWillBeGrouped:${7: false},",
			"});",
		],
		"description": "Insert throw error template for customErrorObject"
	}
}
