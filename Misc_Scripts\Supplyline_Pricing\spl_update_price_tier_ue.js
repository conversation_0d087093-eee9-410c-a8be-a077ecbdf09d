/**
 * @description Changes a customer's pricing levels if the tier is changed
 *
 * </br><b>Deployed On:</b> Customers
 * </br><b>Event Type/Mode:</b> ALL
 * </br><b>Entry Points:</b> AfterSubmit
 *
 * @NApiVersion 2.1
 * @NScriptType UserEventScript
 * @NAmdConfig /SuiteScripts/config.json
 *
 * <AUTHOR>
 * @module spl_update_price_tier_ue
 */
define([
  "require",
  "N/query",
  "N/record",
  "N/log",
  "../../Classes/vlmd_custom_error_object",
  "../../Classes/spl_price_tier",
], (/** @type {any} */ require) => {
  const record = require("N/record");
  const log = require("N/log");
  /** @type {import("../../Classes/vlmd_custom_error_object").CustomErrorObject} */
  const CustomErrorObject = require("../../Classes/vlmd_custom_error_object");
  const customErrorObject = new CustomErrorObject();

  /** @type {import("../../Classes/spl_price_tier").SplPriceTier} */
  const SplPriceTier = require("../../Classes/spl_price_tier");

  function afterSubmit(context) {
    try {
      const { newRecord, oldRecord } = context;
      const customerRecord = record.load({
        type: record.Type.CUSTOMER,
        id: newRecord.id,
        isDynamic: true,
      });

      const subsidiary = customerRecord.getValue("subsidiary");

      if (subsidiary != 1) {
        return;
      }

      const oldPriceTier = !oldRecord
        ? null
        : oldRecord.getValue("custentity_spl_price_tier");
      const newPriceTier = customerRecord.getValue("custentity_spl_price_tier");

      if (oldPriceTier != newPriceTier) {
        let tierObj = new SplPriceTier(newPriceTier, customerRecord);
        try{
          tierObj.setPriceLevels();
        } catch (e) {
          log.error("Failed to set price levels", {
            customerId: customerRecord.id,
            newPriceTier: newPriceTier,
            error: e.message
          });
        }
      }
    } catch (e) {
      customErrorObject.throwError({
        summaryText: `ERROR_UPDATING_CUSTOMER`,
        error: e,
        recordId: context.newRecord.id,
        recordType: `CUSTOMER`,
      });
    }
  }

  return { afterSubmit };
});
