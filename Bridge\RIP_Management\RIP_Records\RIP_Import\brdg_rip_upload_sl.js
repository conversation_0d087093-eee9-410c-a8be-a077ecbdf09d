/**
 * @description Import RIP file and trigger the MRs that will create
 * tier levels, tier groups, agreement records and agreement details
 *
 * @NApiVersion 2.1
 * @NScriptType Suitelet
 * @NAmdConfig /SuiteScripts/config.json
 *
 * @param {import ("N/types")}
 * <AUTHOR>
 * <AUTHOR>
 * @module brdg_rip_upload_sl
 */

define([
  "require",
  "N/ui/serverWidget",
  "N/redirect",
  "N/runtime",
  "N/task",
  "../../../../Classes/vlmd_custom_error_object",
], function (require) {
  const serverWidget = require("N/ui/serverWidget");
  const redirect = require("N/redirect");
  const runtime = require("N/runtime");
  const task = require("N/task");

  return {
    /**
     * Displays the general instructions on how to import RIP records from a file
     * Accepts a file and vendor as input in GET
     * Uploads the file to RIP Imports folder and submits the RIP Import Records MR in POST
     *
     * @param {import("N/types").EntryPoints.Suitelet.onRequestContext} context Suitelet context
     */
    onRequest: function (context) {
      /** @type {import("../../../../Classes/vlmd_custom_error_object").CustomErrorObject} */

      const CustomErrorObject = require("../../../../Classes/vlmd_custom_error_object");
      const customErrorObject = new CustomErrorObject();

      try {
        const request = context.request;
        const response = context.response;
        const folderId = "4525344"; // RIP Imports folder

        if (context.request.method === "GET") {
          const form = serverWidget.createForm({
            title: "Import RIPs",
          });

          const instructions = form.addField({
            id: "custpage_instructions",
            type: serverWidget.FieldType.INLINEHTML,
            label: "Instructions",
          });
          instructions.updateLayoutType({
            layoutType: serverWidget.FieldLayoutType.OUTSIDEABOVE,
          });
          instructions.defaultValue = `
						<div class="instructions">
							<h2>Prepare the File for Upload</h2>
							<ol>
								<li>1. Convert the RIPs workbook sheet to a <b>CSV</b> file.</li>
								<li>2. Edit the header so that there is only <b>1 header row</b>. </li>
								<li>3. Remove any <b>forward slashes</b> from the headers</li>
								<li>4. Remove all <b>lines before the header</b>. This results to line 1 containing the header, and line 2 marking the start of the data rows.</li>
								<li>5. Search for all <b>commas</b> in the RIP sheet and replace them with whitespaces.</li>
							</ol>
							<br/>
								<p> After you click the "Import File" button, you will be redirected to <b><a href='https://5802576.app.netsuite.com/app/common/search/searchresults.nl?searchid=4628'>RIP Import Tasks<a/></b> saved search where you can track the import progress.<br/>
								When the scipt is finished processing an email with the results will be sent to the RIP import owner.
								</p>
						</div>
						<style type = "text/css"> 
							.instructions {
								border: 1px solid #417ed9;
								background: #cfeefc;
								padding: 0 10px 0 10px
							}
							.instructions ol {
								padding-left: unset;
								list-style: none !important;
							}
							.instructions li {
								font-size: 12px;
							}
						</style>	
					`;

          const vendorField = form.addField({
            id: "custpage_vendor",
            type: serverWidget.FieldType.SELECT,
            source: "vendor",
            label: "Vendor",
          });

          vendorField.defaultValue = 13694; //Allied Beverage

          vendorField.updateLayoutType({
            layoutType: serverWidget.FieldLayoutType.STARTROW,
          });

          const fileField = form.addField({
            id: "file",
            type: serverWidget.FieldType.FILE,
            label: "Rip Information Sheet",
          });
          fileField.updateLayoutType({
            layoutType: serverWidget.FieldLayoutType.OUTSIDEBELOW,
          });

          form.addSubmitButton({
            label: "Import File",
          });

          response.writePage(form);
        } else {
          const vendorId = context.request.parameters.custpage_vendor;

          if (!vendorId) {
            throw customErrorObject.updateError({
              errorType: customErrorObject.ErrorTypes.MISSING_USER_INPUT,
              summary: "MISSING_VENDOR_ID",
              details: `No vendor was selcted from the input form`,
            });
          }

          const fileToSave = request.files.file;

          if (!fileToSave) {
            throw customErrorObject.updateError({
              errorType: customErrorObject.ErrorTypes.MISSING_USER_INPUT,
              summary: "MISSING_FILE",
              details: `No file was selected for upload.`,
            });
          }

          fileToSave.name = new Date().toISOString() + "_" + fileToSave.name;
          fileToSave.folder = folderId;
          const fileId = fileToSave.save();

          const importRecordsMr = task.create({
            taskType: task.TaskType.MAP_REDUCE,
            scriptId: "customscript_brdg_rip_import_records_mr",
            deploymentId: "customdeploy_brdg_rip_import_records_mr",
            params: {
              custscript_brdg_rip_import_file: fileId,
              custscript_brdg_rip_import_owner: runtime.getCurrentUser().id,
              custscript_brdg_rip_vendor: vendorId,
            },
          });

          importRecordsMr.submit();

          redirect.toSavedSearchResult({
            id: "customsearch_brdg_rip_import_tasks",
          });
        }
      } catch (err) {
        customErrorObject.throwError({
          summaryText: `ERROR_UPLOADING_RIPS`,
          error: err,
        });
      }
    },
  };
});
