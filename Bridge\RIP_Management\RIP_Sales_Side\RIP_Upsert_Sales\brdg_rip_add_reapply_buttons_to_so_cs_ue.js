/**
 * @description Adds a button to the vendor credit to apply RIPS on applicable sales
 *
 * </br><b>Deployed On:</b> Vendor Credit
 * </br><b>Execution Context:</b> ALL
 * </br><b>Event Type/Mode:</b> ALL
 * </br><b>Entry Points:</b> beforeLoad
 *
 * @NApiVersion 2.1
 * @NScriptType UserEventScript
 *
 * <AUTHOR>
 * @module brdg_rip_reapply_button_ue
 */

define([
  "require",
  "N/log",
  "../../../../Classes/vlmd_custom_error_object",
  "../../../Libraries/brdg_helper_functions_lib",
], (/** @type {any} */ require) => {
  const log = require("N/log");
  /**@type {import ("../../../../Classes/vlmd_custom_error_object").CustomErrorObject}} */
  const CustomErrorObject = require("../../../../Classes/vlmd_custom_error_object");

  /**@type {import("../../../Libraries/brdg_helper_functions_lib")} */
  const bridgeHelperFunctionsLib = require("../../../Libraries/brdg_helper_functions_lib");

  /*
   *
   * @param {import("N/types").EntryPoints.UserEvent.beforeLoadContext} context Before load script context
   */
  function beforeLoad(context) {
    const customErrorObject = new CustomErrorObject();
    try {
      if (context.type != context.UserEventType.CREATE) {
        const billCreditRecord = context.newRecord;
        const subsidiariesArr = billCreditRecord.getValue({
          fieldId: "subsidiary",
        });
        const bridgeSubsidiaries =
          bridgeHelperFunctionsLib.getBridgeSubsidiaries();
        const isBridgeSubsidiary = bridgeHelperFunctionsLib.isBridgeSubsidiary(
          subsidiariesArr,
          bridgeSubsidiaries
        );

        if (!isBridgeSubsidiary) {
          return;
        }
        const billCreditId = billCreditRecord.id;
        const recordType = billCreditRecord.type;

        //Add the update & apply rips button to bills
        recordType == "vendorcredit" &&
          context.form.addButton({
            id: "custpage_update_rips_sales",
            label: "Update Current RIP Applications",
            functionName: `confirmAndUpdateRips(${billCreditId})`,
          });

        recordType == "vendorcredit" &&
          context.form.addButton({
            id: "custpage_apply_remaining_sales",
            label: "Apply Remaining RIPS",
            functionName: `confirmAndApplyRips(${billCreditId})`,
          });

        context.form.clientScriptFileId = "8905158";
      }
    } catch (e) {
      customErrorObject.throwError({
        summaryText: "ERROR_ADDING_BUTTON",
        error: e,
        recordType: recordType,
        recordId: billCreditId,
      });
    }
  }

  return {
    beforeLoad,
  };
});
