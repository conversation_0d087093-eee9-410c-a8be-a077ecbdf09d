/**
 * @NApiVersion 2.1
 * @NScriptType MapReduceScript
 * @NAmdConfig /SuiteScripts/config.json
 */
//@ts-ignore
define([
	"N/log",
	"N/error",
	"N/search",
	"UpdateGrossProfitValuesOnInvoiceLib",
], function (log, error, search, updateGrossProfitValuesOnInvoicesLib) {
	function getInputData(context) {
		var recordsArr = [];

		/* REMEMBER TO TURN OFF AVATAX BEFORE RUNNING
		 https://5802576.app.netsuite.com/app/common/search/searchresults.nl?searchid=3716&whence=
		 */
		const searchInternalId = 3716; //SuiteScript Search: Invoices to Update Commission Values

		const searchObj = search.load({
			id: searchInternalId,
		});

		const myPagedData = searchObj.runPaged();

		myPagedData.pageRanges.forEach(function (pageRange) {
			let myPage = myPagedData.fetch({ index: pageRange.index });

			myPage.data.forEach(function (result) {
				recordsArr.push({
					invoiceInternalId: result.id,
					invoiceName: result.getValue(searchObj.columns[1]),
					recordType: result.getValue(searchObj.columns[2]),
					createdFromRecordType: result.getValue(searchObj.columns[3]),
				});
			});
		});

		return recordsArr;
	}

	function map(context) {
		const parsedObj = JSON.parse(context.value);
		const {
			invoiceInternalId,
			invoiceName,
			recordType,
			createdFromRecordType,
		} = parsedObj;

		if (invoiceInternalId % 30 == 0) {
			log.debug("invoiceInternalId", invoiceInternalId);
		}

		const updatedGrossProfitValuesObj =
			updateGrossProfitValuesOnInvoicesLib.updateGrossProfitValues(
				invoiceInternalId,
				recordType,
				createdFromRecordType
			);

		if (updatedGrossProfitValuesObj.errorLog.length <= 0) {
			//Write successfully processed invoices to context
			context.write(invoiceName, invoiceInternalId);
		} else {
			throw error.create({
				name: invoiceName,
				message: `${invoiceName} - ${updatedGrossProfitValuesObj.errorLog}`,
			});
		}
	}

	function summarize(context) {
		var documentsProcessedSuccesfullyText =
			getDocumentsProcessedSuccessfully(context);
		var errorMessagesText = getErrorMessages(context);
		logResults();

		function getDocumentsProcessedSuccessfully(summary) {
			let summaryText = ``;

			summary.output.iterator().each(function (key, value) {
				summaryText += `${key}, `;

				return true;
			});

			return summaryText;
		}

		function getErrorMessages(summary) {
			let errorText = ``;

			summary.mapSummary.errors.iterator().each(function (key, value) {
				var errorMessage = JSON.parse(value).message;

				errorText += `${errorMessage}, 
				`;
				log.debug("Error Updating Gross Profit Values", errorMessage);

				return true;
			});

			return errorText;
		}

		function logResults() {
			if (documentsProcessedSuccesfullyText) {
				log.debug({
					title: `Invoices Processed Successfully`,
					details: documentsProcessedSuccesfullyText,
				});
			}

			if (errorMessagesText) {
				log.error({
					title: "Error Log",
					details: errorMessagesText,
				});
			}
		}
	}

	return {
		getInputData,
		map,
		summarize,
	};
});
