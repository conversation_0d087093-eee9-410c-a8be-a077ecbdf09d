/**
 * @NApiVersion 2.1
 * @NScriptType scheduledscript
 * @NAmdConfig /SuiteScripts/config.json
 */

define(["N/search", "PostAfterShipTrackingLib"], function (
	search,
	postAftershipTrackingLib
) {
	function execute(context) {
		var trackingsToPost = getTrackingsToPost();
		log.debug({
			title: "trackings arr",
			details: trackingsToPost,
		});
		postNewTrackings();

		function getTrackingsToPost() {
			var searchObj = search.create({
				type: "itemfulfillment",
				filters: [
					["type", "anyof", "ItemShip"],
					"AND",
					["mainline", "is", "T"],
					"AND",
					["datecreated", "onorafter", "5/14/2021 12:00 am"],
					"AND",
					["custbody_spl_trckng_psted_to_aftership", "is", "F"],
					"AND",
					["custbody_spl_tracking_number", "isnotempty", ""],
					//TODO: rename custbody4 in Prod on field record and replace in IF email script - see if used anywhere else
					"AND",
					["number", "equalto", "18818"],
				],
				columns: [
					search.createColumn({ name: "tranid", label: "Document Number" }),
					search.createColumn({
						name: "custbody_spl_tracking_number",
						label: "Tracking Number",
					}),
				],
			});

			return searchObj.run().getRange({
				start: 0,
				end: 1000,
			});
		}

		function postNewTrackings() {
			trackingsToPost.forEach((tracking) => {
				log.debug({
					title: "tracking",
					details: tracking,
				});
				var trackingNumber = tracking.getValue(tracking.columns[1]);

				if (!trackingNumber) {
					return;
				}

				var itemFulfillmentId = tracking.id;

				postAftershipTrackingLib.postTracking(
					trackingNumber,
					itemFulfillmentId
				);
			});
		}
	}

	return {
		execute: execute,
	};
});
