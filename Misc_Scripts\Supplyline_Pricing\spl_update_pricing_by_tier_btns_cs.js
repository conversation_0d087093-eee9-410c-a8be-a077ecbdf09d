/**
 * @description Redirects to SL that creates MR Task to Update Customers Based on Tier
 *
 * </br><b>Implemented On:</b> SPL Price Tier Records
 * </br><b>Execution Context:</b> ALL
 * </br><b>Event Type/Mode:</b> ALL
 * </br><b>Entry Points:</b> Page Init
 *
 * @NApiVersion 2.1
 * @NScriptType ClientScript
 * @NAmdConfig /SuiteScripts/config.json
 * @param {import ("N/types")}
 *
 * <AUTHOR>
 * @module spl_update_pricing_by_tier_btns_cs
 */

define(["require", "N/log", "N/url"], function (require) {
  const log = require("N/log");
  const url = require("N/url");

  function pageInit() {
    //CS Scripts need at least one entry mode function
  }

  function _resolveScript(params) {
    //Brings to the update pricing tier SL
    var suiteletURL = url.resolveScript({
      scriptId: "customscript_spl_update_pricing_by_tier",
      deploymentId: "customdeploy_spl_update_price_by_tier",
      params: params,
    });

    document.location = suiteletURL;
  }

  function updateCustomers(tierLevelId, priceGroupId, priceLevelId) {
    _resolveScript({
      custpage_tier_level: tierLevelId,
      custpage_product_category: priceGroupId,
      custpage_price_level: priceLevelId,
      goStraightToMRTask: true,
    });
  }

  return {
    updateCustomers,
    pageInit,
  };
});
