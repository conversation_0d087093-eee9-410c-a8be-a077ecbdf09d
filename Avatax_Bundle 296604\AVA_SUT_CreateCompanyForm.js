/******************************************************************************************************
	Script Name - AVA_SUT_CreateCompanyForm.js
	Company -     Avalara Technologies Pvt Ltd.
******************************************************************************************************/

/**
 *@NApiVersion 2.0
 *@NScriptType Suitelet
*/

define(['N/ui/serverWidget', 'N/search', 'N/redirect', './utility/AVA_Library'],
	function(ui, search, redirect, ava_library){
		function onRequest(context){
			var checkServiceSecurity = ava_library.mainFunction('AVA_CheckSecurity', 27);
			
			if(checkServiceSecurity == 0){
				if(context.request.method === 'GET'){
					var form = ui.createForm({
						title: 'Create AvaTax Company'
					});
					form.clientScriptModulePath = './AVA_CLI_CreateCompanyForm.js';
					
					var subsidiaryList = form.addField({
						id: 'ava_subsidiarylist',
						label: 'Subsidiary',
						type: ui.FieldType.SELECT
					});
					subsidiaryList.isMandatory = true;
					subsidiaryList.addSelectOption({
						value : '',
						text: '<Select Subsidiary>'
					});
					
					var searchRecord = search.create({
						type: search.Type.SUBSIDIARY,
						filters: ['isinactive', 'is', 'F'],
						columns: ['namenohierarchy']
					});
					var searchresult = searchRecord.run();
					
					var i = 0;
					searchresult.each(function(result){
						subsidiaryList.addSelectOption({
							value : result.id,
							text: result.getValue('namenohierarchy')
						});
						
						i++;
						return true;
					});
					
					var avaConfigObjRec = ava_library.mainFunction('AVA_LoadValuesToGlobals', '');
					
					var companyName = form.addField({
						id: 'ava_companyname',
						label: 'Company Name',
						type: ui.FieldType.TEXT
					});
					companyName.isMandatory = true;
					companyName.maxLength = 50;
					companyName.updateDisplaySize({
						width: 40,
						height: 0
					});
					
					var addressLine1 = form.addField({
						id: 'ava_address1',
						label: 'Address 1',
						type: ui.FieldType.TEXT
					});
					addressLine1.isMandatory = true;
					addressLine1.updateDisplaySize({
						width: 40,
						height: 0
					});
					
					var addressLine2 = form.addField({
						id: 'ava_address2',
						label: 'Address 2',
						type: ui.FieldType.TEXT
					});
					addressLine2.updateDisplaySize({
						width: 40,
						height: 0
					});
					
					var city = form.addField({
						id: 'ava_city',
						label: 'City',
						type: ui.FieldType.TEXT
					});
					city.isMandatory = true;
					city.updateDisplaySize({
						width: 40,
						height: 0
					});
					
					var state = form.addField({
						id: 'ava_state',
						label: 'State',
						type: ui.FieldType.TEXT
					});
					state.isMandatory = true;
					state.updateDisplaySize({
						width: 40,
						height: 0
					});
					
					var zip = form.addField({
						id: 'ava_zip',
						label: 'Zip',
						type: ui.FieldType.TEXT
					});
					zip.isMandatory = true;
					zip.updateDisplaySize({
						width: 40,
						height: 0
					});
					
					var country = form.addField({
						id: 'ava_country',
						label: 'Country',
						type: ui.FieldType.TEXT
					});
					country.isMandatory = true;
					country.updateDisplaySize({
						width: 40,
						height: 0
					});
					
					var companyCode = form.addField({
						id: 'ava_companycode',
						label: 'Company Code',
						type: ui.FieldType.TEXT
					});
					companyCode.isMandatory = true;
					companyCode.maxLength = 25;
					companyCode.updateDisplaySize({
						width: 40,
						height: 0
					});
					companyCode.updateLayoutType({
						layoutType: ui.FieldLayoutType.STARTROW
					});
					companyCode.updateBreakType({
						breakType: ui.FieldBreakType.STARTCOL
					});
					
					var email = form.addField({
						id: 'ava_email',
						label: 'Email',
						type: ui.FieldType.EMAIL
					});
					email.isMandatory = true;
					email.updateDisplaySize({
						width: 40,
						height: 0
					});
					
					var fName = form.addField({
						id: 'ava_firstname',
						label: 'First Name',
						type: ui.FieldType.TEXT
					});
					fName.isMandatory = true;
					fName.updateDisplaySize({
						width: 40,
						height: 0
					});
					
					var lName = form.addField({
						id: 'ava_lastname',
						label: 'Last Name',
						type: ui.FieldType.TEXT
					});
					lName.isMandatory = true;
					lName.updateDisplaySize({
						width: 40,
						height: 0
					});
					
					var phoneNumber = form.addField({
						id: 'ava_phonenumber',
						label: 'Phone Number',
						type: ui.FieldType.PHONE
					});
					phoneNumber.isMandatory = true;
					phoneNumber.updateDisplaySize({
						width: 40,
						height: 0
					});
					
					var tinNumber = form.addField({
						id: 'ava_tinnumber',
						label: 'Business Tax Identification Number (TIN)',
						type: ui.FieldType.TEXT
					});
					tinNumber.maxLength = 11;
					tinNumber.updateDisplaySize({
						width: 40,
						height: 0
					});
					
					var accountValue = form.addField({
						id: 'ava_accvalue',
						label: 'Account Number',
						type: ui.FieldType.TEXT
					});
					accountValue.updateDisplayType({
						displayType: ui.FieldDisplayType.HIDDEN
					});
					accountValue.defaultValue = avaConfigObjRec['AVA_AccountValue'];
					var serviceUrl = form.addField({
						id: 'ava_serviceurl',
						label: 'Service URL',
						type: ui.FieldType.TEXT
					});
					serviceUrl.updateDisplayType({
						displayType: ui.FieldDisplayType.HIDDEN
					});
					serviceUrl.defaultValue = avaConfigObjRec['AVA_ServiceUrl'];
					
					var companyId = form.addField({
						id: 'ava_companyid',
						label: 'Company ID',
						type: ui.FieldType.TEXT
					});
					companyId.updateDisplayType({
						displayType: ui.FieldDisplayType.HIDDEN
					});
					
					form.addSubmitButton({
						label: 'Create Company'
					});
					
					context.response.writePage({
						pageObject: form
					});
				}
				else{
					redirect.toSuitelet({
						scriptId: 'customscript_avaenablenexus_suitelet',
						deploymentId: 'customdeploy_avaenablenexus_suitelet',
						parameters: {
							'subid': context.request.parameters.ava_subsidiarylist,
							'companyid': context.request.parameters.ava_companyid
						}
					});
				}
			}
			else{
				context.response.write({
					output: checkServiceSecurity
				});
			}
		}
		
		return{
			onRequest: onRequest
		};
	}
);
