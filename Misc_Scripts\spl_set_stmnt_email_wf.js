/**
 *@NApiVersion 2.0
 *@NScriptType workflowactionscript
 */

//@ts-ignore
define(["N/log", "N/record", "N/runtime"], function (log, record, runtime) {
	function onAction(context) {
		var script = runtime.getCurrentScript();
		var statementEmail = script.getParameter({
			name: "custscript_spl_stmnt_email",
		});
		var invoiceEmail = script.getParameter({
			name: "custscript_spl_invoice_email",
		});

		var customer = context.newRecord;
		var customerEmail = customer.getValue("email");

		var recipientEmail = "";
		if (statementEmail) {
			recipientEmail = statementEmail;
		} else if (invoiceEmail) {
			recipientEmail = invoiceEmail;
		} else {
			recipientEmail = customerEmail;
		}

		return recipientEmail;
	}

	return {
		onAction: onAction,
	};
});
