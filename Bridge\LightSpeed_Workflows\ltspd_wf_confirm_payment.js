/**
 * @description SL to show pop-up form to confirm check/doordash payment

 * @NApiVersion 2.1
 * @NScriptType Suitelet
 * @NAmdConfig /SuiteScripts/config.json
 * 
 * <AUTHOR> <PERSON>
 * @module ltspd_wf_check_payment
 */

define(["require", "N/log", "N/ui/serverWidget"], function (require) {
	const log = require("N/log");
	const serverWidget = require("N/ui/serverWidget");

	function onRequest(context) {
		if (context.request.method === "GET") {
			var form = serverWidget.createForm({
				title: " ", //No title because we don't want any extra text shown
			});

			var jsField = form.addField({
				id: "check_payment_form",
				type: serverWidget.FieldType.INLINEHTML,
				label: "HTML",
			});

			jsField.defaultValue = `<html>

			<body>
				<!--The below links are stylesheets from LightSpeed. If something is displaying funny, check these first.-->
				<link href='//vendappcdn.freetls.fastly.net/webregister/css/vendor-9e83fa82a9.css' rel='stylesheet'>
				<link href='//vendfrontendassets.freetls.fastly.net/fonts/fonts-v9.css' rel='stylesheet'>
			
			
				<div class="vd-modals-container">
					<div class="vd-overlay vd-overlay--invisible"></div>
			
					<div class="vd-dialog" role="dialog">
						<div class="vd-modal-container vd-modal--size-medium vd-modal--with-close-button" tabindex="-1">
							<div class="vd-modal-inner-container">
			
								<div class="vd-dialog-header vd-modal--size-medium">
									<h1 class="vd-header vd-header--dialog">Confirm payment was received.</h1>
								</div>
			
								<div class="vd-dialog-actions vd-modal--size-medium">
									<div class="vd-btn-group">
										<button type="button" class="vd-btn vd-btn--no" onClick="
									window.parent.postMessage(JSON.stringify({'step':'DECLINE'}), 'https://thevineyardmadison.vendhq.com');  <!--LightSpeed is switching to the retail.lightspeed.app domain but madison's old vendhq domain is still active.-->
									window.parent.postMessage(JSON.stringify({'step':'DECLINE'}), 'https://thevineyardmadison.retail.lightspeed.app'); 
									window.parent.postMessage(JSON.stringify({'step':'DECLINE'}), 'https://thevineyardwestgate.retail.lightspeed.app'); 
									window.parent.postMessage(JSON.stringify({'step':'DECLINE'}), 'https://thevineyardsouth.retail.lightspeed.app'); 
									window.parent.postMessage(JSON.stringify({'step':'DECLINE'}), 'https://thevineyardexpress.retail.lightspeed.app'); 
									window.parent.postMessage(JSON.stringify({'step':'DECLINE'}), 'https://thevineyardbergenfield.retail.lightspeed.app'); 
									window.parent.postMessage(JSON.stringify({'step':'DECLINE'}), 'https://lotsofliquor.retail.lightspeed.app'); 
									">Choose another payment method</button>
			
										<button type="button" class="vd-btn vd-btn--supplementary" onClick="
										window.parent.postMessage(JSON.stringify({'step':'ACCEPT'}), 'https://thevineyardmadison.vendhq.com');
										window.parent.postMessage(JSON.stringify({'step':'ACCEPT'}), 'https://thevineyardmadison.retail.lightspeed.app'); 
										window.parent.postMessage(JSON.stringify({'step':'ACCEPT'}), 'https://thevineyardwestgate.retail.lightspeed.app'); 
										window.parent.postMessage(JSON.stringify({'step':'ACCEPT'}), 'https://thevineyardsouth.retail.lightspeed.app'); 
										window.parent.postMessage(JSON.stringify({'step':'ACCEPT'}), 'https://thevineyardexpress.retail.lightspeed.app'); 
										window.parent.postMessage(JSON.stringify({'step':'ACCEPT'}), 'https://thevineyardbergenfield.retail.lightspeed.app'); 
										window.parent.postMessage(JSON.stringify({'step':'ACCEPT'}), 'https://lotsofliquor.retail.lightspeed.app'); 
										">Payment Received</button>
									</div>
								</div>
			
			
							</div>
						</div>
					</div>
			
				</div>
			
			</body>
			
			</html>`;

			context.response.writePage(form);
		}
	}
	return { onRequest };
});
