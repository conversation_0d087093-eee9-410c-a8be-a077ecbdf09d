/**
 * A helper lib to compare 2 addresses, used to compare an address sent and what already exists in NS
 * Called by multiple EDI scripts
 *
 * @NApiVersion 2.x
 */

//@ts-ignore
define([], function () {
	function compareAddresses(addressSent, addressReceived) {
		var addressesObj = [
			{
				findWord: "NORTH",
				replaceWith: "N",
			},
			{
				findWord: "SOUTH",
				replaceWith: "S",
			},
			{
				findWord: "EAST",
				replaceWith: "E",
			},
			{
				findWord: "WEST",
				replaceWith: "W",
			},
			{
				findWord: "NORTHWEST",
				replaceWith: "NW",
			},
			{
				findWord: "SOUTHWEST",
				replaceWith: "SW",
			},
			{
				findWord: "NORTHEAST",
				replaceWith: "NE",
			},
			{
				findWord: "SOUTHEAST",
				replaceWith: "SE",
			},
			{
				findWord: "DRIVE",
				replaceWith: "DR",
			},
			{
				findWord: "ROAD",
				replaceWith: "RD",
			},
			{
				findWord: "AVENUE",
				replaceWith: "AVE",
			},
			{
				findWord: "PLAZA",
				replaceWith: "PLZ",
			},
			{
				findWord: "STREET",
				replaceWith: "ST",
			},
			{
				findWord: "TURNPIKE",
				replaceWith: "TPKE",
			},
			{
				findWord: "HIGHWAY",
				replaceWith: "HWY",
			},
			{
				findWord: "SAINT",
				replaceWith: "ST",
			},
			{
				findWord: "COURT",
				replaceWith: "CT",
			},
			{
				findWord: "ROUTE",
				replaceWith: "RT",
			},
			{
				findWord: "HIGHWAY",
				replaceWith: "HWY",
			},
			{
				findWord: "EXTENSION",
				replaceWith: "EXT",
			},
			{
				findWord: "TRAIL",
				replaceWith: "TRL",
			},
			{
				findWord: "PARKWAY",
				replaceWith: "PKWY",
			},
			{
				findWord: "MOUNT",
				replaceWith: "MT",
			},
		];

		//Convert all to uppercase, remove . and , duplicate spaces, and extra white space
		addressSent = addressSent
			.toUpperCase()
			.split(".")
			.join("")
			.split(",")
			.join("")
			.split("  ")
			.join(" ")
			.trim();
		addressReceived = addressReceived
			.toUpperCase()
			.split(".")
			.join("")
			.split(",")
			.join("")
			.split("  ")
			.join(" ")
			.trim();

		var addressSentArr = [];
		var addressReceivedArr = [];

		if (addressSent == addressReceived) {
			//Address matches exactly
			return true;
		} else {
			//Compare using a less strict validation
			addressSentArr = addressSent.split(" ");
			addressReceivedArr = addressReceived.split(" ");

			//Replace common word differences - Example: ROAD vs. RD, WEST vs. W
			addressesObj.forEach((obj) =>
				replaceSentWords(obj.findWord, obj.replaceWith)
			);
			addressesObj.forEach((obj) =>
				replaceReceivedWords(obj.findWord, obj.replaceWith)
			);

			addressSent = addressSentArr.join(" ");
			addressReceived = addressReceivedArr.join(" ");

			if (addressSent == addressReceived) {
				return true;
			}

			//Compare only the first word, usually the street #
			let firstWordSent = addressSentArr.shift();
			let firstWordReceived = addressReceivedArr.shift();

			if (firstWordSent == firstWordReceived) {
				return true;
			}
		}

		return false;

		function replaceSentWords(findWord, replaceWith) {
			var index = addressSentArr.indexOf(findWord);
			if (index !== -1) {
				addressSentArr[index] = replaceWith;
			}
		}

		function replaceReceivedWords(findWord, replaceWith) {
			var index = addressReceivedArr.indexOf(findWord);
			if (index !== -1) {
				addressReceivedArr[index] = replaceWith;
			}
		}
	}

	return {
		compareAddresses,
	};
});
