/**
 * @description Iterates over all active BRDG items and updates the LPP, executive and cost price levels
 *
 * </br><b>Schedule:</b> Runs every night @ 11:30 PM
 *
 * @NApiVersion 2.1
 * @NScriptType MapReduceScript
 * @NAmdConfig /SuiteScripts/config.json
 *
 * <AUTHOR>
 * @module brdg_update_pricing_mr
 */

//@ts-ignore
define([
	"N/log",
	"N/record",
	"N/search",
	"N/email",
	"N/error",
	"UpdateBridgeItemFieldsLib",
	"MapReduceSummaryStageHandling",
	"BridgeHelperFunctionsLib"
], function (
	log,
	record,
	search,
	email,
	error,
	updateItemFieldsLib,
	MapReduceSummaryStageHandling,
	bridgeHelperFunctionsLib
) {
	function getInputData() {
		//Get internal ids of all BRDG items
		try {
			const bridgeSubsidiaries = bridgeHelperFunctionsLib.getBridgeSubsidiaries();

			var itemSearchObj = search.create({
				type: "item",
				filters: [
					[
						"subsidiary",
						"anyof"
					].concat(bridgeSubsidiaries.map((/** @type {number} */ subsidiary) => subsidiary.toString())),
					"AND",
					["isinactive", "is", "F"],
					"AND",
					["type", "anyof", "InvtPart", "Kit"],
					//Use this for testing purposes
					// "AND",
					// ["internalid", "anyof", "38003", "25086","25316","25323"],
				],
				columns: [
					search.createColumn({ name: "internalid", label: "Internal ID" }),
					search.createColumn({ name: "type", label: "Type" }),
				],
			});
			const myPagedData = itemSearchObj.runPaged();

      var itemInternalIdsArr = [];
      myPagedData.pageRanges.forEach(function (pageRange) {
        let myPage = myPagedData.fetch({ index: pageRange.index });
        myPage.data.forEach(function (result) {
          let itemId = result.getValue({
            name: "internalId",
          });
          let itemType = result.getValue({
            name: "type",
          });

          itemInternalIdsArr.push({ itemId, itemType });
        });
      });
    } catch (e) {
      throw "Error getting input data! " + e;
    }

    return itemInternalIdsArr;
  }

  function reduce(context) {
    const itemObj = JSON.parse(context.values);
    const itemId = itemObj.itemId;
    try {
      const itemRecordType =
        itemObj.itemType == "KIT" ||
        itemObj.itemType == "Kit" ||
        itemObj.itemType == "kititem"
          ? "KIT_ITEM"
          : "INVENTORY_ITEM";
      const itemRecord = record.load({
        type: record.Type[itemRecordType],
        id: itemId,
        isDynamic: true,
      });
      //Needs dynamic to select line

      //Get the values of the last purchase price currently set on the item
      const currentLolLppPrice = itemRecord.getValue(
        "custitem_vend_last_price_lol"
      );
      const currentGgnLppPrice = itemRecord.getValue(
        "custitem_vend_last_price_vnggn"
      );
      const currentGGsLppPrice = itemRecord.getValue(
        "custitem_vend_last_price_vnggs"
      );
      const currentVybLppPrice = itemRecord.getValue(
        "custitem_vend_last_price_vyb"
      );
      const currentExpLppPrice = itemRecord.getValue(
        "custitem_vend_last_price_vexp"
      );
      const currentVwgLppPrice = itemRecord.getValue(
        "custitem_vend_last_price_vnyrd_wstgt"
      );
      //Query for actual last purchase price of the item
      const lppObj = updateItemFieldsLib.getLastPurchasePrice(
        itemId,
        itemRecordType
      );
      if (!lppObj || JSON.stringify(lppObj) === "{}") {
        return;
      }

      if (
        //2 cases(for each level):
        //Either there is a current price that is NOT accurate,
        //or there is no price but there should be
        (currentGgnLppPrice &&
          currentGgnLppPrice != lppObj.lastPurchasePriceGgn) ||
        (!currentGgnLppPrice && lppObj.lastPurchasePriceGgn) ||
        (currentGGsLppPrice &&
          currentGGsLppPrice != lppObj.lastPurchasePriceGgs) ||
        (!currentGGsLppPrice && lppObj.lastPurchasePriceGgs) ||
        (currentLolLppPrice &&
          currentLolLppPrice != lppObj.lastPurchasePriceLol) ||
        (!currentLolLppPrice && lppObj.lastPurchasePriceLol) ||
        (currentVybLppPrice &&
          currentVybLppPrice != lppObj.lastPurchasePriceVyb) ||
        (!currentVybLppPrice && lppObj.lastPurchasePriceVyb) ||
        (currentExpLppPrice &&
          currentExpLppPrice != lppObj.lastPurchasePriceExp) ||
        (!currentExpLppPrice && lppObj.lastPurchasePriceExp) ||
        (currentVwgLppPrice &&
          currentVwgLppPrice != lppObj.lastPurchasePriceVwg) ||
        (!currentVwgLppPrice && lppObj.lastPurchasePriceVwg)
      ) {
        //If LPP should be changed, set it and also update the executive and cost pricing levels accordingly
        const setSecondQuantity =
          updateItemFieldsLib.checkForQuantityPricing(itemRecord);
        updateItemFieldsLib.setLastPurchasePricesFields(itemRecord, lppObj);
        updateItemFieldsLib.addExecutivePricingPerStore(
          itemRecord,
          lppObj,
          setSecondQuantity
        );
        updateItemFieldsLib.addCostPricePerStore(
          itemRecord,
          lppObj,
          setSecondQuantity
        );
        itemRecord.save({
          ignoreMandatoryFields: true,
        });
        context.write({
          key: itemId,
          value: "updated lpp or pricing levels",
        });
      }
    } catch (e) {
      throw error.create({
        name: "Processing Error(s): ",
        message: `Error with item ${itemId}! ${e.message}`,
      });
    }
  }

  function summarize(context) {
    const stageHandling = new MapReduceSummaryStageHandling(context);
    stageHandling.printRecordsProcessed();
    const itemsErrorsArr = [];
    context.reduceSummary.errors.iterator().each((key, err) => {
      const errorObject = JSON.parse(err);
      itemsErrorsArr.push(`${errorObject.message}\n`);
    });
    if (itemsErrorsArr.length > 0) {
      email.send({
        author: 15131,
        recipients: [43398],//Miri Landau NS user ID
        subject: "BRDG Items Unable to Update",
        body: itemsErrorsArr.toString(),
      });
    }
  }

  return {
    getInputData: getInputData,
    reduce: reduce,
    summarize: summarize,
  };
});
