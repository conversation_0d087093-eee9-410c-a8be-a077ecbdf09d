/**
 * @NApiVersion 2.1
 */

//@ts-ignore
define(["N/log", "N/email", "Numeral"], function (log, email, numeral) {
	function send850Email(
		csrErrorsArr,
		programmingErrorsArr,
		documentName,
		customerName,
		customerData,
		purchaseOrderObj,
		documentInternalId,
		isEdiTransaction, //Specifies if transaction is an EDI transaction or an ORM transaction (Valmar/Revival)
		failureErrorRecipient,
		failureErrorCC,
		ediFileName
	) {
		try {
			var poNumber,
				programmingErrorsText,
				csrErrorText,
				programmingResultsDataObj = {},
				csrResultsDataObj = { documentControlNumbers: [] };

			poNumber = isEdiTransaction
				? purchaseOrderObj.poNumber
				: purchaseOrderObj.transactionControlNumber;

			programmingErrorsText = programmingErrorsArr
				.filter((errorObj) => errorObj.logMessage)
				.map((errorObj) => `${errorObj.logMessage}`)
				.join("\n");

			csrErrorText = csrErrorsArr
				.filter((errorObj) => errorObj.logMessage)
				.map((errorObj) => `${errorObj.logMessage}`)
				.join("\n");

			programmingErrorsText && setProgrammingObjValues();
			setCSRObjValues();
			logResults();

			return {
				csrResultsData: csrResultsDataObj,
				programmingResultsData: programmingResultsDataObj,
			};

			//****Helper Functions****/
			function setProgrammingObjValues() {
				programmingResultsDataObj.processingStatus = 3; //Programming Error
				programmingResultsDataObj.logTitle = `Programming Errors: ${customerData.documentType} Not Created`;
				programmingResultsDataObj.recipients = ["<EMAIL>"];
				programmingResultsDataObj.cc = ["<EMAIL>"];
				programmingResultsDataObj.subject = `Programming Errors: ${customerData.purchasingSoftware} ${customerData.documentType} Not Created for ${customerName}  ${poNumber}`;
				programmingResultsDataObj.body = `Please investigate the errors below.\n\n${addGenericBodyData(
					programmingErrorsText
				)}`;

				send850Email(programmingResultsDataObj);
			}

			function setCSRObjValues() {
				try {
					if (
						documentName &&
						csrErrorsArr.length <= 0 &&
						programmingErrorsArr.length <= 0
					) {
						csrResultsDataObj.processingStatus = 1; //Processed With No Errors
						csrResultsDataObj.logTitle = "Created Successfully";
						csrResultsDataObj.recipients = [
							isEdiTransaction
								? "<EMAIL>"
								: "<EMAIL>",
						];
						csrResultsDataObj.subject = `Success: Please Approve - ${customerData.purchasingSoftware} ${customerData.documentType} ${documentName} Created for ${customerName} ${poNumber}`;
						csrResultsDataObj.body = `EDI file processed successfully and created ${
							customerData.documentType
						} ${documentName} for ${customerName}. ${
							isEdiTransaction ? "Please review and approve the order." : ""
						}
                    `;
					}

					if (documentName && csrErrorsArr.length > 0) {
						csrResultsDataObj.processingStatus = 2; //Processed With Errors
						csrResultsDataObj.logTitle = "Created with Errors";
						csrResultsDataObj.recipients = [
							isEdiTransaction
								? "<EMAIL>"
								: "<EMAIL>",
						];
						csrResultsDataObj.cc = [
							isEdiTransaction ? "<EMAIL>" : "",
						];
						csrResultsDataObj.subject = `Errors: Please Review and Correct - ${customerData.purchasingSoftware} ${customerData.documentType} ${documentName} Created for ${customerName} ${poNumber}`;
						csrResultsDataObj.body = `EDI file processed and created ${
							customerData.documentType
						} ${documentName} for ${customerName}. 
                    Please review the errors below and correct. ${addGenericBodyData(
											csrErrorText
										)}`;
					}

					if (!documentName) {
						csrResultsDataObj.processingStatus = 4; //Document Not Created
						csrResultsDataObj.logTitle = `${customerData.documentType} Not Created`;
						csrResultsDataObj.recipients = failureErrorRecipient;
						csrResultsDataObj.cc = failureErrorCC;
						csrResultsDataObj.subject = `Failure: ${customerData.purchasingSoftware} ${customerData.documentType} Not Created for ${customerName} ${poNumber}`;
						csrResultsDataObj.body = `Please investigate the errors below. ${addGenericBodyData(
							csrErrorText
						)}\n`;

						send850Email(csrResultsDataObj);
					}
				} catch (e) {
					log.error("setCSRObjValues", e);
				}
			}

			function addGenericBodyData(errorText) {
				var bodyText = `
				
			${errorText}    

			Please review the purchase order details sent.

			PO# : ${poNumber}
			Customer : ${
				isEdiTransaction
					? purchaseOrderObj.customerNameInTheirSystem
					: purchaseOrderObj.patient.address.addressee
			}
			${
				"Cstmr ID : " +
					  (isEdiTransaction
							? purchaseOrderObj.customerExternalId
							: purchaseOrderObj.patient.revivalId)
			}

			${
				purchaseOrderObj.address
					? "Address : " +
					  purchaseOrderObj.address.streetAddress +
					  "\n" +
					  purchaseOrderObj.address.city +
					  ", " +
					  purchaseOrderObj.address.state +
					  " " +
					  purchaseOrderObj.address.zip +
					  "\n"
					: ""
			}`;

				if (purchaseOrderObj.communicationObj) {
					if (purchaseOrderObj.communicationObj.communicationContact) {
						bodyText += `Contact : ${purchaseOrderObj.communicationObj.communicationContact}
					`;
					}
					if (purchaseOrderObj.communicationObj.contactNumber) {
						bodyText += `Contact Number : ${purchaseOrderObj.communicationObj.contactNumber}`;
					}
				}

				purchaseOrderObj.items.forEach((item) => {
					bodyText += `
				
				Item    : ${item.itemName}
					Quantity : ${item.quantity}
					Rate Sent   : ${numeral(item.rate).format("$0,0.00")}
					`;
					item.uom ? (bodyText += "UOM : " + item.uom + "\n") : "";
					item.description
						? (bodyText += "Description : " + item.description + "\n")
						: "";
					item.itemPricing
						? (bodyText +=
								"Item Pricing : " +
								numeral(item.itemPricing).format("$0,0.00") +
								"\n")
						: "";

					item.priceLevelName
						? (bodyText += "Price Level : " + item.priceLevelName + "\n")
						: "";
				});

				bodyText += `
			
			Transaction Control Number: ${purchaseOrderObj.transactionControlNumber}
			EDI File Name: ${ediFileName}`;

				return bodyText;
			}

			function send850Email(resultsData) {
				if (documentInternalId) {
					email.send({
						author: 262579, //EDI
						recipients: resultsData.recipients,
						cc: resultsData.cc,
						subject: resultsData.subject,
						body: resultsData.body,
						documentInternalId: {
							transactionId: documentInternalId,
						},
					});
				} else {
					email.send({
						author: 262579, //EDI
						recipients: resultsData.recipients,
						cc: resultsData.cc,
						subject: resultsData.subject,
						body: resultsData.body,
					});
				}
			}

			function logResults() {
				csrErrorText ??
					log.debug({
						title: csrResultsDataObj.logTitle,
						details: csrResultsDataObj.errorText,
					});

				programmingErrorsText ??
					log.debug({
						title: programmingResultsDataObj.logTitle,
						details: programmingResultsDataObj.errorText,
					});
			}
		} catch (e) {
			log.error("Error in outermost try/catch of send email", e);
		}
	}

	return {
		send850Email,
	};
});
