/**
 * @NApiVersion 2.1
 */

//@ts-ignore
define(["N/log", "N/record"], function (log, record) {
	function createGpoReferral(transactionValuesObj) {
		var helperFunctions = (function () {
			function createReferralRecord({
				transactionId,
				customerId,
				gpoFeePercentage,
				gpoReferralAmount,
			}) {
				try {
					var referral = record.create({
						type: "customrecord_spl_trnsctn_gpo_fee_record",
					});

					referral.setValue(
						"custrecord_spl_gpo_fee_created_for",
						transactionId
					);

					referral.setValue("custrecord_spl_referral_customer", customerId);

					referral.setValue(
						"custrecord_spl_referral_percentage",
						gpoFeePercentage
					);

					referral.setValue("custrecord_spl_referral_fee", gpoReferralAmount);

					try {
						return referral.save();
					} catch (err) {
						throw customErrorObject.updateError({
							errorType: customErrorObject.ErrorTypes.RECORD_NOT_SAVED,
							summary: "REFERRAL_RECORD_NOT_SAVED",
							details: `Error saving referral record`,
						});
					}
				} catch (err) {
					throw customErrorObject.updateError({
						errorType: customErrorObject.ErrorTypes.UNHANDLED_ERROR,
						summary: "ERROR_CREATING_GPO_RECORD",
						details: `Error creating GPO record. Error: ${err.message}`,
					});
				}
			}

			function createJournalEntry({
				subsidiary,
				transactionDate,
				referralInternalId,
				transactionId,
				accountsPaybleAccountId,
				cogsAccountId,
				gpoReferralAmount,
				transactionType,
			}) {
				try {
					var journalEntry = record.create({
						type: record.Type.JOURNAL_ENTRY,
						isDynamic: true,
					});

					journalEntry.setValue("subsidiary", subsidiary);
					journalEntry.setValue("trandate", new Date(transactionDate));

					journalEntry.setValue(
						"custbody_spl_referral_number",
						referralInternalId
					);

					journalEntry.setValue(
						"custbody_gpo_created_for_record",
						transactionId
					);

					if (transactionType == "CustInvc" || transactionType == "CustCred") {
						/*When NS sets the credit/debit on a credit memo, it automatically flips the values -> setting invoices and CM in the same way
						so that the end result on the JE will be as below
							Invoice: 	Credit the payable and debit the COGS
							Credit Memo: Debit the payable and credit the COGS*/
						_setLineItem("credit", accountsPaybleAccountId);
						_setLineItem("debit", cogsAccountId);
					} else {
						throw customErrorObject.updateError({
							errorType: customErrorObject.ErrorTypes.INVALID_DATA_TYPE,
							summary: "INVALID_RECORD_TYPE",
							details: `Record type needs to be CustInvc or CustCred, not ${transactionType}`,
						});
					}

					try {
						return journalEntry.save();
					} catch (err) {
						record.delete({
							type: "customrecord_spl_trnsctn_gpo_fee_record",
							id: referralInternalId,
						});

						throw customErrorObject.updateError({
							errorType: customErrorObject.ErrorTypes.RECORD_NOT_SAVED,
							summary: "JE_NOT_SAVED",
							details: `Error saving JE record, associated referral record was deleted.`,
						});
					}

					function _setLineItem(creditOrDebitField, accountId) {
						journalEntry.selectNewLine({
							sublistId: "line",
						});

						journalEntry.setCurrentSublistValue({
							sublistId: "line",
							fieldId: "account",
							value: accountId,
						});

						journalEntry.setCurrentSublistValue({
							sublistId: "line",
							fieldId: creditOrDebitField,
							value: gpoReferralAmount,
						});

						journalEntry.commitLine({
							sublistId: "line",
						});
					}
				} catch (err) {
					throw customErrorObject.updateError({
						errorType: customErrorObject.ErrorTypes.UNHANDLED_ERROR,
						summary: "ERROR_CREATING_JE",
						details: `Error creating the JE record: ${err.message}`,
					});
				}
			}

			function updateReferralRecordWithJE({
				referralInternalId,
				journalEntryId,
			}) {
				try {
					var referralRecord = record.load({
						type: "customrecord_spl_trnsctn_gpo_fee_record",
						id: referralInternalId,
					});

					referralRecord.setValue(
						"custrecord_spl_referral_journal_entry",
						journalEntryId
					);

					referralRecord.save();
				} catch (err) {
					record.delete({
						type: "customrecord_spl_trnsctn_gpo_fee_record",
						id: referralInternalId,
					});

					record.delete({
						type: "customrecord_spl_trnsctn_gpo_fee_record",
						id: record.Type.JOURNAL_ENTRY,
					});

					throw customErrorObject.updateError({
						errorType: customErrorObject.ErrorTypes.UNHANDLED_ERROR,
						summary: "REFERRAL_RECORD_NOT_SAVED",
						details: `Error updating referral record with JE, JE and referral record were deleted. ${err.message}`,
					});
				}
			}

			function updateInvoice({
				transactionId,
				referralInternalId,
				journalEntryId,
			}) {
				try {
					var transactionRecord = record.load({
						type: record.Type[
							transactionType == "CustInvc" ? "INVOICE" : "CREDIT_MEMO"
						],
						id: transactionId,
					});

					transactionRecord.setValue(
						"custbody_spl_referral_number",
						referralInternalId
					);

					transactionRecord.setValue(
						"custbody_spl_referral_journal_entry",
						journalEntryId
					);

					//setLineValues();

					transactionRecord.save();
				} catch (err) {
					record.submitFields({
						type: "customrecord_spl_trnsctn_gpo_fee_record",
						id: referralInternalId,
						values: {
							custrecord_spl_referral_journal_entry: null,
						},
					});

					record.delete({
						type: record.Type.JOURNAL_ENTRY,
						id: journalEntryId,
					});

					record.delete({
						type: "customrecord_spl_trnsctn_gpo_fee_record",
						id: referralInternalId,
					});

					throw customErrorObject.updateError({
						errorType: customErrorObject.ErrorTypes.UNHANDLED_ERROR,
						summary: "INVOICE_CM_RECORD_NOT_SAVED",
						details: `Error updating invoice or credit memo record. JE and referral record were deleted. ${err.message}`,
					});
				}
			}

			// function setLineValues() {
			// 	var itemLineCount = invoiceRecord.getLineCount({
			// 		sublistId: "item",
			// 	});

			// 	for (var x = 0; x < itemLineCount; x++) {
			// 		var amount = invoiceRecord.getSublistText({
			// 			sublistId: "item",
			// 			fieldId: "amount",
			// 			line: x,
			// 		});

			// 		var estExtendedCost = invoiceRecord.getSublistValue({
			// 			sublistId: "item",
			// 			fieldId: "costestimate",
			// 			line: x,
			// 		});

			// 		var referralPerItem = subtotal * gpoReferralObj.referralPercentage;

			// 		invoiceRecord.setSublistValue({
			// 			sublistId: "item",
			// 			fieldId: "custcol_spl_referral_fee",
			// 			line: x,
			// 			value: referralPerItem,
			// 		});

			// 		var estProfitAfterReferral =
			// 			amount - estExtendedCost - referralPerItem;

			// 		invoiceRecord.setSublistValue({
			// 			sublistId: "item",
			// 			fieldId: "custcol_spl_est_grs_prft_aftr_rfrl",
			// 			line: x,
			// 			value: estProfitAfterReferral,
			// 		});
			// 	}
			// }

			return {
				createReferralRecord,
				createJournalEntry,
				updateReferralRecordWithJE,
				updateInvoice,
			};
		})();

		/**Go through process to create and update all relevant records.
		 * At each stage, if something fails, delete the records already created in this iteration to prevent incorrect records in the DB
		 */

		const {
			mapErrorObject: customErrorObject,
			subtotal,
			gpoFeePercentage,
			gpoReferralAmount,
			accountsPaybleAccountId,
			cogsAccountId,
			subsidiary,
			transactionId,
			transactionType,
			transactionDate,
			customerId,
		} = transactionValuesObj;

		try {
			const referralInternalId =
				helperFunctions.createReferralRecord({
					transactionId,
					customerId,
					gpoFeePercentage,
					gpoReferralAmount,
				}) ?? 0;

			const journalEntryId =
				helperFunctions.createJournalEntry({
					subsidiary,
					transactionDate,
					referralInternalId,
					transactionId,
					accountsPaybleAccountId,
					cogsAccountId,
					gpoReferralAmount,
					transactionType,
				}) ?? 0;

			helperFunctions.updateReferralRecordWithJE({
				referralInternalId,
				journalEntryId,
			});

			helperFunctions.updateInvoice({
				transactionId,
				referralInternalId,
				journalEntryId,
			});

			return { referralInternalId, journalEntryId };
		} catch (err) {
			throw customErrorObject.updateError({
				errorType: customErrorObject.ErrorTypes.UNHANDLED_ERROR,
				summary: "ERROR_PROCESSING_GPO_FEE",
				details: `${err.stack ? err.stack : ""}`,
			});
		}
	}

	return {
		createGpoReferral,
	};
});
