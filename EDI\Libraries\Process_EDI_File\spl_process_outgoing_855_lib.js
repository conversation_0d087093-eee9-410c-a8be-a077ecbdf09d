/**
 * @NApiVersion 2.1
 */

//@ts-ignore
define([
	"require",
	"N/log",
	"N/record",
	"N/file",
	"N/query",
	"Write855Lib",
	"GetEdiFileContents",
	"Numeral",
	"../../../Libraries/Misc_Libs/spl_get_transaction_obj_lib",
], function (
	require,
	log,
	record,
	file,
	query,
	writePoAckLib,
	getEdiFileContentsLib,
	numeral
) {
	function process855(
		salesOrderInternalId,
		customerName,
		customerId,
		accountNumber,
		partnerValues,
		dataObj,
		integrationFolder,
		customErrorObject,
		purchaseOrderJsonObj
	) {
		const getSalesOrderObjLib = require("../../../Libraries/Misc_Libs/spl_get_transaction_obj_lib");

		const helperFunctions = (function () {
			function _processFail(errorMessage) {
				throw errorMessage;
			}

			function getSalesOrderRecord() {
				try {
					return record.load({
						type: record.Type.SALES_ORDER,
						id: salesOrderInternalId,
					});
				} catch (e) {
					_processFail(
						`${customerName}: Sales order not loaded correctly. Error: ${e}`
					);
				}
			}

			function getPurchaseOrderObj(salesOrderObj, salesOrderRecord) {
				try {
					const purchaseOrderObj = salesOrderObj.documentInfo.purchaseOrderObj;

					if (!purchaseOrderObj) {
						throw "No PO Object";
					}

					return purchaseOrderObj;
				} catch (e) {
					salesOrderRecord.setValue(
						"custbody_spl_edi_855_cntrl_nmbr",
						"Needs Purchase Order Object"
					);
					salesOrderRecord.save({
						enableSourcing: true,
						ignoreMandatoryFields: true,
					});

					_processFail(
						`${customerName} - ${salesOrderNumber}: No value was found in the 'Purchase Order Object' field. Please fill out so that this order can be acknowledged.`
					);
				}
			}

			function getAcknowledgementDetails(purchaseOrderObj, salesOrderObj) {
				try {
					purchaseOrderObj.acknowledgementCode = "00";

					//Iterate over each item that was sent on the PO and compare it against the items on the SO
					purchaseOrderObj.items.forEach((purchaseOrderItem) => {
						let purchaseOrderName = purchaseOrderItem.itemName.trim();

						let itemCreatedInSalesOrder = salesOrderObj.items.find(
							(salesOrderItem) => {
								return (
									salesOrderItem.name.toUpperCase() ==
									purchaseOrderName.toUpperCase()
								);
							}
						);

						//Check if was created on the SO with the new item name instead.
						if (!itemCreatedInSalesOrder) {
							const sqlQuery = `SELECT TOP 1
						BUILTIN.DF(onm.custrecord_new_item_id) as new_item_name
						FROM
							transactionline tl 
							JOIN
							customrecord_old_new_item_mapping onm 
							ON BUILTIN.DF(onm.custrecord_old_item_id) = '${purchaseOrderName}' 
						WHERE
							tl.transaction = ${salesOrderInternalId}`;

							const queryResults = query
								.runSuiteQL({
									query: sqlQuery,
								})
								.asMappedResults();

							const newItemName =
								queryResults.length > 0 && queryResults[0]["new_item_name"]
									? queryResults[0]["new_item_name"]
									: "";

							if (newItemName) {
								itemCreatedInSalesOrder = salesOrderObj.items.find(
									(salesOrderItem) => {
										return (
											salesOrderItem.name
												.replace(/^(.*):/, "")
												.trim()
												.toUpperCase() == newItemName.toUpperCase()
										);
									}
								);
							}
						}

						//Check if is a matrix item where the item id (PPE1117M) was sent but the full item name is pulled on the SO (PPE1117M: PPE1117M)
						if (!itemCreatedInSalesOrder) {
							const sqlQuery = `SELECT
						TOP 1 item.itemid item_id,
						tl.item 
						FROM
						transactionline tl 
						JOIN
							item 
							ON item.id = tl.item 
						WHERE
						tl.transaction = ${salesOrderInternalId}
						AND item.itemid = '${purchaseOrderName}'`;

							const queryResults = query
								.runSuiteQL({
									query: sqlQuery,
								})
								.asMappedResults();

							const itemId =
								queryResults.length > 0 && queryResults[0]["item_id"]
									? queryResults[0]["item_id"]
									: "";

							if (itemId) {
								itemCreatedInSalesOrder = salesOrderObj.items.find(
									(salesOrderItem) => {
										return (
											salesOrderItem.name
												.replace(/^(.*):/, "")
												.trim()
												.toUpperCase() == itemId.toUpperCase()
										);
									}
								);
							}
						}

						//Check if is an interchangeable item (Remove any . or - from the key)
						if (!itemCreatedInSalesOrder) {
							const interchangeableItemsDirectory = {
								PPE1105: "PPE.090",
								PPE090: "PPE1105",
								PMOVBT: "13067",
								13067: "PMOVBT",
								1020000: "525DS",
								"525DS": "1020000",
								PPE1101: "PPE.008",
								PPE008: "PPE1101",
								LG3600: "LG2A00",
								LG2A00: "LG3600",
								MDSR020143: "ADL1100",
								ADL1100: "MDSR020143",
								PPE159: "PPE1101",
								PPE1101: "PPE.159",
								4642: "MDS705153H",
								MDS705153H: "4642",
								MDSPH020H: "10-6222",
								"10-6222": "MDSPH020H",
								PTR1252: "SLB48-X",
								SLB48X: "PTR1252",
								DGS1248: "1280E-210",
								"1280E210": "DGS1248",
								RSP1246: "STR1005",
								STR1005: "RSP1246",
								APAPC20A: "APAP-C20",
								APAPC20: "APAP-C20A",
								THRP1156: "THRP11",
								THRP11: "THRP1156",
								A152307: "ORTH1180ML",
								ORTH1180ML: "A152307",
								//TODO: Are really duplicate old/new items but putting here, remove these when items are finished going through system
								PPE154: "PPE1117S",
								PPE155: "PPE1117M",
								PPE156: "PPE1117L",
								PPE157: "PPE1117XL",
								PPE1117S: "PPE.154",
								PPE1117M: "PPE.155",
								PPE1117L: "PPE.156",
								PPE1117XL: "PPE.157",
							};

							const poItemFormattedForLookup = purchaseOrderName
								.split(".")
								.join("")
								.split("-")
								.join("")
								.toUpperCase();

							const interchangeableItem =
								interchangeableItemsDirectory[poItemFormattedForLookup];

							if (interchangeableItem) {
								itemCreatedInSalesOrder = salesOrderObj.items.find(
									(salesOrderItem) => {
										return (
											salesOrderItem.name
												.replace(/^(.*):/, "")
												.trim()
												.toUpperCase() == interchangeableItem.toUpperCase()
										);
									}
								);
							}
						}

						//If still no item found -> throw error
						if (!itemCreatedInSalesOrder) {
							/*We used to set the values as below and process the 855, changed to fail it instead so that we can confirm if the item was legitimitly not processed.
								purchaseOrderItem.quantityAcknowledged = 0;
								purchaseOrderItem.status = "ID"; //Item deleted
								purchaseOrderObj.acknowledgementCode = "04"; //Changes to PO */

							_processFail(
								`${purchaseOrderItem.itemName} wasn't found on the sales order. Please confirm the item name is correct`
							);
						}

						purchaseOrderItem.quantityAcknowledged =
							itemCreatedInSalesOrder.quantity;
						purchaseOrderItem.currentScheduledShipDate =
							salesOrderObj.documentInfo.currentScheduledShipDate ?? "";

						//Check if any changes in how we processed this item
						if (
							numeral(purchaseOrderItem.quantity).format("0,0") !=
								numeral(itemCreatedInSalesOrder.quantity).format("0,0") ||
							numeral(purchaseOrderItem.rate).format("0,0.00") !=
								numeral(itemCreatedInSalesOrder.rate).format("0,0.00") ||
							purchaseOrderItem.uom != itemCreatedInSalesOrder.uom
						) {
							purchaseOrderItem.status = "IC"; //Accepted with changes
							purchaseOrderObj.acknowledgementCode = "04"; //Changes to PO
						} else {
							purchaseOrderItem.status = "IA"; //Accepted with no changes
						}
					});
				} catch (e) {
					_processFail(
						`${customerName}: Error getting purchase order details. Error: ${e}`
					);
				}
			}

			function getDocumentAsEDI(purchaseOrderObj, salesOrderObj) {
				const ediFile = writePoAckLib.getPoAckAsEDI(
					partnerValues,
					purchaseOrderObj,
					salesOrderObj
				);

				if (!ediFile) {
					_processFail(
						`Purchase Order Acknowledgement was converted to EDI file with "Invalid" or "Undefined".`
					);
				}

				return ediFile;
			}

			function _getFileToUpload(purchaseOrderObj, ediFile) {
				return file.create({
					name: purchaseOrderObj.poNumber + "_855.edi",
					fileType: file.Type.PLAINTEXT,
					contents: ediFile,
				});
			}

			function uploadFileToTheirServer(purchaseOrderObj, ediFile) {
				try {
					const theirConnection =
						getEdiFileContentsLib.createConnection(dataObj);

					theirConnection.upload({
						file: _getFileToUpload(purchaseOrderObj, ediFile),
						replaceExisting: true,
					});
				} catch (e) {
					_processFail(
						`${customerName} Purchase Order Acknowledgement ${salesOrderNumber}: Could not upload file to ${dataObj.purchasingSoftware} server. Error: ${e}`
					);
				}
			}

			function uploadFileToSupplyLineServer(purchaseOrderObj, ediFile) {
				try {
					const referenceDirectory = `/EDI Reference Files/${dataObj.purchasingSoftware}/${integrationFolder}/OUT/855`;

					const supplyLineConnection = getEdiFileContentsLib.createConnection(
						dataObj,
						"",
						referenceDirectory
					);

					supplyLineConnection.upload({
						file: _getFileToUpload(purchaseOrderObj, ediFile),
						replaceExisting: true,
					});
				} catch (e) {
					_processFail(
						`${customerName} Purchase Order Acknowledgement ${salesOrderNumber}:  Could not upload file to SupplyLine Server. Error: ${e}`
					);
				}
			}

			function updateAndSaveNetsuiteRecord(salesOrderRecord, salesOrderObj) {
				try {
					salesOrderRecord.setValue(
						"custbody_spl_edi_855_cntrl_nmbr",
						salesOrderObj.documentInfo.controlNumber
					);

					salesOrderRecord.save({
						enableSourcing: true,
						ignoreMandatoryFields: true,
					});
				} catch (e) {
					_processFail(
						`${customerName} Purchase Order Acknowledgement ${salesOrderNumber}:  Could not save record. Error: ${e}`
					);
				}
			}

			return {
				getSalesOrderRecord,
				getPurchaseOrderObj,
				getAcknowledgementDetails,
				getDocumentAsEDI,
				uploadFileToTheirServer,
				uploadFileToSupplyLineServer,
				updateAndSaveNetsuiteRecord,
			};
		})();

		let salesOrderNumber;
		let purchaseOrderNumber;

		try {
			const salesOrderRecord = helperFunctions.getSalesOrderRecord();
			salesOrderNumber = salesOrderRecord.getText("tranid");

			const salesOrderObj = getSalesOrderObjLib.getObj(
				salesOrderRecord,
				{ customerName, customerId },
				dataObj.documentType,
				dataObj.purchasingSoftware,
				false, //isInvoice
				customErrorObject,
				purchaseOrderJsonObj
			);

			const purchaseOrderObj = helperFunctions.getPurchaseOrderObj(
				salesOrderObj,
				salesOrderRecord
			);

			purchaseOrderNumber = purchaseOrderObj.poNumber;

			helperFunctions.getAcknowledgementDetails(
				purchaseOrderObj,
				salesOrderObj
			);

			const ediFile = helperFunctions.getDocumentAsEDI(
				purchaseOrderObj,
				salesOrderObj
			);

			helperFunctions.uploadFileToTheirServer(purchaseOrderObj, ediFile);
			helperFunctions.uploadFileToSupplyLineServer(purchaseOrderObj, ediFile);
			helperFunctions.updateAndSaveNetsuiteRecord(
				salesOrderRecord,
				salesOrderObj
			);
		} catch (e) {
			return {
				processedSuccessfully: false,
				salesOrderNumber: salesOrderNumber ?? "SO# Not Available",
				purchaseOrderNumber: purchaseOrderNumber ?? "",

				errorMessage: e,
			};
		}

		return {
			processedSuccessfully: true,
			salesOrderNumber: salesOrderNumber,
			purchaseOrderNumber: purchaseOrderNumber,
		};
	}

	return {
		process855: process855,
	};
});
