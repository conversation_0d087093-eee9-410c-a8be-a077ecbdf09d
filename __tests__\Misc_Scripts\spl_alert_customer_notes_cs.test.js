import log from "N/log";
import search from "N/search";
import spl_alert_customer_notes_cs from "../../Misc_Scripts/spl_alert_customer_notes_cs";

beforeEach(() =>  {
    jest.resetModules();
});
global.alert = jest.fn();

describe("spl_alert_customer_notes_cs", () => {
    beforeEach(() => {
        jest.spyOn(search, "lookupFields").mockReturnValue({
            custentity_spl_customer_notes_for_so_in: "foo"
        });
    });
    describe("pageInit()", () => {
        it("alerts the user when customer notes are available", () => {
            const pageInitReturn = spl_alert_customer_notes_cs.pageInit({
                mode: "edit",
                currentRecord: {
                    getValue: () => {}
                }
            });
            expect(pageInitReturn).toEqual(undefined);
            expect(global.alert).toHaveBeenCalledWith("foo");
        });
        it("returns undefined when context is not edit", () => {
            expect(spl_alert_customer_notes_cs.pageInit({
                mode: "create"
            })).toEqual(undefined);
            expect(global.alert).not.toHaveBeenCalled();
        });
    });
    describe("saveRecord()", () => {
        it("alerts the user when customer notes are available", () => {
            spl_alert_customer_notes_cs.saveRecord({
                currentRecord: {
                    getValue: () => {}
                }
            });
            expect(global.alert).toHaveBeenCalledWith("foo");
        });
        it("logs the thrown error when currentRecord is null", () => {
            jest.spyOn(log, "error").mockImplementation((error) => error);
            const saveRecordReturn = spl_alert_customer_notes_cs.saveRecord();
            expect(log.error).toHaveBeenCalledTimes(1);
            expect(log.error).toHaveReturnedWith("Error alerting customer notes");
            expect(saveRecordReturn).toEqual(true);
        });
        it("does not alert the user when customer notes are not available", () => {
            jest.spyOn(search, "lookupFields").mockReturnValue({
                custentity_spl_customer_notes_for_so_in: null
            });
            const saveRecordReturn = spl_alert_customer_notes_cs.saveRecord({
                currentRecord: {
                    getValue: () => {}
                }
            });
            expect(global.alert).not.toHaveBeenCalled();
            expect(saveRecordReturn).toEqual(true);
        });
    });
});