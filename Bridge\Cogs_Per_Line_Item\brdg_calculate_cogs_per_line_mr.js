/**
 * @description Calculate COGS per line item on all BRDG sales transaction created/edit that day
 *
 * </br><b>Schedule:</b> Runs every morning @ 1:00 AM
 *
 * @NApiVersion 2.1
 * @NScriptType MapReduceScript
 * @NAmdConfig /SuiteScripts/config.json
 *
 * <AUTHOR>
 * @module brdg_calculate_cogs_per_line_mr
 */

define([
  "require",
  "CalculateCogsPerLineLib",
  "N/runtime",
  "N/format",
  "N/email",
  "N/file",
  "../../Classes/vlmd_custom_error_object",
  "../../Libraries/External_Libraries/vlmd_moment_lib",
  "../../Classes/vlmd_mr_summary_handling",
], (require, calculateCogsPerLineLib) => {
  const runtime = require("N/runtime");
  const format = require("N/format");
  const email = require("N/email");
  const file = require("N/file");

  /** @type {import("../../Classes/vlmd_custom_error_object").CustomErrorObject} */

  const CustomErrorObject = require("../../Classes/vlmd_custom_error_object");
  const customErrorObject = new CustomErrorObject();

  /** @type {import("../../Libraries/External_Libraries/vlmd_moment_lib")} */
  const moment = require("../../Libraries/External_Libraries/vlmd_moment_lib");

  const notificationAuthor = 223244; //<EMAIL>

  /**
   * Load ids of BRDG sales transactions to process
   *
   * @param {import ("N/types").EntryPoints.MapReduce.getInputDataContext } context Get input data context
   * @returns {string[]| undefined}
   */
  function getInputData(context) {
    try {
      const currentScript = runtime.getCurrentScript();

      let queryString = currentScript.getParameter({
        name: "custscript_query_string",
      });

      if (queryString) {
        return {
          type: "suiteql",
          query: queryString,
          params: [],
        };
      }

      let startDateOverride = currentScript.getParameter({
        name: "custscript_start_date",
      });

      let endDateOverride = currentScript.getParameter({
        name: "custscript_end_date",
      });

      let relativeStartDateOverride = currentScript.getParameter({
        name: "custscript_relative_start_date",
      });

      let relativeEndDateOverride = currentScript.getParameter({
        name: "custscript_relative_end_date",
      });

      /*When a date param is passed in directly from a MR (vs. called via MR task from SL), it's formated as a date obj already
			-> will get passed into the lib in 'Mon Jan 22 2024 00:00:00 GMT-0800 (PST)' format  -> convert to standard NS date format*/
      if (
        currentScript.deploymentId ==
          "customdeploy_brdg_clclt_cogs_pr_ln_mr_od" &&
        startDateOverride &&
        endDateOverride
      ) {
        startDateOverride = format.format({
          value: startDateOverride,
          type: format.Type.DATE,
        });

        endDateOverride = format.format({
          value: endDateOverride,
          type: format.Type.DATE,
        });
      }

      let getTransactionsCreatedString =
        currentScript.getParameter({
          name: "custscript_get_transactions_created",
        }) ?? null;

      let getTransactionsEditedString =
        currentScript.getParameter({
          name: "custscript_get_transactions_edited",
        }) ?? null;

      /*Params pulled in when triggered directly from a MR get passed in as true/false, 
			when passed in via SL, they're passed in as 'T'/'F'*/
      getTransactionsCreated =
        getTransactionsCreatedString == "T" ||
        getTransactionsCreatedString == true;

      getTransactionsEdited =
        getTransactionsEditedString == true ||
        getTransactionsEditedString == "T";

      const subsidiary = currentScript.getParameter({
        name: "custscript_subsidiary",
      });

      log.audit(
        "Parameters",
        `Get Created: ${getTransactionsCreatedString}, Get Edited: ${getTransactionsEdited},
				Subsidiary: ${subsidiary}, 
				Start Date: ${startDateOverride}, End Date: ${endDateOverride}, 
				Start Date Range Override: ${relativeStartDateOverride}, End Date Range Override: ${relativeEndDateOverride}`
      );

      /**
       * Query for results from the getTransactionsEditedOrCreatedYesterday func
       *
       * @param {string[]} - Array of record record types to query
       * @param {number|null} - Child subsidiary to query by
       * @param {number|null} - Parent subsidiary to query by
       * @param {number[]|null[]} - Child subsidiaries to exclude in results
       * @returns {void}
       */
      queryString = calculateCogsPerLineLib.getQueryOfTransactionsToProcess(
        {
          numberOfDaysBack: 2,
          startDate: startDateOverride,
          endDate: endDateOverride,
          relativeStartDate: relativeStartDateOverride,
          relativeEndDate: relativeEndDateOverride,
          getTransactionsCreated: getTransactionsCreated,
          getTransactionsEdited: getTransactionsEdited,
          subsidiary,
        },
        customErrorObject
      );

      log.audit("queryString", queryString);

      return {
        type: "suiteql",
        query: queryString,
        params: [],
      };
    } catch (err) {
      customErrorObject.throwError({
        summaryText: "GET_INPUT_DATA_ERROR",
        error: err,
      });
    }
  }

  /**
   * Pass the transactionId and recordType params into the lib to calculate the values
   *
   * @param {import ("N/types").EntryPoints.MapReduce.map} context
   */
  function map(context) {
    const mapErrorObject = new CustomErrorObject();

    var parsedResultValues = JSON.parse(context.value).values;

    var [transactionId, recordType, tranid] = parsedResultValues;

    try {
      calculateCogsPerLineLib.setAllCogsPerItem(
        transactionId,
        recordType,
        mapErrorObject
      );

      context.write(recordType, tranid);

      if (mapErrorObject.ERROR_TYPE) {
        throw "";
      }
    } catch (err) {
      mapErrorObject.throwError({
        summaryText: ``,
        error: err,
        recordId: transactionId ?? "",
        recordName: tranid ?? "",
        recordType: recordType ?? "",
        errorWillBeGrouped: true,
      });
    }
  }

  /**
   * Log the transactions processed successfully and processing errors
   *
   * @param {import ("N/types").EntryPoints.MapReduce.summarize} context
   */
  function summarize(context) {
    /** @type {import("../../Classes/vlmd_mr_summary_handling").MapReduceSummaryStageHandling} */
    const StageHandling = require("../../Classes/vlmd_mr_summary_handling");

    const stageHandling = new StageHandling(context);
    stageHandling.printScriptProcessingSummary();

    let { resultsLog, recordsProcessedMessage } =
      stageHandling.printRecordsProcessed();

    let { errorArr, errorsMessage } = stageHandling.printErrors({
      groupErrors: true,
      formatMessageForEmail: true,
    });

    const currentScript = runtime.getCurrentScript();

    const ownerId =
      currentScript.getParameter({
        name: "custscript_brdg_cogs_process_owner",
      }) ?? 3288;

    const ccEmailAddresses = currentScript.getParameter({
      name: "custscript_cc_email_address",
    });

    const parametersText = `<b>Parameters:</b></br>
Start Date: ${currentScript.getParameter({
      name: "custscript_start_date",
    })}<br/>
End Date: ${currentScript.getParameter({
      name: "custscript_end_date",
    })}<br/>
Get Transactions Created: ${currentScript.getParameter({
      name: "custscript_get_transactions_created",
    })}<br/>
Get Transactions Edited: ${currentScript.getParameter({
      name: "custscript_get_transactions_edited",
    })} <br/>
Subsidiary Internal Id: ${currentScript.getParameter({
      name: "custscript_subsidiary",
    })}<br/>`;

    let processingMessage = `${parametersText}<br/><br/>
        <b>${errorArr.length} Error${errorArr.length == 1 ? "" : "s"}</b>${
      errorsMessage
        ? errorArr.length <= 10
          ? `:<br/>${errorsMessage}`
          : "<br/>Please see attached files for details."
        : ""
    }<br/></br>
            <b>${resultsLog.length} Transaction${
      resultsLog.length == 1 ? "" : "s"
    } Updated</b>${
      recordsProcessedMessage ? ": " + recordsProcessedMessage : ""
    }<br/>`;

    let errorMessageCsvText = errorArr
      .map((err) => `${err.name}, ${err.message}\n`)
      .join("");

    let fileHeadersText = `Header,Transaction,Internal ID,Type,,Title,Details\n`;

    email.send({
      author: notificationAuthor,
      recipients: [ownerId],
      ...(ccEmailAddresses != null &&
        ccEmailAddresses != "" && {
          cc: ccEmailAddresses.split(","),
        }),
      subject: "Update Bridge COGS Results",
      body: `${processingMessage}`,
      ...(errorArr.length > 0 && {
        attachments: [
          file.create({
            name: `Error Details_${moment().format("MMDDYYHHmmss")}.csv`,
            fileType: file.Type.CSV,
            contents: `${fileHeadersText}${errorMessageCsvText}`,
          }),
        ],
      }),
    });
  }

  return {
    getInputData,
    map,
    summarize,
  };
});
