/**
 * @description RIP Import 5:
 * Sets RIP Import records to inactive
 * and sends notification to the user
 *
 * </br><b>Schedule:</b> On-demand, called by MR
 *
 * @NApiVersion 2.1
 * @NScriptType MapReduceScript
 * @NAmdConfig /SuiteScripts/config.json
 *
 * <AUTHOR>
 * @module brdg_rip_import_5_cleanup_import_mr
 */

define([
  "require",
  "BridgeHelperFunctionsLib",
  "N/log",
  "N/record",
  "N/query",
  "N/email",
  "N/runtime",
  "../../../../Classes/vlmd_custom_error_object",
  "../../../../Classes/vlmd_mr_summary_handling",
], (/** @type {any} */ require) => {
  const log = require("N/log");
  const record = require("N/record");
  const query = require("N/query");
  const email = require("N/email");
  const runtime = require("N/runtime");

  /** @type {import("../../../../Classes/vlmd_custom_error_object").CustomErrorObject} */
  const CustomErrorObject = require("../../../../Classes/vlmd_custom_error_object");
  const customErrorObject = new CustomErrorObject();

  const notificationAuthor = 223244; //<EMAIL>
  const maxErrorThreshold = 10;

  /**
   * Searches for all active RIP Import Records
   *
   * @param {import("N/types").EntryPoints.MapReduce.getInputDataContext} context Get input data context
   * @returns {string[]|undefined}
   */
  function getInputData(context) {
    try {
      let /** @type {any[]} */ recordsToCleanup = [];

      query
        .runSuiteQLPaged({
          query: `
          SELECT id
          FROM customrecord_brdg_rip_import
          WHERE isinactive = 'F'
          AND custrecord_item_exists_in_ns = 'T'
        `,
          pageSize: 1000,
        })
        .iterator()
        .each((page) => {
          page.value.data.results.forEach((result) => {
            recordsToCleanup.push(result.values[0]);
          });
          return true;
        });

      if (recordsToCleanup.length <= 0) {
        throw customErrorObject.updateError({
          errorType: customErrorObject.ErrorTypes.NO_VALUE_RETURNED,
          summary: "NO_INACTIVE_RECORDS_RETURNED",
          details: `No import records were found that are active and have "Item Exists in NS?" checked off.`,
        });
      }

      return recordsToCleanup;
    } catch (err) {
      customErrorObject.throwError({
        summaryText: "GET_INPUT_DATA_ERROR",
        error: err,
        recordId: null,
        recordName: null,
        recordType: null,
        errorWillBeGrouped: false,
      });
    }
  }

  /**
   * Inactivate the RIP Import record
   * On error, throw the error including the RIP Import record id
   *
   * @param {import("N/types").EntryPoints.MapReduce.mapContext} context Map context
   * @returns {void}
   */
  function map(context) {
    const mapErrorObject = new CustomErrorObject();
    const ripImportRecordId = context.value;

    try {
      record.submitFields({
        type: "customrecord_brdg_rip_import",
        id: ripImportRecordId,
        values: {
          isinactive: "T",
        },
      });
    } catch (err) {
      mapErrorObject.throwError({
        summaryText: "MAP_ERROR",
        error: err,
        recordId: ripImportRecordId,
        recordName: null,
        recordType: "customrecord_brdg_rip_import",
        errorWillBeGrouped: true,
      });
    }

    context.write("Record Deactivated", ripImportRecordId);
  }

  /**
   * The summarize stage of the Map/Reduce script.
   * Sends an email notification on a successful run.
   *
   * @param {import("N/types").EntryPoints.MapReduce.summarizeContext} context Summarize context
   * @returns {void}
   */
  function summarize(context) {
    let ownerId = 0;
    try {
      const currentScript = runtime.getCurrentScript();

      ownerId =
        Number(
          currentScript.getParameter({
            name: "custscript_brdg_rip_import_cleanup_owner",
          }) || 0
        ) ?? 3288;

      // Error in getInputData stage -> no records were processed -> stop execution.
      if (context.inputSummary.error) {
        throw customErrorObject.updateError({
          errorType: customErrorObject.ErrorTypes.NO_VALUE_RETURNED,
          summary: "ERROR_IN_GET_INPUT_DATA",
          details: `MR 5: Rip cleanup process stopped because data to process wasn't loaded successfully.

${JSON.parse(context.inputSummary.error).message}`,
        });
      }

      const StageHandling = require("../../../../Classes/vlmd_mr_summary_handling");
      // @ts-ignore Type 'typeof import("vlmd_mr_summary_handling")' has no construct signatures.ts(2351)
      const stageHandling = new StageHandling(context);

      stageHandling.printScriptProcessingSummary();
      let { resultsLog } = stageHandling.printRecordsProcessed();
      let { errorArr, errorsMessage } =
        stageHandling.printErrors("groupErrors");

      // Too many errors, likely something wrong with the entire file -> stop execution.
      if (errorArr.length > maxErrorThreshold) {
        throw customErrorObject.updateError({
          errorType: customErrorObject.ErrorTypes.UNHANDLED_ERROR,
          summary: "TOO_MANY_MAP_ERRORS",
          details: "Import process stopped because there were too many errors.",
        });
      }

      if (errorArr.length > 0) {
        email.send({
          author: notificationAuthor,
          recipients: [ownerId],
          cc: ["43398"], //Miri Landau
          subject: "Bridge RIP Cleanup Import",
          body: `An error occurred while importing these rows from the RIP file. 
Please address errors below and create tier levels, tier groups, agreement records, agreement details manually\n\n
${errorsMessage}`,
        });
      }

      const messageFromPreviousStage = currentScript.getParameter({
        name: "custscript_brdg_rip_detail_items_message",
      });

      let processingMessage = `${messageFromPreviousStage}
<b>Script #4: Deactivate Active RIP Import Records</b><br/>
${resultsLog.length} RIP Import Records Deactivated.<br/>
${errorArr.length} Errors${errorsMessage ? ": " + errorsMessage : "."}`;

      email.send({
        author: notificationAuthor,
        recipients: [ownerId],
        subject: "Bridge RIP Import Have Finished",
        body: `RIP import tasks have finished.<br/><br/>
${processingMessage}`,
      });
    } catch (/** @type {any} */ err) {
      email.send({
        author: notificationAuthor,
        recipients: [ownerId],
        subject: "Bridge RIP Import Agreements Error",
        body: `Import process stopped because there was an error while processing.<br><br>Please reach <NAME_EMAIL> for assistance.<br/>
        ${err?.message ?? customErrorObject.details ?? ""}`,
      });

      customErrorObject.throwError({
        summaryText: `SUMMARIZE_ERROR`,
        error: err,
        recordId: null,
        recordName: null,
        recordType: null,
        errorWillBeGrouped: false,
      });
    }
  }

  return {
    getInputData,
    map,
    summarize,
  };
});
