/**
 * Sales Order Item Sublist Record class
 * that represents the line in the item sublist
 * of a Sales Order transaction
 *
 * @NApiVersion 2.1
 * <AUTHOR>
 */

define(["N/search", "CustomItemSublistRecord"], (
	search,
	CustomItemSublistRecord
) => {
	const driveVendorId = "728";
	const dynarexVendorId = "729";
	const hausmannVendorId = "3022";
	const healthCareInternationalId = "4483";

	const dropShipLocationId = "4";
	const lowAirLossMattressItemId = "3451";
	const medlineFlatRateShipAccountId = "1";
	const medlineFreightShipAccountId = "2";
	const msc263754ItemId = "8018";
	const overBedTableItemId = "4353";
	const pmovbtItemId = "50715";
	const ptr1369ItemId = "50715";
	const ptr1125ItemId = "4353";
	const supplyLineMedicalLocationId = "1";

	/**
	 * Sales Order Item Sublist Record class
	 *
	 * @class
	 * @type {import("./spl_item_sublist_record").SalesOrderItemSublistRecord}
	 */
	class SalesOrderItemSublistRecord extends CustomItemSublistRecord {
		constructor(transaction, index) {
			super(transaction, index);

			this.createPO = this.getSublistValue({
				netsuiteRecord: transaction,
				line: index,
				sublistId: this.Sublist.item,
				fieldId: this.Field.createPo,
			});
			this.location = this.getSublistValue({
				netsuiteRecord: transaction,
				line: index,
				sublistId: this.Sublist.item,
				fieldId: this.Field.location,
			});
			this.poVendor = this.getSublistValue({
				netsuiteRecord: transaction,
				line: index,
				sublistId: this.Sublist.item,
				fieldId: this.Field.poVendor,
			});
			this.shipAccount = this.getSublistValue({
				netsuiteRecord: transaction,
				line: index,
				sublistId: this.Sublist.item,
				fieldId: this.Field.shipAccount,
			});
			this.needsImmediateEtaUpdate =
				this.itemLookup && this.itemLookup[this.Column.needsEtaUpdate];

			const vendorLookup =
				this.poVendor &&
				search.lookupFields({
					type: search.Type.VENDOR,
					id: this.poVendor,
					columns: ["custentity_spl_online_ordering_vendor"],
				});

			this.needsOnlineOrder = vendorLookup
				? vendorLookup["custentity_spl_online_ordering_vendor"]
				: false;
		}

		/**
		 * Determine if the item is drop ship but is not using the correct cost estimate type
		 * Item is dropship if either a PO was created, the location is drop ship, or the location is empty
		 *
		 * @returns {boolean} True if item is drop ship but cost estimate type is not Purchase Order Rate
		 */
		isDropShipItemNotUsingPurchaseOrderRate() {
			return (
				this.costEstimateType !== "PURCHORDERRATE" &&
				(this.itemType === "NonInvtPart" ||
					(this.itemType === "InvtPart" &&
						Boolean(
							this.createPO ||
								this.location === dropShipLocationId ||
								!this.location
						)))
			);
		}

		/**
		 * Determine if inventory item is not using average cost estimate
		 *
		 * @returns {boolean} True if item is not using average cost
		 */
		isItemNotUsingAverageCost() {
			return Boolean(
				this.costEstimateType !== "AVGCOST" &&
					this.itemType === "InvtPart" &&
					this.location &&
					this.location !== dropShipLocationId &&
					this.location !== supplyLineMedicalLocationId
			);
		}

		/**
		 * Determine if item is not drop ship and a location is not available
		 *
		 * @returns {boolean} True if item is not drop ship and is missing a location
		 */
		isNotDropShipAndNoLocation() {
			return !this.createPO && !this.location && this.itemType === "InvtPart";
		}

		/**
		 * Determine if the location is valid for either
		 *  an inventory item with create po
		 *  or a non-inventory item
		 *
		 * @returns {boolean} True if item is drop ship and is not using Drop Ship location
		 */
		isDropShipItemUsingWrongLocation() {
			return Boolean(
				this.location &&
					this.location !== dropShipLocationId &&
					this.location !== supplyLineMedicalLocationId &&
					(this.itemType === "NonInvtPart" ||
						(this.createPO && this.itemType === "InvtPart"))
			);
		}

		/**
		 * Determine if item is not for dropshipping
		 *
		 * @returns {boolean} True if item sublist record is both marked for dropship and not dropship
		 */
		isNotForDropShip() {
			return this.dontdropship && this.createPO;
		}

		/**
		 * Determine if the item is Over Bed Table and is at least 10 units
		 *
		 * @returns {boolean} True if Over Bed Table quantity is 10 and is NOT marked for dropship
		 */
		shouldDropShipOverBedTable() {
			return (
				this.itemId === overBedTableItemId &&
				this.quantity >= 10 &&
				!this.createPO
			);
		}

		
		/**
		 * Determine if the item is PMOVBT and is at least 10 units
		 *
		 * @returns {boolean} True if PMOVBT - Over Bed Table quantity is 10 and is NOT marked for dropship
		 */
		shouldDropShipAndSetVendorCostPMOVBT() {
			return (
				this.itemId === pmovbtItemId &&
				!this.createPO &&
				this.quantity >= 10
			);
		}

		/**
		 * Determine if the item is PTR1369 so needs an alert
		 * 
		 * @returns {boolean} True if PTR1369
		 */
		isItemPtr1369(){
			return(
				this.itemId == ptr1369ItemId
			);
		}

		/**
		 * Determine if the item is PTR1125 so needs an alert
		 * 
		 * @returns {boolean} True if PTR1125
		 */
		isItemPtr1125(){
			return(
				this.itemId == ptr1125ItemId
			);
		}

		/**
		 * Determine if the item is Low Air Loss Mattress and is at least 10 units
		 *
		 * @returns {boolean} True if Low Air Loss Mattress quantity is 10 and is NOT marked for dropship
		 */
		shouldDropShipLowAirLossMattress() {
			return (
				this.itemId === lowAirLossMattressItemId &&
				this.quantity >= 10
			);
		}

		/**
		 * Determine if the lift gate alert should be displayed based on the vendor
		 *
		 * @returns {boolean} True if the lift gate alert is necessary
		 */
		isLiftGateAlertRequired() {
			return (
				this.poVendor === hausmannVendorId ||
				this.poVendor === dynarexVendorId ||
				this.poVendor === driveVendorId ||
				this.poVendor === healthCareInternationalId
			);
		}

		/**
		 * Determine if ship account should be updated to Medline Freight
		 *
		 * @returns {boolean} True if the ship account should change to Medline Freight
		 */
		shouldChangeShippingToMedlineFreight() {
			return (
				this.itemId === msc263754ItemId &&
				this.quantity <= 4 &&
				this.shipAccount !== medlineFreightShipAccountId
			);
		}

		/**
		 * Determine if ship account should be updated to Medline Flat Rate
		 *
		 * @returns {boolean} True if the ship account should change to Medline Flat Rate
		 */
		shouldChangeShippingToMedlineFlatRate() {
			return (
				this.itemId === msc263754ItemId &&
				this.quantity > 4 &&
				this.shipAccount !== medlineFlatRateShipAccountId
			);
		}

		/**
		 * Determine if the order should be processed online based on vendor
		 *
		 * @returns {boolean} True if order should be processed online
		 */
		shouldDisplayOrderOnlineProcessingAlert() {
			return Boolean(this.needsOnlineOrder && this.createPO);
		}
	}

	return SalesOrderItemSublistRecord;
});
