{"name": "valmed-netsuite", "dependencies": {"@types/node": "^20.1.5", "@hitc/netsuite-types": "^2023.1.9", "@types/requirejs": "^2.1.34", "docdash": "^2.0.1", "requirejs": "^2.3.6", "typescript": "^5.0.4"}, "devDependencies": {"@babel/core": "^7.21.8", "@babel/preset-env": "^7.21.5", "@hitc/netsuite-types": "^2023.1.9", "@jest/globals": "^29.5.0", "@types/jest": "^29.5.1", "babel-jest": "^29.5.0", "babel-plugin-transform-amd-to-commonjs": "^1.6.0", "jest": "^29.7.0", "jsdoc": "^4.0.2", "docdash": "2.0.1"}, "scripts": {"test": "jest", "docs": "jsdoc -d Codebase_Documentation --configure jsdocConf.json"}}