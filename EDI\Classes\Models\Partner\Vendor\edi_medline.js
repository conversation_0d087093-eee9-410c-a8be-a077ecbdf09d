/**
 * @description Partner class for Medline
 *
 * @NApiVersion 2.1
 * <AUTHOR> <<EMAIL>>
 */

define([
    "exports",
    "require",
    "./edi_vendor"
], (/** @type {any} */ exports, /** @type {any} */ require) => {
    const { EDIVendor } = require("./edi_vendor");

    /**
     * Medline Vendor Class
     *
     * @class
     * @typedef {import("../../../Interfaces/Models/Partner/edi_partner").EDIVendorInterface} EDIVendorInterface
     * @typedef {import("../../../Interfaces/Models/Partner/edi_partner").EDIPartnerConstructorParams} EDIPartnerConstructorParams
     * @typedef {import("../../../Interfaces/Models/Partner/edi_partner").EDIParsingInformation} EDIParsingInformation
     * @typedef {import("../../../Interfaces/Models/Partner/edi_partner").EDIParsingParams} EDIParsingParams
     * @extends {EDIVendor}
     * @implements {EDIVendorInterface}
     */
    class EDIMedline extends EDIVendor {
        /** @param {EDIPartnerConstructorParams} params Constructor Params*/
        constructor(params) {
            super();
            /** @type {string} */
            this.id = "742";
            /** @type {string} */
            this.purchasingSoftwareId = "9";
            /** @type {string} */
            this.name = "Medline";
            /** @type {string} */
            this.prodDirectory = "/users/medlineprod/IN/810";
            /** @type {string} */
            this.testDirectory = `/edi/test/vendor/medline/${params.direction}/${params.transactionType}`;
            /** @type {string} */
            this.referenceDirectory = `/edi/reference/vendor/medline/${params.direction}/${params.transactionType}`;
            /** @type {EDIParsingParams} */
            this.delimiters = {
                fieldDelimiter: "^",
                segmentDelimiter: "~",
                fileDelimiter: "|",
                ediVersion: "00401",
                receiverQualifier: "ZZ",
                receiverId: "MEDLINE",
            };
        }
    }

    exports.EDIMedline = EDIMedline;
});