/**
 * @description Class containing functions to process 810 Bill
 *
 * @NApiVersion 2.1
 * <AUTHOR> <<EMAIL>>
 */

define([
    "exports",
    "require",
    "N/record",
    "N/search",
    "N/log",
    "./edi_810_item",
], (
    /** @type {any} */ exports,
    /** @type {any} */ require,
) => {
    const record = require("N/record");
    const search = require("N/search");
    const log = require("N/log");
    const { EDI810Item } = require("./edi_810_item");

    /**
     * 810 Bill Class
     *
     * @class
     * @typedef {import("../../Interfaces/Models/Partner/edi_partner").EDIVendorInterface} EDIVendorInterface
     */
    class EDI810Bill {
        /** @param {{[key:string]: any}} params */
        constructor (params) {
            /** @type {string} */
            this.fileName = params.fileName;
            /** @type {string[]} */
            this.errors = [];
            /** @type {{[key:string]: any}} */
            this.invoice = params.invoice;
            /** @type {Date} */
            this.invoiceDate = new Date(
                this.invoice.date.slice(0, 4),
                this.invoice.date.slice(4, 6) - 1,
                this.invoice.date.slice(6, 8)
            );
            /** @type {EDIVendorInterface} */
            this.vendor = params.vendor;
            /** @type {import("N/record").Record | null} */
            this.dynamicRecord = null;
            /** @type {import("N/record").Record | null} */
            this.staticRecord = null;
        }

        /**
         * Consolidate error logs for the Bill class
         *
         * @param {Object} args Parameters
         * @param {string} args.functionName Function identifier
         * @param {string} args.logMessage Error message to log
         * @returns {void}
         */
        logErrors({functionName, logMessage}) {
            log.error({
                title: functionName,
                details: logMessage
            });
            this.errors.push(logMessage);
        }

        /**
         * Retrieve the related Purchase Order through the Invoice and Vendor data
         *
         * @returns {number | undefined} Purchase Order ID
         */
        getPurchaseOrderInternalId() {
            const poSearchObject = search.create({
                type: "purchaseorder",
                filters: [
                    ["type", "anyof", "PurchOrd"],
                    "AND",
                    ["tranid", "is", this.invoice.poNumber],
                    "AND",
                    ["entity", "anyof", this.vendor.id],
                    "AND",
                    ["mainline", "is", "T"],
                ],
            });

            log.debug({
                title: 'EDI 810 Bill (getPurchaseOrderInternalId): purchase order filters',
                details: JSON.stringify(poSearchObject.filters)
            });

            const purchaseOrder = poSearchObject.run().getRange({
                start: 0,
                end: 1000,
            })[0];

            if (!purchaseOrder || !purchaseOrder.id) {
                this.logErrors({
                    functionName: "EDI 810 Bill (getPurchaseOrderInternalId)",
                    logMessage: `Purchase Order Internal Id not found for ${this.invoice.invoiceNumber}`
                });

                return;
            }

            return Number(purchaseOrder.id);
        }

        /**
         * Load NetSuite Bill in Dynamic or Static Mode
         *
         * @param {number} billId Bill Internal ID
         * @param {boolean} isDynamic Dynamic mode
         * @returns {void}
         */
        loadRecord(billId, isDynamic) {
            if (isDynamic) {
                this.dynamicRecord = record.load({
                    type: record.Type.VENDOR_BILL,
                    id: billId,
                    isDynamic: true,
                });
            } else {
                this.staticRecord = record.load({
                    type: record.Type.VENDOR_BILL,
                    id: billId,
                    isDynamic: false,
                });
            }
        }

        /**
         * Transform Purchase Order to Bill
         *
         * @param {number} poId Purchase Order ID
         * @returns {number | undefined} Bill internal ID
         */
        transformPurchaseOrder(poId) {
            try {
                const bill = record.transform({
                    fromType: record.Type.PURCHASE_ORDER,
                    fromId: poId,
                    toType: record.Type.VENDOR_BILL,
                });
                // TODO: Add a property to the partner to specify a bill form to remove this default date value for a BRDG field
                bill.setValue({
                    fieldId: 'custbody_rip_bill_invoice_date',
                    value: this.invoiceDate || new Date(),
                });
                const internalId = bill.save();

                if (!internalId) {
                    this.logErrors({
                        functionName: "EDI 810 Bill (transformPurchaseOrder)",
                        logMessage: `Bill not created for PO ${this.invoice.poNumber}`
                    });
                } else {
                    log.debug({ title: "EDI 810 Bill (transformPurchaseOrder)", details: `Purchase Order ${poId} -> Bill ${internalId}` });
                }

                return internalId;
            } catch (/** @type {any} */ err) {
                if (
                    err.name == "INVALID_INITIALIZE_REF" ||
                    err.message ==
                        "You must enter at least one line item for this transaction."
                ) {
                    this.logErrors({
                        functionName: "EDI 810 Bill (transformPurchaseOrder)",
                        logMessage: `All items for ${this.invoice.poNumber} have already been billed`
                    });
                } else {
                    this.logErrors({
                        functionName: "EDI 810 Bill (transformPurchaseOrder)",
                        logMessage: `Bill not created for PO ${this.invoice.poNumber} Error: ${err}`
                    });
                }
            }
        }

        /**
         * Set field values of the Bill record
         * - Transaction Number
         * - Transaction Control Number
         * - Approval Status
         * - Transaction Date
         *
         * @returns {void}
         */
        setValues() {
            try {
                if (!this.dynamicRecord) {
                    return;
                }

                log.debug({
                    title: "EDI 810 Bill (setValues)",
                    details: JSON.stringify({
                        tranid: this.invoice.invoiceNumber,
                        custbody_spl_edi_bill_trns_cntrl_nmbr: this.invoice.transactionControlNumber,
                        approvalstatus: 1,
                        trandate: this.invoiceDate
                    })
                });
                this.dynamicRecord.setValue({
                    fieldId: "tranid",
                    value: this.invoice.invoiceNumber
                });
                this.dynamicRecord.setValue({
                    fieldId: "custbody_spl_edi_bill_trns_cntrl_nmbr",
                    value: this.invoice.transactionControlNumber
                });
                this.dynamicRecord.setValue({
                    fieldId: "approvalstatus",
                    value: 1
                });
                this.dynamicRecord.setValue({
                    fieldId: "trandate",
                    value: this.invoiceDate
                });
            } catch (/** @type {any} */ err) {
                this.logErrors({
                    functionName: "EDI 810 Bill (setValues)",
                    logMessage: `Error setting values for new bill for ${this.invoice.poNumber} Error: ${err.message}`
                });
            }
        }

        /**
         * Set expense amount and account for the bill record
         *
         * @param {object} expense Expense object
         * @param {string} expense.name Expense title
         * @param {number} expense.amount Expense amount
         * @param {number} expense.account Expense account ID
         * @returns {void}
         */
        setExpense({name, amount, account}) {
            try {
                if (amount) {
                    this.dynamicRecord?.selectNewLine({
                        sublistId: "expense",
                    });

                    this.dynamicRecord?.setCurrentSublistValue({
                        sublistId: "expense",
                        fieldId: "account",
                        value: account,
                    });

                    const formattedAmount = amount.toFixed(2);

                    this.dynamicRecord?.setCurrentSublistValue({
                        sublistId: "expense",
                        fieldId: "amount",
                        value: formattedAmount,
                    });

                    this.dynamicRecord?.commitLine({
                        sublistId: "expense",
                    });
                }
            } catch (/** @type {any} */ err) {
                this.logErrors({
                    functionName: "EDI 810 Bill (setExpense)",
                    logMessage: `Error setting ${name} expense for new bill for ${this.invoice.poNumber}. Error: ${err.message}`
                });
            }
        }

        /**
         * Set shipping expense data
         *
         * @returns {void}
         */
        setShippingExpense() {
            log.debug({ title: "EDI 810 Bill (setShippingExpense)", details: 823 });
            this.setExpense({
                name: "shipping",
                amount: this.invoice.shippingAmount,
                account: 823
            }); //51000 - Shipping
        }

        /**
         * Set tax expense data
         *
         * @returns {void}
         */
        setTaxExpense() {
            log.debug({ title: "EDI 810 Bill (setTaxExpense)", details: 833 });
            this.setExpense({
                name: "tax",
                amount: this.invoice.taxAmount,
                account: 833
            }); //50025 - Vendor Tax charge for Goods
        }

        /**
         * Save the values set on the Bill record
         *
         * @returns {number | undefined} Bill record internal ID
         */
        save() {
            try {
                const billId = this.dynamicRecord?.save({
                    enableSourcing: true,
                    ignoreMandatoryFields: true,
                });
                log.debug({ title: "EDI 810 Bill (save)", details: `Bill ${billId} saved` });

                return billId;
            } catch (/** @type {any} */ err) {
                let errorText = `New bill for ${this.invoice.poNumber} not saved: `;

                if (err.message === "You must enter at least one line item for this transaction.") {
                    errorText += "No items to create bill.";
                } else {
                    errorText += `Error: ${err}`;
                }
                this.logErrors({
                    functionName: "EDI 810 Bill (save)",
                    logMessage: errorText
                });

            }
        }

        /**
         * Validate and Update items from the bill
         *
         * @returns {void}
         */
        validateAndUpdateItems() {
            try {
                if (!this.staticRecord) {
                    return;
                }

                const billItems = new EDI810Item({
                    bill: this.staticRecord,
                    referenceNumber: this.invoice.invoiceNumber,
                    vendorItems: this.invoice.items,
                    vendorName: this.vendor.name
                });
                billItems.validateAndUpdate();
                if (billItems.logs.length > 0) {
                    this.errors = this.errors.concat(billItems.logs);
                }
            } catch (err) {
                this.logErrors({
                    functionName: "EDI 810 Bill (validateAndUpdateItems)",
                    logMessage: `Error validating and updating item: ${err}`
                });
            }
        }
    }

    exports.EDI810Bill = EDI810Bill;
});