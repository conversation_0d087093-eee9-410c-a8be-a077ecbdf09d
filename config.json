{"paths": {"PostAfterShipTrackingLib": "/SuiteScripts/Aftership/spl_post_aftership_tracking_lib", "CalculateCogsPerLineLib": "/SuiteScripts/Bridge/Cogs_Per_Line_Item/Libraries/brdg_calculate_cogs_per_line_lib", "BridgeHelperFunctionsLib": "/SuiteScripts/Bridge/Libraries/brdg_helper_functions_lib", "UpdateBridgeItemFieldsLib": "/SuiteScripts/Bridge/Libraries/brdg_update_item_lib", "UpdateItemReceiptFunctionsLib": "/SuiteScripts/Bridge/Item_Receipts/brdg_update_item_receipt_lib", "CreateRipBillCreditLib": "/SuiteScripts/Bridge/RIP_Management/Libraries/brdg_create_bill_credit_lib", "CreateRipRecord": "/SuiteScripts/Bridge/RIP_Management/Libraries/brdg_rip_records_helper_lib", "BridgeItemRecord": "/SuiteScripts/Classes/brdg_item_record", "BridgeRoyalDiscount": "/SuiteScripts/Classes/brdg_royal_discount", "CustomItemSublistRecord": "/SuiteScripts/Classes/spl_custom_item_sublist_record", "CustomTransactionRecord": "/SuiteScripts/Classes/spl_custom_transaction_record", "ItemFulfillmentItemSublistRecord": "/SuiteScripts/Classes/spl_item_fulfillment_item_sublist_record", "ItemFulfillmentTransactionRecord": "/SuiteScripts/Classes/spl_item_fulfillment_transaction_record", "ItemSublistRecord": "/SuiteScripts/Classes/spl_item_sublist_record", "SalesOrderItemSublistRecord": "/SuiteScripts/Classes/spl_sales_order_item_sublist_record", "SalesOrderTransactionRecord": "/SuiteScripts/Classes/spl_sales_order_transaction_record", "TransactionObject": "/SuiteScripts/Classes/spl_transaction_object", "TransactionRecord": "/SuiteScripts/Classes/spl_transaction_record", "PriceForItemForCustomer": "/SuiteScripts/Classes/spl_price_for_item_for_customer", "EdiDataObject": "/SuiteScripts/Classes/vlmd_edi_data_obj", "EdiTransactionRecord": "/SuiteScripts/Classes/vlmd_edi_transaction", "MapReduceSummaryStageHandling": "/SuiteScripts/Classes/vlmd_mr_summary_handling", "CreateNewCreohCustomerLib": "/SuiteScripts/Creoh/Libraries/crh_create_new_customer_lib", "CreateCreohSalesOrderLib": "/SuiteScripts/Creoh/Libraries/crh_create_sales_order_lib", "ParseWalmart850Lib": "/SuiteScripts/Creoh/Libraries/crh_parse_walmart_850_lib", "CreohProcessIncoming850Lib": "/SuiteScripts/Creoh/Libraries/crh_process_incoming_850_lib", "CreohSendIncoming850EmailLib": "/SuiteScripts/Creoh/Libraries/crh_send_incoming_850_email_lib", "CreatePurchaseOrderLib": "/SuiteScripts/EDI/Libraries/Customer_Integration_Libraries/Incoming_850_Libs/spl_create_purchase_order_lib", "CreateSalesOrderLib": "/SuiteScripts/EDI/Libraries/Customer_Integration_Libraries/Incoming_850_Libs/spl_create_sales_order_lib", "SetSalesOrderItemPricingLib": "/SuiteScripts/EDI/Libraries/Customer_Integration_Libraries/Incoming_850_Libs/spl_set_so_item_pricing_lib", "ValidateCreateSupplylineSalesOrderLib": "/SuiteScripts/EDI/Libraries/Customer_Integration_Libraries/Incoming_850_Libs/spl_validate_create_supplyline_sales_order_lib.js", "ValidateItemRatesLib": "/SuiteScripts/EDI/Libraries/Customer_Integration_Libraries/Incoming_850_Libs/spl_validate_item_rates_lib", "SetSalesOrderCorrections": "/SuiteScripts/EDI/Libraries/Customer_Integration_Libraries/Incoming_850_Libs/vlmd_set_sales_order_corrections_lib", "ValidateCreateSalesOrderLib": "/SuiteScripts/EDI/Libraries/Customer_Integration_Libraries/Incoming_850_Libs/vlmd_validate_create_sales_order_lib", "Get810InternalIdsLib": "/SuiteScripts/EDI/Libraries/Customer_Integration_Libraries/Outgoing_810_Libs/spl_get_invoice_internal_ids_lib", "GetResultObjFor832Lib": "/SuiteScripts/EDI/Libraries/Customer_Integration_Libraries/Outgoing_832_Libs/spl_get_result_obj_for_832_lib", "ItemHelperLib": "/SuiteScripts/EDI/Libraries/Customer_Integration_Libraries/Outgoing_832_Libs/spl_item_helper_lib", "Get855InternalIdsLib": "/SuiteScripts/EDI/Libraries/Customer_Integration_Libraries/Outgoing_855_Libs/spl_get_855_internal_ids_lib", "GetItemFulfillmentObjLib": "/SuiteScripts/EDI/Libraries/Customer_Integration_Libraries/Outgoing_856_Libs/spl_get_856_transaction_obj_lib", "Get856InternalIdsLib": "/SuiteScripts/EDI/Libraries/Customer_Integration_Libraries/Outgoing_856_Libs/spl_get_item_fulfillment_internal_ids_lib", "OncareParse850": "/SuiteScripts/EDI/Libraries/Parse_EDI_File/spl_oncare_parse_po_lib", "Parse850Lib": "/SuiteScripts/EDI/Libraries/Parse_EDI_File/spl_parse_850_lib", "Parse856Lib": "/SuiteScripts/EDI/Libraries/Parse_EDI_File/spl_parse_856_lib", "ParseIncoming810Lib": "/SuiteScripts/EDI/Libraries/Parse_EDI_File/spl_parse_inv_lib", "ParseInventoryInquiryLib": "/SuiteScripts/EDI/Libraries/Parse_EDI_File/spl_parse_inventory_inq_lib", "ParseIncoming855Lib": "/SuiteScripts/EDI/Libraries/Parse_EDI_File/spl_parse_po_ack_lib", "ProcessIncoming810Lib": "/SuiteScripts/EDI/Libraries/Process_EDI_File/spl_process_incoming_810_lib", "ProcessIncoming850Lib": "/SuiteScripts/EDI/Libraries/Process_EDI_File/spl_process_incoming_850_lib", "ProcessIncoming855Lib": "/SuiteScripts/EDI/Libraries/Process_EDI_File/spl_process_incoming_855_lib", "ProcessIncoming856Lib": "/SuiteScripts/EDI/Libraries/Process_EDI_File/spl_process_incoming_856_lib", "ProcessOutgoing810Lib": "/SuiteScripts/EDI/Libraries/Process_EDI_File/spl_process_outgoing_810_lib", "ProcessItemCatalogLib": "/SuiteScripts/EDI/Libraries/Process_EDI_File/spl_process_outgoing_832_item_lib", "ProcessCatalogLibMR": "/SuiteScripts/EDI/Libraries/Process_EDI_File/spl_process_outgoing_832_mr_lib", "ProcessPriceCatalogLib": "/SuiteScripts/EDI/Libraries/Process_EDI_File/spl_process_outgoing_832_price_lib", "ProcessOutgoing856Lib": "/SuiteScripts/EDI/Libraries/Process_EDI_File/spl_process_outgoing_856_lib", "ProcessOutgoing855Lib": "/SuiteScripts/EDI/Libraries/Process_EDI_File/spl_process_outgoing_855_lib", "ProcessInvoiceEndLib": "/SuiteScripts/EDI/Libraries/Process_EDI_File_End/spl_process_incoming_810_end_lib", "ProcessPoAckEmailLib": "/SuiteScripts/EDI/Libraries/Process_EDI_File_End/spl_process_incoming_855_email_lib", "ProcessIncoming856EndLib": "/SuiteScripts/EDI/Libraries/Process_EDI_File_End/spl_process_incoming_856_end_lib", "ProcessOutgoing850EmailLib": "/SuiteScripts/EDI/Libraries/Process_EDI_File_End/spl_process_outgoing_850_end_lib", "ProcessOutgoingEdiFileEmailLib": "/SuiteScripts/EDI/Libraries/Process_EDI_File_End/spl_process_outgoing_edi_file_email_lib", "SendIncoming850EmailLib": "/SuiteScripts/EDI/Libraries/Process_EDI_File_End/vlmd_send_incoming_850_email_lib", "ProcessInvoiceValidateAndUpdateItemsLib": "/SuiteScripts/EDI/Libraries/Vendor_Integration_Libraries/Incoming_810_Libs/spl_process_inv_validate_and_update_items_lib", "ProcessInventoryInquiryLib": "/SuiteScripts/EDI/Libraries/Vendor_Integration_Libraries/Incoming_846_Libs/spl_process_inventory_lib", "ComparePoAckValues": "/SuiteScripts/EDI/Libraries/Vendor_Integration_Libraries/Incoming_855_Libs/spl_compare_incoming_855_values_lib", "SetPurchaseOrderCorrections": "/SuiteScripts/EDI/Libraries/Vendor_Integration_Libraries/Incoming_855_Libs/vlmd_set_purchase_order_corrections_lib", "CreateItemFulfillmentLib": "/SuiteScripts/EDI/Libraries/Vendor_Integration_Libraries/Incoming_856_Libs/spl_create_item_fulfillment_for_asn_lib", "GetCarrierIdByScacLib": "/SuiteScripts/EDI/Libraries/Vendor_Integration_Libraries/Incoming_856_Libs/spl_get_carrier_id_by_scac_lib", "CreatePurchaseOrderObjLib": "/SuiteScripts/EDI/Libraries/Vendor_Integration_Libraries/Outgoing_850_Libs/spl_create_purchase_order_obj_lib", "Write810EdiFileLib": "/SuiteScripts/EDI/Libraries/Write_EDI_File/spl_write_outgoing_810_lib", "WriteOutgoing850Lib": "/SuiteScripts/EDI/Libraries/Write_EDI_File/spl_write_outgoing_850_lib", "Write855Lib": "/SuiteScripts/EDI/Libraries/Write_EDI_File/spl_write_outgoing_855_lib", "Write856EdiFileLib": "/SuiteScripts/EDI/Libraries/Write_EDI_File/spl_write_outgoing_856_lib", "RecordModuleHelperLib": "SuiteScripts/Helper_Libraries/vlmd_record_module_helper_lib.js", "Anime": "/SuiteScripts/Libraries/External_Libraries/vlmd_anime_lib", "LoDash": "/SuiteScripts/Libraries/External_Libraries/vlmd_lodash_lib", "Moment": "/SuiteScripts/Libraries/External_Libraries/vlmd_moment_lib", "Numeral": "/SuiteScripts/Libraries/External_Libraries/vlmd_numeral_lib", "PapaParse": "/SuiteScripts/Libraries/External_Libraries/vlmd_papa_parse_lib", "CompareAddressLib": "/SuiteScripts/Libraries/Misc_Libs/spl_compare_addresses_lib", "GetEdiFileContents": "/SuiteScripts/Libraries/Misc_Libs/spl_get_edi_file_contents_lib", "GetEdiIntegrationsLib": "/SuiteScripts/Libraries/Misc_Libs/spl_get_edi_integrations_lib", "GetOrderEmailAddressLib": "/SuiteScripts/Libraries/Misc_Libs/spl_get_order_email_address_lib", "GetEdiPartnerValuesLib": "/SuiteScripts/Libraries/Misc_Libs/spl_get_partner_edi_values_lib", "GetPurchaseOrderAddressLib": "/SuiteScripts/Libraries/Misc_Libs/spl_get_purchase_order_address_lib", "GetSqlResultsObjLib": "/SuiteScripts/Libraries/Misc_Libs/spl_get_suiteql_results_obj_lib", "GetTransactionObj": "/SuiteScripts/Libraries/Misc_Libs/spl_get_transaction_obj_lib", "ItemFulfillmentEmailLib": "/SuiteScripts/Libraries/Misc_Libs/spl_item_fullfillment_email_lib", "MoveFileLib": "/SuiteScripts/Libraries/Misc_Libs/spl_move_file_lib", "PushEdiEmailInfoToDBLib": "/SuiteScripts/Libraries/Misc_Libs/spl_push_edi_email_info_to_db_lib", "AddItemInternalIds": "/SuiteScripts/Libraries/Misc_Libs/vlmd_add_item_internal_ids_lib", "CreateReferralFeeLib": "/SuiteScripts/Misc_Scripts/Libraries/spl_create_gpo_referral_fee_lib", "GetAddressObjLib": "/SuiteScripts/Misc_Scripts/Libraries/spl_get_address_obj_lib", "GetGrossProfitValuesLib": "/SuiteScripts/Misc_Scripts/Libraries/spl_get_gross_profit_values_lib", "GetCreditMemoInfoLib": "/SuiteScripts/Misc_Scripts/Libraries/spl_get_credit_memo_info_lib", "GetInvoicesToUpdateLib": "/SuiteScripts/Misc_Scripts/Libraries/spl_get_invoices_to_update_lib", "GetRepToAssignToLib": "/SuiteScripts/Misc_Scripts/Libraries/spl_get_rep_to_assign_to_lib", "GetShippingCogsLib": "/SuiteScripts/Misc_Scripts/Libraries/spl_get_shipping_cogs_lib", "GetTransactionAndItemObjsLib": "/SuiteScripts/Misc_Scripts/Libraries/spl_get_transaction_and_item_objs_lib", "UpdateGrossProfitValuesOnInvoiceLib": "/SuiteScripts/Misc_Scripts/Libraries/spl_update_gross_profit_on_invoice_lib", "GetGrossProfitValuesHelperLib": "/SuiteScripts/Misc_Scripts/Libraries/spl_get_gp_values_helper_lib", "ParseIncomingHl7OrmLib": "/SuiteScripts/Valmar/HCHB/Libraries/vlmr_parse_incoming_hl7_orm_lib", "CreateNewValmarCustomerLib": "/SuiteScripts/Valmar/Libraries/vlmr_create_new_customer_lib", "CreateValmarSalesOrderLib": "/SuiteScripts/Valmar/Libraries/vlmr_create_sales_order_lib", "ProcessIncomingOrmLib": "/SuiteScripts/Valmar/Libraries/vlmr_process_incoming_orm_lib"}}