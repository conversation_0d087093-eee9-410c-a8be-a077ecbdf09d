/**
 * @NApiVersion 2.1
 * @NScriptType scheduledscript
 * @NAmdConfig /SuiteScripts/config.json
 */

define([
  "require",
  "N/sftp",
  "N/file",
  "N/record",
  "N/query",
  "GetEdiPartnerValuesLib",
  "WriteOutgoing850Lib",
  "ProcessOutgoing850EmailLib",
  "PushEdiEmailInfoToDBLib",
  "../../Libraries/Vendor_Integration_Libraries/Outgoing_850_Libs/spl_create_purchase_order_obj_lib",
  "../../../Classes/vlmd_custom_error_object",
], function (
  require,
  sftp,
  file,
  record,
  query,
  getEdiPartnerValuesLib,
  writePoLib,
  processEnd,
  pushEdiEmailInfoToDBLib
) {
  function execute(context) {
    /** @type {import("../../Libraries/Vendor_Integration_Libraries/Outgoing_850_Libs/spl_create_purchase_order_obj_lib")} */
    const createPurchaseOrderObjLib = require("../../Libraries/Vendor_Integration_Libraries/Outgoing_850_Libs/spl_create_purchase_order_obj_lib");
    /** @type {import("../../../Classes/vlmd_custom_error_object").CustomErrorObject} */
    const CustomErrorObject = require("../../../Classes/vlmd_custom_error_object");
    const customErrorObject = new CustomErrorObject();

    var vendorData = {
      prodGUID: "8bdf15b917d748eba5898e05759e2683",
      sandboxGUID: "",
      vendorInternalId: 13331,
      integrationStartDate: "2/30/2025 12:00 am",
      testDirectory: "/edi/test/vendor/medacure/out/850",
      prodDirectory: "/edi/prod/vendor/medacure/out/850",
      ediRefDirectory: "/edi/reference/vendor/medacure/out/850",
      vendorName: "Medacure",
      getShippingAccount: false,
      documentTypeId: 5,
      pushEmailToDB: true,
    };

    var processingLog = [];
    var transactionsProcessedSuccessfully = [];
    var processedSuccessfully = true;

    try {
      var vendorConnection = getVendorConnection();
      var supplylineConnection = getSupplylineConnection();
      var partnerValues = getPartnerValues();

      var poIds = getInternalIds();

      if (poIds.length <= 0) {
        return true;
      }

      processPurchaseOrders();
      if (customErrorObject.summary) {
        processedSuccessfully = false;
      }

      try {
        var sentEmailObj = processEnd.processEnd(
          processingLog,
          transactionsProcessedSuccessfully,
          processedSuccessfully,
          vendorData.vendorName
        );

        if (vendorData.pushEmailToDB) {
          pushEdiEmailInfoToDBLib.pushEdiEmailInfoToDB(
            vendorData,
            sentEmailObj
          );
        }
      } catch (err) {
        throw {
          name: "ERROR_SENDING_PUSHING_EDI_EMAIL",
          message: err.message,
        };
      }

      if (customErrorObject.summary) {
        throw customErrorObject;
      }

      function getVendorConnection() {
        try {
          var hostKey =
            "AAAAB3NzaC1yc2EAAAABIwAAAIEA2YIvZPcsWA3jP6yq5J" +
            "5XYunNySNspUZUhtzxXoePDZEhxJFIF/am9o+15CqO4hlyEDni0m+VW4" +
            "LVSoiUnj31xJis91EcYrUeCewIcSwof4Xs05ghth/2SKn0cITsR5hOeST" +
            "l4jFsMf9oK0+b2gr4jdV4fria4E/eF2u0qQArWuc=";

          return sftp.createConnection({
            username: "FTPadmin",
            passwordGuid: vendorData.prodGUID,
            url: "************",
            hostKey: hostKey,
            directory: vendorData.prodDirectory,
          });
        } catch (err) {
          throw {
            name: `ERROR_GETTING_MEDACURE_CONNECTION`,
            message: err.message,
          };
        }
      }

      function getSupplylineConnection() {
        try {
          var hostKey =
            "AAAAB3NzaC1yc2EAAAABIwAAAIEA2YIvZPcsWA3jP6yq5J" +
            "5XYunNySNspUZUhtzxXoePDZEhxJFIF/am9o+15CqO4hlyEDni0m+VW4" +
            "LVSoiUnj31xJis91EcYrUeCewIcSwof4Xs05ghth/2SKn0cITsR5hOeST" +
            "l4jFsMf9oK0+b2gr4jdV4fria4E/eF2u0qQArWuc=";

          return sftp.createConnection({
            username: "FTPadmin",
            passwordGuid: vendorData.prodGUID,
            url: "************",
            hostKey: hostKey,
            directory: vendorData.ediRefDirectory,
          });
        } catch (err) {
          throw {
            name: `ERROR_GETTING_SUPPLYLINE_CONNECTION`,
            message: err,
          };
        }
      }

      function getPartnerValues() {
        try {
          return getEdiPartnerValuesLib.getMedacureValues();
        } catch (err) {
          throw {
            name: "ERROR_GETTING_MEDACURE_VALUES",
            message: err.message,
          };
        }
      }

      function getInternalIds() {
        try {
          const transactionQuery = /*sql*/ `
       SELECT
          po.id
       FROM
          transaction po 
          JOIN
             customrecord_vlmd_edi_integration edi 
             ON po.entity = edi.custrecord_edi_intgrtn_vendor 
       WHERE
          po.type = 'PurchOrd' 
          AND edi.custrecord_edi_intgrtn_prchsng_sftwr = 13 
          AND po.custbody_spl_po_edi_trans_cntrl_number IS NULL 
          AND po.createddate > = edi.custrecord_edi_intgrtn_start_date`;

          const resultIterator = query
            .runSuiteQL({
              query: transactionQuery,
            })
            .asMappedResults();

          var internalIdsArr = [];

          resultIterator.forEach(function (result) {
            internalIdsArr.push(result.id);

            return true;
          });

          return internalIdsArr;
        } catch (err) {
          throw {
            name: "ERROR_GETTING_INTERNAL_IDS",
            message: err.message,
          };
        }
      }

      function processPurchaseOrders() {
        poIds.forEach((id) => {
          try {
            processedSuccessfully = true;
            var purchaseOrderObj = getPurchaseOrderObj();

            var purchaseOrderNumber = purchaseOrderObj.number;
            if (!purchaseOrderObj.continueProcessing) {
              processedSuccessfully = false;
              return;
            }
            var ediFile = writePurchaseOrderAsEdiFile();

            if (processedSuccessfully) {
              var fileToUpload = createFile();
              uploadFileToMedacure();
              uploadFileToSupplyline();
              addControlNumberToPurchaseOrder();
              transactionsProcessedSuccessfully.push(purchaseOrderNumber);
            }

            //*******Process Purchase Order Helper Functions*******
            function getPurchaseOrderObj() {
              var obj = createPurchaseOrderObjLib.getPurchaseOrderObj(
                id,
                vendorData.getShippingAccount,
                customErrorObject
              );
              processingLog = processingLog.concat(obj.errorLog);

              return obj;
            }

            function writePurchaseOrderAsEdiFile() {
              var fileObj = writePoLib.getPurchaseOrderAsEDI(
                partnerValues,
                purchaseOrderObj
              );
              if (fileObj.success) {
                return fileObj.value;
              } else {
                throw {
                  name: "ERROR_WRITING_EDI_FILE",
                  message: `${purchaseOrderNumber}: ${fileObj.error}`,
                };
              }
            }

            function createFile() {
              try {
                return file.create({
                  name: purchaseOrderObj.controlNumber + ".edi",
                  fileType: file.Type.PLAINTEXT,
                  contents: ediFile,
                });
              } catch (err) {
                throw {
                  name: "ERROR_CREATING_FILE",
                  message: `${purchaseOrderNumber}: ${err.message}`,
                };
              }
            }

            function uploadFileToMedacure() {
              try {
                vendorConnection.upload({
                  file: fileToUpload,
                  replaceExisting: true,
                });
              } catch (err) {
                throw {
                  name: "ERROR_UPLOADING_FILE",
                  message: `File for ${purchaseOrderNumber} not uploaded to MEDACURE folder. 
								${err.message}`,
                };
              }
            }

            function uploadFileToSupplyline() {
              try {
                supplylineConnection.upload({
                  file: fileToUpload,
                  replaceExisting: true,
                });
              } catch (err) {
                throw {
                  name: "ERROR_UPLOADING_FILE",
                  message: `File for ${purchaseOrderNumber} not uploaded to SUPPLYLINE folder. 
								${err.message}`,
                };
              }
            }

            function addControlNumberToPurchaseOrder() {
              try {
                if (!purchaseOrderObj.controlNumber) {
                  throw {
                    name: `MISSING_VALUE`,
                    message: `${purchaseOrderNumber} is missing a control number to save to the purchase order record`,
                  };
                }

                record.submitFields({
                  type: record.Type.PURCHASE_ORDER,
                  id: id,
                  values: {
                    custbody_spl_po_edi_trans_cntrl_number:
                      purchaseOrderObj.controlNumber,
                  },
                  options: {
                    ignoreMandatoryFields: true,
                  },
                });
              } catch (err) {
                throw {
                  name: "ERROR_SAVING_CONTROL_NUMBER",
                  message: `${purchaseOrderNumber} - ${purchaseOrderObj.controlNumber}: ${err.message}`,
                };
              }
            }
          } catch (err) {
            customErrorObject.updateError({
              errorType: customErrorObject.ErrorTypes.UNHANDLED_ERROR,
              summary: "ERROR_PROCESSING_SPECIFIC_PO",
              details: `Purchase order (internal id: ${id}) not processed successfully. Error: ${JSON.stringify(
                err
              )}`,
            });

            processingLog.push(
              `Summary: ${customErrorObject.summary} Details: ${customErrorObject.details}`
            );
          }
        });
      }
    } catch (err) {
      customErrorObject.throwError({
        summaryText: `ERROR_PROCESSING_MEDACURE_850`,
        error: err,
      });
    }
  }

  return {
    execute,
  };
});
