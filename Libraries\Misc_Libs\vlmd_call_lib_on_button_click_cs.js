/**
 * @NApiVersion 2.0
 * @NScriptType ClientScript
 * @NAmdConfig /SuiteScripts/config.json
 * <AUTHOR>
 */

//@ts-ignore
define(["N/log", "N/url"], function (log, url) {
	function _resolveScript(params) {
		var suiteletURL = url.resolveScript({
			deploymentId: "customdeploy_vlmd_cal_lib_on_btn_clck_sl",
			scriptId: "customscript_vlmd_cal_lib_on_btn_clck_sl",
			params: params,
		});

		document.location = suiteletURL;
	}

	function createBillCreditOnButtonClick(vendorBillId) {
		_resolveScript({
			functionName: "createBillCredit",
			transactionId: vendorBillId,
		});
	}

	function calculateGrossProfitOnButtonClick(
		invoiceId,
		recordType,
		createdFromRecordType
	) {
		_resolveScript({
			functionName: "calculateGrossProfit",
			transactionId: invoiceId,
			recordType: recordType,
			createdFromRecordType,
		});
	}

	function calculateCogsPerItemOnButtonClick(transactionId, recordType) {
		_resolveScript({
			functionName: "calculateCogsOnItems",
			transactionId,
			recordType,
		});
	}

	return {
		createBillCreditOnButtonClick,
		calculateGrossProfitOnButtonClick,
		calculateCogsPerItemOnButtonClick,
	};
});
