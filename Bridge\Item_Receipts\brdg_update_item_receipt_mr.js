/**
 * @description Iterates over all BRDG vendor bills created/edited that day, checks for any rate discrepancies for related Item Receipts and updates the IR rates accordingly.
 *
 * </br><b>Schedule:</b> Runs every morning @ 2:00 AM
 *
 * @NApiVersion 2.1
 * @NScriptType MapReduceScript
 * @NAmdConfig /SuiteScripts/config.json
 *
 * <AUTHOR>
 * @module brdg_update_item_receipt_mr
 */

//@ts-ignore
define([
	"N/runtime",
	"N/log",
	"N/query",
	"N/format",
	"UpdateItemReceiptFunctionsLib",
], function (runtime, log, query, format, updateIRHelperLib) {
	function getInputData(context) {
		try {
			const { startDate, endDate, subsidiary, billIdOverride } =
				updateIRHelperLib.getScriptParameters();

			log.audit(
				"Script Params",
				`Start Date: ${startDate} End Date: ${endDate} Subsidiary: ${subsidiary} Bill ID: ${billIdOverride}`
			);

			/**Get bills with the following criteria:
			 * Status: Approved (Open or Paid)
			 * Contains items (not just expenses)
			 *
			 * Bill ID is the bill ID parameter passed in
			 * OR
			 * Date bill was created/Date the total bill amount was modified -> is yesterday OR the date parameter passed in
			 * AND
			 * Subsidiary is Bridge OR the subsidiary passed in.
			 */
			const selectClause = `SELECT DISTINCT
			   bill.id, BILL.tranid, BILL.createddate `;
			const fromClause = `FROM
			      transaction AS bill `;
			const joinClause = `LEFT OUTER JOIN
			         transactionLine
			         ON transactionLine.transaction = BILL.id
			      JOIN
			         subsidiary AS billSub
			         ON transactionLine.subsidiary = billSub.id
			      JOIN
			         subsidiary AS parent
			         ON billSub.parent = parent.id 
					LEFT JOIN
					 SystemNote 
					 ON bill.id = SystemNote.recordid `;
			const whereClause =
				`WHERE
			BILL.type = 'VendBill' 
			AND 
			(
			   BUILTIN.CF( BILL.Status ) = 'VendBill:A' 
			   OR 
				  BUILTIN.CF( BILL.Status ) = 'VendBill:B' 
			) AND BILL.ID IN 
			(
			   SELECT
				  transaction 
			   FROM
				  transactionline 
			   WHERE
				  itemtype IS NOT NULL
			) ` +
				(billIdOverride && billIdOverride != "null"
					? `AND BILL.ID IN(${billIdOverride}) ` //If bill ID was passed in as parameter, don't filter the subsidiary or dates.
					: (startDate && endDate
							? `AND (
					TO_DATE(BILL.createddate, 'MM/DD/YYYY' ) BETWEEN TO_DATE('${startDate}', 'MM/DD/YYYY') AND TO_DATE('${endDate}', 'MM/DD/YYYY')
					OR
					(
					TO_DATE(SystemNote.date, 'MM/DD/YYYY' ) BETWEEN TO_DATE('${startDate}', 'MM/DD/YYYY') AND TO_DATE('${endDate}', 'MM/DD/YYYY')
					AND SystemNote.field = 'TRANDOC.MAMOUNTMAIN' 
					)
		) `
							: `AND
				(
				   TO_DATE (SYSDATE -1, 'MM/DD/YYYY') = TO_DATE (BILL.createddate, 'MM/DD/YYYY')
				   OR 
				   (TO_DATE (SYSDATE -1, 'MM/DD/YYYY') = TO_DATE (SystemNote.date, 'MM/DD/YYYY')
				   AND SystemNote.field = 'TRANDOC.MAMOUNTMAIN' )
				) `) +
					  (subsidiary && subsidiary != "null"
							? `AND transactionLine.subsidiary = ${subsidiary} `
							: `AND 
			(
			   parent.id = '16' 
			   OR parent.parent = '16' 
			) `));

			const orderyByClause = ` ORDER BY BILL.createddate DESC `;

			const sqlQuery = `${selectClause}${fromClause}${joinClause}${whereClause}${orderyByClause}`;

			log.audit("SQL Query: sqlQuery", sqlQuery);

			return {
				type: "suiteql",
				query: sqlQuery,
				params: [],
			};
		} catch (e) {
			log.error("Error getting bills to update:", e);
			return;
		}
	}

	function map(context) {
		const billId = JSON.parse(context.value).values[0];
		const billName = JSON.parse(context.value).values[1];

		try {
			const itemLinesToUpdate = updateIRHelperLib.runSqlQuery(billId);

			if (itemLinesToUpdate.length <= 0) {
				context.write({
					key: "No item receipts to update",
					value: billName ? billName : `Bill ID: ${billId}`,
				});
				return true; //Break and continue iterating
			}

			itemLinesToUpdate.forEach((line) => {
				context.write({ key: line.itemreceiptid, value: line });
			});
		} catch (e) {
			context.write({
				key: "Error getting item receipt info: ",
				value: {
					BILL: `Bill: ${billName}`,
					ITEMRECEIPT: "N/A",
					ITEMNAME: "N/A",
					PREVIOUSPRICE: "N/A",
					UPDATEDPRICE: "N/A",
					ERROR: `Error getting item receipt info: ${e}`,
				},
			});
		}
	}

	//Reduce runs once fore each item receipt key passed in
	function reduce(context) {
		if (!Number(context.key)) {
			//If the key is not an item receipt - an error/result message
			for (let i = 0; i < context.values.length; i++) {
				context.write({ key: context.key, value: context.values[i] });
			}

			return true;
		}

		const itemReceiptId = context.key;

		const itemLinesToUpdate = context.values.map((value) => JSON.parse(value));

		const resultsLog = updateIRHelperLib.updateItemReceipt(
			itemReceiptId,
			itemLinesToUpdate
		);

		resultsLog.forEach((result) => {
			context.write({ key: "Results", value: result });
		});
	}

	function summarize(context) {
		const {
			errorLog,
			itemReceiptsUpdatedSuccessfully,
			itemReceiptsWithErrors,
			noIRtoUpdate,
			resultsLogToPrint,
		} = updateIRHelperLib.getItemReceiptsResults(context);

		const parameters = updateIRHelperLib.getScriptParameters();

		log.audit({
			title: `${itemReceiptsUpdatedSuccessfully.length} Item Receipts Updated`,
			details: itemReceiptsUpdatedSuccessfully.join(","),
		});

		log.audit({
			title: `${itemReceiptsWithErrors.length} Item Receipts With Errors`,
			details: itemReceiptsWithErrors.join(","),
		});

		log.audit({
			title: `${noIRtoUpdate.length} bills have no item receipts to update`,
			details: noIRtoUpdate.join(","),
		});

		if (
			itemReceiptsUpdatedSuccessfully.length <= 0 &&
			itemReceiptsWithErrors.length <= 0
		) {
			return; //Don't send results email if nothing was updated
		}

		updateIRresultsFile =
			updateIRHelperLib.createCSVResultFile(resultsLogToPrint);

		updateIRHelperLib.sendResultsEmail(
			parameters,
			updateIRresultsFile,
			errorLog
		);
	}

	return {
		getInputData,
		map,
		reduce,
		summarize,
	};
});
