/******************************************************************************************************
	Script Name - AVA_SUT_CertCaptureViewBatch.js
	Company -     Avalara Technologies Pvt Ltd.
******************************************************************************************************/

/**
 *@NApiVersion 2.0
 *@NScriptType Suitelet
 */

define(['N/ui/serverWidget', 'N/search', 'N/record', 'N/task', 'N/redirect', 'N/url', './utility/AVA_Library'],
	function(ui, search, record, task, redirect, url, ava_library){
		function onRequest(context){
			try{
				var checkServiceSecurity = ava_library.mainFunction('AVA_CheckService', 'AvaCert');
				if(checkServiceSecurity == 0){
					checkServiceSecurity = ava_library.mainFunction('AVA_CheckSecurity', 31);
				}

				if(checkServiceSecurity == 0){
					if(context.request.method === 'GET'){
						var avaCertCaptureForm = ui.createForm({
							title: 'View Batch'
						});
						var batchId = context.request.parameters.batchid;
						log.debug('onRequest', 'batchId = ' + batchId);
						avaCertCaptureForm.clientScriptModulePath = './AVA_CLI_CertCaptureViewBatch.js';
						addFormFields(avaCertCaptureForm, context, batchId);
						addFormSublist(avaCertCaptureForm);
						setSublistFieldValues(avaCertCaptureForm, batchId)
						avaCertCaptureForm.addSubmitButton({
							label: 'Delete'
						});
						avaCertCaptureForm.addButton({
							id: 'custpage_ava_certcapture_refresh',
							label: 'Refresh',
							functionName: 'AVA_CertCaptureRefresh()'
						});
						avaCertCaptureForm.addPageLink({
							title: 'Create Batch',
							type: ui.FormPageLinkType.CROSSLINK,
							url: url.resolveScript({
								scriptId: 'customscript_avaaddcuststocert_suitelet',
								deploymentId: 'customdeploy_avaaddcuststocertcapture'
							})
						});
						context.response.writePage({
							pageObject: avaCertCaptureForm
						});
					}
					else{
						if(context.request.parameters.custpage_batchnamelist){
							record.submitFields({
								type: 'customrecord_avacertcapturebatch',
								id: context.request.parameters.custpage_batchnamelist,
								values: {
									custrecord_ava_certcapturebatchstatus: 'Delete'
								},
								options: {
									enableSourcing: false,
									ignoreMandatoryFields: true
								}
							});

							var scheduledScript = task.create({
								taskType: task.TaskType.SCHEDULED_SCRIPT
							});
							scheduledScript.scriptId = 'customscript_avadeletecertbatches_sched';
							scheduledScript.submit();

							redirect.toTaskLink({
								id: 'CARD_-29'
							});
						}
					}
				}
				else{
					context.response.write({
						output: checkServiceSecurity
					});
				}
			}
			catch(e){
				log.error('ERROR', 'ERROR NAME = ' + e.name + ', ERROR TYPE = ' + e.type + ', ERROR MESSAGE = ' + e.message);
			}
		}

		function setSublistFieldValues(avaCertCaptureForm, batchId){
			try{
				if(batchId){
					var bulkCustomerSublist = avaCertCaptureForm.getSublist({
						id: 'ava_responsebulkcustomersublist'
					});
					var lineNum = 0;
					var customrecord_avacustomerstocertcaptureSearchObj = search.create({
						type: "customrecord_avacustomerstocertcapture",
						filters: [
							["custrecord_ava_certcapture_batchid", "anyof", batchId]
						],
						columns: [
							"custrecord_ava_certcapture_customer",
							"custrecord_ava_certcapture_customercode",
							"custrecord_ava_certcapture_customername",
							"custrecord_ava_certcapture_res_status",
							"custrecord_ava_certcapture_res_message"
						]
					});
					customrecord_avacustomerstocertcaptureSearchObj.run().each(function(result){
						var customerCode = result.getValue({
							name: 'custrecord_ava_certcapture_customercode'
						});
						var customerName = result.getValue({
							name: 'custrecord_ava_certcapture_customername'
						});
						var responseStatus = result.getValue({
							name: 'custrecord_ava_certcapture_res_status'
						});
						var responseMessage = result.getValue({
							name: 'custrecord_ava_certcapture_res_message'
						});
						if(customerCode){
							bulkCustomerSublist.setSublistValue({
								id: 'customercode',
								line: lineNum,
								value: customerCode
							});
						}
						if(customerName){
							bulkCustomerSublist.setSublistValue({
								id: 'customername',
								line: lineNum,
								value: customerName
							});
						}
						if(responseStatus){
							bulkCustomerSublist.setSublistValue({
								id: 'responsestatus',
								line: lineNum,
								value: responseStatus
							});
						}
						if(responseMessage){
							bulkCustomerSublist.setSublistValue({
								id: 'responsemessage',
								line: lineNum,
								value: responseMessage
							});
						}
						lineNum++;
						return true;
					});
				}
			}
			catch(e){
				log.error('setSublistFieldValues', 'ERROR NAME = ' + e.name + ', ERROR TYPE = ' + e.type + ', ERROR MESSAGE = ' + e.message);
			}
		}

		function addFormSublist(avaCertCaptureForm){
			try{
				var bulkCustomerSublist = avaCertCaptureForm.addSublist({
					id: 'ava_responsebulkcustomersublist',
					label: 'Customers',
					type: 'list'
				});
				bulkCustomerSublist.addField({
					id: 'customercode',
					label: 'Customer Code',
					type: 'text'
				});
				bulkCustomerSublist.addField({
					id: 'customername',
					label: 'Customer Name',
					type: 'text'
				});
				bulkCustomerSublist.addField({
					id: 'responsestatus',
					label: 'STATUS',
					type: 'text'
				});
				bulkCustomerSublist.addField({
					id: 'responsemessage',
					label: 'MESSAGE',
					type: 'text'
				});
				return avaCertCaptureForm;
			}
			catch(e){
				log.error('addFormSublist', 'ERROR NAME = ' + e.name + ', ERROR TYPE = ' + e.type + ', ERROR MESSAGE = ' + e.message);
			}
		}

		function addFormFields(avaCertCaptureForm, context, batchId){
			try{
				var avaBatchNameList = avaCertCaptureForm.addField({
					id: 'custpage_batchnamelist',
					label: 'Batch Name',
					type: ui.FieldType.SELECT
				});
				var avaBatchStatus = avaCertCaptureForm.addField({
					id: 'ava_batchstatus',
					label: 'Batch Status',
					type: ui.FieldType.TEXT
				});
				avaBatchStatus.updateDisplayType({
					displayType: ui.FieldDisplayType.DISABLED
				});
				avaBatchStatus.updateDisplaySize({
					width: 40,
					height: 0
				});
				setBatchNameListValues(avaBatchNameList, batchId, avaBatchStatus)
			}
			catch(e){
				log.error('addFormFields', 'ERROR NAME = ' + e.name + ', ERROR TYPE = ' + e.type + ', ERROR MESSAGE = ' + e.message);
			}
		}

		function setBatchNameListValues(avaBatchNameList, batchId, avaBatchStatus){
			try{
				var allBatchNames = getAllBatchNames(batchId, avaBatchStatus);
				for(var i = 0; i < allBatchNames.length; i++){
					if(i == 0){
						avaBatchNameList.addSelectOption({
							value: '',
							text: ''
						});
					}
					if(batchId == allBatchNames[i].batchid){
						avaBatchNameList.addSelectOption({
							value: allBatchNames[i].batchid,
							text: allBatchNames[i].batchname,
							isSelected: true
						});
					}
					else{
						avaBatchNameList.addSelectOption({
							value: allBatchNames[i].batchid,
							text: allBatchNames[i].batchname
						});
					}
				}
			}
			catch(e){
				log.error('setBatchNameListValues', 'ERROR NAME = ' + e.name + ', ERROR TYPE = ' + e.type + ', ERROR MESSAGE = ' + e.message);
			}
		}

		function getAllBatchNames(batchId, avaBatchStatus){
			var allBatchNames = new Array();
			try{
				var customrecord_avacertcapturebatchSearchObj = search.create({
					type: "customrecord_avacertcapturebatch",
					filters: [
						["isinactive", "is", "F"],
						"AND",
						["custrecord_ava_certcapturebatchstatus", "isnot", "Delete"]
					],
					columns: [
						search.createColumn({
							name: "internalid"
						}),
						search.createColumn({
							name: "custrecord_ava_certcapturebatchstatus"
						}),
						search.createColumn({
							name: "custrecord_ava_certcapturebatchname"
						})
					]
				});
				customrecord_avacertcapturebatchSearchObj.run().each(function(result){
					if(batchId == result.getValue({name: "internalid"})){
						avaBatchStatus.defaultValue = result.getValue({
							name: "custrecord_ava_certcapturebatchstatus"
						})
					}
					var batchDetails = ({
						batchid: result.getValue({
							name: "internalid"
						}),
						batchname: result.getValue({
							name: "custrecord_ava_certcapturebatchname"
						})
					});
					allBatchNames.push(batchDetails);
					return true;
				});
			}
			catch(e){
				log.error('getAllBatchNames', 'ERROR NAME = ' + e.name + ', ERROR TYPE = ' + e.type + ', ERROR MESSAGE = ' + e.message);
			}
			
			return allBatchNames;
		}
		
		return{
			onRequest: onRequest
		};
	}
);