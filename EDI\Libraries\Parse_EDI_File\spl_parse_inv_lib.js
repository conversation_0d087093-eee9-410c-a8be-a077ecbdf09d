/**
 * @NApiVersion 2.x
 */

//@ts-ignore
define(["N/log", "AddItemInternalIds"], function (log, addInternalIdsLib) {
	function parse810(ediFile, partnerValues) {
		var errorLog = [];
		var fieldDelimiter = partnerValues.formattingInfo[0].partnerValue;
		var segmentDelimiter = partnerValues.formattingInfo[1].partnerValue;
		ediFile = ediFile.split(/\n\r/).join("");
		try {
			var invoiceObj = {
				transactionControlNumber: getTransactionControlNumber(),
				invoiceNumber: getInvoiceNumber(),
				poNumber: getPONumber(),
				total: getTotal(),
				shippingAmount: getShippingAmount(),
				taxAmount: getTaxAmount(),
				date: getDate(),
			};
			invoiceObj.items = getItems();
		} catch (e) {
			errorLog.push(e.stack);
		}

		return {
			//@ts-ignore
			invoiceObj: invoiceObj,
			errorLog: errorLog,
		};

		/**********Parse Invoice Helper Functions**********/
		function getSegment(segmentTitle) {
			var regExp = new RegExp(
				segmentDelimiter + segmentTitle + ".*?" + segmentDelimiter
			);
			var segment = ediFile.match(regExp);
			if (segment) {
				segment = segment[0].split(segmentDelimiter).join("");
				return segment;
			} else {
				return;
			}
		}

		function getTransactionControlNumber() {
			var regExp = new RegExp("ISA" + ".*?" + segmentDelimiter);
			var isaSegment = ediFile.match(regExp);
			if (isaSegment) {
				isaSegment = isaSegment[0].replace(segmentDelimiter, "");
				return isaSegment.split(fieldDelimiter)[13];
			} else {
				return;
			}
		}

		function getInvoiceNumber() {
			var bigSegment = getSegment("BIG");
			return bigSegment.split(fieldDelimiter)[2];
		}

		function getPONumber() {
			var bigSegment = getSegment("BIG");
			return bigSegment.split(fieldDelimiter)[4];
		}

		function getTotal() {
			var totalSegment = getSegment("TDS");
			return totalSegment.split(fieldDelimiter)[1] / 100;
		}

		function getShippingAmount() {
			var shippingSegment = getSegment("SAC");
			if (shippingSegment) {
				return shippingSegment.split(fieldDelimiter)[5] / 100;
			}
		}

		function getTaxAmount() {
			var taxSegment = getSegment("TXI");
			if (taxSegment) {
				return taxSegment.split(fieldDelimiter)[2];
			}
		}

		function getDate() {
			var bigSegment = getSegment("BIG");
			if (bigSegment) {
				return bigSegment.split(fieldDelimiter)[1];
			}
		}

		function getItems() {
			var regExp = new RegExp("IT1" + ".*?" + segmentDelimiter, "g");
			var itemSegments = ediFile.match(regExp);
			var itemsArr = [];

			itemSegments.forEach(function (itemSegment) {
				var itemFields = itemSegment.split(fieldDelimiter);
				var item = {
					quantity: itemFields[2],
					rate: itemFields[4],
				};
				var length = itemFields.length;
				if (length >= 9) {
					item.itemName = itemFields[9].replace(segmentDelimiter, "");
				} else {
					item.itemName = itemFields[7].replace(segmentDelimiter, "");
				}

				itemsArr.push(item);
			});

			var itemsWithInternalIds = addInternalIdsLib.addItemInternalIds(
				itemsArr,
				[1, 2], //Supplyline and Valmar subsidiaries
			);
			
			return itemsWithInternalIds;
		}
	}

	return {
		parse810: parse810,
	};
});
