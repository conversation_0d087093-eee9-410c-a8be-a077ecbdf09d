/******************************************************************************************************
	Script Name - AVA_SUT_IMSViewBatch.js
	Company -     Avalara Technologies Pvt Ltd.
******************************************************************************************************/

/**
 *@NApiVersion 2.0
 *@NScriptType Suitelet
 */

define(['N/ui/serverWidget', 'N/search', 'N/record', 'N/redirect', 'N/url', './utility/AVA_Library'],
	function(ui, search, record, redirect, url, ava_library){
		function onRequest(context){
			try{
				var checkServiceSecurity = ava_library.mainFunction('AVA_CheckService', 'TaxSvc');
				if(checkServiceSecurity == 0){
					checkServiceSecurity = ava_library.mainFunction('AVA_CheckSecurity', 33);
				}

				if(checkServiceSecurity == 0){
					if(context.request.method === 'GET'){
						var avaIMSForm = ui.createForm({
							title: 'View Batch'
						});
						var batchId = context.request.parameters.batchid;
						log.debug('onRequest', 'batchId = ' + batchId);
						avaIMSForm.clientScriptModulePath = './AVA_CLI_IMSViewBatch.js';
						addFormFields(avaIMSForm, context, batchId);
						addFormSublist(avaIMSForm);
						setSublistFieldValues(avaIMSForm, batchId)
						avaIMSForm.addSubmitButton({
							label: 'Delete'
						});
						avaIMSForm.addButton({
							id: 'custpage_ava_imsviewbatch_refresh',
							label: 'Refresh',
							functionName: 'AVA_IMSViewBatchRefresh()'
						});
						avaIMSForm.addPageLink({
							title: 'Create Batch',
							type: ui.FormPageLinkType.CROSSLINK,
							url: url.resolveScript({
								scriptId: 'customscript_ava_imscreatebatch_suitelet',
								deploymentId: 'customdeploy_ava_imscreatebatch'
							})
						});
						context.response.writePage({
							pageObject: avaIMSForm
						});
					}
					else{
						if(context.request.parameters.custpage_batchnamelist){
							record.delete({
								type: 'customrecord_avaimsbatch',
								id: context.request.parameters.custpage_batchnamelist
							});
							
							redirect.toTaskLink({
								id: 'CARD_-29'
							});
						}
					}
				}
				else{
					context.response.write({
						output: checkServiceSecurity
					});
				}
			}
			catch(e){
				log.error('ERROR', 'ERROR NAME = ' + e.name + ', ERROR TYPE = ' + e.type + ', ERROR MESSAGE = ' + e.message);
			}
		}

		function setSublistFieldValues(avaIMSForm, batchId){
			try{
				if(batchId){
					var imsSublist = avaIMSForm.getSublist({
						id: 'ava_responseimssublist'
					});
					var lineNum = 0;
					var customrecord_avaIMSSearchObj = search.create({
						type: "customrecord_avaimsdetails",
						filters: [
							["custrecord_ava_imsbatchdetails_ref", "contains", batchId]
						],
						columns: [
							"custrecord_ava_imsitem",
							"custrecord_ava_imsresponsecode",
							"custrecord_ava_imsrequestdata",
							"custrecord_ava_imsresponseerror",
							"custrecord_ava_imsbatchdetails_ref"
						]
					});
					customrecord_avaIMSSearchObj.run().each(function(result){
						
						var imsBatchDetailsRef = result.getValue({
							name: 'custrecord_ava_imsbatchdetails_ref'
						});
						if(imsBatchDetailsRef){
							var imsBatchDetailsArray = imsBatchDetailsRef.split('-');
							batchId = batchId.toString();
							if(imsBatchDetailsArray.indexOf(batchId) >= 0){
								var itemName = result.getText({
									name: 'custrecord_ava_imsitem'
								});
								var responseCode = result.getValue({
									name: 'custrecord_ava_imsresponsecode'
								});
								var requestData = result.getValue({
									name: 'custrecord_ava_imsrequestdata'
								});
								var errorMessage = result.getValue({
									name: 'custrecord_ava_imsresponseerror'
								});
								
								if(itemName){
									imsSublist.setSublistValue({
										id: 'imsitem',
										line: lineNum,
										value: itemName
									});
								}
								if(responseCode){
									imsSublist.setSublistValue({
										id: 'imsresponsecode',
										line: lineNum,
										value: responseCode
									});
								}
								if(requestData){
									imsSublist.setSublistValue({
										id: 'imsrequestdata',
										line: lineNum,
										value: requestData
									});
								}
								if(errorMessage){
									imsSublist.setSublistValue({
										id: 'imserrormessage',
										line: lineNum,
										value: errorMessage
									});
								}
								lineNum++;
							}
						}
						
						return true;
					});
				}
			}
			catch(e){
				log.error('setSublistFieldValues', 'ERROR NAME = ' + e.name + ', ERROR TYPE = ' + e.type + ', ERROR MESSAGE = ' + e.message);
			}
		}

		function addFormSublist(avaIMSForm){
			try{
				var imsSublist = avaIMSForm.addSublist({
					id: 'ava_responseimssublist',
					label: 'Items',
					type: 'list'
				});
				imsSublist.addField({
					id: 'imsitem',
					label: 'Item Name',
					type: 'text'
				});
				imsSublist.addField({
					id: 'imsrequestdata',
					label: 'Request JSON',
					type: 'textarea'
				});
				imsSublist.addField({
					id: 'imsresponsecode',
					label: 'Response Code',
					type: 'text'
				});
				imsSublist.addField({
					id: 'imserrormessage',
					label: 'Error',
					type: 'text'
				});
				return avaIMSForm;
			}
			catch(e){
				log.error('addFormSublist', 'ERROR NAME = ' + e.name + ', ERROR TYPE = ' + e.type + ', ERROR MESSAGE = ' + e.message);
			}
		}

		function addFormFields(avaIMSForm, context, batchId){
			try{
				var avaBatchNameList = avaIMSForm.addField({
					id: 'custpage_batchnamelist',
					label: 'Batch Name',
					type: ui.FieldType.SELECT
				});
				var avaBatchStatus = avaIMSForm.addField({
					id: 'ava_batchstatus',
					label: 'Batch Status',
					type: ui.FieldType.TEXT
				});
				avaBatchStatus.updateDisplayType({
					displayType: ui.FieldDisplayType.DISABLED
				});
				avaBatchStatus.updateDisplaySize({
					width: 40,
					height: 0
				});
				setBatchNameListValues(avaBatchNameList, batchId, avaBatchStatus)
			}
			catch(e){
				log.error('addFormFields', 'ERROR NAME = ' + e.name + ', ERROR TYPE = ' + e.type + ', ERROR MESSAGE = ' + e.message);
			}
		}

		function setBatchNameListValues(avaBatchNameList, batchId, avaBatchStatus){
			try{
				var allBatchNames = getAllBatchNames(batchId, avaBatchStatus);
				for(var i = 0; i < allBatchNames.length; i++){
					if(i == 0){
						avaBatchNameList.addSelectOption({
							value: '',
							text: ''
						});
					}
					if(batchId == allBatchNames[i].batchid){
						avaBatchNameList.addSelectOption({
							value: allBatchNames[i].batchid,
							text: allBatchNames[i].batchname,
							isSelected: true
						});
					}
					else{
						avaBatchNameList.addSelectOption({
							value: allBatchNames[i].batchid,
							text: allBatchNames[i].batchname
						});
					}
				}
			}
			catch(e){
				log.error('setBatchNameListValues', 'ERROR NAME = ' + e.name + ', ERROR TYPE = ' + e.type + ', ERROR MESSAGE = ' + e.message);
			}
		}

		function getAllBatchNames(batchId, avaBatchStatus){
			var allBatchNames = new Array();
			try{
				var customrecord_avacIMSBatchSearchObj = search.create({
					type: "customrecord_avaimsbatch",
					filters: [
						["isinactive", "is", "F"],
						"AND",
						["custrecord_ava_imsstatus", "isnot", "Delete"]
					],
					columns: [
						search.createColumn({
							name: "internalid"
						}),
						search.createColumn({
							name: "custrecord_ava_imsstatus"
						}),
						search.createColumn({
							name: "custrecord_ava_imsbatchname"
						})
					]
				});
				customrecord_avacIMSBatchSearchObj.run().each(function(result){
					if(batchId == result.getValue({name: "internalid"})){
						avaBatchStatus.defaultValue = result.getValue({
							name: "custrecord_ava_imsstatus"
						})
					}
					var batchDetails = ({
						batchid: result.getValue({
							name: "internalid"
						}),
						batchname: result.getValue({
							name: "custrecord_ava_imsbatchname"
						})
					});
					allBatchNames.push(batchDetails);
					return true;
				});
			}
			catch(e){
				log.error('getAllBatchNames', 'ERROR NAME = ' + e.name + ', ERROR TYPE = ' + e.type + ', ERROR MESSAGE = ' + e.message);
			}
			
			return allBatchNames;
		}
		
		return{
			onRequest: onRequest
		};
	}
);