/**
 * @description Library containing function that creates new rip related records
 *
 * @NApiVersion 2.1
 *
 * <AUTHOR>
 * @module brdg_create_rip_record_lib
 *
 */

define([
  "require",
  "N/log",
  "N/query",
  "N/record",
  "../../../Helper_Libraries/vlmd_record_module_helper_lib",
], (/** @type {any} */ require, _) => {
  const log = require("N/log");
  const query = require("N/query");
  const record = require("N/record");
  const recordHelperLib = require("../../../Helper_Libraries/vlmd_record_module_helper_lib");

  //#region Create Rip Records
  function createRipTierRecord(tierObj) {
    const ripTierRecord = record.create({
      type: "customrecord_rip_tier_and_quantity",
    });
    recordHelperLib.setBodyValues(tierObj, ripTierRecord);

    return recordHelperLib.saveRecord(ripTierRecord);
  }
  function createVendorCredit(vendorCreditObj, itemSublistArr) {
    const vendorCreditRecord = recordHelperLib.createRecord("VENDOR_CREDIT");
    recordHelperLib.setBodyValues(vendorCreditObj, vendorCreditRecord);
    recordHelperLib.setSublistValues(
      itemSublistArr,
      "item",
      vendorCreditRecord
    );

    const newVendorCredit = vendorCreditRecord.save();
    return newVendorCredit;
  }
  //#endregion

  //#region Delete Rip Records
  function _getRelatedRipAccrualRecords(billId) {
    const relatedRipAccrualRecordsQuery = `
        select id from customrecord_rip_po_rip_code_accrual where custrecord_vendor_bill_accruing_for = ?`;

    var sqlResults = query
      .runSuiteQL({
        query: relatedRipAccrualRecordsQuery,
        params: [billId],
      })
      .asMappedResults();

    ripAccrualIdsArr = [];

    sqlResults.forEach((accrualObj) =>
      ripAccrualIdsArr.push(Number(accrualObj.id))
    );

    return ripAccrualIdsArr;
  }
  function _getTierQuantityRecords(ripAccrualRecord) {
    const relatedTierQuantityRecordsQuery = `
      select id from customrecord_rip_tier_and_quantity where custrecord_rip_code_accrual_per_bill_rec = ?`;

    var sqlResults = query
      .runSuiteQL({
        query: relatedTierQuantityRecordsQuery,
        params: [ripAccrualRecord],
      })
      .asMappedResults();

    tierQuantityIdsArr = [];

    sqlResults.forEach((tierQuantityObj) =>
      tierQuantityIdsArr.push(Number(tierQuantityObj.id))
    );

    return tierQuantityIdsArr;
  }

  function _clearAccrualLinksOnTransactions(billId) {
    const billInEditMode = record.load({
      type: record.Type.VENDOR_BILL,
      id: billId,
    });

    const itemSublistLineCount = billInEditMode.getLineCount({
      sublistId: "item",
    });

    for (let line = 0; line < itemSublistLineCount; line++) {

      //You cannot edit the last line of an item group on a transaction
      const itemType = billInEditMode.getSublistValue({
        sublistId: "item",
        fieldId: "itemtype",
        line: line,
      });

      itemType!="EndGroup" && billInEditMode.setSublistValue({
        sublistId: "item",
        fieldId: "custcol_rip_accrual_record_link",
        line: line,
        value: "",
      });
    }

    billInEditMode.save({
      ignoreMandatoryFields: true,
      ignoreFieldChange: true,
    });
  }
  function _deleteRecord(recordId, recordType) {
    record.delete({
      id: recordId,
      type: recordType,
    });
  }
  function deleteRipRecords(billId, vendorCreditId, onlyDelete) {
    //Delete all related rip records to this bill. Goes in order of dependent records
    //Get the rip accural records - for each one delete all related tier quantity records and then delete the accrual record
    let ripAccrualsArr = _getRelatedRipAccrualRecords(billId);
    if (ripAccrualsArr.length > 0) {
      ripAccrualsArr.forEach((accrualRecordId) => {
        let tierQuantityRecordsArr = _getTierQuantityRecords(accrualRecordId);
        if (tierQuantityRecordsArr.length > 0) {
          tierQuantityRecordsArr.forEach((tierRecordId) => {
            _deleteRecord(tierRecordId, "customrecord_rip_tier_and_quantity");
          });
        }
        _clearAccrualLinksOnTransactions(billId, accrualRecordId);
        _deleteRecord(accrualRecordId, "customrecord_rip_po_rip_code_accrual");
      });
    }

    if (vendorCreditId) {
      _deleteRecord(vendorCreditId, "vendorcredit");
    }

    if (onlyDelete) {
      return { billId: billId, relatedCreditDeleted: vendorCreditId };
    }

    //After deleting applicable records, load and save the bill to trigger the UE to re-run the bills
    record
      .load({
        type: record.Type.VENDOR_BILL,
        id: billId,
      })
      .save({
        ignoreMandatoryFields: true,
      });
    return { billId: billId, relatedCreditDeleted: vendorCreditId };
  }
  //#endregion

  return {
    createRipTierRecord,
    createVendorCredit,
    deleteRipRecords,
  };
});
