/**
 * @description Represents connection to partner MFT servers
 *
 * @NApiVersion 2.1
 * <AUTHOR> <<EMAIL>>
 */

define([
    "exports",
    "require",
    "N/log",
    "N/https",
    "N/encode",
    "./edi_server",
], (/** @type {any} */ exports, /** @type {any} */ require) => {
    const log = require("N/log");
    const https = require("N/https");
    const encode = require("N/encode");
    const { EDIServer } = require("./edi_server");

    /**
     * EDI MFT Server Class
     *
     * @class
     * @typedef {import("../../Interfaces/Models/Server/edi_server").EDIMFTServerInterface} EDIMFTServerInterface
     * @typedef {import("../../Interfaces/Models/Server/edi_server").EDIConnectionParameters} EDIConnectionParameters
     * @typedef {import("../../Interfaces/Models/Partner/edi_partner").EDIPartnerInterface} EDIPartnerInterface
     * @typedef {import("../../../../Classes/vlmd_custom_error_object").CustomErrorObject} CustomErrorObject
     * @implements {EDIMFTServerInterface}
     */
    class EDIMFTServer extends EDIServer{
        /** @param {{[key:string]: any}} props */
        constructor(props) {
            super(props);
            /** @type {string} */
            this.username = props.username || "<EMAIL>";
            /** @type {string} */
            this.password = props.password || "Drillick1!";
            /** @type {string} */
            this.authorizeUrl = "https://api.mftgateway.com/authorize";
            /** @type {string} */
            this.getMessagesUrl = "https://api.mftgateway.com/message/inbox?fetchAll=false";
            /** @type {string} */
            this.markAsRead = "https://api.mftgateway.com/message/inbox/<MESSAGE_ID>?markAsRead=true";
            /** @type {string} */
            this.markAsUnread = "https://api.mftgateway.com/message/inbox/<MESSAGE_ID>/markUnread";
            /** @type {string} */
            this.getAttachmentsUrl = "https://api.mftgateway.com/message/inbox/<MESSAGE_ID>/attachments";
            /** @type {string} */
            this.getStation = "https://api.mftgateway.com/station/<STATION_ID>";
            /** @type {string} */
            this.listPartners = "https://api.mftgateway.com/partner?service=as2";
            /** @type {string} */
            this.getPartner = "https://api.mftgateway.com/partner/<PARTNER_ID>?service=as2";
            /** @type {string} */
            this.sendUrl = "https://api.mftgateway.com/message/submit?service=as2";
            /** @type {string[]} */
            this.messageUrls = [];
            /** @type {string} */
            this.token = "";
        }

        /**
         * Authorize the user by granting a token
         *
         * @returns {EDIMFTServer} This instance
         */
        authorize() {
            try {
                const responseObj = https.post({
                    url: this.authorizeUrl,
                    headers: "Content-Type: application/json",
                    body: {
                        username: this.username,
                        password: this.password,
                    },
                });
              
                this.token = JSON.parse(responseObj.body).apiToken;
    
                return this;
            } catch(/** @type {any} */ err) {
                throw this.customError.updateError({
                    errorType: this.customError.ErrorTypes.MISSING_VALUE,
                    summary: "AUTHORIZATION_FAILED",
                    details: `Failed to authorize the user: ${err}`,
                });
            }
            
        }

        /**
         * List the files available from the inbox
         *
         * @returns {{url:string, name:string}[]} Objects containing file name and URL
         */
        list() {
            const { code, body } = https.get({
                url: this.getMessagesUrl,
                headers: {
                    "content-type": "application/json; charset=utf-8",
                    Authorization: this.token,
                },
            });
        
            if (code !== 200) {
                throw this.customError.updateError({
                    errorType: this.customError.ErrorTypes.MISSING_VALUE,
                    summary: "ERROR_CONNECTING_TO_MFT",
                    details: `Could not successfully connect to the MFT server: Error ${code}.`,
                });
            }

            try {
                this.attachmentUrls = JSON.parse(body).messages.map((/** @type {string} */ messageId) => {
                    const { body } = https.get({
                        url: this.getAttachmentsUrl.replace("<MESSAGE_ID>", messageId),
                        headers: {
                            "content-type": "application/json; charset=utf-8",
                            Authorization: this.token,
                        },
                    });

                    https.get({
                        url: this.markAsRead.replace("<MESSAGE_ID>", messageId),
                        headers: {
                            "content-type": "application/json; charset=utf-8",
                            Authorization: this.token,
                        },
                    });

                    return {
                        name: JSON.parse(body).attachments[0].name,
                        url: JSON.parse(body).attachments[0].url
                    }
                });
            } catch (/** @type {any} */ err) {
                throw this.customError.updateError({
                    errorType: this.customError.ErrorTypes.EVAL_ERROR,
                    summary: "ERROR_PARSING_RESPONSE",
                    details: `Cannot parse the body of the response: ${err}`,
                });
            }

            return this.attachmentUrls;
        }

        /**
         * Download the file from the SFTP server
         *
         * @param {string} url File URL in the server
         * @param {{[key:string]: boolean}} options Additional optional flags
         * @returns {string} File content
         */
        download(url, options) {
            try {
                const { code, body } = https.get({
                    url,
                });

                if (code === 200) {
                    try {
                        if (body && options?.decode) {
                            return encode.convert({
                                string: body,
                                inputEncoding: encode.Encoding.BASE_64,
                                outputEncoding: encode.Encoding.UTF_8,
                            });
                        }
                    } catch (/** @type {any} */ err) {
                        log.error({
                            title: "EDIMFTServer (download)",
                            details: "Failed to convert the response body. File is most likely already in UTF-8."
                        })
                    }

                    return body;
                }

                return "";
            } catch (/** @type {any} */ err) {
                log.error({
                    title: "EDIMFTServer (download)",
                    details: `Failed to download ${url}: ${err}`,
                });
                throw this.customError.updateError({
                    errorType: this.customError.ErrorTypes.FILE_NOT_LOADED,
                    summary: "MFT_SERVER_DOWNLOAD_FAILED",
                    details: err,
                });
            }
        }

        /**
         * Send the EDI File through an https POST request
         *
         * @param {object} params Parameters
         * @param {import("N/file").File} params.file NetSuite file
         * @param {string} params.targetStationId Target Station
         * @param {string} params.subject Message subject
         * @returns {https.ClientResponse} Response object
         */
        send(params) {
            try {
                log.audit({ title: 'MFT Server (send): params', details: JSON.stringify(params) });
                return https.post({
                    url: this.sendUrl,
                    headers: {
                        Authorization: this.token,
                        "content-type": "application/EDI",
                        "as2-from": "CREOH",
                        "as2-to": params.targetStationId,
                        "attachment-name": params.file.name,
                        "subject": params.subject,
                    },
                    body: params.file.getContents(),
                });
            } catch (/** @type {any} */ err) {
                log.error({
                    title: "EDIMFTServer (send)",
                    details: `Failed to send the file to the MFT server: ${err}`,
                });
                throw this.customError.updateError({
                    errorType: this.customError.ErrorTypes.FILE_NOT_SAVED,
                    summary: "MFT_SERVER_SEND_FAILED",
                    details: `Failed to send the file to the MFT server: ${err}`,
                });
            }
            
        }
    }

    exports.EDIMFTServer = EDIMFTServer;
});