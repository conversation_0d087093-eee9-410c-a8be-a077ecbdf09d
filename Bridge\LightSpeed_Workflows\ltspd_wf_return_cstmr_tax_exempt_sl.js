/**
 * @description SL to show pop-up form to confirm check/doordash payment
 *
 * @NApiVersion 2.1
 * @NScriptType Suitelet
 */

define(["N/log", "N/search"], function (log, search) {
	function onRequest(context) {
		if (context.request.method === "POST") {
			try {
				const data = context.request.body;
				const jsonObj = JSON.parse(data);
				// log.debug("jsonObj['retailer']", jsonObj["retailer"]); //Log this line to find the retail ID

				let taxAmountOnTransaction = jsonObj["sale"]["total_tax"];
				taxAmountOnTransaction = parseInt(taxAmountOnTransaction);

				if (!taxAmountOnTransaction || !jsonObj.hasOwnProperty("customer")) {
					return;
				}

				const vendCustomerId = jsonObj.customer.id;

				if (
					!vendCustomerId ||
					vendCustomerId == "0ac54c19-8c55-11ed-f913-3920e12ba852"
					//Walk-In Customer
				) {
					return;
				}

				var customerSearchObj = search.create({
					type: "customer",
					filters: [["custentity_in8_vend_id", "is", vendCustomerId]],
					columns: ["taxable"],
				});

				var searchResultObj = customerSearchObj.run().getRange({
					start: 0,
					end: 1,
				})[0];

				if (!searchResultObj) {
					return;
				}

				let custIsTaxable = searchResultObj.getValue(
					searchResultObj.columns[0]
				);

				if (custIsTaxable) {
					return;
				}

				if (taxAmountOnTransaction && !custIsTaxable) {
					var returnStatementObj = {
						actions: [
							{
								type: "stop",
								title: "Remove Taxes",
								message: "Please Remove Taxes - This Customer is Tax Exempt.",
								dismiss_label: "OK",
							},
						],
					};

					const returnStatementJsonObj = JSON.stringify(returnStatementObj);

					context.response.write(returnStatementJsonObj);
				}
			} catch (e) {
				log.error("Error Getting Tax Exempt Value", e);
			}
		}
	}

	return {
		onRequest: onRequest,
	};
});
