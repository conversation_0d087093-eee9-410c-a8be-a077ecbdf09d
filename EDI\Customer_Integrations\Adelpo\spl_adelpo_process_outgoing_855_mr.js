/**
 * @NApiVersion 2.1
 * @NScriptType MapReduceScript
 * @NAmdConfig /SuiteScripts/config.json
 * @param {Array} customerFolders Returns an array of objects - {customerId, accountNumber, name, savedSearchInternalId}
 * @param {Object} partnerValues - Adelpo partner values
 * @param {string} partnerValues.fieldDelimiter - Character separating the fields
 * @HBoxer
 */

//@ts-ignore
define([
  "require",
  "GetEdiIntegrationsLib",
  "Get855InternalIdsLib",
  "GetEdiPartnerValuesLib",
  "ProcessOutgoingEdiFileEmailLib",
  "PushEdiEmailInfoToDBLib",
  "./../../../EDI/Libraries/Process_EDI_File/spl_process_outgoing_855_lib",
  "../../../Classes/vlmd_custom_error_object",
  "../../../Classes/vlmd_mr_summary_handling",
  "N/log",
  "N/runtime",
  "N/query",
], function (
  require,
  getEdiIntegrationsLib,
  getPoAckInternalIdsLib,
  getEdiPartnerValuesLib,
  processOutgoingEdiFileEmailLib,
  pushEdiEmailInfoToDBLib
) {
  const runtime = require("N/runtime");
  const query = require("N/query");

  /**@type {import ('../../Libraries/Process_EDI_File/spl_process_outgoing_855_lib')} */
  const process855Lib = require("../../Libraries/Process_EDI_File/spl_process_outgoing_855_lib");

  /** @type {import("../../../Classes/vlmd_custom_error_object").CustomErrorObject} */
  const CustomErrorObject = require("../../../Classes/vlmd_custom_error_object");

  const dataObj = {
    prodGuidBool: true,
    prodDirectoryBool: true,
    decodeContent: true,
    prodGUID: "43c0ba8ea88a411291a637734cf210ab",
    sandboxGUID: "",
    prodDirectory: "/users/Adelpo/OUT/855",
    testDirectory: "",
    documentType: "Puchase Order Acknowledgement",
    documentTypeId: 7,
    purchasingSoftware: "Adelpo",
    purchasingSoftwareId: 5,
    pushEmailToDB: true,
  };

  function getInputData(context) {
    const customErrorObject = new CustomErrorObject();
    try {
      const getInputDataResultsArr = [];

      const currentScript = runtime.getCurrentScript();

      const queryString = currentScript.getParameter({
        name: "custscript_855_out_query_string",
      });

      if (queryString) {
        const results = query
          .runSuiteQL({
            query: queryString,
          })
          .asMappedResults();

        return results;
      }

      const customerFoldersArr =
        getEdiIntegrationsLib.getInfoForOutgoingDocument(
          dataObj.purchasingSoftwareId,
          dataObj.documentTypeId
        );

      customerFoldersArr.forEach((customer) => {
        dataObj.customerName = customer.customerName;

        const salesOrderArr = getPoAckInternalIdsLib.getInternalIds(
          customer.customerId,
          dataObj.purchasingSoftwareId
        );

        if (
          salesOrderArr.errorLog.length > 0 ||
          salesOrderArr.internalIds.length < 0
        ) {
          throw customErrorObject.updateError({
            errorType: customErrorObject.ErrorTypes.NO_VALUE_RETURNED,
            summary: "ERROR_GETTING_INTERNAL_IDS",
            details: `Error getting sales order internal ids for ${customer.customerName}: ${salesOrderArr.errorLog}`,
          });
        }

        salesOrderArr.internalIds.forEach((salesOrderInternalId) => {
          getInputDataResultsArr.push({
            customerName: customer.customerName,
            customerId: customer.customerId,
            accountNumber: customer.accountNumber,
            salesOrderInternalId: salesOrderInternalId,
            integrationFolder: customer.integrationFolder,
          });
        });
      });

      return getInputDataResultsArr;
    } catch (err) {
      customErrorObject.throwError({
        summaryText: "GET_INPUT_DATA_ERROR",
        error: err,
      });
    }
  }

  function map(context) {
    const customErrorObject = new CustomErrorObject();

    const currentScript = runtime.getCurrentScript();
    const queryString = currentScript.getParameter({
      name: "custscript_855_out_query_string",
    });

    const parsedResult = JSON.parse(context.value);

    let purchaseOrderJsonObj;

    if (queryString) {
      const jsonObjDictionaryParam = currentScript.getParameter({
        name: "custscript_json_object_for_sales_order",
      });

      if (jsonObjDictionaryParam) {
        let parsedJsonObjDictionary = JSON.parse(jsonObjDictionaryParam);
        let purchaseOrderNumber = parsedResult.ponumber;

        purchaseOrderJsonObj = parsedJsonObjDictionary[purchaseOrderNumber];
      }
    }

    const customerName = queryString
      ? parsedResult.customername
      : parsedResult.customerName;
    const customerId = queryString
      ? parsedResult.customerid
      : parsedResult.customerId;
    const accountNumber = queryString
      ? parsedResult.accountnumber
      : parsedResult.accountNumber;
    const salesOrderInternalId = queryString
      ? parsedResult.salesorderinternalid
      : parsedResult.salesOrderInternalId;
    const integrationFolder = queryString
      ? parsedResult.integrationfolder
      : parsedResult.integrationFolder;

    try {
      const partnerValues =
        getEdiPartnerValuesLib.getAdelpoValues(integrationFolder);

      if (!partnerValues) {
        throw customErrorObject.updateError({
          errorType: customErrorObject.ErrorTypes.NO_VALUE_RETURNED,
          summary: "ERROR_GETTING_PARTNER_VALUES",
          details: `EDI partner values not gotten for ${accountNumber} - ${customerName}`,
        });
      }

      const processResultsObj = process855Lib.process855(
        salesOrderInternalId,
        customerName,
        customerId,
        accountNumber,
        partnerValues,
        dataObj,
        integrationFolder,
        customErrorObject,
        purchaseOrderJsonObj
      );

      if (!processResultsObj.processedSuccessfully) {
        throw customErrorObject.updateError({
          errorType: customErrorObject.ErrorTypes.UNHANDLED_ERROR,
          summary: "ERROR_PROCESSING_855",
          details: `Sales Order: ${
            processResultsObj.salesOrderNumber
          }, Purchase Order: ${
            processResultsObj.purchaseOrderNumber ?? ""
          }, Error: ${processResultsObj.errorMessage}`,
        });
      }

      context.write(
        customerName,
        `${processResultsObj.salesOrderNumber} : ${processResultsObj.purchaseOrderNumber}`
      );
    } catch (err) {
      customErrorObject.throwError({
        summaryText: `MAP_ERROR`,
        error: err,
        recordId: salesOrderInternalId,
        recordType: "SALES_ORDER",
        errorWillBeGrouped: true,
      });
    }
  }

  function reduce(context) {
    context.write({
      key: context.key,
      value: context.values,
    });
  }

  function summarize(context) {
    const customErrorObject = new CustomErrorObject();
    try {
      const StageHandling = require("../../../Classes/vlmd_mr_summary_handling");
      const stageHandling = new StageHandling(context);

      stageHandling.printScriptProcessingSummary();
      stageHandling.printRecordsProcessed();
      stageHandling.printErrors({
        groupErrors: true,
      });

      try {
        var sentEmailObj = processOutgoingEdiFileEmailLib.processEmail(
          context,
          [],
          dataObj.purchasingSoftware,
          dataObj.documentType
        );
      } catch (err) {
        throw customErrorObject.updateError({
          errorType: customErrorObject.ErrorTypes.UNHANDLED_ERROR,
          summary: "ERROR_PROCESSING_EMAIL",
          details: `Error: ${err}`,
        });
      }

      if (dataObj.pushEmailToDB) {
        try {
          pushEdiEmailInfoToDBLib.pushEdiEmailInfoToDB(dataObj, sentEmailObj);
        } catch (e) {
          throw customErrorObject.updateError({
            errorType: customErrorObject.ErrorTypes.UNHANDLED_ERROR,
            summary: "ERROR_PUSHING_TO_EDI_DB",
            details: `Error pushing EDI information to database: ${err.errorMessage}`,
          });
        }
      }
    } catch (err) {
      customErrorObject.throwError({
        summaryText: `SUMMARIZE_ERROR`,
        error: err,
      });
    }
  }

  return {
    getInputData,
    map,
    reduce,
    summarize,
  };
});
