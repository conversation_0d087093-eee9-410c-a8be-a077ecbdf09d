/**
 *
 * @description Runs when a cash sale/sales order is deleted with a RIP applied to it, to update the bill credit that more RIPS are now available
 *
 * @NApiVersion 2.1
 * @NScriptType UserEventScript
 *
 * <AUTHOR>
 * @module brdg_rip_deleted_sale_ue
 */
define([
  "require",
  "N/log",
  "N/task",
  "N/email",
  "../../Libraries/brdg_helper_functions_lib",
], (/** @type {any} */ require) => {
  const log = require("N/log");
  const task = require("N/task");
  const email = require("N/email");

  /**@type {import("../../Libraries/brdg_helper_functions_lib")} */
  const bridgeHelperFunctionsLib = require("../../Libraries/brdg_helper_functions_lib");

  function afterSubmit(context) {
    if (context.type === context.UserEventType.DELETE) {
      try {
        const salesRec = context.newRecord;
        const subsidiariesArr = salesRec.getValue({
          fieldId: "subsidiary",
        });
        const bridgeSubsidiaries =
          bridgeHelperFunctionsLib.getBridgeSubsidiaries();
        const isBridgeSubsidiary = bridgeHelperFunctionsLib.isBridgeSubsidiary(
          subsidiariesArr,
          bridgeSubsidiaries
        );

        if (!isBridgeSubsidiary) {
          return;
        }

        var linesWithBillCreditsArr = [];
        const lineCount = salesRec.getLineCount({ sublistId: "item" });

        for (let i = 0; i < lineCount; i++) {
          const appliedBillCredit = salesRec.getSublistValue({
            sublistId: "item",
            fieldId: "custcol_item_rip_bill_credit_applied",
            line: i,
          });

          if (appliedBillCredit) {
            const quantityUsed = salesRec.getSublistValue({
              sublistId: "item",
              fieldId: "custcol_item_rip_quantity_used",
              line: i,
            });

            const item = salesRec.getSublistValue({
              sublistId: "item",
              fieldId: "item",
              line: i,
            });

            linesWithBillCreditsArr.push({
              billCreditId: appliedBillCredit,
              quantityUsed: quantityUsed,
              itemId: item,
            });
          }
        }

        if (linesWithBillCreditsArr.length > 0) {
          const mrTask = task.create({
            taskType: task.TaskType.MAP_REDUCE,
            scriptId: "customscript_brdg_update_bc_mr",
            deploymentId: "customdeploy_brdg_update_bc_mr",
            params: {
              custscript_bc_transactions_to_update: JSON.stringify(
                linesWithBillCreditsArr
              ),
            },
          });

          mrTask.submit();
        }
      } catch (e) {
        log.error("Error in updating bill credits off deleted sale!", e);
        email.send({
          author: 15131,
          recipients: "<EMAIL>",
          subject: "Error Updating Bill on Deleted Sale!",
          body: `Error: ${
            e.message
          } <br/> Affected Items/Bill Credits: ${JSON.stringify(
            linesWithBillCreditsArr
          )}`,
        });
      }
    }
  }

  return {
    afterSubmit,
  };
});
