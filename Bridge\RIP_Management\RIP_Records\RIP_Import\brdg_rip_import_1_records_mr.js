/**
 * @description RIP Import 1:
 * This MR iterates through the import file and stores each row
 *   to NS as a custom record called RIP Import record
 * Submit the import tiers MR once the records have been created
 * Errors will be collated in the summarize stage
 *
 * </br><b>Schedule:</b> On-demand, called by SL
 *
 * @NApiVersion 2.1
 * @NScriptType MapReduceScript
 * @NAmdConfig /SuiteScripts/config.json
 *
 * <AUTHOR>
 * @module brdg_rip_import_1_records_mr
 */

define([
  "require",
  "N/log",
  "N/record",
  "N/query",
  "N/email",
  "N/file",
  "N/format",
  "N/runtime",
  "N/task",
  "./Classes/Factories/brdg_rip_vendor_factory",
  "../../../../Helper_Libraries/vlmd_record_module_helper_lib",
  "../../../../Classes/vlmd_custom_error_object",
  "../../../../Classes/vlmd_mr_summary_handling",
], (/** @type {any} */ require) => {
  const log = require("N/log");
  const record = require("N/record");
  const query = require("N/query");
  const email = require("N/email");
  const file = require("N/file");
  const format = require("N/format");
  const runtime = require("N/runtime");
  const task = require("N/task");

  const { BridgeRIPVendorFactory } = require("./Classes/Factories/brdg_rip_vendor_factory");

  /** @type {import("../../../../Helper_Libraries/vlmd_record_module_helper_lib")} */
  const recordHelperLib = require("../../../../Helper_Libraries/vlmd_record_module_helper_lib");

  /** @type {import("../../../../Classes/vlmd_custom_error_object").CustomErrorObject} */
  const CustomErrorObject = require("../../../../Classes/vlmd_custom_error_object");

  const notificationAuthor = 223244; //<EMAIL>
  const maxErrorThreshold = 10;

  /**
   * Load the CSV, iterate over all lines and add a comma delimited JSON string to the dateArr
   *
   * @param {import("N/types").EntryPoints.MapReduce.getInputDataContext} context Get input data context
   * @returns {string[]|undefined}
   */
  function getInputData(context) {
    const customErrorObject = new CustomErrorObject();

    try {
      const currentScript = runtime.getCurrentScript();

      const fileId = currentScript.getParameter({
        name: "custscript_brdg_rip_import_file",
      });

      if (!fileId) {
        throw customErrorObject.updateError({
          errorType: customErrorObject.ErrorTypes.MISSING_PARAMETER,
          summary: "MISSING_FILE_ID",
          details: `No file id passed in from Suitelet`,
        });
      }

      const vendorId = currentScript.getParameter({
        name: "custscript_brdg_rip_vendor",
      });

      if (!vendorId) {
        throw customErrorObject.updateError({
          errorType: customErrorObject.ErrorTypes.MISSING_PARAMETER,
          summary: "MISSING_VENDOR_ID",
          details: `No vendor id passed in from Suitelet`,
        });
      }

      const ripFile = file.load({
        id: fileId.toString(),
      });

      if (!ripFile) {
        throw customErrorObject.updateError({
          errorType: customErrorObject.ErrorTypes.RECORD_NOT_LOADED,
          summary: "FILE_NOT_LOADED",
          details: `File ${fileId} wasn't loaded`,
        });
      }

      if (ripFile.fileType?.toString() !== "CSV") {
        throw customErrorObject.updateError({
          errorType: customErrorObject.ErrorTypes.RECORD_NOT_LOADED,
          summary: "INVALID_FILE_TYPE",
          details: `File must be a CSV, not ${ripFile.fileType}`,
        });
      }

      return BridgeRIPVendorFactory.createVendor(vendorId.toString(), {
        customErrorObject,
      }).splitLines(ripFile);
    } catch (err) {
      customErrorObject.throwError({
        summaryText: "GET_INPUT_DATA_ERROR",
        error: err,
        recordId: null,
        recordName: null,
        recordType: null,
        errorWillBeGrouped: false,
      });
    }
  }

  /**
   * Parse the rows from the CSV
   *
   * @param {import("N/types").EntryPoints.MapReduce.mapContext} context Map context
   * @returns {void}
   */
  function map(context) {
    const mapErrorObject = new CustomErrorObject();
    try {
      const currentScript = runtime.getCurrentScript();
      const vendorId = currentScript.getParameter({
        name: "custscript_brdg_rip_vendor",
      });
      const /** @type {any[]} */ parsedObj = JSON.parse(context.value);

      const rowObj = BridgeRIPVendorFactory.createVendor(vendorId.toString(), {
        customErrorObject: mapErrorObject,
      }).parseLine(parsedObj);

      if (rowObj.sku) {
        context.write(rowObj.sku, rowObj);
      }
    } catch (/** @type {any} */ err) {
      log.error({ title: err.name, details: err.message })
      mapErrorObject.throwError({
        summaryText: "MAP_ERROR",
        error: err,
        recordId: null,
        recordName: null,
        recordType: null,
        errorWillBeGrouped: true,
      });
    }    
  }

  /**
   * Create Bridge RIP Import records
   * On error, throw the error including the context.value
   *
   * @param {import("N/types").EntryPoints.MapReduce.reduceContext} context Reduce context
   * @returns {void}
   */
  function reduce(context) {
    const reduceErrorObject = new CustomErrorObject();
    try {
      const currentScript = runtime.getCurrentScript();
      const vendorId = currentScript.getParameter({
        name: "custscript_brdg_rip_vendor",
      });
      
      const [
        sku,
        ripCode,
        brandRegistration,
        fromDate,
        toDate,
        description,
        uom1,
        qty1,
        amt1,
        uom2,
        qty2,
        amt2,
        uom3,
        qty3,
        amt3,
        uom4,
        qty4,
        amt4,
        uom5,
        qty5,
        amt5,
        countAs,
        comments,
        lineCount,
      ] = BridgeRIPVendorFactory.createVendor(vendorId.toString(), {
        customErrorObject: reduceErrorObject,
      }).mergeLevels(context.values);

      try {
        if (!sku) {
          throw reduceErrorObject.updateError({
            errorType: reduceErrorObject.ErrorTypes.MISSING_VALUE,
            summary: "MISSING_SKU",
            details: `No sku was passed in for line ${lineCount}. Please confirm that the file is formatted correctly and that there are no forward slashes in the headers`,
          });
        }
        const currentScript = runtime.getCurrentScript();
        const vendorId = currentScript.getParameter({
          name: "custscript_brdg_rip_vendor",
        });

        let queryResults;

        try {
          //Check if item group for vendor code
          queryResults = query.runSuiteQL({
            query: /*sql */ `
              SELECT
                item.id,
              FROM
                item
              JOIN
                itemmember
                ON itemmember.parentitem = item.id
              JOIN
                item component
                ON itemmember.item = component.id
              JOIN
                itemvendor
                ON component.id = itemvendor.item
              WHERE
                item.isinactive = 'F'
                AND item.itemtype = 'Group'
                AND (item.vendorname = '${sku}'
                  OR item.vendorname = '0${sku}'
                  OR item.vendorname = '00${sku}')
                AND itemvendor.vendor = '${vendorId}'`,
          });
        } catch (/** @type {any} */ err) {
          throw reduceErrorObject.updateError({
            errorType: reduceErrorObject.ErrorTypes.INVALID_SEARCH,
            summary: "ERROR_RUNNING_QUERY",
            details: `Error checking if item group for vendor code ${err.name} - ${err.message}`,
          });
        }

        try {
          //Check if inventory item for vendor code. Checking for perfect match and for match with 1 or 2 leading zeros.
          if (queryResults.results.length <= 0) {
            queryResults = query.runSuiteQL({
              query: /*sql */ `
              SELECT item.id
              FROM item
              JOIN itemVendor ON item.id = itemVendor.item
              WHERE 
                (itemvendor.vendorCode = '${sku}' 
                  OR
                  itemvendor.vendorCode = '0${sku}'
                  OR
                  itemvendor.vendorCode = '00${sku}'
                )
                AND item.isinactive = 'F'
                AND item.itemtype  = 'InvtPart'
                AND itemVendor.vendor = '${vendorId}'`,
            });
          }
        } catch (/** @type {any} */ err) {
          throw reduceErrorObject.updateError({
            errorType: reduceErrorObject.ErrorTypes.INVALID_SEARCH,
            summary: "ERROR_RUNNING_QUERY",
            details: `Error checking if inventory item for vendor code ${err.name} - ${err.message}`,
          });
        }

        //Item exists in NS -> check off 'exists in ns' and set the itemId to be the found item from NS
        const itemId =
          (queryResults.results[0] && queryResults.results[0].values[0]) || null;
        const itemExistsInNs = itemId ? true : false;

        try {
          //Check if an import record for these details already exists in NS
          const duplicateValidationQuery =
            /*sql */ `
          SELECT
                    id
                  FROM
                    customrecord_brdg_rip_import
                  WHERE
            custrecord_brdg_rip_import_item ${itemId ? "= '" + itemId + "'" : "IS NULL"} 
            AND custrecord_brdg_rip_import_vendor = '${vendorId}' 
            AND custrecord_brdg_rip_import_sku = '${sku}' 
            AND custrecord_brdg_rip_import_code = '${ripCode}' 
            AND custrecord_brdg_rip_import_brand ${
              brandRegistration ? `= '${brandRegistration}'` : "IS NULL"
            }
            AND custrecord_brdg_rip_import_from = '${fromDate}'
            AND custrecord_brdg_rip_import_to = '${toDate}' 
            AND custrecord_brdg_rip_import_desc =` +
            "'" +
            description.split("'").join("''") +
            "'" +
            /*sql */
            ` AND custrecord_brdg_rip_import_uom_1 = '${uom1}' 
            AND custrecord_brdg_rip_import_qty_1 = '${qty1}' 
            AND custrecord_brdg_rip_import_amt_1 = '${amt1}' 
            AND custrecord_brdg_rip_import_uom_2 ${uom2 ? `= '${uom2}'` : "IS NULL"}  
            AND custrecord_brdg_rip_import_qty_2 ${qty2 ? `= '${qty2}'` : "IS NULL"}  
            AND custrecord_brdg_rip_import_amt_2 ${amt2 ? `= '${amt2}'` : "IS NULL"}  
            AND custrecord_brdg_rip_import_uom_3 ${uom3 ? `= '${uom3}'` : "IS NULL"}  
            AND custrecord_brdg_rip_import_qty_3 ${qty3 ? `= '${qty3}'` : "IS NULL"}  
            AND custrecord_brdg_rip_import_amt_3 ${amt3 ? `= '${amt3}'` : "IS NULL"}  
            AND custrecord_brdg_rip_import_uom_4  ${uom4 ? `= '${uom4}'` : "IS NULL"}  
            AND custrecord_brdg_rip_import_qty_4 ${qty4 ? `= '${qty4}'` : "IS NULL"}  
            AND custrecord_brdg_rip_import_amt_4 ${amt4 ? `= '${amt4}'` : "IS NULL"}  
            AND custrecord_brdg_rip_import_uom_5 ${uom5 ? `= '${uom5}'` : "IS NULL"}  
            AND custrecord_brdg_rip_import_qty_5 ${qty5 ? `= '${qty5}'` : "IS NULL"}  
            AND custrecord_brdg_rip_import_amt_5 ${amt5 ? `= '${amt5}'` : "IS NULL"}
            AND custrecord_brdg_rip_import_count_as ${countAs ? `= '${countAs}'` : "IS NULL"}  
            AND custrecord_brdg_rip_import_comments ${
              comments ? `= '${comments.split("'").join("''")}'` : "IS NULL"
            }`;

          const validateNotDuplicateQueryResults = query.runSuiteQL({
            query: duplicateValidationQuery,
          });

          //An import record with these details already exists -> return
          if (validateNotDuplicateQueryResults.results.length > 0) {
            return;
          }
        } catch ( /** @type {any} */ err) {
          throw reduceErrorObject.updateError({
            errorType: reduceErrorObject.ErrorTypes.INVALID_SEARCH,
            summary: "ERROR_RUNNING_QUERY",
            details: `Error checking if an import record for these details already exists in NS ${err.name} - ${err.message}`,
          });
        }

        //No record in NS -> Continue on with creating an import record for this line.
        const fromDateFormatted = format.parse({
          value: fromDate,
          type: format.Type.DATE,
        });

        const toDateFormatted = format.parse({
          value: toDate,
          type: format.Type.DATE,
        });

        const mappedValuesObj = {
          custrecord_brdg_rip_import_item: itemId,
          custrecord_item_exists_in_ns: itemExistsInNs,
          custrecord_brdg_rip_import_vendor: vendorId,
          custrecord_brdg_rip_import_sku: sku,
          custrecord_brdg_rip_import_code: ripCode,
          custrecord_brdg_rip_import_brand: brandRegistration || "",
          custrecord_brdg_rip_import_from: fromDateFormatted,
          custrecord_brdg_rip_import_to: toDateFormatted,
          custrecord_brdg_rip_import_desc: description || "",
          custrecord_brdg_rip_import_uom_1: uom1,
          custrecord_brdg_rip_import_qty_1: parseInt(qty1, 10),
          custrecord_brdg_rip_import_amt_1: parseInt(amt1, 10),
          custrecord_brdg_rip_import_uom_2: uom2,
          ...(qty2 && { custrecord_brdg_rip_import_qty_2: parseInt(qty2, 10) }),
          ...(amt2 && { custrecord_brdg_rip_import_amt_2: parseInt(amt2, 10) }),
          custrecord_brdg_rip_import_uom_3: uom3,
          ...(qty3 && { custrecord_brdg_rip_import_qty_3: parseInt(qty3, 10) }),
          ...(amt3 && { custrecord_brdg_rip_import_amt_3: parseInt(amt3, 10) }),
          custrecord_brdg_rip_import_uom_4: uom4,
          ...(qty4 && { custrecord_brdg_rip_import_qty_4: parseInt(qty4, 10) }),
          ...(amt4 && { custrecord_brdg_rip_import_amt_4: parseInt(amt4, 10) }),
          custrecord_brdg_rip_import_uom_5: uom5,
          ...(qty5 && { custrecord_brdg_rip_import_qty_5: parseInt(qty5, 10) }),
          ...(amt5 && { custrecord_brdg_rip_import_amt_5: parseInt(amt5, 10) }),
          custrecord_brdg_rip_import_comments: comments || "",
          custrecord_brdg_rip_import_count_as: countAs,
        };

        const importRecord = record.create({
          type: "customrecord_brdg_rip_import",
          isDynamic: true,
        });

        try {
          // @ts-ignore Property 'setBodyValues' does not exist on type 'typeof import("vlmd_record_module_helper_lib")'.ts(2339)
          recordHelperLib.setBodyValues(mappedValuesObj, importRecord);
        } catch (/** @type {any} */ err) {
          throw reduceErrorObject.updateError({
            errorType: reduceErrorObject.ErrorTypes.VALUE_NOT_SET,
            summary: "ERROR_SETTING_IMPORT_RECORD_VALUES",
            details: `Please check that all columns are included in the import file and are in the correct order. ${err.name} - ${err.message}`,
          });
        }

        try {
          let importRecordId = importRecord.save();

          if (importRecordId) {
            context.write("Import Record Id", importRecordId.toString());
          }
        } catch (/** @type {any} */ err) {
          throw reduceErrorObject.updateError({
            errorType: reduceErrorObject.ErrorTypes.RECORD_NOT_SAVED,
            summary: "ERROR_SAVING_IMPORT_RECORD",
            details: err.message,
          });
        }
      } catch (err) {
        reduceErrorObject.throwError({
          summaryText: "REDUCE_ERROR",
          error: err,
          recordId: lineCount + 2, //Add one to account for header row on import file, and one to account for zero based.
          recordName: sku ?? "",
          recordType: "customrecord_brdg_rip_import",
          errorWillBeGrouped: true,
        });
      }
    } catch (/** @type {any} */ err) {
      log.error({ title: err.name, details: err.message })
      reduceErrorObject.throwError({
        summaryText: "REDUCE_ERROR",
        error: err,
        recordId: null,
        recordName: null,
        recordType: null,
        errorWillBeGrouped: true,
      });
    }
  }

  /**
   * The summarize stage of the Map/Reduce script.
   * Submits the Create Tier Levels and Groups MR
   * On Get Input Data Error,
   *   throw the error
   *   do not trigger the Tiers MR
   *   send an email notification to the process owner
   * On Map Error,
   *   collate the affected rows from the file into an HTML table
   *   error count <= 10 - send an email containing the table
   *   error count > 10 - send an email saying there's too many map stage errors
   *   trigger Tiers MR if error count <= 10
   *
   * @param {import("N/types").EntryPoints.MapReduce.summarizeContext} context Summarize context
   * @returns {void}
   */
  function summarize(context) {
    const customErrorObject = new CustomErrorObject();

    const currentScript = runtime.getCurrentScript();
    const ownerId =
      Number(currentScript.getParameter({
        name: "custscript_brdg_rip_import_owner",
      }) || 0) ?? 3288;

    try {
      // Error in getInputData stage -> no records were processed -> stop execution. Generic error email will be sent to user.
      if (context.inputSummary.error) {
        throw customErrorObject.updateError({
          errorType: customErrorObject.ErrorTypes.NO_VALUE_RETURNED,
          summary: "ERROR_IN_GET_INPUT_DATA",
          details: `Import process stopped because data to process wasn't loaded successfully.
${JSON.parse(context.inputSummary.error).message}`,
        });
      }

      /** @type {import("../../../../Classes/vlmd_mr_summary_handling").StageHandling} */
      const StageHandling = require("../../../../Classes/vlmd_mr_summary_handling");
      // @ts-ignore Type 'typeof import("vlmd_mr_summary_handling")' has no construct signatures.ts(2351)
      const stageHandling = new StageHandling(context);

      stageHandling.printScriptProcessingSummary();
      let { resultsLog, recordsProcessedMessage } =
        stageHandling.printRecordsProcessed();
      let { errorArr, errorsMessage } = stageHandling.printErrors({
        groupErrors: true,
      });

      // Too many errors, likely something wrong with the entire file -> stop execution. Generic error email will be sent to user.
      if (errorArr.length > maxErrorThreshold) {
        throw customErrorObject.updateError({
          errorType: customErrorObject.ErrorTypes.UNHANDLED_ERROR,
          summary: "TOO_MANY_MAP_ERRORS",
          details: `<b>Sample Errors:</b><br/>${errorsMessage} `,
        });
      }

      if (errorArr.length > 0) {
        // Send a table containing rows that caused errors
        email.send({
          author: notificationAuthor,
          recipients: [ownerId],
          subject: "Bridge RIP Import Records Error",
          body: `An error occurred while importing these rows from the RIP file.
    Please address errors below and create tier levels, tier groups, agreement records, agreement details manually\n\n
    ${errorsMessage}`,
        });
      }

      let processingMessage = `<b>Script #1: Create RIP Import Records</b><br/>
    ${resultsLog.length} RIP Import Record${
        resultsLog.length == 1 ? "" : "s"
      } Created${resultsLog.length > 0 ? " (internal ids): " : "."}<br/>
    ${recordsProcessedMessage ? recordsProcessedMessage + "<br/><br/>" : ""}
    ${errorArr.length} Error${errorArr.length == 1 ? "" : "s"}${
        errorsMessage ? ": " + errorsMessage : "."
      }<br/><br/>`;

      const createTiersMr = task.create({
        taskType: task.TaskType.MAP_REDUCE,
        scriptId: "customscript_brdg_rip_import_tiers_mr",
        deploymentId: "customdeploy_brdg_rip_import_tiers_mr",
        params: {
          custscript_brdg_rip_import_tier_owner: ownerId || "",
          custscript_brdg_rip_import_rcrds_message: processingMessage,
        },
      });

      createTiersMr.submit();
    } catch (/** @type {any} */ err) {
      email.send({
        author: notificationAuthor,
        recipients: [ownerId],
        subject: "Bridge RIP Import Records Error",
        body: `Import process stopped because there was an error while processing.<br><br>Please reach <NAME_EMAIL> for assistance if needed.<br/>
                ${err?.message ?? customErrorObject.details ?? ""}`,
      });

      customErrorObject.throwError({
        summaryText: "SUMMARIZE_ERROR",
        error: err,
        recordId: null,
        recordName: null,
        recordType: null,
        errorWillBeGrouped: false,
      });
    }
  }

  return {
    getInputData,
    map,
    reduce,
    summarize,
  };
});
