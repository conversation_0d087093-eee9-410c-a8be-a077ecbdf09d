SELECT
   po.trandate po_date,
   BUILTIN.DF(potl.subsidiary) subsidiary,
   BUILTIN.DF (potl.transaction) purchase_order,
   BUILTIN.DF (btl.transaction) bill,
   BUILTIN.DF (potl.item) item,
   SUM(potl.quantity) po_quantity,
   SUM(btl.quantity) bill_quantity,
   CASE
      WHEN SUM(potl.quantity) != SUM(btl.quantity) THEN 'X'
      ELSE ''
   END quantity_discrepancy,
   ROUND(SUM(potl.rate), 2) po_rate,
   ROUND (SUM(btl.rate), 2) bill_rate,
   CASE
      WHEN SUM (potl.rate) != SUM(btl.rate) THEN 'X'
      ELSE ''
   END rate_discrepancy,
FROM
   transactionline potl
   JOIN transaction po ON po.id = potl.transaction
   AND po.type = 'PurchOrd'
   JOIN nexttransactionlink ntl ON po.id = ntl.previousdoc
   AND ntl.linktype = 'OrdBill'
   JOIN transactionline btl ON potl.item = btl.item
   AND btl.transaction = ntl.nextdoc
 WHERE
   potl.subsidiary IN (1, 2)
HAVING
   SUM (potl.quantity) != SUM (btl.quantity)
   OR ROUND (SUM(potl.rate), 2) != ROUND (SUM(btl.rate), 2)
GROUP BY
   po.trandate,
   BUILTIN.DF(potl.subsidiary),
   BUILTIN.DF (potl.transaction),
   BUILTIN.DF (btl.transaction),
   BUILTIN.DF (potl.item)
 
ORDER BY
   po.trandate DESC