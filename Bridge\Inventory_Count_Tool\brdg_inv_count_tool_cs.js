/**
 * @description Client Script for the Inventory Count Tool Suitelet
 *
 * @NApiVersion 2.1
 * @NScriptType ClientScript
 * @NAmdConfig /SuiteScripts/config.json
 * 
 * <AUTHOR>
 * @module brdg_inv_count_tool_cs
 */

define([
	'require',
	'N/query',
	'N/url',
	'N/runtime',
	'N/record',
	'N/format'
	
], function(require) {
	const query = require("N/query");
	const url = require("N/url");
	const runtime = require("N/runtime");
	const record = require("N/record");
	const format = require("N/format");
	let arrItemScanned = [];
	function pageInit(context){
	}

	jQuery(document).ready(function(){		
		let itemRowCounter = 0;
		let subsidiaries = getSubsidiaryLists(); 
		let sessionRecords = getSessionsLists();
		subsidiaries.forEach(function(subsidiary) {
			let option = "";
      
			if(subsidiary.id == 16){
				option = jQuery('<option selected></option>').attr('value', subsidiary.id).text(subsidiary.name);
			} else{
				option = jQuery('<option></option>').attr('value', subsidiary.id).text(subsidiary.name);
			}			 
            jQuery("#fldSubsidiary").append(option);
		});
		sessionRecords.forEach(function(sessionRec) {
			let option = "";
			option = jQuery('<option selected></option>').attr('value', sessionRec.id).text(sessionRec.name);
					 
            jQuery("#fldLoadData").append(option);
		});
		jQuery('#openingScreenModal').modal({
			backdrop: 'static',
			keyboard: false
		});
		jQuery('#btnOpen').click(function(e){
			e.preventDefault();
			jQuery('#openingScreenModal').modal('show');
		});
		
		let today = new Date().toISOString().split('T')[0];
		jQuery('#datepicker').val(today);
		jQuery('#itemFormDiv').append(`<br/><br/><button class="btn btn-primary" id='btnSubmit'>Submit</button>`);
		jQuery('#itemFormDiv').append(`<button class="btn btn-success" id='btnLoad' style="float: right;">Load Data</button><br/><br/>`);
		jQuery('#loadDataModal').modal('hide');
		jQuery('#openingScreenModal').modal('show');
		let htmlItemTable = createItemTable();
		let cssItemTable = addItemTableCss();
		jQuery('#itemFormDiv').append(cssItemTable);
		jQuery('#itemFormDiv').append(htmlItemTable);
		let rowCount = 1;
		addTableDataRow(rowCount);
		jQuery('#itemFormDiv').hide();
		jQuery('#itemFormDiv > #itemTableSublist').addClass('customTable');

		jQuery('#btnStart').click(function(e){
			e.preventDefault();
			jQuery('#itemFormDiv').show();
			jQuery('#btnOpen').hide();
			jQuery('#openingScreenModal').modal('hide');
			jQuery('#itemFormDiv > #itemTableSublist').closest('tr').find('.upcInput').focus(); 
		});
		//Move the mouse focus on the UPC Input field
		if (!jQuery('#itemFormDiv > #itemTableSublist').closest('tr').find('.upcInput').is(':focus')) {
			jQuery('#itemFormDiv > #itemTableSublist').closest('tr').find('.upcInput').focus(); 
		}

		jQuery('#btnLoad').click(function(e){
			e.preventDefault();
			
			jQuery('#loadDataModal').modal('show');
		});
		jQuery('#btnLoadData').click(function(e){
			e.preventDefault();
			let objRecSession = record.load({
				type:'customrecord_brdg_inv_count_session',
				id:jQuery('#fldLoadData').val(),
				isDynamic:false
			});
			loadResults(objRecSession.getValue({fieldId:'custrecord_brdg_table_data'}));
			jQuery('#loadDataModal').modal('hide');
		});
		
		let isUnique;
		//Functionality on the UPC Code Text Field

		//Setting the maximum character length accepted by the UPC Input field 
		jQuery('#itemFormDiv > #itemTableSublist').on('input', '.upcInput', function() {

			let maxLength = 13;

			if (jQuery(this).val().length > maxLength) {
				jQuery(this).val(jQuery(this).val().slice(0, maxLength));
			}
		});


		jQuery('#itemFormDiv > #itemTableSublist').on('change', '.upcInput', function() {

			let rowId = jQuery(this).data('row');
			let upcCode = jQuery(this).val();
			let subsidiaryId = jQuery("#fldSubsidiary").val();
			let itemResults = getItemByItemId(upcCode,subsidiaryId);
			isUnique = isInputValueUnique(upcCode, this, rowId);
			
			if (jQuery(this).closest('tr').is(':last-child')) {
				
					if(itemResults.length > 0){
						
						let isFirstFieldPopulated = jQuery(this).val() !== '';

						jQuery(this).prop('disabled', true);
						jQuery('#fldSubsidiary').prop('disabled', true);
						jQuery(this).closest('tr').find('td[data-row="' + rowId + '"]:eq(0)').text(itemResults[0]["itemid"]?.toString());
						jQuery(this).closest('tr').find('td[data-row="' + rowId + '"]:eq(1)').text(itemResults[0]["itemdescription"]?.toString());
						jQuery(this).closest('tr').find('td[data-row="' + rowId + '"]:eq(2)').text(itemResults[0]["stockunitid"]?.toString());
						jQuery(this).closest('tr').find('td[data-row="' + rowId + '"]:eq(3)').text(itemResults[0]["stockunit"]?.toString()); 
						if(!isUnique){
							
							let prevRow = jQuery(this).closest('tr').prev();
							while (prevRow.length > 0) {
								let prevUpcValue = prevRow.find('.upcInput').val();
								
								if (prevUpcValue === upcCode) {
									let prevInputValue = prevRow.find('td:nth-child(10)').text();
									jQuery(this).closest('tr').find('td[data-row="' + rowId + '"]:eq(4)').text(itemResults[0]["stock_unit_available"]?.toString());
									jQuery(this).closest('tr').find('td:nth-child(10)').text(prevInputValue);
									break;
								}
								prevRow = prevRow.prev();
							}
						}
						else{
							jQuery(this).closest('tr').find('td[data-row="' + rowId + '"]:eq(4)').text(itemResults[0]["stock_unit_available"]?.toString());
						}

						jQuery(this).closest('tr').find('.qtyInput[data-row="' + rowId + '"]:last').prop('disabled', !isFirstFieldPopulated);
						
						if(itemResults[0]["stock_price_per_unit"]){
							jQuery(this).closest('tr').find('td[data-row="' + rowId + '"]:eq(5)').text(itemResults[0]["stock_price_per_unit"].toFixed(2)?.toString() || 0.00);
						}	
						else{
							jQuery(this).closest('tr').find('td[data-row="' + rowId + '"]:eq(5)').text(0.00);
						}
						jQuery(this).closest('tr').find('td[data-row="' + rowId + '"]:eq(6)').text(itemResults[0]["itemnumber"]?.toString());
					}
					else{
						alert('No Item Found!');
						jQuery(this).val("");
					}	
					jQuery('#itemFormDiv > #itemTableSublist').closest('tr').find('.qtyInput').focus(); 			
			} 
			
		});
		
		//Functionality on the Quantity Text Field
		jQuery('#itemFormDiv > #itemTableSublist').on('change', '.qtyInput', function() {
			
			let rowId = jQuery(this).data('row'); 
			let upcCode =jQuery(this).closest('tr').find('.upcInput').val();
			let quantityEntered = Number(jQuery(this).val());
			

			if(isUnique){
				let quantityAvailable = 0;
				
				if(jQuery(this).closest('tr').find('td[data-row="' + rowId + '"]:eq(4)').text().trim() !== ""){
					quantityAvailable = Number(jQuery(this).closest('tr').find('td[data-row="' + rowId + '"]:eq(4)').text());
				}
				
				jQuery(this).closest('tr').find('td:nth-child(10)').text(Number(quantityAvailable - quantityEntered));
				jQuery(this).closest('tr').find('td:nth-child(11)').text(Number(quantityEntered - quantityAvailable));
			}
			else{
				let currentRowArr = [];
				jQuery('.upcInput').each(function() {
					let otherRowId = jQuery(this).data('row');
					if(jQuery(this).closest('tr').find('.upcInput').val() === upcCode){
						let currentRowObj = {};
							currentRowObj.rowId = otherRowId;
							currentRowObj.quantityAvailable = Number(jQuery(this).closest('tr').find('td[data-row="' + otherRowId + '"]:eq(4)').text());
							currentRowObj.quantityEntered = Number(jQuery(this).closest('tr').find('.qtyInput').val());
							currentRowObj.discrepancy = 	Number(jQuery(this).closest('tr').find('td:nth-child(10)').text());
							currentRowObj.reverseDiscrepancy = Number(jQuery(this).closest('tr').find('td:nth-child(11)').text());
							currentRowArr.push(currentRowObj);
					}
					
				});
				
				for(let i = 0; i < currentRowArr.length; i++){
					if(currentRowArr.length >= 1){
						if(i == 0){
							jQuery('#itemFormDiv > #itemTableSublist').closest('tr').find('td[data-row="' + currentRowArr[i].rowId + '"]:eq(7)').text(Number(currentRowArr[i].quantityAvailable) - Number(currentRowArr[i].quantityEntered));
							jQuery('#itemFormDiv > #itemTableSublist').closest('tr').find('td[data-row="' + currentRowArr[i].rowId + '"]:eq(8)').text(Number(currentRowArr[i].quantityEntered) - Number(currentRowArr[i].quantityAvailable));
						}
						else{
							let previousRowDiscrepancy = jQuery('#itemFormDiv > #itemTableSublist').closest('tr').find('td[data-row="' + currentRowArr[i-1].rowId + '"]:eq(7)').text();
							let previousRowReverseDiscrepancy = jQuery('#itemFormDiv > #itemTableSublist').closest('tr').find('td[data-row="' + currentRowArr[i-1].rowId + '"]:eq(8)').text();
							jQuery('#itemFormDiv > #itemTableSublist').closest('tr').find('td[data-row="' + currentRowArr[i].rowId + '"]:eq(7)').text(Number(previousRowDiscrepancy) - Number(currentRowArr[i].quantityEntered));
							jQuery('#itemFormDiv > #itemTableSublist').closest('tr').find('td[data-row="' + currentRowArr[i].rowId + '"]:eq(8)').text(Number(currentRowArr[i].quantityEntered) - Number(previousRowReverseDiscrepancy));
						}
					}
				}
				
			}

			if (jQuery(this).closest('tr').is(':last-child') && Number(jQuery(this).val()) >= 0) {
				let newRowId = rowId + 1;
				itemRowCounter++;
				if(itemRowCounter % 10 === 0){
					updateResults();
					createRecordEntrySession();

				}
				else{
					updateResults();
				}
				 
       			 addTableDataRow(newRowId);
			}
			
		});

		//Functionality on the Delete Row Button
		jQuery('#itemFormDiv > #itemTableSublist').on('click', '.btnDeleteRow', function(e) {
			e.preventDefault();
			let getUpcValue =  jQuery(this).closest('tr').find('.upcInput').val();
			let getQtyValue =  jQuery(this).closest('tr').find('.qtyInput').val();
			let rowId = jQuery(this).data('row');

				if(getUpcValue && getQtyValue){
					let currentRowArr = [];
					jQuery('.upcInput').not(this).each(function() {
						let otherRowId = jQuery(this).data('row');
						if(jQuery(this).closest('tr').find('.upcInput').val() === getUpcValue && rowId != otherRowId){
							let currentRowObj = {};
								currentRowObj.rowId = otherRowId;
								currentRowObj.quantityAvailable = Number(jQuery(this).closest('tr').find('td[data-row="' + otherRowId + '"]:eq(4)').text());
								currentRowObj.quantityEntered = Number(jQuery(this).closest('tr').find('.qtyInput').val());
								currentRowObj.discrepancy = 	Number(jQuery(this).closest('tr').find('td:nth-child(10)').text());
								currentRowArr.push(currentRowObj);
						}
						
					});
					jQuery(this).closest('tr').remove();
					
					for(let i = 0; i < currentRowArr.length; i++){
						if(currentRowArr.length >= 1){
							if(i == 0){
								jQuery('#itemFormDiv > #itemTableSublist').closest('tr').find('td[data-row="' + currentRowArr[i].rowId + '"]:eq(7)').text(Number(currentRowArr[i].quantityAvailable) - Number(currentRowArr[i].quantityEntered));
								jQuery('#itemFormDiv > #itemTableSublist').closest('tr').find('td[data-row="' + currentRowArr[i].rowId + '"]:eq(8)').text(Number(currentRowArr[i].quantityEntered) - Number(currentRowArr[i].quantityAvailable));
							}
							else{
								let previousRowDiscrepancy = jQuery('#itemFormDiv > #itemTableSublist').closest('tr').find('td[data-row="' + currentRowArr[i-1].rowId + '"]:eq(7)').text();
								let previousRowReverseDiscrepancy = jQuery('#itemFormDiv > #itemTableSublist').closest('tr').find('td[data-row="' + currentRowArr[i-1].rowId + '"]:eq(8)').text();
								jQuery('#itemFormDiv > #itemTableSublist').closest('tr').find('td[data-row="' + currentRowArr[i].rowId + '"]:eq(7)').text(Number(previousRowDiscrepancy) - Number(currentRowArr[i].quantityEntered));
								jQuery('#itemFormDiv > #itemTableSublist').closest('tr').find('td[data-row="' + currentRowArr[i].rowId + '"]:eq(8)').text(Number(currentRowArr[i].quantityEntered) - Number(previousRowReverseDiscrepancy));
							}
						}
					}
					updateResults();
					
				}
            
        });
	   
		jQuery('#itemFormDiv > #btnSubmit').on('click', function(e){
			e.preventDefault();
			generateCsvFile();
		});

		
	});
	function createRecordEntrySession(){
		let custRecSession = record.create({
			type: 'customrecord_brdg_inv_count_session',
			isDynamic: true
		});
		let dateToday = new Date();
		let formattedDate = formatDate(dateToday);
		custRecSession.setValue({fieldId:'name',value:runtime.getCurrentUser().name + ": " + formattedDate});
		custRecSession.setValue({fieldId:'custrecord_brdg_employee',value:runtime.getCurrentUser().id});
		custRecSession.setValue({fieldId:'custrecord_brdg_timestamp',value:dateToday});
		custRecSession.setValue({fieldId:'custrecord_brdg_table_data',value:JSON.stringify(arrItemScanned)});

		custRecSession.save();
		
	}
	function loadResults(tableData){
		let arrItemLoadResults = JSON.parse(tableData);
		let newRowId = 1;
		let tbody = jQuery('#itemFormDiv > #itemTableSublist tbody');
		tbody.empty();
		arrItemLoadResults.forEach(function(rowData,index) {
			
				let row = jQuery('<tr>')
					.append(jQuery('<td data-row="' + newRowId + '" hidden>').text(rowData[3]))
					.append('<td colspan="9"><input class="upcInput" data-row="' + newRowId + '" type="number" disabled/></td>')
					.append(jQuery('<td data-row="' + newRowId + '" colspan="9">').text(rowData[5]))
					.append('<td colspan="9"><input class="qtyInput" data-row="' + newRowId + '" type="number" value='+rowData[6]+' disabled/></td>')
					.append(jQuery('<td data-row="' + newRowId + '" hidden>').text(rowData[7]))
					.append(jQuery('<td data-row="' + newRowId + '" colspan="9">').text(rowData[8]))
					.append(jQuery('<td data-row="' + newRowId + '">').text(rowData[9]))
					.append(jQuery('<td data-row="' + newRowId + '" hidden>').text(rowData[10]))
					.append(jQuery('<td data-row="' + newRowId + '" hidden>').text(rowData[11]))
					.append(jQuery('<td data-row="' + newRowId + '">').text(rowData[12]))
					.append(jQuery('<td data-row="' + newRowId + '" hidden>').text(rowData[13]))
					.append(`<td align="center"><button type="button" data-row="${newRowId}" class='btnDeleteRow'> Delete </button></td>`);
				tbody.append(row);
				jQuery('#itemFormDiv > #itemTableSublist').closest('tr').find('input.upcInput[data-row="'+newRowId+'"]').val(rowData[4]);
				newRowId++;
						
				
		});
		addTableDataRow(newRowId);
		jQuery('#itemFormDiv > #itemTableSublist').closest('tr').find('.upcInput').focus(); 
	}
	function updateResults(){
		arrItemScanned = [];
		jQuery('#itemFormDiv > #itemTableSublist tbody tr').each(function() {
			
			let rowContents = [];
			let isEmptyRow = true;
      
			if(jQuery(this).find('.upcInput').val()){
				
				rowContents.push(jQuery("#datepicker").val());
				rowContents.push(jQuery("#fldSubsidiary").find(":selected").val());
				rowContents.push(jQuery("#fldSubsidiary").find(":selected").text());
				jQuery(this).find('td').each(function() {
					let cellData = "";
					
					if (jQuery(this).find('input').length > 0) {
						if(jQuery(this).find('input').attr('type') === 'number'){
							cellData = jQuery(this).find('input').val();
							rowContents.push(cellData);
						}	
					} 
					else {
						if(jQuery(this).find('button').attr('type') !== 'button'){
							if(jQuery(this).text() == jQuery(this).closest('tr').find('td:nth-child(2)').text()){
								cellData = jQuery(this).text();
								rowContents.push(cellData);
								
							}
							else{
								
								cellData = jQuery(this).text();
								rowContents.push(cellData);
							}
						}				
					}
	
					
					if (jQuery(this).text().trim() !== '') {
						isEmptyRow = false;
					}
				});
			}
						
			if (!isEmptyRow) {
                arrItemScanned.push(rowContents);
            }
		});
	}
	/**
	 *
	 * @description This function executes the backend Suitelet (File:brdg_send_csv_file_sl.js) that generates the CSV file and email notification.
	 */

	function generateCsvFile(){

		let tableContents = [];
		
		jQuery('#itemFormDiv > #itemTableSublist tbody tr').each(function() {

			let rowContents = [];
			let isEmptyRow = true;
      
			if(jQuery(this).find('.upcInput').val()){
				rowContents.push(jQuery("#datepicker").val());
				rowContents.push(jQuery("#fldSubsidiary").find(":selected").val());
				rowContents.push(jQuery("#fldSubsidiary").find(":selected").text());
				jQuery(this).find('td').each(function() {
					let cellData = "";
					
					if (jQuery(this).find('input').length > 0) {
						if(jQuery(this).find('input').attr('type') === 'number'){
							cellData = jQuery(this).find('input').val();
							rowContents.push(cellData);
						}	
					} 
					else {
						if(jQuery(this).find('button').attr('type') !== 'button'){
							if(jQuery(this).text() == jQuery(this).closest('tr').find('td:nth-child(2)').text()){
								cellData = jQuery(this).text();
								rowContents.push(cellData);
								
							}
							else{
								
								cellData = jQuery(this).text();
								rowContents.push(cellData);
							}
						}				
					}
	
					
					if (jQuery(this).text().trim() !== '') {
						isEmptyRow = false;
					}
				});
			}
						
			if (!isEmptyRow) {
                tableContents.push(rowContents);
            }
		});
		
		let getCurrentUserId = runtime.getCurrentUser().id;

		let subsidiaryId = jQuery("#fldSubsidiary").find(":selected").text();
		
		let suiteletURL = url.resolveScript({
			scriptId:'customscript_brdg_send_csv_inv_count_sl',
			deploymentId: 'customdeploy_brdg_send_csv_inv_count_sl',
			params: {
			 'senderId':getCurrentUserId,
			 'rowDataArr': JSON.stringify(tableContents),
			 'subsidiaryId': subsidiaryId
			}
		   });
		   jQuery.ajax({
			type: "POST",
			contentType: "application/json; charset=utf-8",
			url: suiteletURL,
			success: function(response){
			  alert('Your count has been received and will be emailed to the back office.');
			},
			error: function(response){
			  console.log('error: ' + JSON.stringify(response));
			}
		});
		 
	}
	/**

	 *
	 * @description This function checks if the UPC code is already existing in the Item table.
	 * @returns {String}
	 */

	function isInputValueUnique(value, currentInput, rowId) {
		let isUnique = true;
		jQuery('.upcInput').not(currentInput).each(function() {
			if (jQuery(this).val() === value && rowId > 1) {
				isUnique = false;
				return false;
			}
		});
		return isUnique;
	}

	


	/**  
	 *	
	 * @description This function adds the CSS on the HTML Item Table.
	 * @returns {String}
	 */
	function addItemTableCss(){
		let cssValue = "";

		cssValue +=`
		<style>
		/* Define styles in a separate style block */
		.customTable {
			border-collapse: collapse;
			width: 100%;
		}

		.customTable th, .customTable td {
			border: 1px solid #dddddd;
			text-align: center;
			font-size:20pt;
		}

		.customTable th {
			background-color: #f2f2f2;
		}
		
		</style>`

		return cssValue;
	}

	/**

	 *
	 * @description This function adds the HTML Item Table on the <div> inside the Inline HTML field.
	 * @returns {String}
	 */

	function createItemTable(){
		let htmlTable = "";

		
		htmlTable += `<table id='itemTableSublist'>
		<thead>
		<tr>
		<th colspan='9'>UPC Code</th>
		<th colspan='9'>Description</th>
		<th colspan='9'>Quantity in Store</th>
		<th colspan='9'>Item UOM</th>
		<th>Quantity in NS</th>
		<th hidden>Last Purchase Price</th>
		<th hidden>Item ID</th>
		<th>Discrepancy</th>
		<th>Action</th>
		</tr>
		</thead>
		<tbody>
		</tbody>
		</table>
		`;

		
		return htmlTable;
	}
	/**
	 *
	 * @description This function pulls the list of Subsidiaries/stores that is eligible for Inventory Count.
	 * @returns {Object} SQL mapped results pulled from the Subsidiary list.
	 */
	function getSubsidiaryLists(){
		const sqlQuery = `SELECT
		subsidiary.id AS id,
		subsidiary.name AS name FROM
		subsidiary JOIN
		subsidiary parent ON subsidiary.parent = parent.id where
		(
		subsidiary.parent = 16 or parent.parent = 16
		)
		and subsidiary.id not in (
		26,
		27,
		22
		)`;

			let results = query
					.runSuiteQL({
						query: sqlQuery
					}).asMappedResults();
		return results;

	}

	function getSessionsLists(){
		const sqlQuery = `SELECT 
		customrecord_brdg_inv_count_session.id as id,
		customrecord_brdg_inv_count_session.name as name,
		customrecord_brdg_inv_count_session.custrecord_brdg_employee as empId, 
		BUILTIN.DF(customrecord_brdg_inv_count_session.custrecord_brdg_employee) as empName,
		customrecord_brdg_inv_count_session.custrecord_brdg_timestamp as recTimestamp, 
		customrecord_brdg_inv_count_session.custrecord_brdg_table_data as sessionData
	FROM 
		customrecord_brdg_inv_count_session 
	WHERE 
		custrecord_brdg_timestamp >= (CURRENT_DATE - 7)
				 AND
		custrecord_brdg_employee = '${runtime.getCurrentUser().id}'`;

			let results = query
					.runSuiteQL({
						query: sqlQuery
					}).asMappedResults();
		return results;
	}
   
	/**
	 *
	 * @description This function runs a query to pull an Item by UPC code.
	 * @param {Number}
	 * @returns {Object} SQL mapped results of the pulled Item
	 */

	function getItemByItemId(upcCode,subsidiaryId){
		
		const lppFieldId = `
		SELECT
		custrecord_last_purchase_price_by_store 
		FROM
		customrecord_bridge_store 
		WHERE
		custrecord_brdg_store_subsidiary = ${subsidiaryId}`;
		let resultsLppFieldId = query
					.runSuiteQL({
						query: lppFieldId
					}).asMappedResults();

		const sqlQuery = `
		SELECT
		item.itemid AS itemnumber,
		item.displayname AS itemname,
		item.description AS itemdescription,
		item.id AS itemid,
		item.stockunit AS stockunitid,
		BUILTIN.DF(item.stockunit) AS stockunit,
		BUILTIN.DF(inventoryItemLocations.location) inventory_location,
		inventoryItemLocations.quantityavailable available,
		inventoryItemLocations.quantityavailable / stockunit.conversionrate stock_unit_available,
		(
			item.${resultsLppFieldId[0]["custrecord_last_purchase_price_by_store"]} / saleunit.conversionrate 
		)
		* stockunit.conversionrate stock_price_per_unit,
		inventoryItemLocations.lastpurchasepricemli,
		BUILTIN.DF(Location.subsidiary) subsidiary, --Field not available in criteria, only in results
        item.itemtype AS itemtype
		FROM
		item 
		LEFT OUTER JOIN
		   inventoryItemLocations 
		   ON inventoryItemLocations.item = item.id 
		LEFT OUTER JOIN
		   Location 
		   ON inventoryItemLocations.location = Location.id 
		LEFT OUTER JOIN
		   LocationSubsidiaryMap 
		   ON Location.id = LocationSubsidiaryMap.location 
		INNER JOIN
		   unitstypeuom purchaseunit 
		   ON purchaseunit.internalid = item.purchaseunit 
		INNER JOIN
		   unitstypeuom stockunit 
		   ON stockunit.internalid = item.stockunit 
		INNER JOIN
		   unitstypeuom saleUnit 
		   ON saleunit.internalid = item.saleunit
WHERE
LocationSubsidiaryMap.subsidiary = ${subsidiaryId}   
AND item.upccode = '${upcCode}'
AND item.isinactive = 'F' `;

				let results = query
					.runSuiteQL({
						query: sqlQuery

					})
					.asMappedResults();
					console.log(`results: ${JSON.stringify(results)}`)
	// if(results[0].itemtype == 'Kit'){
    //     console.log(`Item ${results[0].itemname} is a Kit`);
    // }	
		return results;
					
					
	}

	/**
	 *
	 * @description This function adds the data row on the table.
	 *
	 */
	function addTableDataRow(newRowId){
		
		let tableBody = jQuery('#itemFormDiv > #itemTableSublist tbody');
		let newRow = jQuery('<tr>')

					.append(jQuery('<td data-row="' + newRowId + '" hidden>').text(""))
					.append('<td colspan="9"><input class="upcInput" data-row="' + newRowId + '" type="number" /></td>')
					.append(jQuery('<td data-row="' + newRowId + '" colspan="9">').text(""))
					.append('<td colspan="9"><input class="qtyInput" data-row="' + newRowId + '" type="number" disabled/></td>')
					.append(jQuery('<td data-row="' + newRowId + '" hidden>').text(""))
					.append(jQuery('<td data-row="' + newRowId + '" colspan="9">').text(""))
					.append(jQuery('<td data-row="' + newRowId + '">').text(""))
					.append(jQuery('<td data-row="' + newRowId + '" hidden>').text(""))
					.append(jQuery('<td data-row="' + newRowId + '" hidden>').text(""))
					.append(jQuery('<td data-row="' + newRowId + '">').text(""))
					.append(jQuery('<td data-row="' + newRowId + '" hidden>').text(""))
					.append(`<td align="center"><button type="button" data-row="${newRowId}" class='btnDeleteRow'> Delete </button></td>`);


		tableBody.append(newRow);
					
	}
	function formatDate(date) {
        var month = date.getMonth() + 1;
        var day = date.getDate();
        var year = date.getFullYear();
        var hours = date.getHours();
        var minutes = date.getMinutes();
    
        // Add leading zeros if necessary
        month = (month < 10 ? "0" : "") + month;
        day = (day < 10 ? "0" : "") + day;
        hours = (hours < 10 ? "0" : "") + hours;
        minutes = (minutes < 10 ? "0" : "") + minutes;
    
        // Return the formatted date string
        return month + "/" + day + "/" + year + " " + hours + ":" + minutes;
    }

	return {pageInit:pageInit}
});