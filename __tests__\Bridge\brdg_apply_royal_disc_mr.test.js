// @ts-nocheck
import log from "N/log";
import record from "N/record";
import format from "N/format";
import runtime from "N/runtime";

beforeEach(() =>  {
    jest.resetModules();
    jest.resetAllMocks();
    jest.clearAllMocks();
});

import bridgeHelperFunctionsLib from "../../Bridge/Libraries/brdg_helper_functions_lib";
import brdg_apply_royal_disc_mr from "../../Bridge/brdg_apply_royal_disc_mr";

describe("brdg_apply_royal_disc_mr", () => {

    beforeEach(() => {
        jest.spyOn(log, "error");
    });

    describe("getInputData", () => {

        const transactionDates = [
            "someDate", "anotherDate"
        ];

        beforeEach(() => {
            jest.spyOn(runtime, "getCurrentScript").mockReturnValue({
                getParameter: (param) => {
                    if (param.name === "custscript_run_specific_transaction_date") {
                        return false;
                    } else {
                        return param;
                    }
                }
            });
            jest.spyOn(format, "format").mockReturnValue(new Date("1999-12-31T13:00:00.000Z"));
            jest.spyOn(bridgeHelperFunctionsLib,"getTransactionDatesFromDateCreated").mockReturnValue(transactionDates);
        });
        it("returns the dates returned by getTransactionDatesFromDateCreated", () => {
            const getInputDataReturnValue = brdg_apply_royal_disc_mr.getInputData();
            expect(getInputDataReturnValue).toEqual(transactionDates);
        });
        it("returns a single date when custscript_run_specific_transaction_date is true", () => {
            jest.spyOn(runtime, "getCurrentScript").mockReturnValue({
                getParameter: (param) => {
                    if (param.name === "custscript_run_specific_transaction_date") {
                        return true;
                    } else {
                        return param;
                    }
                }
            });
            const getInputDataReturnValue = brdg_apply_royal_disc_mr.getInputData();
            expect(getInputDataReturnValue).toEqual([new Date("1999-12-31T13:00:00.000Z")]);
        });
    });

    describe("map", () => {

        let wineVendorBillsObj1, wineVendorBillsObj2, spiritVendorBillsObj1, spiritVendorBillsObj3, winePurchaseOrdersObj, spiritPurchaseOrdersObj;
        const mapContext = {
            value: "someDate",
            write: jest.fn()
        };

        beforeEach(() => {
            jest.spyOn(bridgeHelperFunctionsLib, "getWineAndSpiritCategories").mockReturnValue({
                wineCategories: "wine",
                spiritCategories: "spirit"
            });
            wineVendorBillsObj1 = {
                10001 : {
                    recordType: "VendBill",
                    indices: [0, 1, 2, 3, 4],
                    linkedTransaction: 20001
                }
            };
            wineVendorBillsObj2 = {
                10002 : {
                    recordType: "VendBill",
                    indices: [0, 1, 2, 3, 4],
                    linkedTransaction: 20002
                }
            };
            spiritVendorBillsObj1 = {
                10001 : {
                    recordType: "VendBill",
                    indices: [5, 6, 7, 8, 9],
                    linkedTransaction: 20001
                }
            };
            spiritVendorBillsObj3 = {
                10003 : {
                    recordType: "VendBill",
                    indices: [5, 6, 7, 8, 9],
                    linkedTransaction: 20003
                }
            };
            winePurchaseOrdersObj = {
                20001 : {
                    recordType: "PurchOrd",
                    indices: [0, 1, 2, 3, 4],
                    linkedTransaction: 10001
                }
            };
            spiritPurchaseOrdersObj = {
                20001 : {
                    recordType: "PurchOrd",
                    indices: [5, 6, 7, 8, 9],
                    linkedTransaction: 10001
                }
            };
        });
        it("writes indices from Vendor Bills only when all subsidiaries meet the minimum threshold to context", () => {
            jest.spyOn(bridgeHelperFunctionsLib, "getTransactionsObj").mockImplementation((param) => {
                if (param.transactionType === "VendBill") {
                    if (param.categoryIds === "wine") {
                        return {
                            transactionsObj : wineVendorBillsObj1,
                            casesCount : 100
                        }
                    } else if (param.categoryIds === "spirit") {
                        return {
                            transactionsObj : spiritVendorBillsObj1,
                            casesCount : 50
                        }
                    }
                } else {
                    return {};
                }
            });
            brdg_apply_royal_disc_mr.map(mapContext);
            expect(mapContext.write).toHaveBeenCalledWith({
                key: "10001",
                value: {
                    indices: [0, 1, 2, 3, 4, 5, 6, 7, 8 ,9],
                    recordType: "VendBill",
                    linkedTransaction: 20001
                }
            });
        });
        it("does not write to context when a subsidiary does not meet the minimum case threshold even if the total cases count from Purchase Orders does", () => {
            jest.spyOn(bridgeHelperFunctionsLib, "getTransactionsObj").mockImplementation((param) => {
                if (param.transactionType === "VendBill") {
                    if (param.categoryIds === "wine") {
                        return {
                            1 : {
                                transactionsObj : wineVendorBillsObj1,
                                casesCount : 49,
                                linkedTransaction: 20001
                            }
                        }
                    } else if (param.categoryIds === "spirit") {
                        return {
                            1 : {
                                transactionsObj : spiritVendorBillsObj1,
                                casesCount : 14,
                                linkedTransaction: 20001
                            }
                        }
                    }
                } else {
                    if (param.categoryIds === "wine") {
                        return {
                            1 : {
                                transactionsObj : winePurchaseOrdersObj,
                                casesCount : 100
                            }
                        }
                    } else if (param.categoryIds === "spirit") {
                        return {
                            1 : {
                                transactionsObj : spiritPurchaseOrdersObj,
                                casesCount : 50
                            }
                        }
                    }
                }
            });
            brdg_apply_royal_disc_mr.map(mapContext);
            expect(mapContext.write).not.toHaveBeenCalled();
            
        });
        it("does not include lines from Vendor Bills of a subsidiary that do not meet the minimum vase threshold", () => {
            jest.spyOn(bridgeHelperFunctionsLib, "getTransactionsObj").mockImplementation((param) => {
                if (param.transactionType === "VendBill") {
                    if (param.categoryIds === "wine") {
                        return {
                            transactionsObj : wineVendorBillsObj2,
                            casesCount : 100
                        }
                    } else if (param.categoryIds === "spirit") {
                        return {
                            transactionsObj : spiritVendorBillsObj1,
                            casesCount : 14
                        }
                    }
                } else {
                    return {};
                }
            });
            brdg_apply_royal_disc_mr.map(mapContext);
            expect(mapContext.write).toHaveBeenCalledWith({
                key: "10002",
                value: {
                    indices: [0, 1, 2, 3, 4],
                    recordType: "VendBill",
                    linkedTransaction: 20002
                }
            });
        });
        it("does not write to context when no subsidiary meets the minimum threshold", () => {
            jest.spyOn(bridgeHelperFunctionsLib, "getTransactionsObj").mockImplementation((param) => {
                if (param.transactionType === "VendBill") {
                    if (param.categoryIds === "wine") {
                        return {
                            1 : {
                                transactionsObj : wineVendorBillsObj1,
                                casesCount : 49
                            }
                        }
                    } else if (param.categoryIds === "spirit") {
                        return {
                            1 : {
                                transactionsObj : spiritVendorBillsObj1,
                                casesCount : 14
                            }
                        }
                    }
                } else {
                    if (param.categoryIds === "wine") {
                        return {
                            1 : {
                                transactionsObj : winePurchaseOrdersObj,
                                casesCount : 49
                            }
                        }
                    } else if (param.categoryIds === "spirit") {
                        return {
                            1 : {
                                transactionsObj : spiritPurchaseOrdersObj,
                                casesCount : 14
                            }
                        }
                    }
                }
            });
            brdg_apply_royal_disc_mr.map(mapContext);
            expect(mapContext.write).not.toHaveBeenCalled();
        });
        it("skips the subsidiary that does not meet the minimum threshold", () => {
            jest.spyOn(bridgeHelperFunctionsLib, "getTransactionsObj").mockImplementation((param) => {
                if (param.transactionType === "VendBill") {
                    if (param.categoryIds === "wine") {
                        return {
                            transactionsObj : wineVendorBillsObj2,
                            casesCount : 50
                        }
                    } else if (param.categoryIds === "spirit") {
                        return {
                            transactionsObj : spiritVendorBillsObj3,
                            casesCount : 35
                        }
                    }
                } else {
                    return {};
                }
            });
            brdg_apply_royal_disc_mr.map(mapContext);
            expect(mapContext.write).toHaveBeenCalledWith({
                key: "10002",
                value : {
                    indices: [0, 1, 2, 3, 4],
                    recordType: "VendBill",
                    linkedTransaction: 20002
                }
            });
            expect(mapContext.write).toHaveBeenCalledWith({
                key: "10003",
                value :  {
                    indices: [5, 6, 7, 8, 9],
                    recordType: "VendBill",
                    linkedTransaction: 20003
                }
            });
        });
        it("logs the caught error", () => {
            jest.spyOn(bridgeHelperFunctionsLib, "getTransactionsObj").mockReturnValue();
            brdg_apply_royal_disc_mr.map(mapContext);
            expect(bridgeHelperFunctionsLib.getTransactionsObj).toHaveBeenCalledTimes(2);
            expect(log.error).toHaveBeenCalledWith(undefined, "Cannot read properties of undefined (reading 'casesCount')");
        });
    });
    describe("reduce", () => {
        const reduceContext = {
            key: 1,
            values: ['{"recordType": "VendBill", "indices": [0, 1, 2]}']
        };
        let getSublistValue, setSublistValue, save, setValue;
        beforeEach(() => {
            getSublistValue = jest.fn().mockImplementation(param => 1);
            setSublistValue = jest.fn();
            save = jest.fn();
            setValue = jest.fn();
            jest.spyOn(record, "load").mockReturnValue({
                getLineCount: () => 1,
                setValue,
                getSublistValue,
                setSublistValue,
                save
            });
            brdg_apply_royal_disc_mr.reduce(reduceContext);
        });
        it("retrieves the quantity of the item sublist row", () => {
            expect(getSublistValue).toHaveBeenCalledWith({
                fieldId: "quantity",
                line: 0,
                sublistId: "item"
            });
        });
        it("retrieves the rate of the item sublist row", () => {
            expect(getSublistValue).toHaveBeenCalledWith({
                fieldId: "rate",
                line: 0,
                sublistId: "item"
            });
        });
        it("retrieves the rate before discount of the item sublist row", () => {
            expect(getSublistValue).toHaveBeenCalledWith({
                fieldId: "custcol_brdg_rate_before_discount",
                line: 0,
                sublistId: "item"
            });
        });
        it("sets the rate before discount of the item sublist row", () => {
            expect(setSublistValue).toHaveBeenCalledWith({
                fieldId: "custcol_brdg_rate_before_discount",
                line: 0,
                sublistId: "item",
                value: 1
            });
        });
        it("sets the rate of item sublist row", () => {
            expect(setSublistValue).toHaveBeenCalledWith({
                fieldId: "rate",
                line: 0,
                sublistId: "item",
                value: 0.95
            });
        });
        it("saves the total discount applied to all item sublist rows", () => {
            expect(setValue).toHaveBeenCalledWith({
                fieldId: "custbody_brdg_total_discount",
                value: 0.05
            });
        });
        it("saves the original rate and updates the original rate", () => {
            expect(save).toHaveBeenCalled();
        });
        it("logs error when context value cannot be parsed", () => {
            const reduceContext = {
                key: 1,
                values: ['foo']
            };
            brdg_apply_royal_disc_mr.reduce(reduceContext);
            expect(log.error).toHaveBeenCalledWith(undefined, "Unexpected token 'o', \"foo\" is not valid JSON");
        });
    });
});