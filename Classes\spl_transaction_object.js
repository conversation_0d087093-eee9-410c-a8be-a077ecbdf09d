/**
 * Transaction Object class
 * that represents the data from the document
 * that we receive and send to customers and vendors
 *
 * @NApiVersion 2.1
 * <AUTHOR>
 */

define([], () => {

    /**
     * Transaction Object class
     *
     * @class
     * @type {import("./spl_transaction_object").TransactionObject}
     */
    class TransactionObject {
        constructor(json) {
            this.acknowledgementCode = json["acknowledgementCode"];
            this.billingAddress = json["billingAddress"] || json["address"];
            this.communicationObj = json["communicationObj"];
            this.customerAccountNumber = json["customerAccountNumber"] || json["customerId"];
            this.customerInternalId = json["customerInternalId"];
            this.customerName = json["customerName"];
            this.customerNameInTheirSystem = json["customerNameInTheirSystem"];
            this.gln = json["gln"];
            this.items = json["items"];
            this.memo = json["memo"];
            this.mustArriveByDate = json["mustArriveByDate"];
            this.orderDate = json["orderDate"];
            this.parentObj = json["parentObj"];
            this.poDate = json["poDate"];
            this.poNumber = json["poNumber"];
            this.shipOnDate = json["shipOnDate"];
            this.shippingAddress = json["shippingAddress"];
            this.shippingAddressObj = json["shippingAddressObj"];
            this.streetAddress = json["streetAddress"];
            this.totalAmount = json["totalAmount"];
            this.transactionControlNumber = json["transactionControlNumber"];
        }

        /**
         * Get street, city, state and zip
         *
         * @returns {{
         *  streetAddress: string|null|undefined,
         *  city: string|null|undefined,
         *  state: string|null|undefined,
         *  zip: string|null|undefined
         * }} Basic address components
        */
        getBasicAddress() {
            return {
                streetAddress: this.billingAddress?.streetAddress,
                city: this.billingAddress?.city,
                state: this.billingAddress?.state,
                zip: this.billingAddress?.zip
            }
        }

        /**
         * Trims the string properties of the transaction object's items' elements
         *
         * @returns {void}
         */
        trimItemsStringProperties() {
            this.items && Array.isArray(this.items) && this.items.forEach((item) => {
                try {
                    Object.keys(item).forEach((property) => {
                        item[property] = typeof item[property] === "string"
                            ? item[property].trim()
                            : item[property];
                    });
                } catch(error) {
                    throw `Error trimming string values of ${item}: ${error}`;
                }
            });
        }

        /**
         * Trims the string properties of the transaction object
         *
         * @returns {void}
         */
        trimStringProperties() {
            Object.keys(this).forEach((property) => {
                try {
                    this[property] = typeof this[property] === "string"
                        ? this[property].trim()
                        : this[property];
                } catch(error) {
                    throw `Error trimming string value of ${property}: ${error}`;
                }
            });
        }

    }

    return TransactionObject;
});