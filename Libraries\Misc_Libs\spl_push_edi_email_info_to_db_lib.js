//@ts-ignore
define(["N/log", "N/record"], function (log, record) {
	function pushEdiEmailInfoToDB(
		dataObj,
		sentEmailObj,
		transactionControlNumber,
		poRefNumber
	) {
		//Check if missing values needed
		//Moving 5 - Outgoing 850s to here until control number subrecord functionality is added
		if (
			(dataObj.documentTypeId == 1 ||
				dataObj.documentTypeId == 6 ||
				dataObj.documentTypeId == 7 ||
				dataObj.documentTypeId == 10 ||
				dataObj.documentTypeId == 5) &&
			(!dataObj || !sentEmailObj)
		) {
			log.error(
				"Missing Parameter",
				`dataObj: ${dataObj}, sentEmailObj: ${sentEmailObj}`
			);
		} else if (
			(dataObj.documentTypeId == 3 ||
				dataObj.documentTypeId == 2 ||
				dataObj.documentTypeId == 4) &&
			(!dataObj || !sentEmailObj || !transactionControlNumber || !poRefNumber)
		) {
			log.error(
				"Missing Parameter",
				`dataObj: ${dataObj}, sentEmailObj: ${sentEmailObj}, transactionControlNumber: ${transactionControlNumber} `
			);

			return;
		}

		try {
			var ediTransactionRecord = record.create({
				type: "customrecord_spl_edi_transaction",
			});

			//Set body fields
			ediTransactionRecord.setValue(
				"custrecord_spl_edi_document_type",
				dataObj.documentTypeId
			);

			ediTransactionRecord.setValue(
				"custrecord_spl_processing_status",
				sentEmailObj.processingStatus
			);

			ediTransactionRecord.setValue(
				"custrecord_spl_edi_email_subject",
				sentEmailObj.subject
			);

			ediTransactionRecord.setValue(
				"custrecord_spl_edi_email_message",
				sentEmailObj.body
			);

			ediTransactionRecord.setValue(
				"custrecord_spl_edi_email_recipient",
				sentEmailObj.recipients[0]
			);

			if (sentEmailObj.cc) {
				ediTransactionRecord.setValue(
					"custrecord_spl_edi_email_cc_recipient",
					sentEmailObj.cc[0]
				);
			}

			ediTransactionRecord.setValue(
				"custrecord_spl_edi_trnsctn_cntrl_nmbr",
				transactionControlNumber
			);

			ediTransactionRecord.setValue(
				"custrecord_spl_edi_po_ref_nmbr",
				poRefNumber
			);

			ediTransactionRecord.setValue(
				"custrecord_spl_edi_purchasing_software",
				dataObj.purchasingSoftwareId
			);

			ediTransactionRecord.setValue(
				"custrecord_spl_edi_vendor",
				dataObj.vendorInternalId
			);

			var ediTransactionRecordId = ediTransactionRecord.save();

			if (!ediTransactionRecordId) {
				throw "EDI transaction not saved to database.";
			}

			//Set EDI transaction record id on the document control number record
			if (
				sentEmailObj.documentControlNumbers &&
				sentEmailObj.documentControlNumbers.length > 0
			) {
				sentEmailObj.documentControlNumbers.forEach(function (
					documentControlNumberId
				) {
					if (documentControlNumberId && parseInt(documentControlNumberId)) {
						//Load customrecord_edi_dcn_doc_ctrl_num record and set value of ediTransaction record to link them.
						var documentCtrlNumberRecObj = record.load({
							type: "customrecord_edi_dcn_doc_ctrl_num",
							id: documentControlNumberId,
						});

						if (!documentCtrlNumberRecObj) {
							throw `documentCtrlNumberRecObj not gotten for ${documentControlNumberId}`;
						}

						documentCtrlNumberRecObj.setValue({
							fieldId: "custrecord_edi_dcn_edi_tran_rec",
							value: ediTransactionRecordId,
						});

						documentCtrlNumberRecObj.save();
					}
				});
			}
		} catch (e) {
			log.error(
				"Error Pushing EDI Transaction to DB",
				`Transaction Title: ${sentEmailObj.subject ?? ""}
			Error: ${e}`
			);
		}
	}
	return {
		pushEdiEmailInfoToDB,
	};
});
