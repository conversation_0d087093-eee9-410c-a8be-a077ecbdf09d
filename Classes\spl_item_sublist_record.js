/**
 * Item Sublist Record class
 * that represents the line in the item sublist
 * of a transaction
 *
 * @NApiVersion 2.1
 * <AUTHOR>
 */

define([], () => {

	const gloveItemIdArr = ["4847", "6756", "4849", "6755"];
	const ppeGloveItemIdArr = ["54942", "54941", "54940", "54943", "54949", "54948", "54947", "54950"];
	const nyl81012ItemId = "3477";

	/**
	 * Item Sublist Record class
	 *
	 * @class
	 * @type {import("./spl_item_sublist_record").ItemSublistRecord}
	 */
	class ItemSublistRecord {
		constructor(transaction, index) {
			this.Field = {
				costEstimateRate: "costestimaterate",
				costEstimateType: "costestimatetype",
				createPo: "createpo",
				item: "item",
				itemType: "itemtype",
				lastPurchasePrice: "lastpurchaseprice",
				location: "location",
				poVendor: "povendor",
				price: "price",
				rate: "rate",
				quantity: "quantity",
				shipAccount: "custcol_spl_ship_account"
			};
			this.Sublist = {
				item: "item"
			};

			/**
			 * Switch between getCurrentSublistText and getSublistText based on text of line
			 * This works for select fields, which are assigned a text and value
			 * Retrieve the text to return the string assigned to represent the value
			 *
			 * @param {import("./spl_item_sublist_record").SublistParameters} param Sublist parameters 
			 * @returns {string} Text found on the sublist
			 */
			this.getSublistText = ({netsuiteRecord, line, sublistId, fieldId}) => {
				if (typeof line === "number" && line > -1) {
					return netsuiteRecord.getSublistText({
						sublistId,
						fieldId,
						line
					});
				} else {
					return netsuiteRecord.getCurrentSublistText({
						sublistId,
						fieldId
					});
				}
			};

			/**
			 * Switch between getCurrentSublistValue and getSublistValue based on value of line
			 * This is intended for all types of fields
			 * In case of a select field, retrieve the ID assigned to the record
			 *
			 * @param {import("./spl_item_sublist_record").SublistParameters} param Sublist parameters 
			 * @returns {any} Value found on the sublist
			 */
			this.getSublistValue = ({netsuiteRecord, sublistId, fieldId, line}) => {
				if (typeof line === "number" && line > -1) {
					return netsuiteRecord.getSublistValue({
						sublistId,
						fieldId,
						line
					});
				} else {
					return netsuiteRecord.getCurrentSublistValue({
						sublistId,
						fieldId
					});
				}
			};

			this.costEstimateType = this.getSublistValue({
				netsuiteRecord: transaction,
				line: index,
				sublistId: this.Sublist.item,
				fieldId: this.Field.costEstimateType
			});
			this.itemCost =  this.getSublistValue({
				netsuiteRecord: transaction,
				line: index,
				sublistId: this.Sublist.item,
				fieldId: this.Field.lastPurchasePrice
			})
			|| this.getSublistValue({
				netsuiteRecord: transaction,
				line: index,
				sublistId: this.Sublist.item,
				fieldId: this.Field.costEstimateRate
			});
			this.itemId = this.getSublistValue({
				netsuiteRecord: transaction,
				line: index,
				sublistId: this.Sublist.item,
				fieldId: this.Field.item,
			});
			this.itemName = this.getSublistText({
				netsuiteRecord: transaction,
				line: index,
				sublistId: this.Sublist.item,
				fieldId: this.Field.item,
			});
			this.itemType = this.getSublistValue({
				netsuiteRecord: transaction,
				line: index,
				sublistId: this.Sublist.item,
				fieldId: this.Field.itemType
			});
			this.priceLevel = this.getSublistValue({
				netsuiteRecord: transaction,
				line: index,
				sublistId: this.Sublist.item,
				fieldId: this.Field.price
			});
			this.quantity = this.getSublistValue({
				netsuiteRecord: transaction,
				line: index,
				sublistId: this.Sublist.item,
				fieldId: this.Field.quantity
			});
			this.rate = this.getSublistValue({
				netsuiteRecord: transaction,
				line: index,
				sublistId: this.Sublist.item,
				fieldId: this.Field.rate
			});
		}

		/**
		 * Determine if item has base price based on price level
		 *
		 * @returns {boolean} True if item sublist record price level is 1
		 */
		isUsingBasePrice() {
			return this.priceLevel === "1";
		}

		/**
		 * Determine if the item is a glove and is not using the correct updated price
		 *
		 * @returns {boolean} True if the glove is NOT priced at $18
		 */
		isGloveNotUsingCorrectRate() {
			return gloveItemIdArr.includes(this.itemId) && this.rate !== 18;
		}

		/**
		 * Determine if the item is a glove item
		 *
		 * @returns {boolean} True if the item is a glove item
		 */
		isPpeGloveItem() {
			return ppeGloveItemIdArr.includes(this.itemId);
		}

		/**
		 * Determine if the vendor for the mattress should be updated based on quantity
		 *
		 * @returns {boolean} True if mattress vendor should be Drive
		 */
		shouldChangeMattressVendorToDrive() {
			return this.itemId === nyl81012ItemId && this.quantity >= 4;
		}

		/**
		 * Determine if 400-879 has correct increments
		 *
		 * @returns {boolean} True if the item has correct increments
		 */
		hasCorrectIncrementsFor400879() {
			return this.itemId === "2450"
				&& this.quantity % 4 !== 0
				&& this.quantity % 6 !== 0;
		}

		/**
		 * Determine if MDTIU7SEFBLU has correct increments
		 *
		 * @returns {boolean} True if the item has correct increments
		 */
		hasCorrectIncrementsForMDTIU7SEFBLU() {
			return this.itemId === "39240"
				&& this.quantity % 24 !== 0;
		}

		/**
		 * Determine if MDTTB4C22WHIR has correct increments
		 *
		 * @returns {boolean} True if the item has correct increments
		 */
		hasCorrectIncrementsForMDTTB4C22WHIR() {
			return this.itemId === "38259"
				&& this.quantity % 48 !== 0;
		}

		/**
		 * Determine if RSP1270 has quantity of 4 or more
		 *
		 * @returns {boolean} True if the item quantity is 4 or more
		 */
		hasQuantityOfFourForRSP1270() {
			return this.itemId === "65256"
				&& this.quantity >= 4;
		}
	}

	return ItemSublistRecord;
});
