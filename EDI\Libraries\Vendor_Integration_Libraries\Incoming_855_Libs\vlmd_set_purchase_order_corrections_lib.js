/**
 * @NApiVersion 2.1
 */

//@ts-ignore
define(["N/record", "N/log"], function (record, log) {
	function createEdiNoteOnPurchaseOrder(purchaseOrderID, processingLog) {
		var createNoteErrors = [];

		if (processingLog.length > 0) {
			try {
				var note = record.create({
					type: record.Type.NOTE,
				});

				note.setValue("notetype", 9); //EDI Processing Log
				note.setValue("transaction", purchaseOrderID);
				var text = "";

				processingLog.forEach((logValue) => (text += logValue + "\n\n"));

				note.setValue("note", text);
				note.setValue("title", "EDI Transaction Errors");
				note.setValue("folder", 6511263); // EDI Files

				note.save();
			} catch (e) {
				log.error("createEdiNoteOnPurchaseOrder", e.name + ": " + e.message);
				createNoteErrors.push(`Error creating correction note ${e}`);
			}
		}

		return createNoteErrors;
	}

	return {
		createEdiNoteOnPurchaseOrder,
	};
});
