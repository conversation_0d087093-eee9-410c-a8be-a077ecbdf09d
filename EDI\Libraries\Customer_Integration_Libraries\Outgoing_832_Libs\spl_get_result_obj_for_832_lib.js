/**
 * @NApiVersion 2.1
 * @param {Array} results
 * @param {Array} slice - for paginated search results
 * @param {Number} resultRowNumber
 */

//@ts-ignore
define([
	"require",
	"N/log",
	"../../../../EDI/Libraries/Customer_Integration_Libraries/Outgoing_832_Libs/spl_item_helper_lib",
], function (require, log) {
	const itemHelperLib = require("../../../../EDI/Libraries/Customer_Integration_Libraries/Outgoing_832_Libs/spl_item_helper_lib");

	function getForPriceCatalog(itemRow, accountNumber) {
		try {
			return {
				SUPPLIER_ID: "SPLN",
				PRICE_LEVEL_ID: accountNumber,
				FILE_TYPE: "FULL",
				ITEM_NUM: itemRow.item.split(" ").join(""),
				UM: itemHelperLib.getUomAbbreviationForEdi(itemRow),
				PRICE: itemRow.price,
				IS_AUDITED: "TRUE",
				START_DATE: "11/15/2021",
				END_DATE: "12/31/9999",
			};
		} catch (err) {
			log.error("ERROR_GETTING_PRICE_OBJ", err);
		}
	}

	return {
		getForPriceCatalog,
	};
});
