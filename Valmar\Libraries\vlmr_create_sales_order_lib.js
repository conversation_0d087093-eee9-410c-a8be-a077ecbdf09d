/**
 * @NApiVersion 2.1
 */
//@ts-ignore
define(["N/record", "N/log"], function (record, log) {
	function createSalesOrder(
		purchaseOrderObj,
		referenceNumber,
		errorsCreateSalesOrder
	) {
		var processingErrors = [];
		var salesOrder = createSalesOrderRecord();

		setSalesOrderValues();
		setItems();
		var salesOrderId = saveSalesOrder();

		return {
			salesOrderId: salesOrderId,
			processingErrors: processingErrors,
		};

		function _processFail(logMessage, programmingError) {
			processingErrors.push({
				logMessage: logMessage,
				programmingError: programmingError,
			});
		}

		function createSalesOrderRecord() {
			return record.create({
				type: record.Type.SALES_ORDER,
				isDynamic: true,
			});
		}

		function setSalesOrderValues() {
			try {
				salesOrder.setValue("customform", 201); //Valmar Sales Order
				salesOrder.setValue("entity", purchaseOrderObj.customerId);
				salesOrder.setText(
					"orderstatus",
					errorsCreateSalesOrder ? "Pending Approval" : "Pending Fulfillment"
				);
				salesOrder.setValue("memo", purchaseOrderObj.memo);
				salesOrder.setValue("otherrefnum", referenceNumber);
				salesOrder.setValue(
					"custbody_spl_edi_trans_cntrl_num",
					referenceNumber
				);
				salesOrder.setValue("tobeemailed", false);
				salesOrder.setValue(
					"custbody_edi_850_in_json_obj",
					JSON.stringify(purchaseOrderObj)
				);

				//Revival Drop Ship - Flat Rate (will automatically set 6.95 rate)
				salesOrder.setValue("shipmethod", 37161);
			} catch (e) {
				log.error("Error setting SO values", e);

				_processFail(
					"Sales order values not set successfully ".concat(e.message),
					true
				);
			}
		}

		function setItems() {
			var items = purchaseOrderObj.items;
			items.forEach(function (item) {
				if (item.internalId) {
					try {
						salesOrder.selectNewLine({
							sublistId: "item",
						});

						salesOrder.setCurrentSublistValue({
							sublistId: "item",
							fieldId: "item",
							value: item.internalId,
						});

						salesOrder.setCurrentSublistValue({
							sublistId: "item",
							fieldId: "quantity",
							value: item.quantity,
						});

						salesOrder.setCurrentSublistValue({
							sublistId: "item",
							fieldId: "custcol_spl_hb_po1_line_number",
							value: item.hchbItemId,
						});

						salesOrder.setCurrentSublistValue({
							sublistId: "item",
							fieldId: "custcol_spl_ship_account",
							value: 3, //Medline Revival shipping account
						});

						salesOrder.commitLine({
							sublistId: "item",
						});
					} catch (e) {
						log.error("Error setting line values", `${e}, Item: ${item}`);
						if (e.message == "Please enter a value for amount.") {
							_processFail(
								"".concat(
									item.itemName,
									" doesn't have any price entered for it. Please add."
								),
								false
							);
						} else if (
							/You have entered an Invalid Field Value.*for the following field: price/.test(
								e.message
							)
						) {
							_processFail(
								"No pricing set for ".concat(
									item.itemName,
									" on the item record. Please add."
								),
								false
							);
						} else {
							_processFail(
								""
									.concat(item.itemName, " not set successfully: ")
									.concat(e.message),
								true
							);
						}
					}
				}
			});
		}

		function saveSalesOrder() {
			try {
				return salesOrder.save({
					enableSourcing: true,
					ignoreMandatoryFields: true,
				});
			} catch (e) {
				if (
					e.message ==
					"You must enter at least one line item for this transaction."
				) {
					_processFail("No valid items to create sales order with.", false);
				} else {
					_processFail("EDI File not saved: ".concat(e.message), true);
				}
			}
		}
	}

	return {
		createSalesOrder,
	};
});
