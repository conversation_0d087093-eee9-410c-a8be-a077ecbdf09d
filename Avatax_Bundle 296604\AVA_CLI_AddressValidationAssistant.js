/******************************************************************************************************
	Script Name - 	AVA_CLI_AddressValidationAssistant.js
	Company - 		Avalara Technologies Pvt Ltd.
******************************************************************************************************/

/**
 *@NApiVersion 2.0
 *@NScriptType ClientScript
*/

define(['N/url', 'N/https'],
	function(url, https){
		function AVA_PageInit(context){
			var cRecord = context.currentRecord;
			var locType = cRecord.getValue({
				fieldId: 'ava_loctype'
			});
			
			if(locType != null && locType == 'a'){
				var subsidiaryList = cRecord.getField({
					fieldId: 'ava_subsidiarylist'
				});
				var locationList = cRecord.getField({
					fieldId: 'ava_locationlist'
				});
				var subLocation = cRecord.getField({
					fieldId: 'ava_subloc'
				});
				subsidiaryList.isDisabled = true;
				locationList.isDisabled = true;
				subLocation.isDisabled = true;
			}
		}
		
		function AVA_FieldChange(context){
			if(context.fieldId == 'ava_loctype'){
				var cRecord = context.currentRecord;
				var locType = cRecord.getValue({
					fieldId: 'ava_loctype'
				});
				var subsidiaryList = cRecord.getField({
					fieldId: 'ava_subsidiarylist'
				});
				var locationList = cRecord.getField({
					fieldId: 'ava_locationlist'
				});
				var subLocation = cRecord.getField({
					fieldId: 'ava_subloc'
				});
				
				if(locType == 'a'){
					subsidiaryList.isDisabled = true;
					locationList.isDisabled = true;
					subLocation.isDisabled = true;
				}
				else if(locType == 'p'){
					subsidiaryList.isDisabled = true;
					locationList.isDisabled = false;
					subLocation.isDisabled = false;
				}
				else if(locType == 's'){
					subsidiaryList.isDisabled = false;
					locationList.isDisabled = true;
					subLocation.isDisabled = true;
				}
			}
		}
		
		function AVA_SaveRecord(context){
			var cRecord = context.currentRecord;
			var recordType = cRecord.getValue({
				fieldId: 'ava_recordtype'
			});
			var subsidiaryList = cRecord.getValue({
				fieldId: 'ava_subsidiarylist'
			});
			var locType = cRecord.getValue({
				fieldId: 'ava_loctype'
			});
			var locationList = cRecord.getValue({
				fieldId: 'ava_locationlist'
			});
			var batchName = cRecord.getValue({
				fieldId: 'ava_batchname'
			});
			
			//To check if any subsidiary has been selected or not
			if((recordType == 's' || recordType == 'c') && (subsidiaryList != null && subsidiaryList[0].length <= 0)){
				alert('Please select the Subsidiary(s).');
				return false;
			}
			
			//When Subsidiary location needs to be validated then check if Subsidiary has been selected or not
			if(recordType == 'l' && locType != null && locType == 's' && (subsidiaryList != null && subsidiaryList[0].length <= 0)){
				alert('Please select a Subsidiary.');
				return false;
			}
			
			//When particular location(s) needs to be validated then check if Location(s) have been selected or not
			if(recordType == 'l' && locType != null && locType == 'p' && (locationList != null && locationList[0].length <= 0)){
				alert('Please select Location(s).');
				return false;
			}
			
			if(subsidiaryList != null && subsidiaryList.length > 0){
				var subsidiaryValues = '';
				for(var i = 0; i < subsidiaryList.length; i++){
					subsidiaryValues += subsidiaryList[i] + '+';
				}
				
				subsidiaryValues = subsidiaryValues.substring(0, subsidiaryValues.lastIndexOf('+'))
				cRecord.setValue({
					fieldId: 'ava_subsidiarylistvalues',
					value: subsidiaryValues
				});
			}
			
			if(locationList != null && locationList.length > 0){
				var locationValues = '';
				for(var i = 0; i < locationList.length; i++){
					locationValues += locationList[i] + '+';
				}
				
				locationValues = locationValues.substring(0, locationValues.lastIndexOf('+'))
				cRecord.setValue({
					fieldId: 'ava_locationlistvalues',
					value: locationValues
				});
			}
			
			if(batchName != null){
				var response = https.request({
					method: https.Method.GET,
					url: url.resolveScript({
						scriptId: 'customscript_ava_recordload_suitelet',
						deploymentId: 'customdeploy_ava_recordload',
						params: {'type': 'customrecord_avaaddressvalidationbatch', 'batchname': batchName}
					})
				});
				
				if(response.body == '0'){ // Batch name already exists
					alert('Batch Name already Exists. Enter a new Batch Name');
					document.forms['main_form'].ava_batchname.focus();
					return false;
				}
			}
			
			return true;
		}
		
		return{
			pageInit: AVA_PageInit,
			fieldChanged: AVA_FieldChange,
			saveRecord: AVA_SaveRecord
		};
	}
);