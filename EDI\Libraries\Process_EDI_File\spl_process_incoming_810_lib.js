/**
 * @NApiVersion 2.x
 */

//@ts-ignore
define([
	"N/log",
	"N/record",
	"N/search",
	"ProcessInvoiceValidateAndUpdateItemsLib",
	"ProcessInvoiceEndLib",
	"MoveFileLib",
	"PushEdiEmailInfoToDBLib",
	"Numeral",
	"LoDash",
], function (
	log,
	record,
	search,
	validateAndUpdateItemsLib,
	processIncoming810EndLib,
	moveFileLib,
	pushEdiEmailInfoToDBLib,
	numeral,
	_
) {
	function processInvoice(
		ediInvoiceObj,
		fileName,
		previousErrors,
		vendorData,
		connection
	) {
		var helperFunctions = (function () {
			function _processFail(logMessage) {
				processingLog.push(logMessage);
				continueProcessingBool = false;
				return false;
			}

			function getPurchaseOrderInternalId() {
				var searchObj = search.create({
					type: "purchaseorder",
					filters: [
						["type", "anyof", "PurchOrd"],
						"AND",
						["numbertext", "is", ediInvoiceObj.poNumber],
						"AND",
						["name", "anyof", vendorData.vendorInternalId],
						"AND",
						["mainline", "is", "T"],
					],
				});

				var internalIdObj = searchObj.run().getRange({
					start: 0,
					end: 1000,
				})[0];

				if (_.isEmpty(internalIdObj)) {
					_processFail(
						`Purchase Order Internal Id not found for ${ediInvoiceObj.invoiceNumber}`
					);

					return;
				}

				return internalIdObj.id;
			}

			function transformPurchaseOrder() {
				try {
					var bill = record.transform({
						fromType: record.Type.PURCHASE_ORDER,
						fromId: purchaseOrderInternalId,
						toType: record.Type.VENDOR_BILL,
					});

					var internalId = bill.save();

					if (!internalId) {
						_processFail(`Bill not created for PO ${ediInvoiceObj.poNumber}`);
					}

					return internalId;
				} catch (e) {
					if (
						e.name == "INVALID_INITIALIZE_REF" ||
						e.message ==
							"You must enter at least one line item for this transaction."
					) {
						_processFail(
							`All items for ${ediInvoiceObj.poNumber} have already been billed`
						);
					} else {
						_processFail(
							`Bill not created for PO ${ediInvoiceObj.poNumber} Error: ${e}`
						);
					}
				}
			}

			function loadBillDynamic() {
				return record.load({
					type: record.Type.VENDOR_BILL,
					id: netSuiteBillId,
					isDynamic: true,
				});
			}

			function setBillValues() {
				try {
					netSuiteBill.setValue("tranid", ediInvoiceObj.invoiceNumber);
					netSuiteBill.setValue(
						"custbody_spl_edi_bill_trns_cntrl_nmbr",
						ediInvoiceObj.transactionControlNumber
					);

					netSuiteBill.setValue("approvalstatus", 1);
					netSuiteBill.setValue("trandate", ediInvoiceObj.date);
				} catch (error) {
					_processFail(
						"Error setting values for new bill for " +
							ediInvoiceObj.poNumber +
							" Error: " +
							error
					);
				}
			}

			function setShippingExpense() {
				_setExpense("shipping", ediInvoiceObj.shippingAmount, 823); //51000 - Shipping
			}

			function setTaxExpense() {
				_setExpense("tax", ediInvoiceObj.taxAmount, 833); //50025 - Vendor Tax charge for Goods
			}

			function _setExpense(propName, objProp, expenseAccount) {
				try {
					if (objProp) {
						netSuiteBill.selectNewLine({
							sublistId: "expense",
						});

						netSuiteBill.setCurrentSublistValue({
							sublistId: "expense",
							fieldId: "account",
							value: expenseAccount,
						});

						var amount = numeral(objProp).format("0.00");

						netSuiteBill.setCurrentSublistValue({
							sublistId: "expense",
							fieldId: "amount",
							value: amount,
						});

						netSuiteBill.commitLine({
							sublistId: "expense",
						});
					}
				} catch (e) {
					_processFail(
						`Error setting ${propName} expense for new bill for ${ediInvoiceObj.poNumber}. Error: ${e}`
					);
				}
			}

			async function saveAndValidateAndUpdateItemsAndMoveFile() {
				var netSuiteBillId = await saveBill();
				var staticBill = await loadBillStatic();
				await validateAndUpdateItems(staticBill);
				moveFile();
			}

			function saveBill() {
				try {
					var billId = netSuiteBill.save.promise({
						enableSourcing: true,
						ignoreMandatoryFields: true,
					});
					return billId;
				} catch (error) {
					var errorText = `New bill for ${ediInvoiceObj.poNumber} not saved: `;

					if (
						error.message ==
						"You must enter at least one line item for this transaction."
					) {
						errorText += "No items to create bill.";
					} else {
						errorText += `Error: ${error}`;
					}
					_processFail(errorText);
				}
			}

			function validateAndUpdateItems(staticBill) {
				try {
					var errorsValidatingLog =
						validateAndUpdateItemsLib.validateAndUpdateItems(
							netSuiteBillRefNumber,
							ediInvoiceObj.items,
							staticBill,
							vendorData.vendorName
						);

					if (errorsValidatingLog.length > 0) {
						processingLog = processingLog.concat(errorsValidatingLog);
					}
				} catch (e) {
					_processFail(`Error validating and updating item: ${e}`);
				}
			}

			function loadBillStatic() {
				return record.load({
					type: record.Type.VENDOR_BILL,
					id: netSuiteBillId,
					isDynamic: false,
				});
			}

			function moveFile() {
				var moveErrorLog = moveFileLib.moveFile(
					vendorData,
					fileName,
					processingLog.length <= 0
				);

				if (moveErrorLog.length > 0) {
					_processFail(`${fileName} not moved. Error: ${moveErrorLog}`);
				}
			}

			function processEnd() {
				return processIncoming810EndLib.processEnd(
					processingLog,
					ediInvoiceObj,
					netSuiteBillId,
					ediInvoiceObj.invoiceNumber,
					fileName,
					vendorData.vendorName
				);
			}

			function pushEdiEmailInfoToDB(sentEmailObj) {
				if (vendorData.pushEmailToDB) {
					pushEdiEmailInfoToDBLib.pushEdiEmailInfoToDB(
						vendorData,
						sentEmailObj,
						ediInvoiceObj.transactionControlNumber,
						netSuiteBillRefNumber
					);
				}
			}

			return {
				getPurchaseOrderInternalId,
				transformPurchaseOrder,
				loadBillDynamic,
				setBillValues,
				setShippingExpense: setShippingExpense,
				setTaxExpense: setTaxExpense,
				saveAndValidateAndUpdateItemsAndMoveFile,
				saveBill,
				validateAndUpdateItems,
				loadBillStatic,
				moveFile,
				processEnd,
				pushEdiEmailInfoToDB,
			};
		})();

		var processingLog = [];
		var continueProcessingBool = true;
		var netSuiteBillRefNumber = ediInvoiceObj.invoiceNumber;

		if (previousErrors.length > 0) {
			processingLog.push(previousErrors);
			helperFunctions.moveFile();
			var sentEmailObj = helperFunctions.processEnd();
			helperFunctions.pushEdiEmailInfoToDB(sentEmailObj);
		} else {
			var purchaseOrderInternalId =
				helperFunctions.getPurchaseOrderInternalId();
			if (continueProcessingBool) {
				var netSuiteBillId = helperFunctions.transformPurchaseOrder();

				if (netSuiteBillId) {
					//A new vendor bill was created for the PO
					var netSuiteBill = helperFunctions.loadBillDynamic();

					helperFunctions.setBillValues();
					helperFunctions.setShippingExpense();
					helperFunctions.setTaxExpense();
					async function processNextSteps() {
						netSuiteBillId = await helperFunctions.saveBill();
						var staticBill = await helperFunctions.loadBillStatic();
						await helperFunctions.validateAndUpdateItems(staticBill);
						await helperFunctions.moveFile();
						var sentEmailObj = helperFunctions.processEnd();
						helperFunctions.pushEdiEmailInfoToDB(sentEmailObj);
					}
					processNextSteps().catch((e) =>
						log.error("Error Processing Bill end", e)
					);
					return;
				}
			}
		}

		helperFunctions.moveFile();
		var sentEmailObj = helperFunctions.processEnd();
		helperFunctions.pushEdiEmailInfoToDB(sentEmailObj);
	}

	return {
		processInvoice: processInvoice,
	};
});