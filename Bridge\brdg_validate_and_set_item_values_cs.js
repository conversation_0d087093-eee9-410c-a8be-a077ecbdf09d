/**
 * @description Validate and set values for BRDG items
 *
 * </br><b>Deployed On:</b> BRDG Inventory Items, Kit Items, Service Items
 * </br><b>Execution Context:</b> ALL
 * </br><b>Event Type/Mode:</b> ALL
 * </br><b>Entry Points:</b> pageInit, validateField, fieldChanged,saveRecord,
 *
 * - Set Sync to LS - pageInit, subsidiary fieldChange,
 * - Set match bill to receipt - pageInit
 * - Set correct subsidiary - page init, subsidiary validateField
 * - Set item values - page init - Create/Copy only, subsidiary fieldChange,
 * - Set correct subsidiary - page init
 * - Validate UPC filled out and unique - on upccode validateField, saveRecord
 *
 *
 *
 * @NApiVersion 2.1
 * @NScriptType ClientScript
 * @NAmdConfig /SuiteScripts/config.json
 *
 * <AUTHOR>
 * @module brdg_validate_and_set_item_values_cs
 */

define([
	"require",
	"N/query",
	"N/runtime",
	"./Libraries/brdg_helper_functions_lib",
	"../Classes/spl_ui_notification_object",
	"../Classes/brdg_item_client_record",
	"../Classes/vlmd_custom_error_object",
], function (/** @type {any} */ require) {
	const query = require("N/query");
	const runtime = require("N/runtime");
	const bridgeHelperFunctionsLib = require("./Libraries/brdg_helper_functions_lib");

	/** @type {import("../Classes/spl_ui_notification_object").UINotification} */
	const UINotification = require("../Classes/spl_ui_notification_object");

	/** @type {import("../Classes/brdg_item_record").BridgeItemClientRecord} */
	const BridgeItemClientRecord = require("../Classes/brdg_item_client_record");

	/** @type {import("../Classes/vlmd_custom_error_object").CustomErrorObject} */
	const CustomErrorObject = require("../Classes/vlmd_custom_error_object");
	const customErrorObject = new CustomErrorObject();

	/** @type {number[]} */ let bridgeSubsidiaries = [];
	/** @type {import("../Classes/brdg_item_record").ItemCache[]} */ let itemCache;

	/**
	 * Cache item records
	 *
	 * @param {import("../Classes/brdg_item_record").ItemCache[]} cache Item cache
	 * @returns {import("../Classes/brdg_item_record").ItemCache[]} Updated item cache
	 */
	const updateItemCache = (cache) => {
		if (cache) {
			return cache;
		}

		const /** @type {import("../Classes/brdg_item_record").ItemCache[]} */ resultsArr =
				[];

		query
			.runSuiteQLPaged({
				query: `
					SELECT
						id,
						itemid,
						subsidiary,
						upccode,
						displayname
					FROM item
					WHERE isinactive = 'F'
					AND
					BUILTIN.MNFILTER(subsidiary, 'MN_INCLUDE', '', 'FALSE', NULL, '16' ) = 'T'`,
				pageSize: 1000,
			})
			.iterator()
			.each((page) => {
				page.value.data.results.forEach((result) => {
					if (result) {
						resultsArr.push({
							id: result.values[0]?.toString() || "",
							itemId: result.values[1]?.toString() || "",
							subsidiaries: result.values[2]
								? result.values[2].toString().split(",")
								: [],
							upcCode: result.values[3]?.toString() || "",
							displayName: result.values[4]?.toString() || "",
						});
					}
				});
				return true;
			});

		return resultsArr;
	};

	/**
	 * Retrieve existing items with the same Item Number as the current item record's Item Number
	 *
	 * @param {import("../Classes/brdg_item_record").ItemCache[]} itemCache Item cache
	 * @param {import("../Classes/brdg_item_record").BridgeItemClientRecord} item Item record
	 * @returns {string[]} IDs of items from the cache with the same Item Number
	 */
	const getItemsWithId = (itemCache, item) => {
		return itemCache
			.filter(
				(
					/** @type {import("../Classes/brdg_item_record").ItemCache} */ {
						id,
						itemId,
						subsidiaries,
					}
				) =>
					id !== item.id.toString() &&
					itemId === item.itemNumber &&
					subsidiaries.includes("16")
			)
			.map(
				(
					/** @type {import("../Classes/brdg_item_record").ItemCache} */ { id }
				) => id
			);
	};

	/**
	 * Set default values to fields on page initialization
	 *
	 * @param {import("N/types").EntryPoints.Client.pageInitContext} context Page init context
	 * @returns {void}
	 */
	function pageInit(context) {
		try {
			bridgeSubsidiaries = bridgeHelperFunctionsLib.getBridgeSubsidiaries();
			const item = new BridgeItemClientRecord(context.currentRecord);
			if (
				!bridgeHelperFunctionsLib.isBridgeSubsidiary(
					item.subsidiaries,
					bridgeSubsidiaries
				)
			) {
				return;
			}

			if (
				Array.isArray(item.subsidiaries) &&
				(item.subsidiaries.length > 1 || item.subsidiaries[0] !== "16")
			) {
				item.setSubsidiaryToBridgeManagementOnly();
			}

			if (
				//Filtering to only set values on a new item so that if a value was set manually by the user, the script won't override it.
				context.mode === "create" ||
				context.mode === "copy"
			) {
				item.setClientDefaultValues();
			}

			item.setSyncToLightSpeedClientFields();
			item.setClientMatchBillToReceipt();
		} catch (e) {
			customErrorObject.throwError({
				summaryText: `PAGE_INIT_ERROR`,
				error: e,
				recordId: context.currentRecord.id,
				recordType: "ITEM",
				errorWillBeGrouped: false,
			});
		}
	}

	/**
	 * Validate the UPC Code and Subsidiary fields
	 *
	 * @param {import("N/types").EntryPoints.Client.validateFieldContext} context Validate field context
	 * @returns {boolean} Returns true if validation passes
	 */
	function validateField(context) {
		try {
			const item = new BridgeItemClientRecord(context.currentRecord);

			if (
				!bridgeHelperFunctionsLib.isBridgeSubsidiary(
					item.subsidiaries,
					bridgeSubsidiaries
				)
			) {
				return true;
			}

			const uiNotification = new UINotification();

			try {
				if (context.fieldId == "upccode") {
					itemCache = updateItemCache(itemCache);
					item.validateUpcCode(itemCache);
				}

				if (context.fieldId == "subsidiary") {
					//If more than one subsidiary is chosen, or the chosen subsidiary is not BRDG
					if (
						Array.isArray(item.subsidiaries) &&
						(item.subsidiaries.length > 1 || item.subsidiaries[0] !== "16")
					) {
						uiNotification.addNotification(
							"errors",
							"Please set the subsidiary to 'Vineyard LLC.' only."
						);
					}
				}

				if (context.fieldId === "itemid" && item.itemNumber) {
					itemCache = updateItemCache(itemCache);

					const itemsWithItemId = getItemsWithId(itemCache, item);

					if (itemsWithItemId.length > 0) {
						uiNotification.addNotification(
							"warnings",
							`Item Number ${item.itemNumber} is already in use. Please set the field to another value.`
						);
					}
				}
			} catch (/** @type {any} */ err) {
				uiNotification.addNotification("errors", `${err.name}: ${err.message}`);
			}

			uiNotification.displayNotifications();

			return true;
		} catch (e) {
			customErrorObject.throwError({
				summaryText: `VALIDATE_FIELD_ERROR`,
				error: e,
				recordId: context.currentRecord.id,
				recordType: "ITEM",
				errorWillBeGrouped: false,
			});
		}
	}

	/**
	 * Set default values to fields on field change
	 *
	 * @param {import("N/types").EntryPoints.Client.fieldChangedContext} context Field changed context
	 * @returns {void}
	 */
	function fieldChanged(context) {
		try {
			//Set BRDG values when a BRDG subsidiary is chosen
			if (context.fieldId === "subsidiary") {
				const item = new BridgeItemClientRecord(context.currentRecord);

				if (
					!bridgeHelperFunctionsLib.isBridgeSubsidiary(
						item.subsidiaries,
						bridgeSubsidiaries
					)
				) {
					return;
				}

				item.setClientDefaultValues();
				item.setSyncToLightSpeedClientFields();
			}
		} catch (e) {
			customErrorObject.throwError({
				summaryText: `FIELD_CHANGED_ERROR`,
				error: e,
				recordId: context.currentRecord.id,
				recordType: "ITEM",
				errorWillBeGrouped: false,
			});
		}
	}

	/**
	 * Validate the UPC Code before saving
	 *
	 * @param {import("N/types").EntryPoints.Client.saveRecordContext} context Save record context
	 * @returns {boolean} Returns true if validation passes
	 */
	function saveRecord(context) {
		try {
			const item = new BridgeItemClientRecord(context.currentRecord);

			if (
				!bridgeHelperFunctionsLib.isBridgeSubsidiary(
					item.subsidiaries,
					bridgeSubsidiaries
				)
			) {
				return true;
			}

			const uiNotification = new UINotification(
				"brdg_validate_and_set_item_values.cs"
			);

			itemCache = updateItemCache(itemCache);

			try {
				item.validateUpcCode(itemCache);
			} catch (/** @type {any} */ err) {
				uiNotification.addNotification("errors", `${err.name}: ${err.message}`);
			}

			// Item ID Validation
			const itemsWithItemId = getItemsWithId(itemCache, item);

			if (itemsWithItemId.length > 0) {
				uiNotification.addNotification(
					"errors",
					`Item Number ${item.itemNumber} is already in use. Please set the field to another value.`
				);
			}

			try {
				item.validateBridgeSubsidiary();
			} catch (/** @type {any} */ err) {
				uiNotification.addNotification("errors", `${err.name}: ${err.message}`);
			}

			uiNotification.displayNotifications();

			return uiNotification.errors.length < 1;
		} catch (e) {
			customErrorObject.throwError({
				summaryText: `SAVE_RECORD_ERROR`,
				error: e,
				recordId: context.currentRecord.id,
				recordType: "ITEM",
				errorWillBeGrouped: false,
			});
		}
	}

	return {
		pageInit,
		validateField,
		fieldChanged,
		saveRecord,
	};
});
