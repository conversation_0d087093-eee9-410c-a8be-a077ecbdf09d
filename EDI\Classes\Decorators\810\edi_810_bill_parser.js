/**
 * @description Class containing functions specific to parsing Bill file
 *
 * @NApiVersion 2.1
 * <AUTHOR> <<EMAIL>>
 */

define([
    "exports",
    "require",
    "N/log",
    "./edi_810_line",
    "./edi_810_bill",
    "./edi_810_summary",
    "../810/edi_810_parser",
    "../../Models/File/edi_file",
    "../../../../Classes/vlmd_custom_error_object"
], function (/** @type {any} */ exports, /** @type {any} */ require) {
    const log = require("N/log");
    const { EDI810Line } = require("./edi_810_line");
    const { EDI810Bill } = require("./edi_810_bill");
    const { EDI810Summary } = require("./edi_810_summary");
    const { EDI810Parser } = require("../810/edi_810_parser");
    const { DocumentType } = require("../../Models/File/edi_file");
    /** @type {CustomErrorObject} */
    const CustomErrorObject = require("../../../../Classes/vlmd_custom_error_object");
    const IS_DYNAMIC = true;

    /**
     * 810 Parser Class
     *
     * @class
     * @typedef {import("../../Interfaces/Models/File/edi_file").EDIPostProcessEmail} EDIPostProcessEmail
     * @typedef {import("../../Interfaces/Models/Partner/edi_partner").EDIParsingInformation} EDIParsingInformation
     * @typedef {import("../../Interfaces/Models/Partner/edi_partner").EDIVendorInterface} EDIVendorInterface
     * @typedef {import("../../Interfaces/Decorators/810/edi_810").EDI810ParsedInvoice} EDI810ParsedInvoice
     * @typedef {import("../../Interfaces/Decorators/810/edi_810_parser").EDI810ParserInterface} EDI810ParserInterface
     * @typedef {import("../../../../Classes/vlmd_custom_error_object").CustomErrorObject} CustomErrorObject
     * @extends {EDI810Parser}
     * @implements {EDI810ParserInterface}
     */
    class EDI810BillParser extends EDI810Parser {
        /** @param {{[key:string]: any}} params */
        constructor(params) {
            super(params);
            /** @type {string} */
            this.ediFileContent = params.ediFileContent;
            /** @type {EDIParsingInformation} */
            this.partnerValues = params.partnerValues;
            /** @type {number|undefined} */
            this.billId = undefined;
            /** @type {EDI810ParsedInvoice|null} */
            this.invoice = null;
            /** @type {any[]} */
            this.errors = [];
            /** @type {EDIVendorInterface} */
            this.vendor = params.vendor;
            /** @type {CustomErrorObject} */
            this.customError = new CustomErrorObject();
        }

        /**
         * Update the transaction property with parsed EDI File
         *  while consolidating errors in the errors property
         *
         * @returns {void}
         */
        parse() {
            try {
                const line = new EDI810Line({
                    content: this.ediFileContent.split(/\n\r/).join(""),
                    fieldDelimiter: this.partnerValues.formattingInfo[0].partnerValue,
                    segmentDelimiter: this.partnerValues.formattingInfo[1].partnerValue,
                });

                log.debug({
                    title: "EDI 810 Bill Parser: EDI810Line",
                    details: JSON.stringify(line)
                });

                this.invoice = {
                    transactionControlNumber: line.getTransactionControlNumber(),
                    invoiceNumber: line.getInvoiceNumber(),
                    poNumber: line.getPONumber(),
                    total: line.getTotal(),
                    shippingAmount: line.getShippingAmount(),
                    taxAmount: line.getTaxAmount(),
                    date: line.getDate(),
                    items: line.getItems(),
                };

                log.debug({
                    title: "EDI 810 Bill Parser: EDI810ParsedInvoice",
                    details: JSON.stringify(this.invoice)
                });
            } catch (/** @type {any} */ err) {
                this.errors.push(err.stack);
                throw this.customError.updateError({
                    errorType: this.customError.ErrorTypes.UNHANDLED_ERROR,
                    summary: "EDI810BillParser_parse",
                    details: err.stack,
                });
            }
        }

        /**
         * Create a NetSuite Bill from the Purchase Order linked to the incoming Invoice
         *
         * @returns {void}
         */
        transform() {
            let ediBill;
            try {
                ediBill = new EDI810Bill({
                    fileName: this.filename,
                    invoice: this.invoice,
                    vendor: this.vendor,
                });
                const poId = ediBill.getPurchaseOrderInternalId();

                log.debug("EDI 810 Bill Parser (transform): PO ID", poId);

                if (poId) {
                    this.billId = ediBill.transformPurchaseOrder(poId);

                    if (this.billId) {
                        ediBill.loadRecord(this.billId, IS_DYNAMIC);
                        ediBill.setValues();
                        ediBill.setShippingExpense();
                        ediBill.setTaxExpense();
                        ediBill.save();
                        ediBill.loadRecord(this.billId, !IS_DYNAMIC);
                        ediBill.validateAndUpdateItems();
                    }
                }
            } catch (/** @type {any} */ err) {
                this.errors.push(err.stack);
                log.error({
                    title: "EDI 810 Bill Parser (transform)",
                    details: err.stack
                });
            }

            // Retrieve the EDI Bill processing and validation errors
            if (ediBill && ediBill.errors) {
                this.errors = this.errors.concat(ediBill.errors)
            }
        }

        /**
         * Retrieve the email object after processing the bill
         *
         * @returns {void}
         */
        summarize() {
            const summary = new EDI810Summary({
                errors: this.errors,
                documentType: DocumentType.INCOMING_810,
                filename: this.filename,
                transactionType: "Bill",
                transactionId: this.billId,
                transactionNumber: this.invoice?.poNumber,
                controlNumber: this.invoice?.transactionControlNumber,
                referenceNumber: this.invoice?.invoiceNumber,
                partner: this.vendor,
            });
            summary.createPostProcessEmail();
            summary.pushEdiEmailInfoToDB();

            this.email = summary.data;
        }
    }

    exports.EDI810BillParser = EDI810BillParser;
});