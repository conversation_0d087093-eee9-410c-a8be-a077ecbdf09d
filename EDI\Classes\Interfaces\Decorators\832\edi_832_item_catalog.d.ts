/**
 * @description EDI Item Catalog Types and Interface
 *
 * <AUTHOR> <<EMAIL>>
 */

import {EDIFileInterface} from "../../Models/File/edi_file";
import {EDIIncomingInterface} from "../../Models/File/edi_incoming";
import {EDIOutgoingInterface} from "../../Models/File/edi_outgoing";

export type EDIItemCatalogQueryResult = {
    /** Item ID */
    internalId: string;
    /** Item Number */
    itemNumber: string;
    /** Item Description */
    description: string;
    /** Item Category ID */
    category: string;
    /** Item Type */
    itemType: string;
    /** Item Image URL */
    imageUrl: string;
    /** Check if parent UoM is Each */
    parentUnitIsEach: boolean;
    /** Abbreviation of UoM */
    unitAbbreviation: string;
    /** Item to which this is a replacement for */
    isReplacementFor: string;
}

export interface EDIItemCatalogInterface extends EDIFileInterface, EDIIncomingInterface, EDIOutgoingInterface {}