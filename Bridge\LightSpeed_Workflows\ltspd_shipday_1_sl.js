/**
 * @description SL that gets triggered on pay when a sale includes the Shipday item.
 * It is split into 3 different SL scripts that get triggered in order because you cannot return multiple actions in the specified order. 
 *
 * </br><b>Entry Points:</b> onRequest
 *
 * @NApiVersion 2.1
 * @NScriptType Suitelet
 *
 * <AUTHOR>
 * @module ltspd_shipday
 */

define(["require", "N/log", "N/query"], function (/** @type {any} */ require) {
  const log = require("N/log");
  const query = require("N/query");

  const shipdayProductId = "a14f2663-8845-4cf3-9899-6e5d40d1a1ea";

  /**
   * Return the action object for LightSpeed to set the custom delivery address field
   *
   * @param {import("N/types").EntryPoints.Suitelet.onRequestContext} context Suitelet onRequest context
   */
  function onRequest(context) {
    if (context.request.method === "POST") {
      try {
        const data = context.request.body;
        const jsonObj = JSON.parse(data);
        const username = jsonObj.user["username"];

        if(!jsonObj.customer?.id) return;
        // if(username !=  "<PERSON>ya Drillick" && username != "XXXXXXXXXXXXXXX" && username != "Esther Goldberg") return;

          let addressCustomFieldInOrder = jsonObj["sale"][
						"custom_fields"
					].find((obj) => obj.name == "delivery_address");

          if(addressCustomFieldInOrder != null) return;

          const lineItems = jsonObj.sale["line_items"];
          const customerId = jsonObj.customer.id;
          if (
            lineItems.some((item) => item["product_id"] === shipdayProductId) &&
            addressCustomFieldInOrder == null
          ) {
            const sqlQuery = /*sql*/ `
            SELECT
            LISTAGG(DISTINCT custbody_ls_delivery_address, ',') WITHIN GROUP (
         ORDER BY
            custbody_ls_delivery_address) as custentity_shipday_delivery_address
         FROM
            transaction 
            INNER JOIN
               customer 
               on customer.id = transaction.entity 
         WHERE
            (
               transaction.type = 'SalesOrd' 
               or transaction.type = 'CashSale'
            )
            and custentity_in8_vend_id = '${customerId}'
                    `;

            const customerDeliveryAddressArr = query
              .runSuiteQL({
                query: sqlQuery,
              })
              .asMappedResults()[0];

            const customerDeliveryAddrFromNetsuite = JSON.parse(
              customerDeliveryAddressArr.custentity_shipday_delivery_address
            );

            let actionToReturn;

            if (!customerDeliveryAddrFromNetsuite) {
              //Go straight to the next SL and ask to the user to enter a new address
              actionToReturn = JSON.stringify({
                actions: [
                  {
                    type: "set_custom_field",
                    entity: "sale",
                    custom_field_name: "delivery_address",
                    custom_field_value: JSON.stringify({ address1: "Other" }),
                  },
                ],
              });
            } else {
              //Add the "other" option to the address array
              customerDeliveryAddrFromNetsuite.push({ address1: "Other" });

              log.audit(
                "Customer Delivery Address Array",
                JSON.stringify(customerDeliveryAddrFromNetsuite)
              );

              actionToReturn = JSON.stringify({
                actions: [
                  {
                    type: "require_custom_fields",
                    title: "Address Required",
                    message: "Please select a delivery address:",
                    entity: "sale",
                    required_custom_fields: [
                      {
                        name: "delivery_address",
                        values: customerDeliveryAddrFromNetsuite.map(
                          (address) => ({
                            value: JSON.stringify(address),
                            title: address.address1,
                          })
                        ),
                      },
                    ],
                  },
                ],
              });
            }
            context.response.write(actionToReturn);
          }
        
      } catch (/** @type {any} */ err) {
        log.error(err.name, err.message);
      }
    }
  }

  return {
    onRequest,
  };
});
