/**
 * @NApiVersion 2.1
 * @NScriptType MapReduceScript
 * @NAmdConfig /SuiteScripts/config.json
 */

//@ts-ignore
define(["N/error", "N/search", "N/record", "N/log"], function (
	error,
	search,
	record,
	log
) {
	function getInputData(context) {
		var searchInternalId = 1997; //Sales Commission Without a Journal Entry
		var recordsArr = [];
		var searchObj = search.load({
			id: searchInternalId,
		});
		searchObj.run().each(function (result) {
			recordsArr.push(result);
			return true;
		});
		log.debug("recordArr", recordsArr);
		return recordsArr;
	}

	function map(context) {
		var parsedResult = JSON.parse(context.value);
		try {
			var deletedId = record.delete({
				type: "customrecord_spl_sales_commission",
				id: parsedResult.id,
			});
			log.debug("deletedId", deletedId);
		} catch (e) {
			log.debug("e", e);
			context.write(deletedId, deletedId);
			throw error.create({
				message: e,
				name: "Map Error",
			});
		}
	}

	function reduce(context) {
		context.write({
			key: context.key,
			value: context.values,
		});
	}

	function summarize(context) {
		var recordsDeleted = "";
		context.output.iterator().each(function (key, value) {
			recordsDeleted += "".concat(key, " ").concat(value, ", ");
			return true;
		});
		log.debug(
			"The following records have been deleted: ".concat(recordsDeleted)
		);
	}

	return {
		getInputData: getInputData,
		map: map,
		reduce: reduce,
		summarize: summarize,
	};
});
