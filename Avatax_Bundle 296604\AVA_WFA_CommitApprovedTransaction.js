/******************************************************************************************************
	Script Name - AVA_WFA_CommitApprovedTransaction.js
	Company -     Avalara Technologies Pvt Ltd.
******************************************************************************************************/

/**
 *@NApiVersion 2.0
 *@NScriptType WorkflowActionScript
*/

define(['N/config', 'N/record', 'N/log', './utility/AVA_Library', './utility/AVA_TaxLibrary'],
	function(config, record, log, ava_library, taxLibrary){
		function onAction(context){
			try{
				var nRecord = context.newRecord;
				var loadConfig = config.load({
					type: config.Type.ACCOUNTING_PREFERENCES
				});
				var avaConfigObjRec = ava_library.mainFunction('AVA_LoadValuesToGlobals', '');
				
				if(loadConfig.getValue('CUSTOMAPPROVALCUSTINVC') == true && avaConfigObjRec['AVA_CommitTransaction'] == true){
					var rec = [];
					rec.recordType = nRecord.type;
					rec.id = nRecord.id;
					taxLibrary.AVA_CommitTransaction(rec);
				}
			}
			catch(err){
				log.debug({
					title: 'Try/Catch',
					details: err.message
				});
			}
		}
		
		return{
			onAction: onAction
		};
	}
);