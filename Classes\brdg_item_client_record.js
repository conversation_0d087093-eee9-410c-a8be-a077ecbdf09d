/**
 * Client Item Record class representing a Bridge item record
 *
 * @NAmdConfig /SuiteScripts/config.json
 * @NApiVersion 2.1
 * <AUTHOR>
 * @module BridgeItemClientRecord
 */

define(["require", "exports", "./brdg_item_record"], (
	/** @type {any} */ require,
	/** @type {any} */ exports
) => {
	/** @type {import("./brdg_item_record").BridgeItemRecord} */
	const BridgeItemRecord = require("./brdg_item_record");

	/**
	 * Bridge Item Record class
	 *
	 * @class
	 * @type {import("./brdg_item_record").BridgeItemClientRecord}
	 *
	 */
	class BridgeItemClientRecord extends BridgeItemRecord {
		/**
		 * Constructor
		 *
		 * @param {import("N/record").ClientCurrentRecord} itemRecord Item record
		 */
		constructor(itemRecord) {
			super(itemRecord);
		}

		/**
		 * Sync Light Speed if Do not Sync LS is not set
		 *
		 * @returns {void}
		 */
		setSyncToLightSpeedClientFields() {
			try {
				if (this.doNotSyncToLs) {
					return;
				}

				this.record.setValue({
					fieldId: this.lightSpeed.Field.VINEYARD_NORTH,
					value: true,
				});

				this.record.setValue({
					fieldId: this.lightSpeed.Field.VINEYARD_LOL,
					value: true,
				});

				this.record.setValue({
					fieldId: this.lightSpeed.Field.VINEYARD_SOUTH,
					value: true,
				});

				this.record.setValue({
					fieldId: this.lightSpeed.Field.VINEYARD_BERGENFIELD,
					value: true,
				});

				this.record.setValue({
					fieldId: this.lightSpeed.Field.VINEYARD_EXPRESS,
					value: true,
				});

				this.record.setValue({
					fieldId: this.lightSpeed.Field.VINEYARD_WESTGATE,
					value: true,
				});
			} catch (e) {
				throw this.customErrorObject.updateError({
					errorType: this.customErrorObject.ErrorTypes.VALUE_NOT_SET,
					summary: "ERROR_SETTING_SYNC_TO_LS_FIELDS",
					details: `Error setting sync to LS fields: ${e}`,
				});
			}
		}

		/**
		 * Set the default values of the item record
		 *
		 * @returns {void}
		 */
		setClientDefaultValues() {
			try {
				const taxSchedule3 = 4;

				this.record.setValue({
					fieldId: "taxschedule",
					value: taxSchedule3,
				});

				this.record.setValue({
					fieldId: "includechildren",
					value: true,
				});

				//Only set it if it's not a kit item or service item
				this.record.setValue({
					fieldId: "costestimatetype",
					value:
						this.record.type == "kititem"
							? "MEMBERDEFINED"
							: this.record.type == "serviceitem"
							? "ITEMDEFINED"
							: "LASTPURCHPRICE",
				});

				this.record.setValue({
					fieldId: "costingmethod",
					value: "FIFO",
				});

				this.record.setValue({
					fieldId: "tracklandedcost",
					value: true,
				});

				this.record.setValue({
					fieldId: "custitem_brdg_markup_percentage",
					value: "To Be Generated",
				});
			} catch (e) {
				throw this.customErrorObject.updateError({
					errorType: this.customErrorObject.ErrorTypes.VALUE_NOT_SET,
					summary: "ERROR_SETTING_DEFAULT_VALUES",
					details: `Error setting default values: ${e}`,
				});
			}
		}

		/**
		 * Set the Match Bill To Receipt Checkbox to true
		 *
		 * @returns {void}
		 */
		setClientMatchBillToReceipt() {
			try {
				this.record.setValue({
					fieldId: "matchbilltoreceipt",
					value: true,
				});
			} catch (e) {
				throw this.customErrorObject.updateError({
					errorType: this.customErrorObject.ErrorTypes.VALUE_NOT_SET,
					summary: "ERROR_SETTING_MATCH_BILL_TO_RECEIPT",
					details: `Error setting match bill to receipt: ${e}`,
				});
			}
		}
	}

	exports.BridgeItemClientRecord = BridgeItemClientRecord;

	return BridgeItemClientRecord;
});
