/**
 * @description Pull all open EMRLD invoices past due or within 5 days and emails out
 *
 * @NApiVersion 2.1
 * @NScriptType MapReduceScript
 * @NAmdConfig /SuiteScripts/config.json
 *
 * @module emrld_automate_billing_emails_mr.js
 */

define([
	"require",
	"MapReduceSummaryStageHandling",
	"N/search",
	"N/runtime",
	"N/render",
	"N/email",
], (require, MapReduceSummaryStageHandling) => {
	const search = require("N/search");
	const runtime = require("N/runtime");
	const render = require("N/render");
	const email = require("N/email");

	const getInputData = (context) => {
		const script = runtime.getCurrentScript();
		const savedSearchId = script.getParameter({
			name: "custscript_emrld_blng_email_saved_search",
		});

		if (!savedSearchId) {
			throw {
				name: "ERROR_GETTING_SCRIPT_PARAMETER",
				message: `No value returned for savedSearchId (custscript_emrld_blng_email_saved_search)`,
			};
		}

		var searchObj = search.load({
			id: savedSearchId,
		});

		if (!savedSearchId) {
			throw {
				name: "ERROR_LOADING_SEARCH",
				message: `Saved search, ${savedSearchId}, not loaded correctly`,
			};
		}

		return searchObj;
	};

	const map = (context) => {
		try {
			const parsedValues = JSON.parse(context.value);

			const transactionObj = {
				transactionId: parsedValues.id,
				customerId: parsedValues.values.entity.value,
				customerName: parsedValues.values.entity.text,
				email: parsedValues.values.email,
				documentNumber: parsedValues.values.tranid,
			};

			context.write({
				key: transactionObj.customerName,
				value: transactionObj,
			});
		} catch (e) {
			throw {
				name: "MAP_STAGE_ERROR",
				message: e.message,
			};
		}
	};

	const reduce = (context) => {
		try {
			const script = runtime.getCurrentScript();

			const templateId = script.getParameter({
				name: "custscript_emrld_blng_email_template",
			});

			if (!templateId) {
				throw {
					name: "ERROR_GETTING_SCRIPT_PARAMETER",
					message: `No value returned for templateId (custscript_emrld_blng_email_template)`,
				};
			}

			const author = script.getParameter({
				name: "custscript_emrld_blng_email_author",
			});

			if (!author) {
				throw {
					name: "ERROR_GETTING_SCRIPT_PARAMETER",
					message: `No value returned for author (custscript_emrld_blng_email_author)`,
				};
			}

			const transactionMissingEmailsArr = [];
			const transactionsEmailedSuccessfully = [];

			context.values.forEach((transactionObj) => {
				transactionObj = JSON.parse(transactionObj);

				if (!transactionObj.email) {
					transactionMissingEmailsArr.push(transactionObj.documentNumber);

					return;
				}

				const mergedResultObj = render.mergeEmail({
					templateId: templateId,
					transactionId: parseInt(transactionObj.transactionId),
				});

				email.send({
					author: author,
					recipients: [transactionObj.email.split(";")],
					subject: mergedResultObj.subject,
					body: mergedResultObj.body,
					relatedRecords: {
						transactionId: transactionObj.transactionId,
					},
				});

				transactionsEmailedSuccessfully.push(
					`${transactionObj.documentNumber}, ${transactionObj.email}`
				);
			});

			context.write({
				key: context.key,
				value: transactionsEmailedSuccessfully,
			});

			if (transactionMissingEmailsArr.length > 0) {
				throw {
					name: "MISSING_EMAIL_ADDRESS",
					message: `MISSING_EMAIL_ADDRESS: ${
						context.key
					}: ${transactionMissingEmailsArr.join(", ")}`,
				};
			}
		} catch (e) {
			throw {
				name: e.name,
				message: e.message,
			};
		}
	};

	const summarize = (context) => {
		const stageHandling = new MapReduceSummaryStageHandling(context);
		stageHandling.printScriptProcessingSummary();
		stageHandling.printRecordsProcessed();
		stageHandling.printErrors();
	};

	return {
		getInputData,
		map,
		reduce,
		summarize,
	};
});
