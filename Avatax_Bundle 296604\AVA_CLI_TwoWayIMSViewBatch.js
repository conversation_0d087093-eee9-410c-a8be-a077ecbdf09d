/******************************************************************************************************
	Script Name - AVA_CLI_TwoWayIMSViewBatch.js
	Company -     Avalara Technologies Pvt Ltd.
******************************************************************************************************/

/**
 *@NApiVersion 2.0
 *@NScriptType ClientScript
*/

define(['N/currentRecord', 'N/url'],
	function(currentRecord, url){		
		function saveRecord(context){
			var cRecord = context.currentRecord;
			var alertFlag = 'F', deleteFlag = 'F', alertMsg = '';
			
			var lineCount = cRecord.getLineCount({
				sublistId: 'custpage_twowayimsbatchsublist'
			});
			
			for(var i = 0; i < lineCount; i++){
				var apply = cRecord.getSublistValue({
					sublistId: 'custpage_twowayimsbatchsublist',
					fieldId: 'apply',
					line: i
				});
				
				if(apply == true){
					var batchStatus = cRecord.getSublistValue({
						sublistId: 'custpage_twowayimsbatchsublist',
						fieldId: 'twowayimsbatchstatus',
						line: i
					});
					
					if(batchStatus == 'In Progress' || batchStatus == 'In Queue'){
						var batchName = cRecord.getSublistValue({
							sublistId: 'custpage_twowayimsbatchsublist',
							fieldId: 'twowayimsbatchname',
							line: i
						});
						
						alertFlag = 'T';
						alertMsg += batchName + '\n';
					}
					
					deleteFlag = 'T'
				}
			}
			
			if(deleteFlag == 'F'){
				alert('Select line item for delete.');
				return false;
			}
			
			if(alertFlag == 'T'){
				alert('Following Batches are in Progress or in Queue and cannot be deleted: \n' + alertMsg);
				return false;
			}
			
			return true;
		}
		
		function AVA_TwoWayIMSViewBatchRefresh(){
			try{
				var criteriaSuiteURL = url.resolveScript({
					scriptId: 'customscript_ava_twowayimsviewbatch_suit',
					deploymentId: 'customdeploy_ava_twowayimsviewbatch_suit'
				});
				window.onbeforeunload = null;
				window.location.assign(criteriaSuiteURL);
			}
			catch(e){
				log.error('AVA_TwoWayIMSViewBatchRefresh', 'ERROR NAME = ' + e.name + ', ERROR TYPE = ' + e.type + ', ERROR MESSAGE = ' + e.message);
			}
		}
		
		return{
			saveRecord: saveRecord,
			AVA_TwoWayIMSViewBatchRefresh: AVA_TwoWayIMSViewBatchRefresh
		};
	}
);