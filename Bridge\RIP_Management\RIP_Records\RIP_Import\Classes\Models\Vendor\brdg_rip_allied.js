/**
 * @description Bridge RIP Vendor class for Allied Beverage Group LLC
 *
 * @NApiVersion 2.1
 * <AUTHOR>
 */

define([
  "exports",
  "require",
  "N/log",
  "./brdg_rip_vendor"
], function (/** @type {any} */ exports, /** @type {any} */ require) {
  const log = require("N/log");
  const { BridgeRIPVendor } = require("./brdg_rip_vendor");
  const LINE_LENGTH_THRESHOLD = 30;

  /**
   * Bridge RIP Allied Class
   */
  class BridgeRIPAllied extends BridgeRIPVendor {
    /** @param {{[key:string]: any}} props Constructor params */
    constructor(props){
      super(props);
    }

    /**
     * Split the row string from the CSV file and attach the line index
     *
     * @param {import("N/file").File} ripFile NetSuite File
     * @returns {any[]} Row strings split by commas
     */
    splitLines(ripFile) {
      const fileIterator = ripFile.lines.iterator();

      let lineCount = 0;

      // Since we iterate through the file from top to bottom, we cannot skip the header
      // Iterate over the header by returning false after reading it
      fileIterator.each((/** @type {any} */ header) => false);

      // Continue reading the file, and store the contents to a data lines array
      const /** @type {any[]} */ dataArr = [];
      fileIterator.each((/** @type {any} */ line) => {
        //Likely ',,,,,,,,' etc. - an empty line that the file picked up to read.
        if (line.value.length <= LINE_LENGTH_THRESHOLD) {
          return;
        }

        const lineArr = line.value.trim().split(",");

        if (lineArr.length <= 1) {
          throw this.customErrorObject.updateError({
            errorType: this.customErrorObject.ErrorTypes.INVALID_DATA_TYPE,
            summary: "INVALID_LINE_DATA",
            details: `Error with ${JSON.stringify(
              line.value
            )}. Check that there are no \\ or line spaces in any header cells`,
          });
        }

        dataArr.push([...lineArr, lineCount]);
        lineCount++;

        return true;
      });

      return dataArr;
    }

    /**
     * Create an object from the line extracted from the CSV file
     * - Add a default value of 1 for countAs using splice
     *
     * @param {any[]} fields Array of column values
     * @returns {{[key:string]: any}} Key-value pairs of column values
     */
    parseLine(fields) {
      const fieldNames = [
        "sku", "ripCode", "brandRegistration", "fromDate", "toDate", "description",
        "uom1", "qty1", "amt1", "uom2", "qty2", "amt2", "uom3", "qty3", "amt3",
        "uom4", "qty4", "amt4", "uom5", "qty5", "amt5", "countAs", "comments", "lineCount",
      ];
      const trimIfString = (/** @type {any} */ x) => (typeof x === "string" ? x.trim() : x);
      const values = fields.map(trimIfString);
      values.splice(21, 0, 1);

      return Object.fromEntries(fieldNames.map((key, index) => [key, values[index]]));
    }

    /**
     * Merge RIP Levels into a single row
     *
     * @param {string[]} levels RIP levels in JSON String format
     * @returns {any[]} Merged levels 
     */
    mergeLevels(levels) {
      // Levels from Allied are already merged
      return Object.values(JSON.parse(levels[0]));
    }
  }

  exports.BridgeRIPAllied = BridgeRIPAllied;
});