/**
 * @NApiVersion 2.1
 */

//@ts-ignore
define([
	"N/record",
	"N/file",
	"GetItemFulfillmentObjLib",
	"Write856EdiFileLib",
	"GetEdiFileContents",
], function (record, file, getAsnObjLib, writeAsnLib, getEdiFileContentsLib) {
	function process856(internalIDval, parentCustomer, partnerValues, dataObj, customErrorObject) {
		try {
		const errorLog = [];
		var helperFunctions = (function () {
			function _processFail(logMessage) {
				log.error({
					title: "Process Fail",
					details: logMessage
				})
			}

			function getRecord(internalIDval) {
				try {
					return record.load({
						type: record.Type.ITEM_FULFILLMENT,
						id: internalIDval,
					});
				} catch (err) {
					throw customErrorObject.updateError({
						errorType: customErrorObject.ErrorTypes.RECORD_NOT_LOADED,
						summary: "ERROR_LOADING_RECORD",
						details: `Item Fulfillment record not loaded: ${err.message}`,
					});
				}
			}

			function getDocumentAsEDI(partnerValues, asnObj) {
				const ediFile = writeAsnLib.getAsnAsEDI(partnerValues, asnObj);

				if (!ediFile) {
					throw customErrorObject.updateError({
						errorType: customErrorObject.ErrorTypes.NO_VALUE_RETURNED,
						summary: "EDI_FILE_NOT_GOTTEN",
						details: `Advance Shipment Notice was converted to EDI file with "Invalid" or "Undefined".`,
					});
				}
				return ediFile;
			}

			function _getFileToUpload(docNum, ediFile) {
				return file.create({
					name: docNum + "_856.edi",
					fileType: file.Type.PLAINTEXT,
					contents: ediFile,
				});
			}

			function uploadFileToSupplyLineServer(docNum, ediFile) {
				try {
					const referenceDirectory = `/EDI Reference Files/${dataObj.purchasingSoftware}/${parentCustomer.integrationFolder}/OUT/856`;
					const supplyLineConnection = getEdiFileContentsLib.createConnection(
						dataObj,
						"",
						referenceDirectory
					);

					supplyLineConnection.upload({
						file: _getFileToUpload(docNum, ediFile),
						replaceExisting: true,
					});
				} catch (err) {
					throw customErrorObject.updateError({
						errorType: customErrorObject.ErrorTypes.RECORD_NOT_SAVED,
						summary: "FILE_NOT_SAVED_TO_OUR_SERVER",
						details: `File not saved to our server: ${err.message}`,
					});
				}
			}

			function uploadFileToTheirServer(docNum, ediFile) {
				try {
					const theirConnection =
						getEdiFileContentsLib.createConnection(dataObj);

					theirConnection.upload({
						file: _getFileToUpload(docNum, ediFile),
						replaceExisting: true,
					});
				} catch (err) {
					throw customErrorObject.updateError({
						errorType: customErrorObject.ErrorTypes.RECORD_NOT_SAVED,
						summary: "FILE_NOT_SAVED_TO_THEIR_SERVER",
						details: `File not saved to purchasing server: ${err.message}`,
					});
				}
			}

			function updateControlNumberOnParentRecord(
				parentCustomerId,
				controlNumber
			) {
				try {
					record.submitFields({
						type: record.Type.CUSTOMER,
						id: parentCustomerId,
						values: {
							custentity_spl_hb_edi_cntrl_nmbr: controlNumber,
						},
					});
				} catch (err) {
					throw customErrorObject.updateError({
						errorType: customErrorObject.ErrorTypes.RECORD_NOT_SAVED,
						summary: "CUSTOMER_NOT_UPDATED",
						details: `Value not saved to "custentity_spl_hb_edi_cntrl_nmbr": ${err.message}`,
					});
				}
			}

			function updateAndSaveNetsuiteRecord(itmFulfillmentRec, controlNumber) {
				try {
					itmFulfillmentRec.setValue(
						"custbody_spl_edi_856_cntrl_nmbr",
						controlNumber
					);

					itmFulfillmentRec.save({
						enableSourcing: true,
						ignoreMandatoryFields: true,
					});
				} catch (err) {
					throw customErrorObject.updateError({
						errorType: customErrorObject.ErrorTypes.RECORD_NOT_SAVED,
						summary: "CUSTOMER_NOT_UPDATED",
						details: `Value not saved to "custbody_spl_edi_856_cntrl_nmbr": ${err.message}`,
					});
				}
			}

			function createDocCtrlNumRec() {
				try {
					//Create new Document Control Number Record
					var docCtrlNumRec = record.create({	
						type: "customrecord_edi_dcn_doc_ctrl_num",
					});

					docCtrlNumRec.setValue(
						"custrecord_edi_dcn_tran_ctrl_num",
						controlNumber
					);


					docCtrlNumRec.setValue(
						"custrecord_edi_dcn_doc_num",
						docNum
					);	
					return docCtrlNumRec.save();

				} catch (err) {
					throw customErrorObject.updateError({
						errorType: customErrorObject.ErrorTypes.RECORD_NOT_SAVED,
						summary: "EDI_CNTRL_NMBR_NOT_UPDATED",
						details: `Value not saved for customrecord_edi_dcn_doc_ctrl_num": ${controlNumber} ${docNum} ${err.message}`,
					});
				}
			}


			return {
				_processFail,
				getRecord,
				getDocumentAsEDI,
				uploadFileToTheirServer,
				uploadFileToSupplyLineServer,
				updateControlNumberOnParentRecord,
				updateAndSaveNetsuiteRecord,
				createDocCtrlNumRec,
			};
		})();

			var { customerName: parentCustomerName, customerId: parentCustomerId } =
				parentCustomer;

			var itmFulfillmentRec = helperFunctions.getRecord(internalIDval);

			var docNum = itmFulfillmentRec.getValue({ fieldId: "tranid" });
			var custRecinternalID = itmFulfillmentRec.getValue({ fieldId: "entity" });
			var tranType = itmFulfillmentRec.getValue({ fieldId: "recordType" });

			var trackingNumberFromRec = itmFulfillmentRec.getValue({
				fieldId: "custbody_ifcust_track_num",
			});
			var soIntID = itmFulfillmentRec.getValue({ fieldId: "createdfrom" });
			var shipCarrierFromRec = itmFulfillmentRec.getValue({
				fieldId: "shipcarrier",
			});

			if (
				!docNum ||
				!custRecinternalID ||
				!soIntID ||
				!shipCarrierFromRec
			) {
				helperFunctions._processFail(
					`Value not gotten from item fulfillment record: docNum: ${docNum}, custRecinternalID: ${custRecinternalID}, trackingNumberFromRec: ${trackingNumberFromRec}, soIntID: ${soIntID}, shipCarrierFromRec: ${shipCarrierFromRec}`
				);
			}

			var shipmentVals = {
				trackingNumber: trackingNumberFromRec,
				salesOrderIntId: soIntID,
				shipCarrier: shipCarrierFromRec,
			};

			const getTransactionObjResults = getAsnObjLib.getObj(
				itmFulfillmentRec,
				parentCustomer,
				shipmentVals,
				custRecinternalID
			);

			if (getTransactionObjResults.errorLog.length > 0) {
				throw getTransactionObjResults.errorLog;
			}

			const documentObj = getTransactionObjResults.asnObj;

			const ediFile = helperFunctions.getDocumentAsEDI(partnerValues, documentObj);

			var controlNumber = documentObj.documentInfo.controlNumber;

			helperFunctions.uploadFileToSupplyLineServer(docNum, ediFile);
			helperFunctions.uploadFileToTheirServer(docNum, ediFile);
			helperFunctions.updateControlNumberOnParentRecord(
				parentCustomerId,
				controlNumber
			);
			helperFunctions.updateAndSaveNetsuiteRecord(
				itmFulfillmentRec,
				controlNumber
			);
			var docCtrlNumRecIdVal = helperFunctions.createDocCtrlNumRec();


		return docCtrlNumRecIdVal;

	} catch (err) {
		throw customErrorObject.updateError({
			errorType: customErrorObject.ErrorTypes.UNHANDLED_ERROR,
			summary: "ERROR_PROCESSING_856",
			details: `Error in Process Outgoing 856 Lib: ${err}`,
		});
	}
		
	}

	return {
		process856,
	};
});
