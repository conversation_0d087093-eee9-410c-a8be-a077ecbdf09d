/**
 * @NApiVersion 2.1
 * Incoming EDI 856 MR scripts - Medline and Drive
 * @description Parses 856 edi file and returns a JS object
 * @param {string} ediText - data sent on EDI file
 * @param {object} partnerValues - the specific EDI values for this EDI partner
 * @returns {object} {asnObj, errorLog}
 */

//@ts-ignore
define(["N/log", "AddItemInternalIds"], function (log, addInternalIdsLib) {
	function parse856(ediText, partnerValues) {
		const errorLog = [];

		let asnObj = {
			transactionControlNumber: "",
			poNumber: "",
			shipToName: "",
			address: "",
			carrier: "",
			packages: [],
		};

		const fieldDelimiter = partnerValues.formattingInfo[0].partnerValue;
		const segmentDelimiter = partnerValues.formattingInfo[1].partnerValue;

		ediText = ediText.split("\n").join("").split("\r").join("");

		try {
			asnObj.transactionControlNumber = getTransactionControlNumber();
			asnObj.poNumber = getPONumber();
			asnObj.shipToName = getShipToName();
			asnObj.address = ""; //TODO: research why no N3 ST segment being sent - is this needed? getShipToAddress();
			asnObj.carrier = getCarrier();
			asnObj.packages = getPackages();
		} catch (e) {
			errorLog.push(e.stack);
		}

		return {
			asnObj,
			errorLog,
		};

		/**********Parse Invoice Helper Functions**********/
		function getSegment(segmentTitle) {
			var regExp = new RegExp(
				segmentDelimiter + segmentTitle + ".*?" + segmentDelimiter
			);
			var segment = ediText.match(regExp);
			try {
				segment = segment[0].split(segmentDelimiter).join("");
				return segment;
			} catch (e) {
				errorLog.push(`${segmentTitle} segment not found`);
			}
		}

		function getTransactionControlNumber() {
			var bsnSegment = getSegment("BSN");
			try {
				return bsnSegment.split(fieldDelimiter)[2];
			} catch (e) {
				errorLog.push(`No transaction control number found. Error: ${e}`);
			}
		}

		function getPONumber() {
			var prfSegment = getSegment("PRF");
			try {
				return prfSegment.split(fieldDelimiter)[1];
			} catch (e) {
				errorLog.push(`No purchase order number found. Error: ${e}`);
			}
		}

		function getShipToName() {
			let n1Segment = getSegment("N1\\" + fieldDelimiter + "ST");
			try {
				return n1Segment.split(fieldDelimiter)[2];
			} catch (e) {
				errorLog.push(`No ship to name found. Error: ${e}`);
			}
		}

		function getShipToAddress() {
			let address = "";

			try {
				var n3Segment = getSegment("N3");
				var streetAddress = n3Segment.split(fieldDelimiter)[1];

				var n4Segment = getSegment("N4");
				var city = n4Segment.split(fieldDelimiter)[1];
				var state = n4Segment.split(fieldDelimiter)[2];
				var zip = n4Segment.split(fieldDelimiter)[3];

				address = `${getShipToName()}
				${streetAddress}
				${city}, ${state}${zip}`;
			} catch (e) {
				errorLog.push(`Error processing address. Error: ${e}`);
			}
			return address;
		}

		function getCarrier() {
			let td5Segment = getSegment("TD5");
			let carrier = td5Segment?.split(fieldDelimiter)[3];

			if (!carrier) {
				carrier = td5Segment?.split(fieldDelimiter)[5];
			}

			if (!td5Segment || !carrier) {
				errorLog.push(`No shipping carrier found`);
			}

			return carrier;
		}

		function getPackages() {
			const packagesArr = [];

			try {
				let fileContainsPackages = ediText.includes(`MAN${fieldDelimiter}`);

				if (fileContainsPackages) {
					var packageSegments = ediText.split("MAN" + fieldDelimiter);

					packageSegments.shift();

					packageSegments.forEach((segment) => {
						packagesArr.push({
							trackingNumber: getCpTrackingNumber(segment) ?? "",
							items: getItems(segment) ?? [],
						});
					});
				} else {
					packagesArr.push({
						trackingNumber: getRefTrackingNumber() ?? "",
						items: getItems(ediText) ?? [],
					});
				}
			} catch (e) {
				errorLog.push("Error Getting Packages", e);
			}

			return packagesArr;
		}

		function getCpTrackingNumber(packageSegment) {
			const regExp = new RegExp("CP.*?" + segmentDelimiter);
			let trackingSegment = packageSegment.match(regExp);

			try {
				trackingSegment = trackingSegment[0].split(segmentDelimiter).join("");
			} catch (e) {
				errorLog.push(`CP segment not found`);
			}
			try {
				return trackingSegment.split(fieldDelimiter)[1];
			} catch (e) {
				errorLog.push(`No tracking number found. Error: ${e}`);
			}
		}

		function getRefTrackingNumber() {
			let refSegment = getSegment("REF\\" + fieldDelimiter + "CN");

			try {
				return refSegment ? refSegment.split(fieldDelimiter)[2] : "";
			} catch (e) {
				errorLog.push(`No tracking number found. Error: ${e}`);
			}
		}

		function getItems(textCotainingItems) {
			const itemsArr = [];

			try {
				const regExp = new RegExp(
					segmentDelimiter +
						"LIN.*?" +
						segmentDelimiter +
						"SN1.*?" +
						segmentDelimiter,
					"gs"
				);

				let itemSegments = textCotainingItems.match(regExp);

				if (itemSegments.length <= 0) {
					errorLog.push(`No item segments returned`);
					return [];
				}

				itemSegments.forEach(function (itemSegment) {
					itemSegment = itemSegment.slice(1); //Remove segment delimiter from string.

					const itemFields = itemSegment
						.split(segmentDelimiter)
						.join(fieldDelimiter)
						.split(fieldDelimiter);

					const item = {
						itemName: itemFields[3],
						quantity: parseInt(itemFields[itemFields.length - 3]),
					};

					itemsArr.push(item);
				});

				const itemsWithInternalIds = addInternalIdsLib.addItemInternalIds(
					itemsArr,
					[1, 2]
				); //Supplyline Subsidiary

				return itemsWithInternalIds;
			} catch (e) {
				errorLog.push(`Error processing items. Error: ${e}`);
			}
		}
	}

	return {
		parse856,
	};
});
