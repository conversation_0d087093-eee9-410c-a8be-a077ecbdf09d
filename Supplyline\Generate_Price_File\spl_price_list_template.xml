<?xml version="1.0"?> <!DOCTYPE pdf PUBLIC "-//big.faceless.org//report" "report-1.1.dtd">  
<pdf> 
    <body> 
        <h2 style="margin-bottom: 6px;">${results.records[0]['customer']} Price List</h2> 
        <table width="100%"> 
            <tr> 
                <th width="10%">Item #</th> <th >Description</th> <th >Category</th> <th >UOM</th> <th >Price</th> 
            </tr>
            <#list results.records as item> 
            <tr>
                <#outputformat "XML"> <td>${ item['item_name'] ?replace('&amp;', '&')}</td></#outputformat>
                <#outputformat "XML"> <td>${ item['item_description']?replace('&amp;', '&')}</td></#outputformat>
                <#outputformat "XML"> <td>${ item['item_category']?replace('&amp;', '&')}</td></#outputformat>
                <td>${ item['item_uom']}</td> 
                <td>${ item['item_price'] } </td> 
            </tr>
            </#list> 
        </table>
    </body>
</pdf> 