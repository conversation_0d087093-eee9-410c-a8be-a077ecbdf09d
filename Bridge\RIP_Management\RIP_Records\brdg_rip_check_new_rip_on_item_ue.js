/**
 * @description Looks to see if new RIP records should be created for new items based on the new/updated vendor code
 *
 * </br><b>Deployed On:</b> BRDG Inventory Part, Item Group
 * </br><b>Execution Context:</b> ALL
 * </br><b>Event Type/Mode:</b> CREATE, EDIT
 * </br><b>Entry Points:</b> afterSubmit
 *
 * @NApiVersion 2.1
 * @NScriptType UserEventScript
 * @NAmdConfig /SuiteScripts/config.json
 *
 * <AUTHOR>
 * @module brdg_rip_check_new_rip_ue
 */

define([
  "require",
  "../../../Classes/brdg_item_record",
  "../../Libraries/brdg_helper_functions_lib",
  "../../../Classes/vlmd_custom_error_object",
  "N/log",
  "N/query",
  "N/record",
  "N/task",
  "N/email",
  "N/search",
], (/** @type {any} */ require) => {
  const log = require("N/log");
  const query = require("N/query");
  const record = require("N/record");
  const task = require("N/task");
  const email = require("N/email");
  const search = require("N/search");
  /**@type {import ("../../../Classes/brdg_item_record")}} */
  const BridgeItemRecord = require("../../../Classes/brdg_item_record");
  /**@type {import ("../../Libraries/brdg_helper_functions_lib")}} */
  const bridgeHelperFunctionsLib = require("../../Libraries/brdg_helper_functions_lib");
  /**@type {import ("../../../Classes/vlmd_custom_error_object").CustomErrorObject}} */
  const CustomErrorObject = require("../../../Classes/vlmd_custom_error_object");

  function sendNotificationIfAlreadyIsRipItem(itemId, vendorObj) {
    const sqlQuery = `
    SELECT
    BUILTIN.DF(custrecord_rebate_parent) AS AgreementRecord,
    name AS AgreementDetailRecord, 
    id AS AgreementDetailId
FROM
    CUSTOMRECORD_REBATE_AGREEMENT_DETAIL AD
WHERE
    BUILTIN.MNFILTER(
        AD.custrecord_rebate_items_included,
        'MN_INCLUDE',
        '',
        'FALSE',
        NULL,
        ?
    ) = 'T'
`;

    const sqlResults = query
      .runSuiteQL({
        query: sqlQuery,
        params: [itemId],
      })
      .asMappedResults();
    if (sqlResults.length > 0) {
      const emailText = `A vendor code was updated on an <a href= '/app/common/item/item.nl?id=${itemId}'>item</a> that has a rip applied to it! <br/>
      This vendor code: ${vendorObj.vendorCodeOld} was updated to be this vendor code: ${vendorObj.vendorCodeNew}<br/><br/>
      Please check the <a href= '/custrecordentry.nl?rectype=1703&id=${sqlResults[0].agreementdetailId}'>Rebate Agreement Detail</a> record and update accordingly.
      <br/><br/> Matching Rebate Record(s) Info: 
      <br/>`;
      const resultsText = [];
      sqlResults.forEach((result) => {
        resultsText.push(
          `Agreement Record: ${result.agreementrecord}, 
        Agreement Detail Record: ${result.agreementdetailrecord}<br/>`
        );
      });

      email.send({
        author: 223244, //Requests PROD: 223244
        recipients: 43398, //<EMAIL>
        body: emailText + resultsText,
        subject: `Vendor Code Updated`,
      });
    }
  }

  function checkIfNewRipRecordsNeededForNewCode(newVendorCode, vendorId) {
    const sqlQuery = `
    SELECT
    *
FROM
    customrecord_brdg_rip_import
WHERE
    isinactive = 'F'
    AND custrecord_item_exists_in_ns = 'F'
    AND (custrecord_brdg_rip_import_sku = '${newVendorCode}'
    OR custrecord_brdg_rip_import_sku = '0${newVendorCode}'
    OR custrecord_brdg_rip_import_sku = '00${newVendorCode}')
    AND custrecord_brdg_rip_import_vendor = ${vendorId}
`;

    const sqlResults = query
      .runSuiteQL({
        query: sqlQuery,
      })
      .asMappedResults();
    if (sqlResults.length > 0) {
      return { hasRip: true, matchingRipImportRecordsArr: sqlResults };
    } else {
      return { hasRip: false };
    }
  }

  function checkIfPartOfExistingRipCode(ripImportObj) {
    const sqlQuery = `
    SELECT
    customrecord_rebate_agreement_detail.id, customrecord_rebate_agreement_detail.name
FROM
    customrecord_rebate_agreement_detail
    INNER JOIN customrecord_rebate_agreement ON customrecord_rebate_agreement.id = customrecord_rebate_agreement_detail.custrecord_rebate_parent
WHERE
    custrecord_rip_code = ${ripImportObj.custrecord_brdg_rip_import_code}
    AND TO_DATE(custrecord_rebate_start_date, 'MM/DD/YYYY') = TO_DATE(
        '${ripImportObj.custrecord_brdg_rip_import_from}',
        'MM/DD/YYYY'
    )
    AND TO_DATE(custrecord_rebate_end_date, 'MM/DD/YYYY') = TO_DATE(
        '${ripImportObj.custrecord_brdg_rip_import_to}',
        'MM/DD/YYYY'
    )`;

    const sqlResults = query
      .runSuiteQL({
        query: sqlQuery,
      })
      .asMappedResults();

    if (sqlResults.length > 0) {
      return {
        existingDetailId: sqlResults[0].id,
        existingDetailName: sqlResults[0].name,
      };
    } else {
      return false;
    }
  }

  function addedToExistingRipADRecord(newItemId, existingDetailId) {
    const detailRecord = record.load({
      type: "customrecord_rebate_agreement_detail",
      id: existingDetailId,
      isDynamic: true,
    });

    const items = detailRecord.getValue("custrecord_rebate_items_included");
    items.push(newItemId);
    detailRecord.setValue("custrecord_rebate_items_included", items);
    detailRecord.save();
    return true;
  }

  function updateAndDeactivateRipImportRecord(importRecordId, itemId) {
    record.submitFields({
      type: "customrecord_brdg_rip_import",
      id: importRecordId,
      values: {
        custrecord_brdg_rip_import_item: itemId,
        custrecord_item_exists_in_ns: "T",
        isinactive: "T",
      },
      options: {
        enableSourcing: false,
        ignoreMandatoryFields: true,
      },
    });
  }

  function checkIfChangedVendorCode(oldRecord, newRecord) {
    const itemType = newRecord.getValue("itemtype");
    let vendorCodeOld;
    let vendorCodeNew;
    let vendorId;
    if (itemType == "Group") {
      vendorCodeOld = oldRecord.getValue("vendorname");
      vendorCodeNew = newRecord.getValue("vendorname");
      vendorId = newRecord.getValue("custitem_vendor");
      const isRipVendor = checkIfVendorIsRipVendor(vendorId);
      if (!isRipVendor) {
        return;
      }
    } else {
      const lineCount = newRecord.getLineCount({ sublistId: "itemvendor" });
      if (lineCount > 0) {
        for (var i = 0; i < lineCount; i++) {
          //Looping through every item on the transaction and checking to see if vendor code was updated
          const vendorCodeOldLine = oldRecord.getSublistValue({
            sublistId: "itemvendor",
            fieldId: "vendorcode",
            line: i,
          });
          const vendorCodeNewLine = newRecord.getSublistValue({
            sublistId: "itemvendor",
            fieldId: "vendorcode",
            line: i,
          });
          const vendorIdLine = newRecord.getSublistValue({
            sublistId: "itemvendor",
            fieldId: "vendor",
            line: i,
          });
          const isRipVendor = checkIfVendorIsRipVendor(vendorIdLine);
          if (isRipVendor && vendorCodeOldLine != vendorCodeNewLine) {
            vendorCodeOld = vendorCodeOldLine;
            vendorCodeNew = vendorCodeNewLine;
            vendorId = vendorIdLine;
            break;
          }
        }
      }
    }

    if (vendorCodeOld !== vendorCodeNew) {
      return { vendorCodeNew, vendorCodeOld, vendorId };
    }
  }

  function checkIfVendorIsRipVendor(vendorId) {
    let ripVendor = search.lookupFields({
      type: search.Type.VENDOR,
      id: vendorId,
      columns: "custentity_rip_vendor",
    }).custentity_rip_vendor;

    return ripVendor;
  }

  function sendResultsIfAddedToRecord(rebateDetailsIdArr, itemId) {
    let emailText = `An <a href= '/app/common/item/item.nl?id=${itemId}'>item's</a> vendor code was updated and it was added to the following rebate agreement detail record(s): <br/><br/>`;
    let count = 0;
    rebateDetailsIdArr.forEach((existingDetailObj) => {
      count++,
        (emailText += `${count}: <a href= '/app/common/custom/custrecordentry.nl?rectype=1703&id=${existingDetailObj.existingDetailId}'>${existingDetailObj.existingDetailName}</a><br/>`);
    });

    email.send({
      author: 223244, //Requests
      recipients: "<EMAIL>",
      body: emailText,
      subject: `Results from an updated vendor code on an item`,
    });
  }

  function checkIfScriptIsStillExecuting() {
    return search
      .create({
        type: "scheduledscriptinstance",
        filters: [
          ["status", "anyof", "RETRY", "RESTART", "PROCESSING", "PENDING"],
          "AND",
          ["script.internalid", "anyof", "3173", "3174"],
          "AND",
          ["scriptdeployment.internalid", "anyof", "7676", "7677"],
          //If we ever add deployments to the RIP Import MR, make sure to add the ids here
        ],
        columns: [search.createColumn({ name: "status", label: "Status" })],
      })
      .runPaged().count;
  }

  return {
    afterSubmit: (context) => {
      const customErrorObject = new CustomErrorObject();

      const { newRecord, oldRecord } = context;
      try {
        const bridgeItemRecord = new BridgeItemRecord(newRecord);
        const bridgeSubsidiaries =
          bridgeHelperFunctionsLib.getBridgeSubsidiaries();
        const isBridgeSubsidiary = bridgeHelperFunctionsLib.isBridgeSubsidiary(
          bridgeItemRecord.subsidiaries,
          bridgeSubsidiaries
        );

        if (!isBridgeSubsidiary) {
          return;
        }

        let newVendorCode;
        let vendorId;

        if (context.type != context.UserEventType.CREATE) {
          //If we are editing an item - check if the vendor code was added/changed
          const vendorObj = checkIfChangedVendorCode(oldRecord, newRecord);
          if (_.isEmpty(vendorObj)) {
            //If a vendor code was not changed, leave the script
            return;
          }
          newVendorCode = vendorObj.vendorCodeNew;
          vendorId = vendorObj.vendorId;
          //Check to see if there is RIP already on this item with the old vendor code (the one we just removed)
          sendNotificationIfAlreadyIsRipItem(oldRecord.id, vendorObj);
        } else if (context.type === context.UserEventType.CREATE) {
          //If we are creating an item - check if it has a vendor code that gets a RIP
          const lineCount = newRecord.getLineCount({ sublistId: "itemvendor" });
          if (lineCount > 0) {
            for (var i = 0; i < lineCount; i++) {
              const vendorCodeNew = newRecord.getSublistValue({
                sublistId: "itemvendor",
                fieldId: "vendorcode",
                line: i,
              });
              vendorId = newRecord.getSublistValue({
                sublistId: "itemvendor",
                fieldId: "vendor",
                line: i,
              });
              const isRipVendor = checkIfVendorIsRipVendor(vendorId);
              if (vendorCodeNew && isRipVendor) {
                newVendorCode = vendorCodeNew;
                vendorId = vendorId;
              }
            }
          }
        }
        if (newVendorCode) {
          //If we have an updated/new vendor code - check if a RIP applies and either:
          //A: there already is RIP records that this item can just be added to (if it has the same code)
          //B: the RIP needs the whole process of all the new records
          const applicableRipObj = checkIfNewRipRecordsNeededForNewCode(
            newVendorCode,
            vendorId
          );

          if (!applicableRipObj) {
            throw customErrorObject.updateError({
              errorType: customErrorObject.ErrorTypes.MISSING_PARAMETER,
              summary: "MISSING_VENDOR_CODE",
              details: `Unable to check if RIP info is available on this item!`,
            });
          }

          if (applicableRipObj.hasRip) {
            var regenerateRips = false;
            var rebateDetailsIdArr = [];
            applicableRipObj.matchingRipImportRecordsArr.forEach(
              (ripImportRecordObj) => {
                //If there is RIP info, check if this CODE exists already and add to it, otherwise create new records
                const existingDetailObj =
                  checkIfPartOfExistingRipCode(ripImportRecordObj);

                if (!_.isEmpty(existingDetailObj)) {
                  //If there's an existing rip code already for this item
                  var existingDetailId = existingDetailObj.existingDetailId;
                  addedToExistingRipADRecord(oldRecord.id, existingDetailId);
                  updateAndDeactivateRipImportRecord(
                    ripImportRecordObj.id,
                    oldRecord.id
                  );
                  rebateDetailsIdArr.push(existingDetailObj);
                  regenerateRips = true;
                } else {
                  do {
                    checkIfScriptIsStillExecuting();
                  } while (checkIfScriptIsStillExecuting() != 0);

                  //If no rip details in NS yet, run the rip import process to create everything on this specific rip import record
                  //The create tiers MR is the first step in the process
                  var createTiersMr = task.create({
                    taskType: task.TaskType.MAP_REDUCE,
                  });

                  createTiersMr.scriptId =
                    "customscript_brdg_rip_import_tiers_mr";
                  createTiersMr.deploymentId =
                    "customdeploy_brdg_rip_import_tiers_mr";
                  createTiersMr.params = {
                    custscript_specific_rip_import_id: ripImportRecordObj.id,
                    custscript_item_id_added: oldRecord.id,
                  };
                  var createTiersMrTaskId = createTiersMr.submit();
                  var taskStatus = task.checkStatus({
                    taskId: createTiersMrTaskId,
                  });

                  do {
                    taskStatus = task.checkStatus({
                      taskId: createTiersMrTaskId,
                    });
                    if (taskStatus.status == "FAILED") {
                      throw customErrorObject.updateError({
                        errorType: customErrorObject.ErrorTypes.REFERENCE_ERROR,
                        summary: "FAILED_MAP_REDUCE",
                        details: `Map Reduce Creating New Rip Info Failed!`,
                      });
                    }
                  } while (
                    taskStatus.status != "COMPLETE" &&
                    taskStatus.status != "FAILED"
                  );
                  taskStatus.status == "COMPLETE"
                    ? (regenerateRips = true)
                    : (regenerateRips = false);
                }
              }
            );

            if (rebateDetailsIdArr.length > 0) {
              try {
                //If it got added to an existing agreement detail record - send an email to let them know
                sendResultsIfAddedToRecord(rebateDetailsIdArr, oldRecord.id);
              } catch (e) {
                throw customErrorObject.updateError({
                  errorType: customErrorObject.ErrorTypes.REFERENCE_ERROR,
                  summary: "Error sending results!",
                  details: e.message,
                });
              }
            }

            //Once we have all the rip records in - either because they already existed, or because we just added it
            //Create another MR task here to update all bills with that item
            if (regenerateRips) {
              //Run the MR to update all bills that have this new item
              const regenerateRipsMr = task.create({
                taskType: task.TaskType.MAP_REDUCE,
                scriptId: "customscript_brdg_rips_regenerate_record",
                deploymentId: "customdeploy_brdg_rips_regenerate_mr",
                params: {
                  custscript_updated_item_id: oldRecord.id,
                },
              });
              regenerateRipsMr.submit();
            }
          }
        }
      } catch (e) {
        customErrorObject.throwError({
          summaryText: "ERROR_CREATING_NEW_RIP_INFO",
          error: e,
          recordType: "InvtPart",
          recordId: oldRecord.id,
        });
      }
    },
  };
});
