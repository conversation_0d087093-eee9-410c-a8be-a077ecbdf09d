/**
 * @NApiVersion 2.1
 */

//@ts-ignore
define(["N/log", "N/search", "N/query"], function (log, search, query) {
  function addItemInternalIds(itemObsArr, subsidiary) {
    try {
      //Convert all item names into a single string to use in query
      let itemNamesString = itemObsArr
        .map((itemObj) => `'${itemObj.itemName}'`)
        .join(",");

      //Some Oncare items are sent with a dash in the name, search by this version as well
      if (itemNamesString.includes("-")) {
        itemNamesString += `, ${itemNamesString.split("-").join("")}`;
      }

      //Format subsidiary to account for string or array
      const subsidiaryFilterText = Array.isArray(subsidiary)
        ? subsidiary
            .map(
              (subsidiaryElement) =>
                `BUILTIN.MNFILTER( item.subsidiary, 'MN_INCLUDE', '', 'FALSE', NULL,'${subsidiaryElement}') = 'T'`
            )
            .join(" OR ")
        : `BUILTIN.MNFILTER( item.subsidiary, 'MN_INCLUDE', '', 'FALSE', NULL,'${subsidiary}') = 'T'`;

      const internalIdsQuery = /*sql*/ `
			SELECT
				item.itemid item_number,
				item.fullname item_full_id,
				item.vendorname item_vendor_code,
				item.id internal_id
			FROM
				item 
			WHERE
				item.isinactive = 'F' 
				AND item.custitem_spl_dont_use_item_in_new_tran = 'F' 
				AND 
				(
					item.itemtype = 'InvtPart' 
					OR item.itemtype = 'NonInvtPart'
					OR item.itemtype = 'Kit'
				)
				AND (
					item.itemid IN (${itemNamesString})
						OR
					item.fullname IN (${itemNamesString})
						OR
					item.vendorname  IN (${itemNamesString})
					)
					AND (${subsidiaryFilterText})
		`;

      const queryResults = query
        .runSuiteQL({
          query: internalIdsQuery,
        })
        .asMappedResults();

      //Iterate over each item sent on the PO and see if a corresponding item was found in NS, if found assign the internal id
      itemObsArr.forEach((itemObj) => {
        try {
          let objWithIntId = queryResults.find((intIdObj) => {
            try {
              //Format both names to account for variances, search for corresponding item by item id or full name
              return (
                intIdObj["item_number"].split("-").join("").toUpperCase() ==
                  itemObj.itemName.split("-").join("").toUpperCase() ||
                intIdObj["item_full_id"].split("-").join("").toUpperCase() ==
                  itemObj.itemName.split("-").join("").toUpperCase() ||
                intIdObj["item_vendor_code"]
                  .split("-")
                  .join("")
                  .toUpperCase() ==
                  itemObj.itemName.split("-").join("").toUpperCase()
              );
            } catch (err) {
              log.error(
                "ERROR_FINDING_INTERNAL_ID_OBJECT",
                `Internal ID Object: ${JSON.stringify(intIdObj)}\nError: ${err}`
              );
            }
          });

          //If no match found, check if this is an old/new item
          if (!objWithIntId) {
            var isOldNewItemQuery = `SELECT custrecord_new_item_id internal_id,
					BUILTIN.DF(custrecord_new_item_id) item_name,
					FROM customrecord_old_new_item_mapping 
					WHERE
					UPPER(BUILTIN.DF(custrecord_old_item_id)) = UPPER('${itemObj.itemName}')`;

            objWithIntId = query
              .runSuiteQL({
                query: isOldNewItemQuery,
              })
              .asMappedResults()[0];
          }

          if (!objWithIntId || !objWithIntId["internal_id"]) {
            throw {
              name: "No matching item found",
              message: `${itemObj.itemName}:${isOldNewItemQuery
                .split("\n")
                .join(" ")
                .split("\t")
                .join(" ")}`,
            };
          } else {
            //This is an old item that will be replaced with the new item in NS
            //Add the new item name -> can be used to find the item when comparing our/their price
            itemObj.newItemName = objWithIntId["item_name"];
          }

          itemObj.internalId = objWithIntId["internal_id"]; //If old/new item - this is the internal id of the new item, but the item name is still set as the old item name

          return true;
        } catch (err) {
          log.error(
            "ERROR_ADDING_INTERNAL_ID_TO_ITEM",
            `Item Details: ${JSON.stringify(itemObj)}\nError: ${JSON.stringify(
              err
            )}`
          );
        }
      });
    } catch (err) {
      log.error("ERROR_ADDING_INTERNAL_IDS", err);
    }

    return itemObsArr;
  }
  return {
    addItemInternalIds,
  };
});
