/**
 * Interface and type definitions for EDI Transaction Classes
 */

/**Contains the overall information needed to process the EDI tranaction */
export interface EdiDataObject {
	prodGuidBool: boolean;
	prodDirectoryBool: boolean;
	decodeContent: boolean;
	prodGUID: string | null;
	sandboxGUID: string | null;
	prodDirectory: string | null;
	referenceDirectory: string | null; 
	testDirectory: string | null;
	documentType: string;
	documentTypeId: number;
	purchasingSoftware: string;
	purchasingSoftwareId: number;
	pushEmailToDB: boolean;
}

/**Contains the information of the EDI file processing transaction */
export interface EdiTransactionRecord {
	ccRecipients?: Array; //custrecord_spl_edi_email_cc_recipient
	controlNumber: string; //ref # that the partner sendscustrecord_spl_edi_trnsctn_cntrl_nmbr
	customer?: number; //int id of custoemr in NS custrecord_edi_customer

	documentTypeId: number; //Int id of document type - custrecord_spl_edi_document_type
	emailMessage?: string; //custrecord_spl_edi_email_message

	emailRecipient?: string; //custrecord_spl_edi_email_recipient
	emailSubject?: string; //custrecord_spl_edi_email_subject
	errorTypeId?: number; //custrecord_edi_transaction_error_type
	integrationEntity: number | null; // the int of of the customer in NS that does purchaisng for this customer  custrecord_edi_integration_entity
	poRefNumber?: string; //# the partners sends as a ref custrecord_spl_edi_po_ref_nmbr
	processingStatus: number; //int id of status - custrecord_spl_processing_status
	purchasingSoftwareId: number; //int id of purchasing software companies in NS custrecord_spl_edi_purchasing_software
	vendor?: number; //int if of the vendor this file is for - opp of customer custrecord_spl_edi_vendor
}
