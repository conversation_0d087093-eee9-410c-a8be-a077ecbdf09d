/**
 * Interface and type definitions for the custom error object
 *
 * <AUTHOR>
 */

export interface CustomErrorObject {
	/** Constructor */
	new (): CustomErrorObject;
	/**Error type - chosen from the ErrorTypes object */
	ERROR_TYPE: { [key: string]: string };
	/**Summary of Error */
	summary: string;
	/**Detailed message of error */
	details: string;
	/**Internal id of the record*/
	recordId: number;
	/**Name/External ID of the record*/
	recordName: string;
	/**The type of the record*/
	recordType: string;
	/**A predefined list of error types */
	ErrorTypes: {[key:string]: string};
	/**Update the custom error object instance */
	updateError: ({
		/** @type {string|null} */ errorType,
		/** @type {string|null} */ summary,
		/** @type {string|null} */ details,
	}) => CustomErrorObject.ERROR_TYPE;
	/** Update the custom error object instance if values are undefined */
	handleUnhanldedError: ({
		/** @type {string|null} */ name,
		/** @type {string|null} */ message,
	}) => void;
	/**Throw an error using the custom error object values */
	throwError: ({
		/**@type {Error}*/ error,
		/**@type {string | null}*/ summaryText,
		/** @type {number|null} */ recordId,
		/** @type {string|null} */ recordName,
		/** @type {string|null} */ recordType,
		/** @type {string|null} */ errorWillBeGrouped,
	}) => void;
}
