/**
 * <PERSON><PERSON><PERSON> called by VLMR and <PERSON>H incoming 850 script to see if valid data to create SO.
 *
 * @NApiVersion 2.1
 */

//@ts-ignore
define(["N/log", "N/search", "CompareAddressLib", "LoDash"], function (
	log,
	search,
	compareAddressesLib,
	_
) {
	function validateData(
		purchaseOrderObj,
		streetAddressSent,
		subsidiary,
		referrenceNumber
	) {
		var errorLog = [];
		var continueToCreate = true;

		validateCustomerIdIsInNetsuite();

		if (streetAddressSent) {
			validateCustomersAddress();
		}
		validateSalesOrderNotDuplicate();

		validateItemsHaveInternalIds();

		validateAtLeastOneLineItemToAdd();

		return {
			errorLog,
			continueToCreate,
			purchaseOrderObj,
		};

		function processFail(errorText, continueProcessing, programmingError) {
			errorLog.push({
				logMessage: errorText,
				programmingError: programmingError,
			});

			if (!continueProcessing) {
				continueToCreate = false;
			}
		}

		function validateCustomerIdIsInNetsuite() {
			try {
				var customerToValidate = search.lookupFields({
					type: "customer",
					id: purchaseOrderObj.customerId, //The customers internal ID
					columns: ["companyname"],
				});

				if (_.isEmpty(customerToValidate)) {
					processFail(
						`Customer account number ${purchaseOrderObj.customerId} is invalid/missing.`,
						false,
						false
					);
				}
			} catch (e) {
				processFail(`Error validating customer. ${e}`, false, true);
			}
		}

		function validateCustomersAddress() {
			try {
				const searchObj = search.create({
					type: "customer",
					filters: [["internalid", "anyof", purchaseOrderObj.customerId]],
					columns: ["address1"],
				});

				const customerAddressesInNetsuiteArr = searchObj
					.run()
					.getRange({
						start: 0,
						end: 50,
					})
					.map((row) => {
						return row.getValue({
							name: "address1",
						});
					});

				let foundMatchingAddress = customerAddressesInNetsuiteArr.some(
					(streetAddressInNetSuite) => {
						return compareAddressesLib.compareAddresses(
							streetAddressInNetSuite,
							streetAddressSent
						);
					}
				);

				if (!foundMatchingAddress) {
					processFail(
						`The customer's address sent (${streetAddressSent}) doesn't match the address in NetSuite. 
                    Please confirm that ${
											subsidiary == 2
												? purchaseOrderObj.patient.revivalId
												: purchaseOrderObj.customerId
										} is the correct account number for this customer.`,
						false,
						false
					);
				}
			} catch (e) {
				processFail(
					`Error validating customer's address. Error: ${e}`,
					false,
					true
				);
			}
		}

		function validateSalesOrderNotDuplicate() {
			try {
				var searchObj = search.create({
					type: "salesorder",
					filters: [
						["type", "anyof", "SalesOrd"],
						"AND",
						["mainline", "is", "T"],
						"AND",
						["subsidiary", "anyof", subsidiary],
						"AND",
						["otherrefnum", "equalto", referrenceNumber],
					],
					columns: [
						search.createColumn({ name: "tranid", label: "Document Number" }),
					],
				});

				var salesOrderArr = searchObj.run().getRange({
					start: 0,
					end: 1000,
				});

				if (salesOrderArr.length > 0) {
					var existingSalesOrder = salesOrderArr[0].getValue(
						salesOrderArr[0].columns[0]
					);

					processFail(
						`${existingSalesOrder} has already been created for PO# ${referrenceNumber}. 
					Please compare the second purchase order submitted with the first purchase order to check for any changes and check with the customer if corrections to the sales order are needed.`,
						false,
						false
					);
				}
			} catch (e) {
				throw e;
			}
		}

		function validateItemsHaveInternalIds() {
			purchaseOrderObj.items.forEach((item) => {
				if (!item.internalId) {
					processFail(
						`${item.itemName} is an incorrect item name or is not in NetSuite and was not added to the sales order.`,
						true,
						false
					);
				}
			});
		}

		function validateAtLeastOneLineItemToAdd() {
			var hasItemWithInternalId = purchaseOrderObj.items.some(function (item) {
				return item.internalId;
			});

			if (!hasItemWithInternalId) {
				processFail("No valid line items to create sales order.", false, false);
			}
		}
	}

	return {
		validateData: validateData,
	};
});
