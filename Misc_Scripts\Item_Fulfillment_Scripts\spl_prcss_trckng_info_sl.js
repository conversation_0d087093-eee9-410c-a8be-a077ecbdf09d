/**
 * @NApiVersion 2.0
 * @NScriptType Suitelet
 */

//@ts-ignore
define([
	"N/log",
	"N/record",
	"N/ui/serverWidget",
	"N/file",
	"N/search",
], function (log, record, serverWidget, file, search) {
	return {
		onRequest: function (context) {
			var request = context.request;
			var response = context.response;

			if (request.method === "GET") {
				var form = serverWidget.createForm({
					title: "Upload Shipping Tracking Info",
				});

				var trackingFileUpload = form.addField({
					id: "custpage_spl_tracking_file",
					type: serverWidget.FieldType.FILE,
					label: "Tracking File",
				});

				form.addSubmitButton({
					label: "Process Tracking Info",
				});

				response.writePage(form);
			} else {
				var trackingFileToSave = request.files.custpage_spl_tracking_file;
				trackingFileToSave.folder = 292676; //Sandbox - 199561
				var fileId = trackingFileToSave.save();

				var trackingFile = file.load({
					id: fileId,
				});

				var iterator = trackingFile.lines.iterator();
				iterator.each(function () {
					return false;
				});

				iterator.each(function (line) {
					var lineValues = line.value.split(",");
					var poDocNum = lineValues[0];
					var trackingInfo = lineValues[1];

					log.debug({ title: "PO ID", details: poDocNum });
					log.debug({ title: "Tracking Info", details: trackingInfo });

					var salesOrderId = getSalesOrderId();
					log.debug({ title: "SO ID", details: salesOrderId });
					setTrackingAndSave();
					createItemFulfillment();
					//transformToInvoice();

					function getSalesOrderId() {
						var salesorderSearch = search.create({
							type: "purchaseorder",
							filters: [
								["type", "anyof", "PurchOrd"],
								"AND",
								["transactionnumbertext", "haskeywords", poDocNum],
							],
							columns: [
								search.createColumn({
									name: "tranid",
									label: "Document Number",
								}),
								search.createColumn({
									name: "internalid",
									join: "createdFrom",
									label: "Internal ID",
								}),
							],
						});

						var resultSet = salesorderSearch.run();
						var result = resultSet.getRange({
							start: 0,
							end: 1,
						});
						return result[0].getValue(resultSet.columns[1]);
					}

					function setTrackingAndSave() {
						var salesOrder = record.load({
							type: record.Type.SALES_ORDER,
							id: salesOrderId,
						});

						salesOrder.setValue("custbody_cust_track_num", trackingInfo);
						salesOrder.save();
					}

					function createItemFulfillment() {
						var itemFulfillment = record.transform({
							fromType: record.Type.SALES_ORDER,
							fromId: salesOrderId,
							toType: record.Type.ITEM_FULFILLMENT,
						});
						itemFulfillment.setValue("custbody4", trackingInfo);
						itemFulfillment.save();
					}

					function transformToInvoice() {
						var invoice = record.transform({
							fromType: record.Type.SALES_ORDER,
							fromId: salesOrderId,
							toType: record.Type.INVOICE,
						});
						invoice.save();
					}

					return true;
				});
			}
		},
	};
});
