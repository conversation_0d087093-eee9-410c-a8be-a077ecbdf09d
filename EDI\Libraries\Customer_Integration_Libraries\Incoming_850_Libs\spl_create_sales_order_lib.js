/**
 * @NApiVersion 2.1
 */

define(["N/log", "N/record", "GetOrderEmailAddressLib"], function (
	log,
	record,
	getOrderEmailAddress
) {
	function createSalesOrder(purchaseOrderObj) {
		var processingErrors = [];

		var salesOrder = createSalesOrderRecord();

		setSalesOrderValues();
		setItems();
		var salesOrderId = saveSalesOrder();
		return {
			salesOrderId,
			processingErrors,
		};

		function _processFail(logMessage, programmingError) {
			processingErrors.push({ logMessage, programmingError });
		}

		function createSalesOrderRecord() {
			return record.create({
				type: record.Type.SALES_ORDER,
				isDynamic: true,
			});
		}

		function setSalesOrderValues() {
			try {
				salesOrder.setValue("customform", 142);
				salesOrder.setValue("entity", purchaseOrderObj.customerInternalId);
				salesOrder.setText("orderstatus", "Pending Approval");
				salesOrder.setValue("memo", purchaseOrderObj.memo);
				salesOrder.setValue("otherrefnum", purchaseOrderObj.poNumber);
				salesOrder.setText(
					"custbody_mhi_supplylinepurchasegrp_so",
					"Not Applicable"
				); //Processing Group
				salesOrder.setText("custbody_mhi_supplylinegpo_so", "Not Applicable"); //GPO
				salesOrder.setText("custbody3", "Not Applicable"); //Purchasing Group
				salesOrder.setValue(
					"custbody_spl_edi_trans_cntrl_num",
					purchaseOrderObj.transactionControlNumber
				);
				salesOrder.setValue("tobeemailed", false);
				salesOrder.setValue(
					"custbody_edi_850_in_json_obj",
					JSON.stringify(purchaseOrderObj)
				);

				salesOrder.setValue(
					"custbody_edi_contact_name",
					purchaseOrderObj.communicationObj.communicationContact || null
				);

				salesOrder.setValue(
					"custbody_edi_contact_email",
					purchaseOrderObj.communicationObj.contactNumber || null
				);

				const emailsString = getOrderEmailAddress
					.getSalesOrderEmailAddress(purchaseOrderObj.customerInternalId)
					?.join(";");

				if (!emailsString) {
					_processFail(`Email not gotten successfully`, true);
				}

				salesOrder.setValue("email", emailsString);
			} catch (e) {
				_processFail(
					`Sales order values not set successfully ${e.message}`,
					true
				);
			}
		}

		function setItems() {
			var items = purchaseOrderObj.items;

			items.forEach(function (item) {
				if (item.internalId) {
					try {
						salesOrder.selectNewLine({
							sublistId: "item",
						});

						salesOrder.setCurrentSublistValue({
							sublistId: "item",
							fieldId: "item",
							value: item.internalId,
						});

						salesOrder.setCurrentSublistValue({
							sublistId: "item",
							fieldId: "quantity",
							value: item.quantity,
						});

						salesOrder.setCurrentSublistValue({
							sublistId: "item",
							fieldId: "custcol_spl_hb_po1_line_number",
							value: item.lineNumber,
						});

						if (item.location) {
							salesOrder.setCurrentSublistValue({
								sublistId: "item",
								fieldId: "location",
								value: item.location, //Union Warehouse or DropShip
							});
						}

						if (item.dropShip) {
							salesOrder.setCurrentSublistValue({
								sublistId: "item",
								fieldId: "createpo",
								value: "DropShip",
							});
						}

						if (item.vendorId) {
							salesOrder.setCurrentSublistValue({
								sublistId: "item",
								fieldId: "povendor",
								value: item.vendorId,
							});
						}

						if (item.itemPricing) {
							//The item has item pricing -> use the rate of the item pricing and set price level to Custom
							salesOrder.setCurrentSublistValue({
								sublistId: "item",
								fieldId: "price",
								value: -1, //Custom Pricing
							});

							salesOrder.setCurrentSublistValue({
								sublistId: "item",
								fieldId: "rate",
								value: item.itemPricing,
							});
						} else if (item.priceLevel) {
							//The item has group pricing -> set price level to the correct price level
							salesOrder.setCurrentSublistValue({
								sublistId: "item",
								fieldId: "price",
								value: item.priceLevel,
							});
						} else if (item.missingPricingInNetSuite) {
							//The item has no item or group pricing -> use the customers price and set the level to Custom
							salesOrder.setCurrentSublistValue({
								sublistId: "item",
								fieldId: "price",
								value: -1, //Custom Pricing
							});

							salesOrder.setCurrentSublistValue({
								sublistId: "item",
								fieldId: "rate",
								value: item.rate,
							});
						} else {
							throw "Error with pricing";
						}

						salesOrder.commitLine({
							sublistId: "item",
						});
					} catch (e) {
						if (e.message == "Please enter a value for amount.") {
							_processFail(
								`${item.itemName} doesn't have any price entered for it. Please add.`
							);
						} else if (
							/You have entered an Invalid Field Value.*for the following field: price/.test(
								e.message
							)
						) {
							_processFail(
								`No pricing set for ${item.itemName} on the item record. Please add.`
							);
						} else {
							_processFail(
								`${item.itemName} not set successfully: ${e.message}`,
								true
							);
						}
					}
				}
			});
		}

		function saveSalesOrder() {
			try {
				return salesOrder.save({
					enableSourcing: true,
					ignoreMandatoryFields: true,
				});
			} catch (e) {
				if (
					e.message ==
					"You must enter at least one line item for this transaction."
				) {
					_processFail(`No valid items to create sales order with.`);
				} else {
					_processFail(`EDI File not saved: ${e.message}`, true);
				}
			}
		}
	}

	return {
		createSalesOrder: createSalesOrder,
	};
});
