/******************************************************************************************************
	Script Name - AVA_MAP_AddCustomersToCertCapture.js
	Company -     Avalara Technologies Pvt Ltd.
******************************************************************************************************/

/**
 *@NApiVersion 2.0
 *@NScriptType MapReduceScript
 */

define(['N/record', 'N/runtime', 'N/url', 'N/https', './utility/AVA_Library', './utility/AVA_CommonServerFunctions'],
	function (record, runtime, url, https, ava_library, ava_commonFunction){
		var CertCapture;

		function getInputData(context){
			try{
				var customerCertCaptureDetails = runtime.getCurrentScript().getParameter({
					name: 'custscript_certcapturecustomerdetails'
				});
				log.debug('getInputData', 'customerCertCaptureDetails = ' + customerCertCaptureDetails);
				if(customerCertCaptureDetails){
					customerCertCaptureDetails = JSON.parse(customerCertCaptureDetails);
					log.debug('getInputData', 'customerCertCaptureDetails length = ' + customerCertCaptureDetails.length);
					if(customerCertCaptureDetails[0].parentbatchid){
						record.submitFields({
							type: 'customrecord_avacertcapturebatch',
							id: customerCertCaptureDetails[0].parentbatchid,
							values: {
								custrecord_ava_certcapturebatchstatus: 'In Progress'
							},
							options: {
								enableSourcing: false,
								ignoreMandatoryFields: true
							}
						});
					}
				}
				return customerCertCaptureDetails;
			}
			catch(e){
				log.error('getInputData', 'ERROR NAME = ' + e.name + ', ERROR TYPE = ' + e.type + ', ERROR MESSAGE = ' + e.message);
			}
		}

		function map(context){
			try{
				var mapObject = context.value;
				mapObject = JSON.parse(mapObject);
				var responseDetails = callCertCaptureAPI(mapObject);
				createCustCertCaptureRecord(mapObject, responseDetails);
				context.write({
					key: mapObject.parentbatchid,
					value: mapObject.parentbatchid
				});
			}
			catch(e){
				log.error('map', 'ERROR NAME = ' + e.name + ', ERROR TYPE = ' + e.type + ', ERROR MESSAGE = ' + e.message);
			}
		}
		
		function summarize(context){
			try{
				var parentRecId = '';
				context.output.iterator().each(function(key, value) {
					if(parentRecId != value){
						parentRecId = record.submitFields({
							type: 'customrecord_avacertcapturebatch',
							id: value,
							values: {
								custrecord_ava_certcapturebatchstatus: 'Completed'
							},
							options: {
								enableSourcing: false,
								ignoreMandatoryFields: true
							}
						});
						log.debug('summarize', 'Status Completed, parentRecId = ' + parentRecId);
					}
					return true;
				});
			}
			catch(e){
				log.error('summarize', 'ERROR NAME = ' + e.name + ', ERROR TYPE = ' + e.type + ', ERROR MESSAGE = ' + e.message);
			}
		}

		function callCertCaptureAPI(mapObject){
			var responseDetails = new Array();
			responseDetails['status'] = '';
			responseDetails['message'] = '';
			
			try{
				var details;
				var avaConfigObjRecvd = ava_library.mainFunction('AVA_LoadValuesToGlobals', '');
				var defCompanyId = ava_library.mainFunction('AVA_GetDefaultCompanyCode', mapObject.subsidiary);
				
				if(avaConfigObjRecvd.AVA_AdditionalInfo3 != null && avaConfigObjRecvd.AVA_AdditionalInfo3.length > 0){
					details = avaConfigObjRecvd.AVA_AdditionalInfo3;
				}
				else{
					details = ava_commonFunction.mainFunction('AVA_General', (avaConfigObjRecvd.AVA_AccountValue + '+' + avaConfigObjRecvd.AVA_AdditionalInfo + '+' + avaConfigObjRecvd.AVA_AdditionalInfo1 + '+' + avaConfigObjRecvd.AVA_AdditionalInfo2));
				}
				
				var avaTax = ava_library.mainFunction('AVA_InitSignatureObject', avaConfigObjRecvd.AVA_ServiceUrl);
				CertCapture = new avaTax.certCapture();
				var customers = createCustomerAvaCertBody(mapObject);
				var customerSave = CertCapture.customerSave(details, defCompanyId[1], customers, avaConfigObjRecvd.AVA_AdditionalInfo3);
				
				try{
					var response = https.post({
						url: customerSave.url,
						body: customerSave.data,
						headers: customerSave.headers
					});
					var responseBody = JSON.parse(response.body);
					
					if(response.code == 201){
						responseDetails['status'] = 'Success';
						responseDetails['message'] = 'The customer is successfully added to CertCapture.';
					}
					else if(responseBody.error.code == 'DuplicateEntry'){
						responseDetails['status'] = 'DuplicateEntry';
						responseDetails['message'] = 'The customer you are trying to add already exists.';
					}
					else{
						responseDetails['status'] = 'Failed';
						responseDetails['message'] = responseBody.error.message;
					}
				}
				catch(err){
					responseDetails['status'] = 'Failed';
					responseDetails['message'] = err.message;
				}
				
				return responseDetails;
			}
			catch(e){
				responseDetails['status'] = 'Failed';
				responseDetails['message'] = e.message;
				log.error('callCertCaptureAPI', 'ERROR NAME = ' + e.name + ', ERROR TYPE = ' + e.type + ', ERROR MESSAGE = ' + e.message);
				return responseDetails;
			}
		}

		function createCustomerAvaCertBody(mapObject){
			var customers = [];
			var customerPhone = mapObject.addressphone;
			if(customerPhone){
				customerPhone = customerPhone.replace(/\(|\)/gi, '');
			}
			var customerFax = mapObject.fax;
			if(customerFax){
				customerFax = customerFax.replace(/\(|\)/gi, '');
			}
			
			var customer = new CertCapture.customer();
			customer.phoneNumber = customerPhone;
			customer.faxNumber = customerFax;
			customer.emailAddress = mapObject.email;
			customer.customerCode = (mapObject.customercode != null ? mapObject.customercode.substring(0, 49) : '');;
			customer.name = (mapObject.contactname != null ? mapObject.contactname.substring(0, 49) : '');
			customer.attnName = mapObject.attention;
			customer.line1 = mapObject.address1;
			customer.line2 = mapObject.address2;
			customer.city = mapObject.city;
			customer.region = mapObject.state;
			customer.postalCode = mapObject.zipcode;
			var returnCountryName = ava_library.mainFunction('AVA_CheckCountryName', mapObject.country);
			customer.country = returnCountryName[1];
			customer.contactName = (mapObject.contactname != null ? mapObject.contactname.substring(0, 49) : '');
			customers.push(customer);
			return customers;
		}

		function createCustCertCaptureRecord(mapObject, responseDetails){
			try{
				var custCertCaptureObj = record.create({
					type: 'customrecord_avacustomerstocertcapture'
				});
				if(nullValidation(mapObject.batchname)){

					custCertCaptureObj.setValue({
						fieldId: 'name',
						value: mapObject.batchname
					});
					custCertCaptureObj.setValue({
						fieldId: 'custrecord_ava_certcapture_batchname',
						value: mapObject.batchname
					});
				}
				if(nullValidation(mapObject.customerinternalid)){
					custCertCaptureObj.setValue({
						fieldId: 'custrecord_ava_certcapture_customer',
						value: mapObject.customerinternalid
					});
				}
				custCertCaptureObj.setValue({
					fieldId: 'custrecord_ava_certcapture_res_status',
					value: responseDetails['status']
				});
				custCertCaptureObj.setValue({
					fieldId: 'custrecord_ava_certcapture_res_message',
					value: responseDetails['message']
				});
				if(nullValidation(mapObject.customercode)){
					custCertCaptureObj.setValue({
						fieldId: 'custrecord_ava_certcapture_customercode',
						value: mapObject.customercode
					});
				}
				if(nullValidation(mapObject.customername)){
					custCertCaptureObj.setValue({
						fieldId: 'custrecord_ava_certcapture_customername',
						value: mapObject.customername
					});
				}
				if(nullValidation(mapObject.attention)){
					custCertCaptureObj.setValue({
						fieldId: 'custrecord_ava_certcapture_attention',
						value: mapObject.attention
					});
				}
				if(nullValidation(mapObject.address1)){
					custCertCaptureObj.setValue({
						fieldId: 'custrecord_ava_certcapture_addressline1',
						value: mapObject.address1
					});
				}
				if(nullValidation(mapObject.address2)){
					custCertCaptureObj.setValue({
						fieldId: 'custrecord_ava_certcapture_addressline2',
						value: mapObject.address2
					});
				}
				if(nullValidation(mapObject.city)){
					custCertCaptureObj.setValue({
						fieldId: 'custrecord_ava_certcapture_city',
						value: mapObject.city
					});
				}
				if(nullValidation(mapObject.zipcode)){
					custCertCaptureObj.setValue({
						fieldId: 'custrecord_ava_certcapture_postalcode',
						value: mapObject.zipcode
					});
				}
				if(nullValidation(mapObject.addressphone)){
					custCertCaptureObj.setValue({
						fieldId: 'custrecord_ava_certcapture_phone',
						value: mapObject.addressphone
					});
				}
				if(nullValidation(mapObject.fax)){
					custCertCaptureObj.setValue({
						fieldId: 'custrecord_ava_certcapture_fax',
						value: mapObject.fax
					});
				}
				if(nullValidation(mapObject.email)){
					custCertCaptureObj.setValue({
						fieldId: 'custrecord_ava_certcapture_email',
						value: mapObject.email
					});
				}
				if(nullValidation(mapObject.state)){
					custCertCaptureObj.setValue({
						fieldId: 'custrecord_ava_certcapture_stateregion',
						value: mapObject.state
					});
				}
				if(nullValidation(mapObject.country)){
					custCertCaptureObj.setValue({
						fieldId: 'custrecord_ava_certcapture_country',
						value: mapObject.country
					});
				}
				custCertCaptureObj.setValue({
					fieldId: 'custrecord_ava_certcapture_batchid',
					value: mapObject.parentbatchid
				});
				var custCertCaptureId = custCertCaptureObj.save({
					enableSourcing: true,
					ignoreMandatoryFields: true
				});
			}
			catch(e){
				log.error('createCustCertCaptureRecord', 'ERROR NAME = ' + e.name + ', ERROR TYPE = ' + e.type + ', ERROR MESSAGE = ' + e.message);
			}
		}

		function nullValidation(value){
			if(value == 'null' || value == null || value == '' || value == ' ' || value == undefined || value == 'undefined' || value == 'NaN' || value == NaN){
				return false;
			}
			else{
				return value;
			}
		}

		return {
			getInputData: getInputData,
			map: map,
			summarize: summarize
		};
	}
);
