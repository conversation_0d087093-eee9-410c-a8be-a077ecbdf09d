/**
 * Class that handles SPL customers pricing
 *
 * @NApiVersion 2.1
 * <AUTHOR>
 */

define(["require", "exports", "N/query", "N/ui/dialog"], (
	/** @type {any} */ require,
	/** @type {any} */ exports
) => {
	const query = require("N/query");
	const dialog = require("N/ui/dialog");

	class SplPriceTier {
		constructor(chosenTierValue, customerRecord) {
			this.chosenTierValue = chosenTierValue;
			this.customerRecord = customerRecord;
		}

		//Uses SPL Pricing Group Tier Price Levels custom record to find the corresponding price level for each price group for this tier.
		setPriceLevels() {
			let getFieldIdQuery = `
      SELECT
        custrecord_price_level_field_id_on_spl_p as pricelevelfieldid
      FROM
        customrecord_spl_price_tiers
      where id = '${this.chosenTierValue}'
      `;

			const queryResults = query
				.runSuiteQL({
					query: getFieldIdQuery,
				})
				.asMappedResults();
			
			if (!queryResults || queryResults.length === 0) {
				dialog.alert({
					title: "ERROR",
					message: `No price tier found with ID ${this.chosenTierValue}. Please check that this tier exists in the system.`,
				});
				return false;
			}

			const splCustomRecordPriceLevelField = queryResults[0].pricelevelfieldid;

			if (!splCustomRecordPriceLevelField) {
				dialog.alert({
					title: "ERROR",
					message: `There is a technical error setting the price levels. Please reach <NAME_EMAIL> for assistance.
          [Technical Info: No price level field ID was chosen for price tier, ${this.chosenTierValue}, go to the SPL Pricing Tiers custom record and correct.]`,
				});

				return false;
			}
			
			let sqlQuery = `
			SELECT
            ${splCustomRecordPriceLevelField} AS pricelevel, 
            custrecord_pricing_group as pricinggroup
			FROM
				customrecord_spl_pricing_group_by_tier
			WHERE
            isinactive = 'F'
			`;

			this.categoryLevelArr = query
				.runSuiteQL({
					query: sqlQuery,
				})
				.asMappedResults();

			if (this.categoryLevelArr.length <= 0) {
				dialog.alert({
					title: "ERROR",
					message: `There are no price levels set up for  price tier ${this.chosenTierValue}! Please set price levels for each product category.`,
				});

				return;
			}

			if (this.categoryLevelArr.filter((obj) => !obj.pricelevel).length > 0) {
				dialog.alert({
					title: "ERROR",
					message: `These price groups (${this.categoryLevelArr
						.filter((obj) => !obj.pricelevel)
						.map((obj) => `Internal ID: ${obj.pricinggroup}`)
						.join(", ")}) don't have a price level chosen. Please add and try again.`,
				});

				return;
			}

			this.categoryLevelArr.forEach((priceObj) => {
				var lineNumber = this.customerRecord.findSublistLineWithValue({
					sublistId: "grouppricing",
					fieldId: "group",
					value: priceObj.pricinggroup,
				});

				if (lineNumber > -1) {
					//Update existing line
					this.customerRecord.selectLine({
						sublistId: "grouppricing",
						line: lineNumber,
					});
				} else {
					//Add new line
					this.customerRecord.selectNewLine({ sublistId: "grouppricing" });

					this.customerRecord.setCurrentSublistValue({
						sublistId: "grouppricing",
						fieldId: "group",
						value: priceObj.pricinggroup,
					});
				}

				this.customerRecord.setCurrentSublistValue({
					sublistId: "grouppricing",
					fieldId: "level",
					value: priceObj.pricelevel,
				});

				this.customerRecord.commitLine({ sublistId: "grouppricing" });
			});
		}
	}

	exports.splPriceTier = SplPriceTier;

	return SplPriceTier;
});
