/**
 * @NApiVersion 2.1
 */

//@ts-ignore
define(["N/log", "N/search"], function (log, search) {
	function getSalesOrderEmailAddress(customerId) {
		if (customerId) {
			const searchObj = search.lookupFields({
				type: "customer",
				id: customerId,
				columns: ["custentity_spl_order_confirmation_email", "email"],
			});

			const orderConfirmationEmail =
				searchObj["custentity_spl_order_confirmation_email"];

			const mainCustomerEmail = searchObj["email"];

			//If the customer has a specific email address for order confirmations, use that email address, otherwise, use the standard email address.
			const recipientEmail = orderConfirmationEmail
				? orderConfirmationEmail
				: mainCustomerEmail;

			//If email address(es) was/were found return an array of email addresses, else return an empty array.
			return recipientEmail ? recipientEmail.split(",") : [];
		}
	}

	function getPurchaseOrderEmailAddress(vendorId) {
		if (vendorId) {
			const searchObj = search.lookupFields({
				type: "vendor",
				id: vendorId,
				columns: ["custentity_spl_purchase_order_email", "email"],
			});

			const purchaseOrderConfirmationEmail =
				searchObj["custentity_spl_purchase_order_email"];

			const mainVendorEmail = searchObj["email"];

			//If the vendor has a specific email address for order confirmations, use that email address, otherwise, use the standard email address.
			const recipientEmail = purchaseOrderConfirmationEmail
				? purchaseOrderConfirmationEmail
				: mainVendorEmail;

			//If email address(es) was/were found return an array of email addresses, else return an empty array.
			return recipientEmail ? recipientEmail.split(",") : [];
		}
	}

	return {
		getSalesOrderEmailAddress,
		getPurchaseOrderEmailAddress,
	};
});
