/**
 * @NApiVersion 2.1
 * @description get the NS internal id for the carrier
 * @param {string} carrierAbbreviation
 * @returns {object} errorLog and carrierId
 */

//@ts-ignore
define(["N/log"], function (log) {
	function getCarrier(carrierScac) {
		//Pull the internal ids of the ship carries in NS, a custom list
		//https://5802576.app.netsuite.com/app/common/custom/custrecordentrylist.nl?rectype=486
		const errorLog = [];
		let carrierId;

		var carrierObjArr = [
			{
				scac: "FXFE",
				name: "FedEx Freight",
				id: 2,
			},
			{
				scac: "JPXS",
				name: "J. P. Express",
				id: 4,
			},
			{
				scac: "PYLE",
				name: "<PERSON><PERSON>",
				id: 9,
			},
			{
				scac: "PITD",
				name: "Pitt-Ohio Express",
				id: 13,
			},
			{
				scac: "FEDX",
				name: "FedEx",
				id: 1,
			},
			{
				scac: "AVRT",
				name: "Averitt Express",
				id: 10,
			},
			{
				scac: "SAIA",
				name: "SAIA",
				id: 12,
			},
			{
				scac: "CNWY",
				name: "Conway Freight Lines",
				id: 23,
			},
			{
				scac: "DYXE",
				name: "Dynamex",
				id: 24,
			},
			{
				scac: "UPSN",
				name: "UPSs",
				id: 3,
			},
			{
				scac: "FDXG",
				name: "FedEx Ground",
				id: 35,
			},
		];

		try {
			var carrierObj = carrierObjArr.find(
				(carrier) => carrier.scac == carrierScac
			);

			if (!carrierObj) {
				errorLog.push(
					`No match found in NetSuite for ship code ${carrierScac}. Please add if needed.`
				);
			}
			carrierId = carrierObj ? carrierObj.id : null;
		} catch (e) {
			errorLog.push(e);
		}
		return { errorLog, carrierId };
	}

	return {
		getCarrier,
	};
});
