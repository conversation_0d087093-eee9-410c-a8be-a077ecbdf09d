/**
 * @NApiVersion 2.x
 */


define([
    'Numeral'
],
    function (
        numeral
    ) {
        function parse846(ediFile, partnerValues) {
            try {
                //var fieldDelimiter = partnerValues.formattingInfo[0].partnerValue;
                var fieldDelimiter = '*'; //change when get new file with correct delimiters

                var segmentDelimiter = partnerValues.formattingInfo[1].partnerValue;
                ediFile = ediFile.split('\r').join('').split('\n').join('');
                var inventoryRefNum = getinventoryRefNum();
                var itemsArr = getItemsArr().map(function (item) {
                    return getItem(item);
                });

                return {
                    inventoryRefNum: inventoryRefNum,
                    items: itemsArr
                };


                function getinventoryRefNum() {
                    var regExp = new RegExp('BIA.*?' + segmentDelimiter);
                    var biaSegment = ediFile.match(regExp)[0].replace(segmentDelimiter, '');
                    return biaSegment.split(fieldDelimiter)[3];
                };

                function getItemsArr() {
                    var regExp = new RegExp('LIN' + '\\' + fieldDelimiter + '\\' + fieldDelimiter + 'VN' + '.*?' + segmentDelimiter + 'LE', 'g');
                    var items = ediFile.match(regExp);
                    return items
                }

                function getItem(ediItem) {
                    return {
                        name: getName(),
                        description: getDescription(),
                        price: getPrice(),
                        quantityAvailable: getQuantityAvailable(),
                        uom: getUom()
                        //CATEGORY, WHY 2 REF LINES, change field dilimters, why some are missing dtm, ctp segments
                    }

                    function getItemSegment(segmentTitle) {
                        var regExp = new RegExp(segmentTitle + '.*?' + segmentDelimiter);
                        try {
                            return ediItem.match(regExp)[0].replace(segmentDelimiter, '');
                        } catch (error) {
                            return false;
                        }
                    };

                    function getName() {
                        var linSegment = getItemSegment('LIN');
                        return linSegment.split(fieldDelimiter)[3];
                    }

                    function getDescription() {
                        var pidSegment = getItemSegment('PID');
                        return pidSegment.split(fieldDelimiter)[5];
                    }

                    function getPrice() {
                        var ctpSegment = getItemSegment('CTP');
                        if (ctpSegment) {
                            return ctpSegment.split(fieldDelimiter)[3];
                        }
                    }

                    function getQuantityAvailable() {
                        var qtySegment = getItemSegment('QTY');
                        return qtySegment.split(fieldDelimiter)[2];
                    }

                    function getUom() {
                        var qtySegment = getItemSegment('QTY');
                        return qtySegment.split(fieldDelimiter)[3];
                    }
                }
            } catch (error) {
                throw error;
            }
        }

        return {
            parse846: parse846
        }
    });