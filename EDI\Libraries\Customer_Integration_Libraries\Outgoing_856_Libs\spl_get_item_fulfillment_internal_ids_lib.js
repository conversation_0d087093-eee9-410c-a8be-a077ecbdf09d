/**
 * @NApiVersion 2.1
 */

//@ts-ignore
define(["N/log", "N/search"], function (log, search) {
	function getInternalIds(customerParentId, purchasingSoftwareId, searchId, customErrorObject) {
		var internalIdsObj = {
			internalIds: [],
		};

		try {
			var itemFulfillmentSearch;

			if (searchId) {
				itemFulfillmentSearch = search.load({
					id: searchId,
				});
			} else {
				var filtersArr = [
					["type", "anyof", "ItemShip"],
					"AND",
					["datecreated", "within", "yesterday"],
					"AND",
					["customersubof", "anyof", customerParentId],
					"AND",
					["mainline", "is", "T"],
					"AND",
					["status", "anyof", "ItemShip:C"], //Status 'Item Fulfillment: Shipped'
					"AND",
					["custbody_spl_edi_856_cntrl_nmbr", "isempty", ""],
					"AND",
					[
						"customer.custentity_spl_purchasing_software",
						"anyof",
						purchasingSoftwareId,
					],
					"AND", 
					["customer.custentity_spl_exclude_from_edi_intgrtn","is","F"],
			  
				];

				itemFulfillmentSearch = search.create({
					type: "transaction",
					filters: filtersArr,
				});
			}

			itemFulfillmentSearch.run().each(function (result) {
				internalIdsObj.internalIds.push({
					//@ts-ignore
					transactionId: result.id,
				});

				return true;
			});
		} catch (err) {
			throw customErrorObject.updateError({
				errorType: customErrorObject.ErrorTypes.NO_VALUE_RETURNED,
				summary: "INTERNAL_IDS_NOT_GOTTEN",
				details: `Error getting Internal IDs: ${err.message}`,
			});
		}

		return internalIdsObj;
	}

	return {
		getInternalIds,
	};
});