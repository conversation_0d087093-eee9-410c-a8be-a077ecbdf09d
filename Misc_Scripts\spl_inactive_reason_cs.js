/**
 * @NApiVersion 2.x
 * @NScriptType ClientScript
 */
//@ts-ignore
define(["N/currentRecord", "N/error", "N/log"], function (
	currentRecord,
	error,
	log
) {
	var userChangedToInactive;
	
	function fieldChanged(context) {
		const currentRecord = context.currentRecord;

		try {
			if (context.fieldId === "isinactive") {
				const inactive = currentRecord.getValue("isinactive");
				const inactiveReason = currentRecord.getField(
					"custitem_spl_inactive_reason"
				);
				if (inactive == 1) {
					inactiveReason.isMandatory = true;
					userChangedToInactive = true;
					if (!inactiveReason.value) {
						alert("Please fill out an inactive reason!");
					}
				} else {
					inactiveReason.isMandatory = false;
					userChangedToInactive = false;
				}
			}
		} catch (e) {
			log.error(e, "Inactive error!");
		}
	}

	function saveRecord(context) {
		const currentRecord = context.currentRecord;
		const inactiveReason = currentRecord.getValue(
			"custitem_spl_inactive_reason"
		);
		if (userChangedToInactive && !inactiveReason) {
			alert("You must fill out an inactive reason!");
			return false;
		}

		return true;
	}

	return {
		fieldChanged: fieldChanged,
		saveRecord: saveRecord,
	};
});
