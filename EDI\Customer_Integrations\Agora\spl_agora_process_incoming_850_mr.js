/**
 * @NApiVersion 2.1
 * @NScriptType MapReduceScript
 * @NAmdConfig /SuiteScripts/config.json
 */

//@ts-ignore
define([
	"require",
	"GetEdiIntegrationsLib",
	"GetEdiFileContents",
	"GetEdiPartnerValuesLib",
	"Parse850Lib",
	"ProcessIncoming850Lib",
	"MoveFileLib",
	"../../../Classes/vlmd_mr_summary_handling",
	"../../../Classes/vlmd_custom_error_object",
	"N/log",
	"N/runtime",
], function (
	require,
	getEdiIntegrationsLib,
	getEdiFileContentsLib,
	getEdiPartnerValuesLib,
	getParsedPurchaseOrderLib,
	processIncoming850Lib,
	moveFileLib
) {
	const log = require("N/log");
	const runtime = require("N/runtime");

	/** @type {import("../../../Classes/vlmd_custom_error_object").CustomErrorObject} */

	const CustomErrorObject = require("../../../Classes/vlmd_custom_error_object");
	const customErrorObject = new CustomErrorObject();

	const dataObj = {
		prodGuidBool: true,
		prodDirectoryBool: true,
		decodeContent: false,
		prodGUID: "7a1a769de204479b95926881a8c19c12",
		sandboxGUID: "********************************",
		testDirectory: `/users/Agora/Test/IN/850`,
		documentType: "Sales Order",
		documentTypeId: 1,
		customerName: "Agora",
		purchasingSoftware: "Agora",
		purchasingSoftwareId: 6,
		pushEmailToDB: true,
	};

	function getInputData() {
		try {
			//Get array of all folders for this purchasing software
			const integrationFoldersArr = getEdiIntegrationsLib.getIntegrationFolders(
				dataObj.purchasingSoftwareId
			);

			if (!integrationFoldersArr || integrationFoldersArr.length <= 0) {
				throw customErrorObject.updateError({
					errorType: customErrorObject.ErrorTypes.UNHANDLED_ERROR,
					summary: "INTEGRATION_FOLDERS_NOT_GOTTEN",
					details: `Integration folders not gotten successfully from lib`,
				});
			}

			const filePathObjsArr = [];

			//Get file names for each folder
			for (const integration of integrationFoldersArr) {
				const remainingUsage = runtime.getCurrentScript().getRemainingUsage();

				/*Not enough usage to get the information for the next folder
					Stop iteration here and return the array with whatever was gotten so far*/
				if (remainingUsage < 10) {
					customErrorObject.updateError({
						errorType: customErrorObject.ErrorTypes.NO_VALUE_RETURNED,
						summary: "SCRIPT_USAGE_MAX_REACHED",
						details: `Execution stopped at getting the files for ${integration.integrationName}.`,
					});

					break;
				}

				dataObj.customerName = integration.integrationName;
				dataObj.prodDirectory = `/users/Agora/${integration.accountNumber}/IN/850`;

				//Get file names for this folder
				const connection = getEdiFileContentsLib.createConnection(dataObj);

				if (!connection) {
					throw customErrorObject.updateError({
						errorType: customErrorObject.ErrorTypes.SFTP_CONNECTION_FAILURE,
						summary: "CONNECTION_NOT_CREATED",
						details: `No connection returned from lib`,
					});
				}

				const fileNames =
					getEdiFileContentsLib.getFileNamesForCurrentIntegration(
						dataObj,
						connection
					);

				//Add fileName obj to filePathObjsArr array
				fileNames.forEach((fileObj) => {
					if (!fileObj || !fileObj.name) {
						throw customErrorObject.updateError({
							errorType: customErrorObject.ErrorTypes.MISSING_VALUE,
							summary: "FILE_OBJ_MISSING_VALUE",
							details: `Missing value for fileObj: ${JSON.stringify(fileObj)}`,
						});
					}

					filePathObjsArr.push({
						integrationAccountNumber: integration.accountNumber,
						integrationName: integration.integrationName,
						fileName: fileObj.name,
						prodDirectory: dataObj.prodDirectory,
					});
				});
			}

			return filePathObjsArr;
		} catch (err) {
			customErrorObject.throwError({
				summaryText: "GET_INPUT_DATA_ERROR",
				error: err,
			});
		}
	}

	function map(context) {
		const customErrorObject = new CustomErrorObject();

		var parsedResult = JSON.parse(context.value);

		var { integrationAccountNumber, integrationName, fileName, prodDirectory } =
			parsedResult;

		try {
			dataObj.prodDirectory = prodDirectory;

			const connection = getEdiFileContentsLib.createConnection(dataObj);

			if (!connection) {
				throw customErrorObject.updateError({
					errorType: customErrorObject.ErrorTypes.SFTP_CONNECTION_FAILURE,
					summary: "CONNECTION_NOT_CREATED",
					details: `No connection returned from lib`,
				});
			}

			const fileContent = getEdiFileContentsLib.getContentForFile(
				connection,
				fileName,
				dataObj
			);

			if (!fileContent) {
				throw customErrorObject.updateError({
					errorType: customErrorObject.ErrorTypes.NO_VALUE_RETURNED,
					summary: "FILE_CONTENT_NOT_GOTTEN",
					details: `Error getting file content for file name/path`,
				});
			}

			const partnerValues = getEdiPartnerValuesLib.getAgoraValues(
				integrationAccountNumber
			);

			if (!partnerValues) {
				throw customErrorObject.updateError({
					errorType: customErrorObject.ErrorTypes.NO_VALUE_RETURNED,
					summary: "PARTNER_VALUES_NOT_GOTTEN",
					details: `Error getting partner values.`,
				});
			}

			const parsedObj = getParsedPurchaseOrderLib.parse850(
				fileContent.content,
				partnerValues,
				integrationName
			);

			if (!parsedObj || !parsedObj.errorLog || parsedObj.errorLog.length > 0) {
				throw customErrorObject.updateError({
					errorType: customErrorObject.ErrorTypes.UNHANDLED_ERROR,
					summary: "ERROR_PARSING_FILE",
					details: `Error parsing 850 edi file: ${
						parsedObj && parsedObj.errorLog
					}`,
				});
			}

			const purchaseOrderObj = parsedObj.purchaseOrderObj;

			if (!purchaseOrderObj) {
				//Move file out of folder and into failure folder so that it won't be picked up each time the script runs.
				try {
					dataObj.referenceDirectory = `/EDI Reference Files/${dataObj.purchasingSoftware}/${integrationAccountNumber}/IN/850`;

					moveFileLib.moveFile(
						dataObj,
						fileName,
						false //Not processed successfully
					);
				} catch (e) {
					throw customErrorObject.updateError({
						errorType: customErrorObject.ErrorTypes.UNHANDLED_ERROR,
						summary: "FILE_NOT_MOVED",
						details: `Error moving file into ${dataObj.referenceDirectory}`,
					});
				}

				throw customErrorObject.updateError({
					errorType: customErrorObject.ErrorTypes.NO_VALUE_RETURNED,
					summary: "ERROR_GETTING_PO_OBJ",
					details: `Purchase order obj not gotten`,
				});
			}

			const errorsProcessingEnd = processIncoming850Lib.processIncoming850(
				dataObj,
				purchaseOrderObj,
				fileName,
				integrationAccountNumber,
				customErrorObject
			);

			if (errorsProcessingEnd.length > 0) {
				throw customErrorObject.updateError({
					errorType: customErrorObject.ErrorTypes.UNHANDLED_ERROR,
					summary: "ERRORS_IN_PROCESS_LIB",
					details: `Errors processing 850 lib: ${errorsProcessingEnd}`,
				});
			}

			context.write(integrationAccountNumber, fileName);
		} catch (err) {
			customErrorObject.throwError({
				summaryText: `${integrationAccountNumber}_ERROR`,
				error: err,
				recordName: fileName ?? "",
				recordType: "INTEGRATION_FOLDER",
				errorWillBeGrouped: true,
			});
		}
	}

	function reduce(context) {
		context.write({
			key: context.key,
			value: context.values,
		});
	}

	function summarize(context) {
		/** @type {import("../../../Classes/vlmd_mr_summary_handling").MapReduceSummaryStageHandling} */
		const StageHandling = require("../../../Classes/vlmd_mr_summary_handling");

		const stageHandling = new StageHandling(context);
		stageHandling.printScriptProcessingSummary();
		stageHandling.printRecordsProcessed();
		stageHandling.printErrors({
			groupErrors: true,
		});
	}

	return {
		getInputData,
		map,
		reduce,
		summarize,
	};
});
