/**
 * @description SL to run the royal discounts for a specific date

 * @NApiVersion 2.1
 * @NScriptType Suitelet
 * 
 * @param {import ("N/types")}
 * <AUTHOR>
 * @module brdg_run_royal_disc_sl
 */

define([
	"require",
	"N/log",
	"N/ui/serverWidget",
	"N/redirect",
	"N/url",
	"N/task",
	"../Classes/vlmd_custom_error_object",
], function (require) {
	const log = require("N/log");
	const serverWidget = require("N/ui/serverWidget");
	const redirect = require("N/redirect");
	const url = require("N/url");
	const task = require("N/task");

	/**@type {import ("../Classes/vlmd_custom_error_object").CustomErrorObject}} */
	const CustomErrorObject = require("../Classes/vlmd_custom_error_object");

	function _redirectToSl(taskId, redirectedFromSl) {
		var suiteletURL = url.resolveScript({
			scriptId: "customscript_brdg_run_royal_discounts_sl",
			deploymentId: "customdeploy_brdg_run_royal_discounts_sl",
			params: {
				task_id: taskId,
				redirected_from_sl: redirectedFromSl,
			},
		});

		redirect.redirect({ url: suiteletURL });
	}

	var exports = {};

	function onRequest(context) {
		const customErrorObject = new CustomErrorObject();
		try {
			if (context.request.method === "GET") {
				const redirectedFromSl =
					context.request.parameters["redirected_from_sl"];
				if (redirectedFromSl) {
					const royalDiscTaskId = context.request.parameters["task_id"];

					var taskStatus = task.checkStatus({
						taskId: royalDiscTaskId,
					});
					do {
						taskStatus = task.checkStatus({
							taskId: royalDiscTaskId,
						});
						if (taskStatus.status == "FAILED") {
							throw customErrorObject.updateError({
								errorType: customErrorObject.ErrorTypes.REFERENCE_ERROR,
								summary: "FAILED_MAP_REDUCE",
								details: `Map Reduce Running Royal Discounts Failed!`,
							});
						}
					} while (
						taskStatus.status != "COMPLETE" &&
						taskStatus.status != "FAILED"
					);

					const postForm = serverWidget.createForm({
						title: " ",
					});
					postForm.addField({
						id: "custpage_task_status",
						label: " ",
						type: serverWidget.FieldType.INLINEHTML,
					}).defaultValue = /*html*/ `
		  <body style="
			  font-family: Georgia, serif;
			  background-color: #f8f8f8;
			  display: flex;
			  justify-content: center;
			  align-items: center;
			  height: 100vh;
			  margin: 0;
		  ">
			  <div style="
				  text-align: center;
				  background-color: #fff;
				  padding: 40px;
				  border-radius: 10px;
				  box-shadow: 0 0 20px rgba(0, 0, 0, 0.1);
			  ">
				  <p style="
					  color: #800080;
					  font-size: 24px;
					  margin-bottom: 20px;
				  ">Royal case discount script status: ${taskStatus.status}</p>
				  <br/><br/>
				  <a href="https://5802576.app.netsuite.com/app/site/hosting/scriptlet.nl?script=3752&deploy=1" style="
					  text-decoration: none;
					  border: 0.5px solid purple;
					  border-radius: 10px;
					  font-size: 22px;
					  padding: 8px 16px;
					  font-family: Georgia, serif;
					  box-shadow: 0 0 30px 0 purple, 0 0 30px 0 purple, 0 0 10px 0 purple inset;
					  color: #800080;
					  background-color: #fff;
					  cursor: pointer;
				  ">Run another date</a>
			  </div>
		  </body>
`;

					context.response.writePage(postForm);
				} else {
					const form = serverWidget.createForm({
						title: "Run Royal Discounts by Date:",
					});

					form.addField({
						id: "custscript_run_specific_transaction_date",
						label: "Date",
						type: serverWidget.FieldType.DATE,
					});

					form.addSubmitButton("Apply Discounts");

					context.response.writePage(form);
				}
			} else {
				const dateParam =
					context.request.parameters[
						"custscript_run_specific_transaction_date"
					];

				const runRoyalDiscMr = task.create({
					taskType: task.TaskType.MAP_REDUCE,
					scriptId: "customscript_brdg_apply_royal_disc_mr",
					deploymentId: "customdeploy_brdg_apply_royal_disc_mr_sl",
					params: {
						custscript_run_specific_transaction_date: dateParam,
					},
				});
				const taskId = runRoyalDiscMr.submit();
				_redirectToSl(taskId, true);
			}
		} catch (e) {
			customErrorObject.throwError({
				summaryText: "ERROR_CALLING_SUITELET",
				error: e,
			});
		}
	}
	exports.onRequest = onRequest;
	return exports;
});
