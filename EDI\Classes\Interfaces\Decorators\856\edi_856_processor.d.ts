/**
 * @description EDI 856 Processor Types and Interface
 *
 * <AUTHOR> <<EMAIL>>
 */

import { Search } from "N/search";
import { SuiteQLObjectReference } from "../../Models/File/edi_outgoing";
import { File } from "N/file";

export interface EDI856ProcessorInterface {
    /** Return SuiteQL query string or Search object to retrieve NetSuite records */
    load(params: {[key:string]: any}): SuiteQLObjectReference;
    /** Load the template and fill out values from the Item Fulfillment record */
    process(): string;
    /** Create the EDI File */
    create(params: {[key:string]: any}): File | null;
    /** Mark the transaction as processed */
    complete(params: {[key:string]: any}): number;
}