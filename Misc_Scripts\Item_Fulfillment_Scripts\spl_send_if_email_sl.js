/**
 * @NApiVersion 2.1
 * @NScriptType Suitelet
 * @NAmdConfig /SuiteScripts/config.json


 * <AUTHOR>
 * @description SL to manually send a specific Item Fulfillment emai to a specific email address. Now, used for testing - can potentially give over to use differently.
 */

define(["N/ui/serverWidget", "N/record", "ItemFulfillmentEmailLib"], function (
  serverWidget,
  record,
  itmFlmntLib
) {
  function onRequest(context) {
    if (context.request.method === "GET") {
      // Create form
      const form = serverWidget.createForm({
        title: "Send Item Fulfillment Email",
      });

      // Add fields
      form.addField({
        id: "custpage_if_id",
        type: serverWidget.FieldType.TEXT,
        label: "Item Fulfillment ID",
      });

      form.addField({
        id: "custpage_email",
        type: serverWidget.FieldType.EMAIL,
        label: "Recipient Email Address",
      });

      form.addSubmitButton({
        label: "Send IF Email",
      });

      context.response.writePage(form);
    } else {
      try {
        const ifId = context.request.parameters.custpage_if_id;
        const emailAddress = context.request.parameters.custpage_email;

        // Load the IF record
        const itemFulfillment = record.load({
          type: record.Type.ITEM_FULFILLMENT,
          id: ifId,
          isDynamic: false,
        });

        // Send the email
        const result = itmFlmntLib.sendItemFulfillmentEmail(itemFulfillment, [
          emailAddress,
        ]);

        // Show result
        const form = serverWidget.createForm({
          title: "Email Test Result",
        });

        const resultField = form.addField({
          id: "custpage_result",
          type: serverWidget.FieldType.LONGTEXT,
          label: "Result",
        });

        resultField.defaultValue = result.processedSuccessfully
          ? "Email sent successfully!"
          : "Error sending email: " + result.errorLog.join("\n");

        context.response.writePage(form);
      } catch (error) {
        // Show error
        const form = serverWidget.createForm({
          title: "Error",
        });

        const errorField = form.addField({
          id: "custpage_error",
          type: serverWidget.FieldType.LONGTEXT,
          label: "Error Message",
        });

        errorField.defaultValue = error.message;

        context.response.writePage(form);
      }
    }
  }

  return {
    onRequest: onRequest,
  };
});
