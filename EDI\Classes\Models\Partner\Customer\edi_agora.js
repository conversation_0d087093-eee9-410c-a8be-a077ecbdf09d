/**
 * @description Partner class for Agora
 *
 * @NApiVersion 2.1
 * <AUTHOR> <<EMAIL>>
 */

define([
    "exports",
    "require",
    "./edi_customer"
], (/** @type {any} */ exports, /** @type {any} */ require) => {
    const { EDICustomer } = require("./edi_customer");

    /**
     * Agora Customer Class
     *
     * @class
     * @typedef {import("../../../Interfaces/Models/Partner/edi_partner").EDICustomerInterface} EDICustomerInterface
     * @extends {EDICustomer}
     * @implements {EDICustomerInterface}
     */
    class EDIAgora extends EDICustomer {
        constructor() {
            super();
            /** @type {string} */
            this.name = "Agora";
            /** @type {string} */
            this.prodDirectory = "/edi/prod/customer/agora/out/832";
            /** @type {string} */
            this.testDirectory = "/edi/test/customer/agora/out/832";
            /** @type {string} */
            this.referenceDirectory = "/edi/reference/customer/agora/out/832";
            /** @type {boolean} */
            this.shouldIncludeIsReplacementFor = true;
            /** @type {boolean} */
            this.shouldIncludeDeactivateItem = true;
        }
    }

    exports.EDIAgora = EDIAgora;
});