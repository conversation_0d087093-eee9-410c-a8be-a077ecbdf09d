/**
 * @description Create an object that contains the notifications from LS Sales Notification section of the customer
 *
 * </br><b>Entry Points:</b> onRequest
 *
 * @NApiVersion 2.1
 * @NScriptType Suitelet
 * 
 * <AUTHOR>
 * @module ltspd_wf_cstmr_sales_notif_sl
 */

define([
	"require",
	"N/log",
	"N/query"
], function (/** @type {any} */ require) {

	const log = require("N/log");
	const query = require("N/query");

	/**
	 * Return the object for LightSpeed containing the notification message
	 *
	 * @param {import("N/types").EntryPoints.Suitelet.onRequestContext} context Suitelet onRequest context
	 */
	function onRequest(context) {
		if (context.request.method === "POST") {
			try {
				const data = context.request.body;
				const jsonObj = JSON.parse(data);
				const walkInCustomerArr = ["0ac54c19-8c55-11ed-f913-3920e12ba852",
					"06e94082-ed01-11ee-f619-7dc003b56e15","06e94082-ed59-11ee-f619-7dc256ca0341","06e94082-ed85-11ee-f619-7dc2195778c8",
					"067f7b40-1e67-11ee-f1f6-7dc1d422e2ed",	"06e94082-ed21-11ee-f619-7dc2a9702784", "06e94082-ed17-11ee-f619-7dc2399171e7",	
					"0ac54c19-8c55-11ed-fd4a-8d314c8d2f66", "0604d568-81ed-11ed-f947-b20066583b1b", "0ac54c19-8c55-11ed-fd4a-8d31f32436c6",
					"0ac54c19-8c55-11ed-fd4a-75a7dd4a4103", "0ac54c19-8c55-11ed-fd4a-75a7faea0064"	];
					//Array of walk-in customer ids, walk in customers should not get any alerts triggered when ready to pay

				if (
					!jsonObj
					|| !jsonObj.event_type
					|| jsonObj.event_type !== "sale.ready_for_payment"
					|| !jsonObj.customer
					|| !jsonObj.customer.id
					|| walkInCustomerArr.includes(jsonObj.customer.id)
				) {
					return;
				}

				const needUpdatedCreditCardInfoField = "custentity_ltspd_update_cc_info";
				const miscMessagesField = "custentity_ltspd_misc_messages";

				const customerResult = query.runSuiteQL({
					query: `
						SELECT
							${needUpdatedCreditCardInfoField} as needUpdatedCreditCardInfo,
							${miscMessagesField} as miscMessages
						FROM
							customer
						WHERE
							custentity_in8_vend_id = '${jsonObj.customer.id}'
					`
				}).asMappedResults();

				if (!customerResult || customerResult.length < 1) {
					return;
				}

				let lightSpeedMessages = [];

				if (customerResult[0].needupdatedcreditcardinfo === "T") {
					lightSpeedMessages.push("Please ask customer to provide an updated credit card to keep on file.");
				}

				if (customerResult[0].miscmessages) {
					const messagesArray = customerResult[0].miscmessages.toString().split(",");
					lightSpeedMessages = lightSpeedMessages.concat(messagesArray);
				}

				if (lightSpeedMessages.length > 0) {
					const salesNotification = JSON.stringify({
						actions: [
							{
								type: "confirm",
								title: "Sales Notifications",
								message: lightSpeedMessages.join("\n")
							},
						],
					});

					context.response.write(salesNotification);
				}
			} catch (/** @type {any} */ err) {
				log.error(err.name, err.message);
			}
		}
	}

	return {
		onRequest: onRequest,
	};
});
