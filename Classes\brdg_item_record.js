/**
 * Item Record class representing a Bridge item record
 *
 * @NAmdConfig /SuiteScripts/config.json
 * @NApiVersion 2.1
 * <AUTHOR>
 * @module BridgeItemRecord
 */

define([
	"require",
	"exports",
	"LoDash",
	"N/log",
	"N/search",
	"N/error",
	"N/query",
	"N/email",
	"N/record",
	"./vlmd_custom_error_object",
], (
	/** @type {any} */ require,
	/** @type {any} */ exports,
	/** @type {any} */ _
) => {
	const log = require("N/log");
	const search = require("N/search");
	const error = require("N/error");
	const query = require("N/query");
	const email = require("N/email");
	const record = require("N/record");
	const CustomErrorObject = require("./vlmd_custom_error_object");

	const bridgePriceLevel = 14;

	/**
	 * Bridge Item Record class
	 *
	 * @class
	 * @type {import("./brdg_item_record").BridgeItemRecord}
	 *
	 */
	class BridgeItemRecord {
		/**
		 * Constructor
		 *
		 * @param {import("N/record").Record | import("N/record").ClientCurrentRecord} itemRecord Item record
		 */
		constructor(itemRecord) {
			this.customErrorObject = new CustomErrorObject();
			this.record = itemRecord;
			this.fields = itemRecord.getFields ? itemRecord.getFields() : [];
			this.id = itemRecord.id;
			this.type = itemRecord.type;
			this.subsidiaries = itemRecord.getValue({
				fieldId: "subsidiary",
			});
			this.upcCode = itemRecord.getValue({
				fieldId: "upccode",
			});
			this.isUpcCodeNotAvailableChecked = itemRecord.getValue({
				fieldId: "custitem_vnyrd_upc_code_not_available",
			});
			this.category = itemRecord.getValue({
				fieldId: "class",
			});
			this.purchasePrice = itemRecord.getValue({
				fieldId: "cost",
			});
			this.ignoreMarkupCalculation = itemRecord.getValue({
				fieldId: "custitem_brdg_dont_use_markup",
			});
			this.doNotSyncToLs = itemRecord.getValue({
				fieldId: "custitem_brdg_dont_sync_to_ls",
			});
			this.itemNumber = itemRecord.getValue({
				fieldId: "itemid",
			});
			this.lightSpeed = {
				Field: {
					VINEYARD_NORTH: "custitem_in8_sync_vend",
					VINEYARD_LOL: "custitem_sync_to_lol",
					VINEYARD_SOUTH: "custitem_sync_to_vs",
					VINEYARD_BERGENFIELD: "custitem_sync_to_vb",
					VINEYARD_EXPRESS: "custitem_sync_to_vexpress",
					VINEYARD_WESTGATE: "custitem_sync_to_vnyrd_westgate",
				},
			};
		}

		resetCustomErrorObject() {
			this.customErrorObject = new CustomErrorObject();
		}

		/**
		 * Retrieves the parent product category from the item collection
		 *
		 * @returns {void}
		 */
		getParentProductCategoryInfo() {
			try {
				if (!this.category) {
					throw error.create({
						name: this.customErrorObject.ErrorTypes.INVALID_DATA,
						message:
							"Cannot retrieve parent category. The item does not have a proper product category.",
					});
				}

				const sqlQuery = `
			SELECT
			CASE
				WHEN basicCategory.fullname LIKE '%:%:%' THEN parentsCategory.parent
				WHEN basicCategory.parent IS NULL THEN basicCategory.id
				ELSE basicCategory.parent
			END AS parent,
			CASE
				WHEN basicCategory.fullname LIKE '%:%:%' THEN parentsCategory.custrecord_brdg_no_set_markup_percentage
				ELSE basicCategory.custrecord_brdg_no_set_markup_percentage
			END AS ignoreMarkup
		FROM
			classification basicCategory
			LEFT OUTER JOIN classification parentsCategory ON parentsCategory.id = basicCategory.parent
		WHERE
			basicCategory.id = ?`;

				/*When it's a double category then find the parent's parent category (Spirits: Whiskey: Bourbon)
				When the category is itself (it is a parent already) take it's own ID (Wine)
				Or else it's a regular category with a regular parent - take the parent id (Wine: Dessert Wine)*/

				const resultIterator = query
					.runSuiteQL({
						query: sqlQuery,
						params: [this.category.toString()],
					})
					.asMappedResults()[0];

				this.parentCategory = resultIterator.parent;
				this.ignoreMarkupCalculation =
					resultIterator.ignoremarkup == "T" ? true : false;

				if (this.ignoreMarkupCalculation) {
					record.submitFields({
						type: this.type,
						id: this.id,
						values: {
							custitem_brdg_dont_use_markup: true,
						},
						options: {
							enableSourcing: false,
							ignoreMandatoryFields: true,
						},
					});
				}
			} catch (e) {
				throw this.customErrorObject.updateError({
					errorType: this.customErrorObject.ErrorTypes.VALUE_NOT_SET,
					summary: "ERROR_GETTING_PARENT_PRODUCT_CATEGORY_INFO",
					details: `Error getting values: ${e}`,
				});
			}
		}

		/**
		 * Retrieve markup amount and record ID from markup record
		 *
		 * @returns {void}
		 */
		calculateMarkupPercentageObj() {
			try {
				if (!this.parentCategory) {
					throw error.create({
						name: this.customErrorObject.ErrorTypes.MISSING_VALUE,
						message:
							"Cannot retrieve markup information. The item does not have a parent product category.",
					});
				}

				if (this.ignoreMarkupCalculation == true) {
					return;
				}

				const markupPercentageSelectQuery = `select custrecord_markup_percentage, id from customrecord_brdg_cat_percentages 
			where custrecord_product_category = ${this.parentCategory}
			 and custrecord_specific_vendor_category = ?`;

				const vendorConditionString = ` and BUILTIN.MNFILTER(
				custrecord_vendors,
				'MN_INCLUDE',
				'',
				'FALSE',
				NULL,
				(
					SELECT
						vendor
					FROM
						itemVendor
					WHERE
						item =  '${this.id}'
				)
			) = 'T'`;

				let resultIterator = query.runSuiteQL({
					//First check to see if there's a markup record for this category and this specific vendor
					query: `${markupPercentageSelectQuery}${vendorConditionString}`,
					params: ["T"],
				});

				if (!_.isEmpty(resultIterator.results)) {
					this.markupAmount = resultIterator.results[0].values[0];
					this.markupRecordId = resultIterator.results[0].values[1];
				} else {
					//If there's not, check for any general percentage record for this parent category
					resultIterator = query.runSuiteQL({
						query: `${markupPercentageSelectQuery}`,
						params: ["F"],
					});

					if (_.isEmpty(resultIterator.results)) {
						email.send({
							author: 3288,
							recipients: "<EMAIL>",
							subject: `Item ${this.itemNumber} added without markup percentage!`,
							body: `This item: ${this.itemNumber} was added and it's category ${this.parentCategory} does 
							not have a matching markup percentage record. Please update.`,
						});
						return;
					}
					this.markupAmount = resultIterator.results[0].values[0];
					this.markupRecordId = resultIterator.results[0].values[1];
				}
			} catch (e) {
				throw this.customErrorObject.updateError({
					errorType: this.customErrorObject.ErrorTypes.VALUE_NOT_SET,
					summary: "ERROR_CALCULATING_MARKUP_PERCENTAGE",
					details: `Error calculating values ${e}`,
				});
			}
		}

		/**
		 * Set the Bridge price level value based on the markup amount
		 *
		 * @returns {void}
		 */
		setMarkupValues() {
			try {
				if (
					this.markupAmount === undefined ||
					!this.markupRecordId ||
					this.ignoreMarkupCalculation == true
				) {
					return;
				}

				record.submitFields({
					type: this.type,
					id: this.id,
					values: {
						custitem_brdg_markup_percentage: this.markupAmount,
						custitem_brdg_markup_per_record: this.markupRecordId,
					},
					options: {
						enableSourcing: false,
						ignoreMandatoryFields: true,
					},
				});

				/*As per BRDG team, we will not be using this as of now
				 const actualCalculatedPrice =
				 	this.markupAmount * this.purchasePrice + this.purchasePrice;
				 const integerOfPrice = Math.floor(actualCalculatedPrice);

				  Taking out the cents part of the price and rounding it to 2 decimal places
				 const centsOfPrice = Number(
				 	(actualCalculatedPrice - integerOfPrice).toFixed(2)
				 );

				  Round the price to end in $.99 / if under $.10 – round down, otherwise round up
				 const priceToUse = integerOfPrice + (centsOfPrice < 0.1 ? -0.01 : +0.99);

				 this.record.setSublistValue({
				 	sublistId: "price",
				 	fieldId: "price_1_",
				 	value: priceToUse,
				 	line: bridgePriceLevel,
				 });
				 */
			} catch (e) {
				throw this.customErrorObject.updateError({
					errorType: this.customErrorObject.ErrorTypes.VALUE_NOT_SET,
					summary: "ERROR_SETTING_MARKUP_VALUES",
					details: `Error setting values: ${e}`,
				});
			}
		}

		/**
		 * Set other price levels to null -  as per BRDG team, we will not be using this as of now
		 *
		 * @returns {void}
		 */
		setOtherPriceFieldsToNull() {
			try {
				const numOfPriceLevels = this.record.getLineCount({
					sublistId: "price",
				});

				for (let line = 0; line < numOfPriceLevels; line++) {
					if (line === bridgePriceLevel) {
						return;
					}

					// @ts-ignore ClientCurrentRecord doesn't have setSublistValue
					this.record.setSublistValue({
						sublistId: "price",
						fieldId: "price_1_",
						value: "",
						line,
					});

					// @ts-ignore ClientCurrentRecord doesn't have setSublistValue
					this.record.setSublistValue({
						sublistId: "price",
						fieldId: "price_2_",
						value: "",
						line,
					});
				}
			} catch (e) {
				throw this.customErrorObject.updateError({
					errorType: this.customErrorObject.ErrorTypes.VALUE_NOT_SET,
					summary: "ERROR_SETTING_OTHER_PRICE_FIELDS_TO_NULL",
					details: `Error setting values: ${e}`,
				});
			}
		}

		/**
		 * Validate the UPC Code used on the item
		 *
		 * @param {import("./brdg_item_record").ItemCache[]} itemCache Optional caching for client-side scripts
		 * @returns {void}
		 */
		validateUpcCode(itemCache) {
			try {
				if (!this.upcCode && !this.isUpcCodeNotAvailableChecked) {
					throw error.create({
						name: this.customErrorObject.ErrorTypes.MISSING_VALUE,
						message:
							"Please enter a upccode or check off 'UPC Code Not Available' if applicable.",
					});
				} else if (this.upcCode) {
					let /** @type {string[]}} */ itemsWithThisUpcCode = [];

					if (itemCache) {
						itemsWithThisUpcCode = itemCache
							.filter(
								(
									/** @type {import("./brdg_item_record").ItemCache} */ {
										id,
										upcCode,
									}
								) => id !== this.id.toString() && upcCode === this.upcCode
							)
							.map(
								(
									/** @type {import("./brdg_item_record").ItemCache} */ {
										itemId,
										displayName,
									}
								) => `${itemId} - ${displayName}`
							);
					} else {
						const searchObj = search.create({
							type: search.Type.ITEM,
							filters: [
								["upccode", "is", this.upcCode],
								"AND",
								["isinactive", "is", "F"],
								"AND",
								["subsidiary", "anyof", 16]
							],
							columns: ["itemid", "displayname"],
						});

						const resultsArr = searchObj.run().getRange({
							start: 0,
							end: 100,
						});

						if (resultsArr.length > 0) {
							//Create an array of all items with this upc code excluding the current item.
							//This covers if it's a new item and the id is null or it's an existing item.
							itemsWithThisUpcCode = resultsArr
								.filter((result) => result.id !== this.id.toString())
								.map((result) => {
									return `${result.getValue("itemid")} - ${result.getValue(
										"displayname"
									)}`;
								});
						}
					}

					if (itemsWithThisUpcCode.length > 0) {
						throw error.create({
							name: this.customErrorObject.ErrorTypes.INVALID_DATA,
							message: `This UPC code already exists for item/s: ${itemsWithThisUpcCode.join(
								", "
							)}`,
						});
					}
				}
			} catch (err) {
				throw this.customErrorObject.throwError({
					error: err,
					summaryText: `Error validating UPC fields`,
				});
			}
		}

		/**
		 * Validate there is no duplicate item number
		 *
		 * @returns {void}
		 */
		validateItemNumber() {
			try {
				const sqlQuery = `SELECT displayname from ITEM where ITEMID = '${this.itemNumber}'
			and BUILTIN.MNFILTER(
				item.subsidiary, 'MN_INCLUDE', '', 'FALSE', NULL, '${this.subsidiaries}'
				) = 'T'  and itemid <> '${this.itemNumber}'
			`;
				const sqlResults = query
					.runSuiteQL({
						query: sqlQuery,
					})
					.asMappedResults();

				if (sqlResults.length > 0) {
					const itemsArr = sqlResults.map((a) => a.displayname);

					throw error.create({
						name: this.customErrorObject.ErrorTypes.INVALID_DATA,
						message: `This item number already exists for: ${itemsArr.join(
							", "
						)}`,
					});
				}
			} catch (e) {
				throw this.customErrorObject.updateError({
					errorType: this.customErrorObject.ErrorTypes.VALUE_NOT_SET,
					summary: this.customErrorObject.ErrorTypes.INVALID_DATA,
					details: `Error validating fields: ${e}`,
				});
			}
		}

		/**
		 * Sync Light Speed if Do not Sync LS is not set
		 *
		 * @returns {void}
		 */
		setSyncToLightSpeedFields() {
			try {
				if (this.doNotSyncToLs) {
					return;
				}

				this.fields.includes(this.lightSpeed.Field.VINEYARD_NORTH) &&
					this.record.setValue({
						fieldId: this.lightSpeed.Field.VINEYARD_NORTH,
						value: true,
					});
				this.fields.includes(this.lightSpeed.Field.VINEYARD_LOL) &&
					this.record.setValue({
						fieldId: this.lightSpeed.Field.VINEYARD_LOL,
						value: true,
					});
				this.fields.includes(this.lightSpeed.Field.VINEYARD_SOUTH) &&
					this.record.setValue({
						fieldId: this.lightSpeed.Field.VINEYARD_SOUTH,
						value: true,
					});
				this.fields.includes(this.lightSpeed.Field.VINEYARD_BERGENFIELD) &&
					this.record.setValue({
						fieldId: this.lightSpeed.Field.VINEYARD_BERGENFIELD,
						value: true,
					});
				this.fields.includes(this.lightSpeed.Field.VINEYARD_EXPRESS) &&
					this.record.setValue({
						fieldId: this.lightSpeed.Field.VINEYARD_EXPRESS,
						value: true,
					});
				this.fields.includes(this.lightSpeed.Field.VINEYARD_WESTGATE) &&
					this.record.setValue({
						fieldId: this.lightSpeed.Field.VINEYARD_WESTGATE,
						value: true,
					});
			} catch (e) {
				throw this.customErrorObject.updateError({
					errorType: this.customErrorObject.ErrorTypes.VALUE_NOT_SET,
					summary: "ERROR_SETTING_SYNC_TO_LS_FIELDS",
					details: `Error setting sync to LS fields: ${e}`,
				});
			}
		}

		/**
		 * Ensure that only Vineyard LLC. is the subsidiary chosen when on a Bridge context
		 *
		 * @returns {void}
		 */
		validateBridgeSubsidiary() {
			try {
				const vineyardLlcSubsidiaryId = "16";

				if (
					Array.isArray(this.subsidiaries) &&
					(this.subsidiaries.length > 1 ||
						this.subsidiaries[0] !== vineyardLlcSubsidiaryId)
				) {
					throw error.create({
						name: "SUBSIDIARY_ERROR",
						message: "Please set the subsidiary to 'Vineyard LLC.' only.",
					});
				}
			} catch (err) {
				throw this.customErrorObject.throwError({
					error: err,
					summaryText: `Error validating Bridge subsidiary`,
				});
			}
		}

		/**
		 * Set the subsidiary to Bridge Management, which is 16
		 * This only applies if the original subsidiary is a Bridge Management subsidiary
		 *   or the subsidiary is an array, and all subsidiaries are Bridge Subsidiaries
		 *
		 * @returns {void}
		 */
		setSubsidiaryToBridgeManagementOnly() {
			try {
				this.record.setValue({
					fieldId: "subsidiary",
					value: Array.isArray(this.subsidiaries) ? ["16"] : "16",
				});
			} catch (e) {
				throw this.customErrorObject.updateError({
					errorType: this.customErrorObject.ErrorTypes.VALUE_NOT_SET,
					summary: "ERROR_SETTING_SUBSIDIARIES",
					details: `Error setting values: ${e}`,
				});
			}
		}

		/**
		 * Set the default values of the item record
		 *
		 * @param {import("N/types").EntryPoints.UserEvent.beforeSubmitContext|import("N/types").EntryPoints.UserEvent.beforeLoadContext} context User event context
		 * @returns {void}
		 */
		setDefaultValues(context) {
			try {
				try {
					if (
						context.type === context.UserEventType.COPY ||
						context.type === context.UserEventType.CREATE
					) {
						const taxSchedule3 = 4;

						this.fields.includes("taxschedule") &&
							this.record.setValue({
								fieldId: "taxschedule",
								value: taxSchedule3,
							});
						this.fields.includes("includechildren") &&
							this.record.setValue({
								fieldId: "includechildren",
								value: true,
							});
						this.fields.includes("costestimatetype") &&
							this.record.setValue({
								fieldId: "costestimatetype",
								value:
									this.type == "kititem"
										? "MEMBERDEFINED"
										: this.type == "serviceitem"
										? "ITEMDEFINED"
										: "LASTPURCHPRICE",
							});
						this.fields.includes("costingmethod") &&
							this.record.setValue({
								fieldId: "costingmethod",
								value: "FIFO",
							});
						this.fields.includes("tracklandedcost") &&
							this.record.setValue({
								fieldId: "tracklandedcost",
								value: true,
							});
						this.fields.includes("custitem_brdg_markup_percentage") &&
							this.record.setValue({
								fieldId: "custitem_brdg_markup_percentage",
								value: "To Be Generated",
							});
					}
				} catch (err) {
					throw this.customErrorObject.updateError({
						errorType: this.customErrorObject.ErrorTypes.UNHANDLED_ERROR,
						summary: "ERROR_SETTING_DEFAULT_VALUES",
						details: `Error setting item default values: ${e}`,
					});
				}
			} catch (e) {
				throw this.customErrorObject.updateError({
					errorType: this.customErrorObject.ErrorTypes.VALUE_NOT_SET,
					summary: "ERROR_SETTING_DEFAULT_VALUES",
					details: `Error setting values: ${e}`,
				});
			}
		}

		/**
		 * Set the Match Bill To Receipt Checkbox to true
		 *
		 * @returns {void}
		 */
		setMatchBillToReceipt() {
			try {
				this.fields.includes("matchbilltoreceipt") &&
					this.record.setValue({
						fieldId: "matchbilltoreceipt",
						value: true,
					});
			} catch (e) {
				throw this.customErrorObject.updateError({
					errorType: this.customErrorObject.ErrorTypes.VALUE_NOT_SET,
					summary: "ERROR_SETTING_MATCH_BILL_TO_RECEIPT",
					details: `Error setting fields: ${e}`,
				});
			}
		}
	}

	exports.BridgeItemRecord = BridgeItemRecord;

	return BridgeItemRecord;
});
