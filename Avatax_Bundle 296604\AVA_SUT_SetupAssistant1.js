/******************************************************************************************************
	Script Name - AVA_SUT_SetupAssistant1.js
	Company -     Avalara Technologies Pvt Ltd.
******************************************************************************************************/

/**
 *@NApiVersion 2.0
 *@NScriptType Suitelet
*/

define(['N/ui/serverWidget', 'N/redirect', './utility/AVA_Library'],
	function(ui, redirect, ava_library){
		function onRequest(context){
			if(context.request.method === 'GET'){
				var avaConfigObjRec = ava_library.mainFunction('AVA_LoadValuesToGlobals', '');
				
				if(avaConfigObjRec['AVA_ConfigFlag'] == false){
					var form = ui.createForm({
						title: 'Setup Assistant'
					});
					form.clientScriptModulePath = './AVA_CLI_Config.js';
					
					var accountValue = form.addField({
						id: 'ava_accvalue',
						label: 'Account Number',
						type: ui.FieldType.TEXT
					});
					accountValue.updateDisplayType({
						displayType: ui.FieldDisplayType.DISABLED
					});
					accountValue.updateDisplaySize({
						width: 40,
						height: 0
					});
					accountValue.setHelpText('This is your Account Number in AvaTax.<br><br> Select Settings > All AvaTax settings. The account number is listed at the top of the page as your Account ID.');
					accountValue.defaultValue = avaConfigObjRec['AVA_AccountValue'];
					
					var serviceUrl = form.addField({
						id: 'ava_serviceurl',
						label: 'Service URL',
						type: ui.FieldType.SELECT
					});
					serviceUrl.addSelectOption({
						value : '0',
						text: 'Production'
					});
					serviceUrl.addSelectOption({
						value : '1',
						text: 'Development'
					});
					serviceUrl.updateDisplayType({
						displayType: ui.FieldDisplayType.DISABLED
					});
					serviceUrl.setHelpText("Select the type of AvaTax account you're connecting to in AvaTax.<br><br> Select 'Development' if you are in NetSuite Sandbox or Test environment.<br><br> Select 'Production' if you are in NetSuite Live / Production environment.");
					serviceUrl.defaultValue = avaConfigObjRec['AVA_ServiceUrl'];
					
					form.addTab({
						id: 'ava_requiredparameter',
						label: 'Required Parameters'
					});
					
					var helpLabel = form.addField({
						id: 'ava_help',
						label: '<b>Connect to Avalara AvaTax</b><br>Please ensure the following are completed in order to start calculating with AvaTax.',
						type: ui.FieldType.LABEL,
						container: 'ava_requiredparameter'
					});
					helpLabel.updateLayoutType({
						layoutType: ui.FieldLayoutType.OUTSIDEBELOW
					});
					helpLabel.updateBreakType({
						breakType: ui.FieldBreakType.STARTROW
					});
					
					var taxAgency = form.addField({
						id: 'ava_taxagency',
						label: '\'Avalara\' created as Tax Agency in NetSuite',
						type: ui.FieldType.CHECKBOX,
						container: 'ava_requiredparameter'
					});
					taxAgency.updateLayoutType({
						layoutType: ui.FieldLayoutType.OUTSIDEBELOW
					});
					taxAgency.updateBreakType({
						breakType: ui.FieldBreakType.STARTROW
					});
					taxAgency.setHelpText({
						help: 'You can set up a vendor as a tax agency as below:<br>By selecting Tax Agency in the Category field on the vendor record.<br><br>You can create new tax agency at Lists > Relationships > Vendors > New.<br>Recommended Tax Agency name is \'Avalara\'.'
					});
					
					var taxcode = form.addField({
						id: 'ava_taxcode',
						label: '\'AVATAX\' created as Tax Code in NetSuite',
						type: ui.FieldType.CHECKBOX,
						container: 'ava_requiredparameter'
					});
					taxcode.updateLayoutType({
						layoutType: ui.FieldLayoutType.OUTSIDEBELOW
					});
					taxcode.updateBreakType({
						breakType: ui.FieldBreakType.STARTROW
					});
					taxcode.setHelpText({
						help: 'A tax code is an entity to remit taxes which is configured to customer or transaction. This default tax code needs to be configured on Avalara Configuration --> Tax Calculation Tab for subsidiaries against which AvaTax tax calculation is to be triggered.<br><br>You can create new tax codes at Setup > Accounting > Tax Codes > New.<br> Recommended Tax Code name is \'AVATAX\' with Tax Rate as 0%.'
					});
					
					form.addButton({
						id: 'ava_back',
						label: 'Previous',
						functionName: 'editCredentials'
					});
					form.addButton({
						id: 'ava_next',
						label: 'Next',
						functionName: 'next'
					});
					form.addButton({
						id: 'ava_createcompany',
						label: 'Create Company',
						functionName: 'createCompany'
					});
					
					context.response.writePage({
						pageObject: form
					});
				}
				else{
					redirect.toSuitelet({
						scriptId: 'customscript_avaconfig_suitlet',
						deploymentId: 'customdeploy_configuration'
					});
				}
			}
		}
		
		return{
			onRequest: onRequest
		};
	}
);
