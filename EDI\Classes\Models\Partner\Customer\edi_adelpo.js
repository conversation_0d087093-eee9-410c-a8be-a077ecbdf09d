/**
 * @description Partner class for Adelpo
 *
 * @NApiVersion 2.1
 * <AUTHOR> <<EMAIL>>
 */

define([
    "exports",
    "require",
    "./edi_customer"
], (/** @type {any} */ exports, /** @type {any} */ require) => {
    const { EDICustomer } = require("./edi_customer");

    /**
     * Adelpo Customer Class
     *
     * @class
     * @typedef {import("../../../Interfaces/Models/Partner/edi_partner").EDICustomerInterface} EDICustomerInterface
     * @extends {EDICustomer}
     * @implements {EDICustomerInterface}
     */
    class EDIAdelpo extends EDICustomer {
        constructor() {
            super();
            /** @type {string} */
            this.name = "Adelpo";
            /** @type {string} */
            this.prodDirectory = "/edi/prod/customer/adelpo/out/832";
            /** @type {string} */
            this.testDirectory = "/edi/test/customer/adelpo/out/832";
            /** @type {string} */
            this.referenceDirectory = "/edi/reference/customer/adelpo/out/832";
            /** @type {boolean} */
            this.shouldIncludeIsReplacementFor = false;
            /** @type {boolean} */
            this.shouldIncludeDeactivateItem = false;
        }
    }

    exports.EDIAdelpo = EDIAdelpo;
});