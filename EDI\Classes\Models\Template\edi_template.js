/**
 * @description EDI class for templates
 *
 * @NApiVersion 2.1
 * <AUTHOR> <<EMAIL>>
 */

define([
    "exports",
], (/** @type {any} */ exports) => {

    /**
     * EDI Template Class
     * 
     * @class
     * @typedef {import("../../Interfaces/Models/Template/edi_template").EDITemplateParams} EDITemplateParams
     */
    class EDITemplate {
        /** @param {EDITemplateParams} params */
        constructor(params){
            this.sd = params.segmentDelimiter;
            this.fd = params.fieldDelimiter;
        };
    }

    exports.EDITemplate = EDITemplate;
});