import { EDI850ParsedTransaction } from "../../../../../EDI/Classes/Decorators/850/edi_850_parsed_transaction";

beforeEach(() =>  {
    jest.resetModules();
});

describe.each([
    {
        content: `ISA*00* *00* *08*925485US00 *ZZ*7188210570 *241001*1630*:*00501*100000014*0*T*>~GS*PO*925485US00*7188210570*20241001*1630*100000014*X*005010~ST*850*0001~BEG*00*SA*6488641234**20110702~CUR*BY*USD~REF*DP*00099~REF*MR*0020~REF*PD*COMA/AA~REF*IA**********~FOB*PP*OR*VARIOUS ZZ ZZ~ITD*05*15*****35~DTM*038*20110709~DTM*037*20110709~DTM*063*20110709~TD5*O****VENDOR ROUTE~N9*L1*SPECIAL INSTRUCTIONS~MTX**NO PRETICKET~MTX**PALLETIZED~N1*BY*SAMS CLUB 6288*UL*0605388000644~N3*5600 STATE ROAD 544~N4*WINTER HAVEN*FL*33881*US~N1*SU*Creoh Usa, LLC~PO1*001*5*CA*20.23**IN***********UP**************VN*1WM*BO*Black~PO4*1~SAC*A*F800***7680***.32*CA*240**03~N9*L1*SPECIAL INSTRUCTIONS~MTX**VOLUME DISCOUNT~AMT*1*101.15~PO1*002*5*CA*20.23**IN***********UP**************VN*2WM*BO*Black~PO4*1~AMT*1*101.15~PO1*003*5*CA*20.23**IN***********UP**************VN*3WM*BO*Black~PO4*1~SAC*A*F800***5400***.24*CA*225**03~N9*L1*SPECIAL INSTRUCTIONS~MTX**PROMO ALLOWANCE (JPI)~AMT*1*101.15~PO1*004*5*CA*20.23**IN***********UP****************BO*Black~PO4*1~SAC*A*F800***5760***.24*CA*240**03~N9*L1*SPECIAL INSTRUCTIONS~MTX**PROMO ALLOWANCE (JAL)~AMT*1*101.15~PO1*005*5*CA*20.23**IN***********UP*123456789149*VN*5WM*BO*Black~PO4*1~SAC*A*F800***8640***.32*CA*270**03~N9*L1*SPECIAL INSTRUCTIONS~MTX**VOLUME DISCOUNT~AMT*1*101.15~CTT*6~AMT*GV*505.75~SE*50*0001~GE*1*100000014~IEA*1*100000014~`,
        segmentBEG: "BEG*00*SA*6488641234**20110702",
        segmentDelimiter: "~",
        fieldDelimiter: "*",
        controlNumber: "100000014",
        department: "00099",
        purchaseOrder: {
            type: "0020",
            number: "6488641234",
            date: "20110702",
        },
        purchaseOrderDate: new Date(2011, 6, 2),
        mustArriveBy: "20110709",
        mustArriveByDate: new Date(2011, 6, 9),
        shipPoint: "VARIOUS ZZ ZZ",
        customer: {
            name: "SAMS CLUB 6288",
            identifier: "0605388000644",
            address: {
                street: "5600 STATE ROAD 544",
                city: "WINTER HAVEN",
                state: "FL",
                zip: "33881",
            }
        },
        supplier: {
            name: "Creoh Usa, LLC",
            identifier: undefined,
            address: {
                street: "",
                city: "",
                state: "",
                zip: "",
            }
        },
        items: [
            {
                quantity: "5",
                units: "CA",
                rate: "20.23",
                name: "*********",
            },
            {
                quantity: "5",
                units: "CA",
                rate: "20.23",
                name: "*********",
            },
            {
                quantity: "5",
                units: "CA",
                rate: "20.23",
                name: "*********",
            },
            {
                quantity: "5",
                units: "CA",
                rate: "20.23",
                name: "*********",
            },
            {
                quantity: "5",
                units: "CA",
                rate: "20.23",
                name: "*********",
            }
        ]
    },
    {
        content: `ISA*00*          *00*          *08*925485US00     *ZZ*7188210570     *240909*2121*:*00501*100000011*0*T*>~GS*PO*925485US00*7188210570*20240909*2121*100000011*X*005010~ST*850*0001~BEG*00*SA*1452651234**20110628~CUR*BY*USD~REF*DP*00099~REF*MR*0007~REF*PD*ONLINEASAP~REF*IA**********~FOB*CC*OR*VARIOUS ZZ                  ZZ~SAC*A*I410***630*6*.4*****02~ITD*05*15*****35~DTM*001*********~DTM*010*********~PID*S*08*VI*FL~TD5*O****CALL **********~N9*L1*SPECIAL INSTRUCTIONS~MTX**==============================IF MULTIPLE DESTINATIONS HAVE~MTX**THE SAME SHIP DATE, PLEASE    SHIP TO FURTHEST DESTINATION~MTX**FIRST AND CLOSEST DESTINATIONSLAST.~MTX**==============================SUPPLIER WILL SHIP ALL~MTX**MERCHANDISE IN ACCORDANCE WITHTHE CURRENT SHIPPING AND~MTX**ROUTING INSTRUCTIONS, WAL-MARTSTORES, INC. (THE "ROUTING~MTX**INSTRUCTIONS"). THE CURRENT   ROUTING INSTRUCTIONS ARE~MTX**AVAILABLE ON RETAIL LINK.     SUPPLIER IS LIABLE FOR THE~MTX**EXCESS TRANSPORTATION COST IF THE DESIGNATED ROUTING IS NOT~MTX**FOLLOWED. IF THE SUPPLIER HAS A QUESTION CONCERNING THE~MTX**ROUTING SELECTED, SUPPLIER    MUST CALL COMPANY S TRAFFIC~MTX**DEPARTMENT BEFORE RELEASING   THE SHIPMENT.~MTX**==============================PLEASE SHIP TO THE FOLLOWING~MTX**WALMART.COM. FC#5926          3101 N HWY 27~MTX**CARROLLTON, GA 30117          678.305.2414~N1*SU*Creoh Usa, LLC~N3*456 Billing St~N4*Chicago*IL*60601~N1*ST*WAL-MART STORES, INC.~N3*123 Test Dr~N4*New York*NY*10001~PO1*001*5*EA*20.23*LE*IN***********UP**************VN*1WM***IZ*9oz*******UK*00************~PO4*2~SDQ*EA*UL***************1***************4~AMT*1*101.15~PO1*001*5*EA*20.23*LE*IN***********UP**************VN*2WM***IZ*9oz*******UK*00************~PO4*2~SDQ*EA*UL***************1***************4~AMT*1*101.15~PO1*001*5*EA*20.23*LE*IN***********UP**************VN*3WM***IZ*9oz*******UK*00************~PO4*2~SDQ*EA*UL***************1***************4~AMT*1*101.15~PO1*001*5*EA*20.23*LE*IN***********UP**************VN*4WM***IZ*9oz*******UK*00************~PO4*2~SDQ*EA*UL***************1***************4~AMT*1*101.15~PO1*002*5*EA*20.23*LE*IN***********UP*123456789149*VN*5WM***********UK*00123456789149~PO4*4~SDQ*EA*UL***************1***************4~AMT*1*101.15~CTT*2~AMT*GV*505.75~SE*55*0001~GE*1*100000011~IEA*1*100000011~`,
        segmentBEG: "BEG*00*SA*1452651234**20110628",
        segmentDelimiter: "~",
        fieldDelimiter: "*",
        controlNumber: "100000011",
        department: "00099",
        purchaseOrder: {
            type: "0007",
            number: "1452651234",
            date: "20110628",
        },
        purchaseOrderDate: new Date(2011, 5, 28),
        mustArriveBy: "",
        mustArriveByDate: null,
        shipPoint: "VARIOUS ZZ                  ZZ",
        customer: {
            name: "WAL-MART STORES, INC.",
            identifier: undefined,
            address: {
                street: "123 Test Dr",
                city: "New York",
                state: "NY",
                zip: "10001",
            }
        },
        supplier: {
            name: "Creoh Usa, LLC",
            identifier: undefined,
            address: {
                street: "456 Billing St",
                city: "Chicago",
                state: "IL",
                zip: "60601",
            }
        },
        items: [
            {
                quantity: "5",
                units: "EA",
                rate: "20.23",
                name: "*********",
            },
            {
                quantity: "5",
                units: "EA",
                rate: "20.23",
                name: "*********",
            },
            {
                quantity: "5",
                units: "EA",
                rate: "20.23",
                name: "*********",
            },
            {
                quantity: "5",
                units: "EA",
                rate: "20.23",
                name: "*********",
            },
            {
                quantity: "5",
                units: "EA",
                rate: "20.23",
                name: "*********",
            }
        ]
    },
    {
        content: `ISA*00*          *00*          *08*925485US00     *ZZ*7188210570     *241001*1630*:*00501*100000014*0*T*>~GS*PO*925485US00*7188210570*20241001*1630*100000014*X*005010~ST*850*0001~BEG*00*SA*6488641234**20110702~CUR*BY*USD~REF*DP*00099~REF*MR*0020~REF*PD*COMA/AA~REF*IA**********~FOB*PP*OR*VARIOUS ZZ                  ZZ~ITD*05*15*****35~DTM*038*20110709~DTM*037*20110709~DTM*063*20110709~TD5*O****VENDOR ROUTE~N9*L1*SPECIAL INSTRUCTIONS~MTX**NO PRETICKET~MTX**PALLETIZED~N1*BY*SAMS CLUB 6288*UL*0605388000644~N3*5600 STATE ROAD 544~N4*WINTER HAVEN*FL*33881*US~N1*SU*Creoh Usa, LLC~PO1*001*5*CA*20.23**IN***********UP**************VN*1WM*BO*Black~PO4*1~SAC*A*F800***7680***.32*CA*240**03~N9*L1*SPECIAL INSTRUCTIONS~MTX**VOLUME DISCOUNT~AMT*1*101.15~PO1*002*5*CA*20.23**IN***********UP**************VN*2WM*BO*Black~PO4*1~AMT*1*101.15~PO1*003*5*CA*20.23**IN***********UP**************VN*3WM*BO*Black~PO4*1~SAC*A*F800***5400***.24*CA*225**03~N9*L1*SPECIAL INSTRUCTIONS~MTX**PROMO ALLOWANCE (JPI)~AMT*1*101.15~PO1*004*5*CA*20.23**IN***********UP****************BO*Black~PO4*1~SAC*A*F800***5760***.24*CA*240**03~N9*L1*SPECIAL INSTRUCTIONS~MTX**PROMO ALLOWANCE (JAL)~AMT*1*101.15~PO1*005*5*CA*20.23**IN***********UP*123456789149*VN*5WM*BO*Black~PO4*1~SAC*A*F800***8640***.32*CA*270**03~N9*L1*SPECIAL INSTRUCTIONS~MTX**VOLUME DISCOUNT~AMT*1*101.15~CTT*6~AMT*GV*505.75~SE*50*0001~GE*1*100000014~IEA*1*100000014~`,
        segmentBEG: "BEG*00*SA*6488641234**20110702",
        segmentDelimiter: "~",
        fieldDelimiter: "*",
        controlNumber: "100000014",
        department: "00099",
        purchaseOrder: {
            type: "0020",
            number: "6488641234",
            date: "20110702",
        },
        purchaseOrderDate: new Date(2011, 6, 2),
        mustArriveBy: "20110709",
        mustArriveByDate: new Date(2011, 6, 9),
        shipPoint: "VARIOUS ZZ                  ZZ",
        customer: {
            name: "SAMS CLUB 6288",
            identifier: "0605388000644",
            address: {
                street: "5600 STATE ROAD 544",
                city: "WINTER HAVEN",
                state: "FL",
                zip: "33881",
            }
        },
        supplier: {
            name: "Creoh Usa, LLC",
            identifier: undefined,
            address: {
                street: "",
                city: "",
                state: "",
                zip: "",
            }
        },
        items: [
            {
                quantity: "5",
                units: "CA",
                rate: "20.23",
                name: "*********",
            },
            {
                quantity: "5",
                units: "CA",
                rate: "20.23",
                name: "*********",
            },
            {
                quantity: "5",
                units: "CA",
                rate: "20.23",
                name: "*********",
            },
            {
                quantity: "5",
                units: "CA",
                rate: "20.23",
                name: "*********",
            },
            {
                quantity: "5",
                units: "CA",
                rate: "20.23",
                name: "*********",
            }
        ]
    }
])("edi_850_line", ({content, segmentBEG, segmentDelimiter, fieldDelimiter, controlNumber, department, purchaseOrder, purchaseOrderDate, mustArriveBy, mustArriveByDate, shipPoint, customer, supplier, items}) => {
    /** @type {EDI850ParsedTransaction} */
    let edi850line;
    beforeEach(() => {
        edi850line = new EDI850ParsedTransaction({content, segmentDelimiter, fieldDelimiter});
    });

    describe("getBEGSegment", () => {
        it("returns the PO Number from the BEG segment", () => {
            expect(edi850line.getPurchaseOrderMetaData(segmentBEG).number).toEqual(purchaseOrder.number);
        });
        it("returns the Date from the BEG segment", () => {
            expect(edi850line.getPurchaseOrderMetaData(segmentBEG).date).toEqual(purchaseOrder.date);
        });
        it("returns blank for the PO type", () => {
            expect(edi850line.getPurchaseOrderMetaData(segmentBEG).type).toEqual("");
        });
    });

    describe("getEntityKey", () => {
        it("returns supplier", () => {
            expect(edi850line.getEntityKey("*SU*")).toEqual("supplier");
        });
        it("returns customer", () => {
            expect(edi850line.getEntityKey("*BY*")).toEqual("customer");
            expect(edi850line.getEntityKey("*ST*")).toEqual("customer");
        });
        it("returns undefined", () => {
            expect(edi850line.getEntityKey("*DUMMY*")).toEqual(undefined);
        });
    });

    describe("parseLine", () => {
        it("returns the object from parsing the content", () => {
            expect(edi850line.parseSegments()).toEqual({
                controlNumber,
                department,
                shipPoint,
                purchaseOrder,
                mustArriveBy,
                customer,
                supplier,
                items,
            });
        });
    });

    describe("constructor", () => {
        it("sets the control number", () => {
            expect(edi850line.controlNumber).toEqual(controlNumber);
        });
        it("sets the transaction type code", () => {
            expect(edi850line.typeCode).toEqual(purchaseOrder.type);
        });
        it("sets the transaction number", () => {
            expect(edi850line.number).toEqual(purchaseOrder.number);
        });
        it("sets the transaction date", () => {
            expect(edi850line.date).toEqual(purchaseOrderDate);
        });
        it("sets the must arrive by date", () => {
            expect(edi850line.mustArriveBy).toEqual(mustArriveByDate);
        });
        it("sets the department", () => {
            expect(edi850line.department).toEqual(department);
        });
        it("sets the ship point", () => {
            expect(edi850line.shipPoint).toEqual(shipPoint);
        });
        it("sets the customer object", () => {
            expect(edi850line.customer).toEqual(customer);
        });
        it("sets the supplier object", () => {
            expect(edi850line.supplier).toEqual(supplier);
        });
        it("retrieves the items", () => {
            expect(edi850line.items).toEqual(items);
        });
    })

});