/**
 * @description MR Summary Stage Handling class used to process the summary stage of a MR script
 *
 * @NAmdConfig /SuiteScripts/config.json
 * @NApiVersion 2.1
 * <AUTHOR> Boxer
 * @module MapReduceSummaryStageHandling
 */

define(["exports", "require", "N/log"], (/**@type {any}*/ exports, /**@type {any}*/ require) => {
	const log = require("N/log");

	/**Map Reduce Summary Stage Handling Class
	 *
	 * @class
	 */
	class MapReduceSummaryStageHandling {
		/**
		 * Constructor
		 *
		 * @param {import ("N/types").EntryPoints.MapReduce.summarizeContext} context
		 */
		constructor(context) {
			this.context = context;
			this.recordsProcessedSuccessfully = [];
			this.processingErrors = [];
		}

		/**
		 * Prints summary of processing details
		 *
		 * @returns {void}
		 */
		printScriptProcessingSummary() {
			log.audit(
				"Processing Summary",
				`Usage Consumed: ${this.context.usage}, Concurrency Number: ${this.context.concurrency}, Number of Yields: ${this.context.yields}`
			);
		}

		/**
		 * Prints the details of all records processed successfully
		 *
		 * @returns {void}
		 *
		 */
		printRecordsProcessed(paramObj) {
			const includeLineBreak = paramObj && paramObj.includeLineBreak;
			const includeKey = paramObj && paramObj.includeKey;
			/**@type {Array<{summarizeBy: string, summarizedResultsString: string}>} resultsLog */
			const resultsLog = [];

			/**
			 * @description Add context results to resultsLog array
			 * @param {string} key
			 * @param {string} value
			 */
			this.context.output.iterator().each(function (key, value) {
				resultsLog.push({ summarizeBy: key, summarizedResultsString: value });

				return true;
			});

			let totalNumberOfRecordsProcessed = 0;
			let recordsProcessedMessage = ``;

			if (resultsLog.length > 0) {
				resultsLog.forEach((result) => {
					let splitResults = result.summarizedResultsString.split(',","');

					//Remove extra characters from text: [ and ] and "
					let formattedResults = splitResults
						.join("\n")
						.replace(/\[|\]|\"/g, "");

					totalNumberOfRecordsProcessed += formattedResults.split(",").length;

					recordsProcessedMessage += `${
						includeKey ? result.summarizeBy + ": " : ""
					}${
						splitResults.length > 1 ? splitResults.length + " results" : ""
					}${formattedResults}${includeLineBreak ? "\n" : ", "}`;

					return true;
				});
			}

			const recordsProcessedTitle = `${totalNumberOfRecordsProcessed} Record${
				resultsLog.length > 1 ? "s" : ""
			} Processed Successfully`;

			log.audit(recordsProcessedTitle, recordsProcessedMessage);

			return { resultsLog, recordsProcessedMessage };
		}

		/**
		 * Prints the details of all errors
		 *
		 * @returns {void}
		 *
		 */
		printErrors(paramObj) {
			const groupErrors = paramObj && paramObj.groupErrors;
			const formatMessageForEmail = paramObj && paramObj.formatMessageForEmail;

			let errorArr = [
				...getErrorInStage(this.context.mapSummary, groupErrors),
				...getErrorInStage(this.context.reduceSummary, groupErrors),
			];

			const errorsTitle = `${errorArr.length} Error${
				errorArr.length > 1 ? "s" : ""
			} in Map/Reduce Stage`;

			let errorsMessage = ``;

			switch (groupErrors) {
				case groupErrors:
					/*Iterate over all errors and combine by error type. 
					Then print the error type as the header and all values as text underneath
					
					Example: 
						MISSING_VALUE
						CS219906, 6007253, CashSale, MAP_ERROR: CS219906, CashSale , NO_COGS_AMOUNT, No cogs amount was found on this transaction.
						CM11013, 6018637, CustCred, MAP_ERROR: CM11013, CustCred , NO_COGS_AMOUNT, No cogs amount was found on this transaction.*/

					const groupedObj = errorArr.reduce(function (
						acumulatorArr,
						errorObj
					) {
						let errorKey = errorObj["name"];

						acumulatorArr[errorKey] = acumulatorArr[errorKey] || [];
						acumulatorArr[errorKey].push(errorObj.message);

						return acumulatorArr;
					},
					{});

					Object.entries(groupedObj).forEach(([key, value]) => {
						errorsMessage += `${key}
${value.join("\n")}

`;
					});

					break;
				default:
					errorArr.forEach(function (error) {
						errorsMessage += error + "\n";
					});
			}

			if (errorArr.length > 0) {
				log.error(errorsTitle, errorsMessage);
			}

			if (formatMessageForEmail) {
				errorsMessage = errorsMessage.split("\n").join("<br/>");
			}

			//@ts-ignore
			function getErrorInStage(stageSummary, groupErrors) {
				/**@type {Array <string>} */
				const errorsLog = [];

				stageSummary.errors
					.iterator()
					.each(function (/**@type {string}*/ key, /**@type {string}**/ value) {
						const { name, message } = JSON.parse(value);
						errorsLog.push(groupErrors ? { name, message } : message);

						return true;
					});

				return errorsLog;
			}

			return { errorArr, errorsMessage };
		}
	}

	exports.StageHandling = MapReduceSummaryStageHandling;

	return MapReduceSummaryStageHandling;
});
