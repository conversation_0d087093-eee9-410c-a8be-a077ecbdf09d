/**
 * @NApiVersion 2.1
 * @NScriptType MapReduceScript
 * @NAmdConfig /SuiteScripts/config.json
 */

//@ts-ignore
define(["N/log", "N/error", "N/search", "N/query", "N/record"], function (
  log,
  error,
  search,
  query,
  record
) {
  //#region Set Parameters
  /*Record Types: 
		See here for a link of all native NS record types:
			https://docs.oracle.com/en/cloud/saas/netsuite/ns-online-help/section_4273205732.html#bridgehead_4441835310
		Here are some common custom records
			Import Record: customrecord_brdg_rip_import : 11722
			Agreement Record: customrecord_rebate_agreement : 5301
			Agreement Details: customrecord_rebate_agreement_detail : 6601
	*/
  let recordType = "INVENTORY_ITEM";

  let searchId = "";

  //NOTE: The SELECT clause should include the record id ONLY
  let sqlQuery = /*sql*/ `SELECT
	item.id,
 FROM
	item 
	JOIN
	   itemsubsidiarymap 
	   ON item.id = itemsubsidiarymap.item 
	   AND itemsubsidiarymap.subsidiary = 1 
 WHERE
	item.isinactive = 'F' 
	AND item.itemtype IN 
	(
	   'InvtPart'
	)
  `;

  function updateRecordAction(netSuiteRecord) {
    netSuiteRecord.setValue({
      fieldId: "customform", //Set the field to update
      value: 96, //Set the value of the field --Supplyline Inventory Form
    });
  }

  function deleteRecord(recordId, recordType) {
    record.delete({
      id: recordId,
      type: recordType,
    });
  }

  //#endregion

  function getInputData(context) {
    var recordIds = [];

    if (searchId) {
      search
        .load({
          id: searchId,
        })
        .run()
        .each((result) => recordIds.push(result.id));
    }

    if (sqlQuery) {
      recordIds = query
        .runSuiteQL({
          query: sqlQuery,
        })
        .results.map((valuesObj) => valuesObj.values[0]);
    }

    return recordIds;
  }

  function map(context) {
    var recordInternalId = JSON.parse(context.value);

    try {
      var netSuiteRecord = record.load({
        type: record.Type[recordType],
        id: recordInternalId,
      });

      updateRecordAction(netSuiteRecord);

      netSuiteRecord.save();

      // deleteRecord(recordInternalId, recordType);

      context.write(recordInternalId, recordInternalId);
    } catch (e) {
      throw error.create({
        name: "Error Updating Record",
        message: e,
      });
    }
  }

  function summarize(context) {
    var recordsProcessedSuccessfullyText =
      getRecordsProcessedSuccessfully(context);
    var errorMessagesText = getErrorMessages(context);
    logResults();

    function getRecordsProcessedSuccessfully(summary) {
      let summaryText = ``;

      summary.output.iterator().each(function (key, value) {
        summaryText += `${key}, `;

        return true;
      });

      return summaryText;
    }

    function getErrorMessages(summary) {
      let errorText = ``;

      summary.mapSummary.errors.iterator().each(function (key, value) {
        var errorMessage = JSON.parse(value).message;

        errorText += `${errorMessage}, 
            `;
        log.debug("Error Updating Records", errorMessage);

        return true;
      });

      return errorText;
    }

    function logResults() {
      if (recordsProcessedSuccessfullyText) {
        log.debug({
          title: `Records Processed Successfully`,
          details: recordsProcessedSuccessfullyText,
        });
      }

      if (errorMessagesText) {
        log.error({
          title: "Error Log",
          details: errorMessagesText,
        });
      }
    }
  }

  return {
    getInputData,
    map,
    summarize,
  };
});
