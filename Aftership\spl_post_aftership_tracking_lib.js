/**
 * @NApiVersion 2.x
 */

define(["N/https", "N/record"], function (https, record) {
	function postTracking(trackingNumber, itemFulfillmentId) {
		var _apiKey = "999aec46-5a8b-4b6c-b13d-402fe50f9883";
		var header = {
			"Content-Type": "application/json",
			"aftership-api-key": _apiKey,
		};
		var apiURL = "https://api.aftership.com/v4/trackings";

		var payload = { tracking: { tracking_number: trackingNumber } };
		payload = JSON.stringify(payload);

		var responseObj = https.post({
			url: apiURL,
			body: payload,
			headers: header,
		});

		var generalStatusCode = responseObj.code;
		var bodyObj = JSON.parse(responseObj.body);
		var statusCode = bodyObj.meta.code;
		var statusMessage = bodyObj.meta.message;
		var data = bodyObj.data;

		log.debug({
			title: "response",
			details: responseObj,
		});
		log.debug({
			title: "statusCode",
			details: statusCode,
		});
		
		var itemFullfillment = record.load({
			type: record.Type.ITEM_FULFILLMENT,
			id: itemFulfillmentId,
		});

		if (generalStatusCode == 201) {
			log.debug(`${trackingNumber} posted to Aftership`);
			itemFullfillment.setValue({
				fieldId: "custbody_spl_trckng_psted_to_aftership",
				value: true,
			});
		}

		if (
			generalStatusCode == 400 ||
			generalStatusCode == 500 ||
			generalStatusCode == 502 ||
			generalStatusCode == 503 ||
			generalStatusCode == 504
		) {
			if (statusCode == 4003) {
				log.debug("Tracking already exists");

				itemFullfillment.setValue({
					fieldId: "custbody_spl_trckng_psted_to_aftership",
					value: true,
				});
			} else {
				if (statusCode == 4012) {
					log.debug(`Set tracking manually: Carrier not detected`);
				} else {
					log.debug(`Set tracking manually: ${statusMessage}`);
				}

				itemFullfillment.setValue({
					fieldId: "custbody_spl_pst_trckng_manually",
					value: true,
				});
			}
		}

		itemFullfillment.save();
	}

	return {
		postTracking: postTracking,
	};
});
