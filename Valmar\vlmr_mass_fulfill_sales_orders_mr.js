/**
 * @NApiVersion 2.1
 * @NScriptType MapReduceScript
 * @NAmdConfig /SuiteScripts/config.json
 */

//@ts-ignore
define([
	"N/log",
	"N/error",
	"N/search",
	"N/record",
	"N/runtime",
	"RecordModuleHelperLib",
], function (log, error, search, record, runtime, recordHelperLib) {
	function getInputData(context) {
		var recordIds = [];

		var myScript = runtime.getCurrentScript();

		var savedSearchInternalId = myScript.getParameter({
			name: "custscript_sales_orders_to_mass_fulfill",
		});

		if (savedSearchInternalId) {
			//Param passed in when on-demand script deployment
			search
				.load({
					id: savedSearchInternalId,
				})
				.run()
				.each((result) => recordIds.push(result.id));
		} else {
			//No param when scheduled script deployment
			search
				.create({
					type: "salesorder",
					filters: [
						["type", "anyof", "SalesOrd"],
						"AND",
						["mainline", "is", "T"],
						"AND",
						["customersubof", "anyof", "13264"], //Revival
						"AND",
						["status", "anyof", "SalesOrd:D", "SalesOrd:B", "SalesOrd:E"], //SO - Partially Fulfilled, Pending Fulfillment, Pending Billing/Partially Fulfilled
						"AND",
						["trandate", "before", "threedaysago"],
					],
				})
				.run()
				.each((result) => recordIds.push(result.id));
		}

		return recordIds;
	}

	function map(context) {
		var recordInternalId = JSON.parse(context.value);

		try {
			var netSuiteRecord = record.load({
				type: record.Type.SALES_ORDER,
				id: recordInternalId,
			});

			var errorFulfillingSO = recordHelperLib.fulfillSO(netSuiteRecord);

			if (errorFulfillingSO) {
				throw errorFulfillingSO;
			}

			context.write(recordInternalId, recordInternalId);
		} catch (e) {
			throw e;
		}
	}

	function summarize(context) {
		var recordsProcessedSuccesfullyText =
			getRecordsProcessedSuccessfully(context);
		var errorMessagesText = getErrorMessages(context);
		logResults();

		//#region Helper Functions
		function getRecordsProcessedSuccessfully(summary) {
			let summaryText = ``;

			summary.output.iterator().each(function (key, value) {
				summaryText += `${key}, `;

				return true;
			});

			return summaryText;
		}

		function getErrorMessages(summary) {
			let errorText = ``;

			summary.mapSummary.errors.iterator().each(function (key, value) {
				var errorMessage = JSON.parse(value).message;

				errorText += `${errorMessage}, 
            `;
				log.error("Error Fulfilling Sales Orders", errorMessage);

				return true;
			});

			return errorText;
		}

		function logResults() {
			if (recordsProcessedSuccesfullyText) {
				log.debug({
					title: `Records Processed Successfully`,
					details: recordsProcessedSuccesfullyText,
				});
			}

			if (errorMessagesText) {
				log.error({
					title: "Error Processing Records",
					details: errorMessagesText,
				});
			}
		}
		//#endregion
	}

	return {
		getInputData,
		map,
		summarize,
	};
});
