/**
 * @NApiVersion 2.1
 * @NScriptType ClientScript
 * @NAmdConfig /SuiteScripts/config.json
 */
//@ts-ignore
define(["N/search", "N/log"], function (search, log) {
	function saveRecord(context) {
		try {
			var currentRecord = context.currentRecord;
			var billId = currentRecord.id;
			var total = currentRecord.getValue("total");
			var fileResultsArr = search.lookupFields({
				type: search.Type.TRANSACTION,
				id: billId,
				columns: "file.internalid",
			})["file.internalid"];
			var fileId;
			if (fileResultsArr.length > 0) {
				fileId = fileResultsArr[0].value;
			}

			if (total >= 500 && !fileId) {
				alert("You must attach a document to save this record.");
				return false;
			}
		} catch (e) {
			log.error("Error with Valmar attachments!", e);
		}
		return true;
	}
	return {
		saveRecord: saveRecord,
	};
});
