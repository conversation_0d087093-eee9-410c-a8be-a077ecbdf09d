/**
 * @description Class containing functions specific to processing Invoices and Credit Memos into Outgoing 810 EDI Files
 *
 * @NApiVersion 2.1
 * <AUTHOR> <<EMAIL>>
 */

define([
  "exports",
  "require",
  "./edi_810_processor",
  "../../Models/File/edi_file",
  "N/file",
  "N/log",
  "N/record",
], (/** @type {any} */ exports, /** @type {any} */ require) => {
  const { EDI810Processor } = require("./edi_810_processor");
  const file = require("N/file");
  const log = require("N/log");
  const record = require("N/record");

  const DEFAULT_SAC_ITEM_CODE = "I410";
  /** @type {import("../../Interfaces/Decorators/810/edi_810_processor").EDI810SACItem[]} */
  const sacItems = [
    { name: "Walmart 1% Discount", code: "C310" },
    { name: "Unsaleable Merchandise Allowance", code: DEFAULT_SAC_ITEM_CODE },
  ];

  /**
   * 810 Invoice Processor Class
   *
   * @typedef {import("../../Models/Transaction/edi_invoice").EDIInvoice} EDIInvoice
   * @typedef {import("../../../../Classes/vlmd_custom_error_object").CustomErrorObject} CustomErrorObject
   * @typedef {import("../../Interfaces/Models/File/edi_outgoing").SuiteQLObjectReference} SuiteQLObjectReference
   * @typedef {import("../../Interfaces/Models/Partner/edi_partner").EDIParsingInformation} EDIParsingInformation
   * @typedef {import("../../Interfaces/Models/Partner/edi_partner").EDITemplate} EDITemplate
   * @typedef {import("../../Interfaces/Decorators/810/edi_810_processor").EDI810ProcessorInterface} EDI810ProcessorInterface
   * @implements {EDI810ProcessorInterface}
   */
  class EDI810InvoiceProcessor extends EDI810Processor {
    /** @param {{[key:string]: any}} params Constructor params */
    constructor(params) {
      super();
      /** @type {CustomErrorObject} */
      this.customError = params.customError;
      /** @type {EDIInvoice} */
      this.invoice = params.invoice;
      /** @type {EDIParsingInformation} */
      this.parsingInformation = params.parsingInformation;
      /** @type {EDITemplate} */
      this.template = params.template;
      /** @type {{base: number, item: number}} */
      this.segmentCount = params.template?.segmentCount;
      /** @type {string} */
      this.fileContent = "";
      /** @type {number} */
      this.purchasingSoftwareId = params.purchasingSoftwareId;
    }

    /**
     * Return the query string or Search object to load the NetSuite Invoices and/or Credit Memos
     *
     * @param {object} [params] Parameters
     * @param {number} [params.searchId] Saved search ID
     * @param {number} [params.customerParent] Customer parent ID
     * @param {Date} [params.startDate] Start date
     * @param {Date} [params.endDate] End date
     * @param {string[]} [params.documentNumbers] Invoice Document Numbers
     * @returns {SuiteQLObjectReference} Query string or Search object to retrieve transaction records
     */
    load(params) {
      try {
        if (!params || !params.customerParent) {
          throw this.customError.updateError({
            errorType: this.customError.ErrorTypes.MISSING_PARAM,
            summary: "NO_PARAMETERS_SUPPLIED",
            details: `Parameters are required for EDI 810 Processor load function. params: ${params}`,
          });
        }

        return {
          type: "suiteql",
          query: `
            SELECT
                ROW_NUMBER() OVER (ORDER BY inv.id) AS result_index,
                inv.id,
                inv.type,
                inv.otherrefnum,
                inv.tranid,
                BUILTIN.DF(inv.custbody_crh_walmart_department),
                ptll.previousdoc,
                inv.trandate,
                tsa.addr1,
                tsa.city,
                tsa.state,
                tsa.zip,
                c.fullname,
                c.custentity_crh_walmart_gln,
                BUILTIN_RESULT.TYPE_INTEGER(edi.custrecord_document_control_number)
            FROM
                transaction inv
            JOIN
                customer c ON inv.entity = c.id
            JOIN
                customer p ON c.parent = p.id
            JOIN
                customrecord_vlmd_edi_integration edi ON p.custentity_spl_edi_integration_record = edi.id
            LEFT JOIN
                previoustransactionlinelink ptll ON inv.id = ptll.nextdoc
            LEFT JOIN
                transactionshippingaddress tsa ON tsa.nkey = inv.shippingaddress
            WHERE
                (inv.type = 'CustInvc' OR inv.type = 'CustCred')
                AND inv.custbody_spl_inv_edi_tran_cntrl_nmbr IS NULL
                AND c.custentity_spl_exclude_from_edi_intgrtn = 'F'
                AND ptll.linktype = 'OrdBill'
                AND (p.id = ${params.customerParent} OR c.id = ${params.customerParent})
                ${
                  params.startDate && params.endDate
                    ? "AND TO_DATE( inv.createddate, 'MM/DD/YYYY' ) BETWEEN TO_DATE('" +
                      params.startDate +
                      "', 'MM/DD/YYYY' ) AND TO_DATE('" +
                      params.endDate +
                      "', 'MM/DD/YYYY')"
                    : ""
                }
                AND inv.createddate >= edi.custrecord_edi_intgrtn_start_date
                AND (
                    SELECT
                        COUNT (*)
                    FROM
                        customrecord_edi_dcn_doc_ctrl_num
                    WHERE
                        customrecord_edi_dcn_doc_ctrl_num.custrecord_edi_dcn_doc_num = inv.tranid
                        AND customrecord_edi_dcn_doc_ctrl_num.custrecord_edi_dcn_doc_num IS NOT NULL
                        AND customrecord_edi_dcn_doc_ctrl_num.isinactive = 'F'
                ) = 0
          `,
          params: [],
        };
      } catch (/** @type {any} */ err) {
        throw this.customError.updateError({
          errorType: this.customError.ErrorTypes.NO_VALUE_RETURNED,
          summary: "FAILED_TO_GENERATE_SEARCH_OR_QUERY",
          details: err.message,
        });
      }
    }

    /**
     * Load the template and fill out values from the Invoice or Credit Memo record
     *
     * @returns {string} EDI file content
     */
    process() {
      try {
        const purchaseOrderDate = this.invoice.getPurchaseOrderDate();
        const purchaseOrderNumber = this.invoice.getPurchaseOrderNumber();
        const departmentNumber = this.invoice.getDepartmentNumber();
        this.invoice.controlNumber += this.invoice.index;

        if (this.template.header) {
          this.fileContent = this.template.header
            .replace(
              /SenderQualifier/g,
              this.parsingInformation.senderInfo[0].value
            )
            .replace(
              /ISASenderId/g,
              this.parsingInformation.senderInfo[1].value
            )
            .replace(
              /ReceiverQualifier/g,
              this.parsingInformation.receiverInfo[0].value
            )
            .replace(
              /ISAReceiverId/g,
              this.parsingInformation.receiverInfo[1].value
            )
            .replace(/ISADate/g, this.parsingInformation.isaGsInfo[0].value)
            .replace(/Time/g, this.parsingInformation.isaGsInfo[2].value)
            .replace(/EdiVersion/g, this.parsingInformation.isaGsInfo[3].value)
            .replace(
              /ControlNumber/g,
              this.invoice.controlNumber.toString().padStart(9, "0")
            )
            .replace(/GSSenderId/g, this.parsingInformation.senderInfo[2].value)
            .replace(
              /GSReceiverId/g,
              this.parsingInformation.receiverInfo[2].value
            )
            .replace(/GSDate/g, this.parsingInformation.isaGsInfo[1].value)
            .replace(/DocumentDate/g, this.invoice.transactionDateFormatted)
            .replace(/DocumentNumber/g, this.invoice.documentNumber)
            .replace(/PoDate/g, purchaseOrderDate)
            .replace(/PoNumber/g, purchaseOrderNumber)
            .replace(/DepartmentNumber/g, departmentNumber)
            .replace(/SupplierName/g, this.invoice.supplierName)
            .replace(/SupplierStreet/g, this.invoice.supplierAddress.street)
            .replace(/SupplierCity/g, this.invoice.supplierAddress.city)
            .replace(/SupplierState/g, this.invoice.supplierAddress.state)
            .replace(/SupplierZip/g, this.invoice.supplierAddress.zip)
            .replace(/SupplierGLN/g, this.invoice.supplierGLN)
            .replace(/CustomerName/g, this.invoice.customerName)
            .replace(/CustomerGLN/g, this.invoice.customerGLN)
            .replace(/CustomerStreet/g, this.invoice.address.street)
            .replace(/CustomerCity/g, this.invoice.address.city)
            .replace(/CustomerState/g, this.invoice.address.state)
            .replace(/CustomerZip/g, this.invoice.address.zip);
        }

        let sacLineCount = 0;
        let itemLineCount = 0;
        if (this.template.item) {
          this.invoice.getLineItems();
          for (let i = 0; i < this.invoice.lineCount; i++) {
            if (sacItems.map(item => item.name).includes(this.invoice.lineItems[i].name)) {
              sacLineCount++;
              this.fileContent += this.template.sac
                .replace(
                  /ChargeType/g,
                  sacItems.find(
                    (item) => item.name === this.invoice.lineItems[i].name
                  )?.code || DEFAULT_SAC_ITEM_CODE
                )
                .replace(
                  /Amount/g,
                  (Math.abs(this.invoice.lineItems[i].amount) * 100)
                    .toFixed(0)
                    .padStart(1, "0")
                );
            } else {
              itemLineCount++;
              this.fileContent += this.template.item
                .replace(
                  /Quantity/g,
                  this.invoice.lineItems[i].quantity.toString()
                )
                .replace(/UOM/g, this.invoice.lineItems[i].units || "EA")
                .replace(/Rate/g, this.invoice.lineItems[i].rate.toString())
                .replace(
                  /GTIN/g,
                  this.invoice.lineItems[i].gtin.padStart(14, "0")
                )
                .replace(
                  /UPC/g,
                  this.invoice.lineItems[i].upc.padStart(12, "0")
                )
                .replace(/ItemName/g, this.invoice.lineItems[i].name)
                .replace(
                  /ItemDescription/g,
                  this.invoice.lineItems[i].description
                );
            }
          }
        }

        if (this.template.summary) {
          this.fileContent += this.template.summary
            .replace(
              /AmountDigits/g,
              this.invoice.subtotal.toString().replace(".", "")
            )
            .replace(
              /SegmentCount/g,
              (
                this.segmentCount.base +
                this.segmentCount.item * itemLineCount +
                sacLineCount
              ).toString()
            )
            .replace(/LineCount/g, itemLineCount.toString())
            .replace(
              /ControlNumber/g,
              this.invoice.controlNumber.toString().padStart(9, "0")
            );
        }
        log.audit("EDI File Content", this.fileContent);

        return this.fileContent;
      } catch (/** @type {any} */ err) {
        throw this.customError.updateError({
          errorType: this.customError.ErrorTypes.NO_VALUE_RETURNED,
          summary: "FAILED_TO_GENERATE_EDI_FILE_STRING",
          details: err.message,
        });
      }
    }

    /**
     * Create the NetSuite File Record
     *
     * @param {{[key:string]: any}} params Parameters
     * @returns {file.File} EDI File
     */
    create(params) {
      try {
        return file.create({
          name: `${params.fileName}_${this.invoice.documentNumber}.810`,
          fileType: file.Type.PLAINTEXT,
          contents: this.fileContent,
        });
      } catch (/** @type {any} */ err) {
        throw this.customError?.updateError({
          errorType: this.customError?.ErrorTypes.FILE_NOT_CREATED,
          summary: "FAILED_TO_CREATE_OUTGOING_810",
          details: `Error creating file object: ${err}`,
        });
      }
    }

    /**
     * Create a Document Control Number for the Invoice
     *
     * @returns {number} Document Control Number record ID
     */
    complete() {
      // Create Document Control Number
      const documentControlNumber = record.create({
        type: "customrecord_edi_dcn_doc_ctrl_num",
        isDynamic: true,
      });
      documentControlNumber.setValue({
        fieldId: "custrecord_edi_dcn_doc_num",
        value: this.invoice.documentNumber,
      });
      documentControlNumber.setValue({
        fieldId: "custrecord_edi_dcn_tran_ctrl_num",
        value: this.invoice.controlNumber,
      });
      const documentControlNumberId = documentControlNumber.save();
      log.audit(
        `Document Control Number Record for ${this.invoice.documentNumber}`,
        documentControlNumberId
      );

      return documentControlNumberId;
    }
  }

  exports.EDI810InvoiceProcessor = EDI810InvoiceProcessor;
});
