/******************************************************************************************************
	Script Name - AVA_CLI_TwoWayIMSBatchDetails.js
	Company -     Avalara Technologies Pvt Ltd.
******************************************************************************************************/

/**
 *@NApiVersion 2.0
 *@NScriptType ClientScript
*/

define(['N/currentRecord', 'N/url', 'N/https'],
	function(currentRecord, url, https){		
		function fieldChanged(context){
			try{
				if(context.fieldId == 'custpage_itemexistcheck'){
					var suiteURL = url.resolveScript({
						scriptId: 'customscript_ava_2wayimsbatchdetails_sui',
						deploymentId: 'customdeploy_ava_2wayimsbatchdetails_sui'
					});
					var currentRecObj = context.currentRecord;
					suiteURL += "&twowayimsbatchid=" + encodeURI(currentRecObj.getValue("ava_batchid")) + "&itemexist=" + encodeURI(currentRecObj.getValue("custpage_itemexistcheck"));
					window.onbeforeunload = null;
					window.location.assign(suiteURL);
				}
			}
			catch(e){
				log.error('fieldChanged', 'ERROR NAME = ' + e.name + ', ERROR TYPE = ' + e.type + ', ERROR MESSAGE = ' + e.message);
			}
		}
		
		function AVA_TwoWayIMSExportCSV(){
			try{
				var cRecord = currentRecord.get();
				var lineCount = cRecord.getLineCount({
					sublistId: 'custpage_twowayimsbatchsublist'
				});
				
				if(lineCount <= 0){
					alert('No item available to download.');
					return false;
				}
				
				var response = https.get({
					url: url.resolveScript({
						scriptId: 'customscript_ava_recordload_suitelet',
						deploymentId: 'customdeploy_ava_recordload',
						params: {'type': 'twowayimscsv', 'twowayimsbatchid': cRecord.getValue('ava_batchid'), 'itemexist': cRecord.getValue('custpage_itemexistcheck')}
					})
				});
				var fieldValues = response.body.split('+');
				window.open(fieldValues[1], '_blank');
				
				https.get({
					url: url.resolveScript({
						scriptId: 'customscript_ava_recordload_suitelet',
						deploymentId: 'customdeploy_ava_recordload',
						params: {'type': 'deletefile', 'FileId': fieldValues[0]}
					})
				});
			}
			catch(e){
				log.error('AVA_TwoWayIMSExportCSV', 'ERROR NAME = ' + e.name + ', ERROR TYPE = ' + e.type + ', ERROR MESSAGE = ' + e.message);
			}
		}
		
		return{
			fieldChanged: fieldChanged,
			AVA_TwoWayIMSExportCSV: AVA_TwoWayIMSExportCSV
		};
	}
);