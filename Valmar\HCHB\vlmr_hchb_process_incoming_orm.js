/**
 * @NApiVersion 2.1
 * @NScriptType MapReduceScript
 * @NAmdConfig /SuiteScripts/config.json
 */

//Summary: Script that pulls Revival ORM files from the SFTP server and sends order emails to the Revival team

//@ts-ignore
define([
	"N/log",
	"GetEdiFileContents",
	"GetEdiPartnerValuesLib",
	"ParseIncomingHl7OrmLib",
	"ProcessIncomingOrmLib",
], function (
	log,
	getEdiFileContentsLib,
	getEdiPartnerValuesLib,
	getParsedHl7OrmLib,
	processIncomingOrmLib
) {
	let dataObj = {
		prodGuidBool: true,
		prodDirectoryBool: true,
		prodDirectory: "/users/Valmar/HCHB/IN/ORM",
		testDirectory: "/users/Valmar/HCHB/TEST/IN/ORM",
		decodeContent: true,
		prodGUID: "7a7bdf03adf445a0a64279e2bb776a3a",
		sandboxGUID: "********************************",
		documentType: "Sales Order",
		documentTypeId: 1,
		purchasingSoftware: "HCHB",
		purchasingSoftwareId: 8,
		pushEmailToDB: true,
		customerName: "Revival",
	};

	function getInputData(context) {
		return getEdiFileContentsLib.getEdiFileContents(dataObj).fileContents;
	}

	function map(context) {
		const parsedResult = JSON.parse(context.value);
		const { content, fileName } = parsedResult;
		const partnerValues = getEdiPartnerValuesLib.getHomecareHomebaseValues();

		const parsedPurchaseOrderObj = getParsedHl7OrmLib.parseHl7Orm(
			content,
			partnerValues
		);

		if (parsedPurchaseOrderObj.errorLog.length <= 0) {
			const programmingErrors = processIncomingOrmLib.processIncomingOrm(
				dataObj,
				fileName,
				parsedPurchaseOrderObj.ormObj
			);

			if (programmingErrors.length > 0) {
				//Write to context if error processing
				context.write(fileName, programmingErrors);
			}
		} else {
			//Write to context if error parsing
			context.write(fileName, parsedPurchaseOrderObj.errorLog);
		}

		return true;
	}

	function reduce(context) {
		context.write({
			key: context.key,
			value: context.values,
		});
	}

	function summarize(context) {
		const mapSummary = context.mapSummary;
		const reduceSummary = context.reduceSummary;

		mapSummary.errors.iterator().each(function (key, value) {
			log.error("Map Error", value);
		});

		reduceSummary.errors.iterator().each(function (key, value) {
			log.error("Reduce Error", value);
		});
	}

	return {
		getInputData: getInputData,
		map: map,
		reduce: reduce,
		summarize: summarize,
	};
});
