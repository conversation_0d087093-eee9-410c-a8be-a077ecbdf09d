/**
 * @description Sets and validates values:
 * - <b>Set subsidiary value:</b> all contexts, all entry points, all deployments
 * - <b>Set item default values:</b> on COPY, all entry points, all deployments
 * - <b>Set Sync to LS:</b> all contexts, all entry points, all deployments
 * - <b>Set last purchase price and validate sales price against lpp:</b> on EDIT, beforeSubmit only, inventory items only
 * - <b>Validate the UPC code:</b> all contexts, beforeSubmit, all deployments
 * - <b>Validate the item number:</b> all contexts, beforeSubmit, all deployments
 * - <b>Set "MatchBillToReceipt":</b> all contexts, beforeSubmit, all deployments
 * - <b>Set the markup amount:</b> all contexts, afterSubmit, all deployments
 *
 * </br><b>Deployed On:</b> BRDG Inventory, Non-Inventory, Kit and Service items
 * </br><b>Execution Context:</b> ALL
 * </br><b>Event Type/Mode:</b> ALL, Price funcs on EDIT only
 * </br><b>Entry Points:</b> beforeLoad, beforeSubmit, afterSubmit
 *
 * @NApiVersion 2.1
 * @NScriptType UserEventScript
 * @NAmdConfig /SuiteScripts/config.json
 *
 * <AUTHOR> Prospero
 * @module brdg_item_ue
 */

define([
	"require",
	"./Libraries/brdg_helper_functions_lib",
	"./Libraries/brdg_update_item_lib",
	"../Classes/brdg_item_record",
	"N/error",
], (/** @type {any} */ require) => {
	const bridgeHelperFunctionsLib = require("./Libraries/brdg_helper_functions_lib");
	const updateItemFieldsLib = require("./Libraries/brdg_update_item_lib");
	/** @type {import("../Classes/brdg_item_record").BridgeItemRecord} */
	const BridgeItemRecord = require("../Classes/brdg_item_record");
	const error = require("N/error");

	return {
		/**
		 * Set the default item record values
		 * Sync Light Speed fields
		 *
		 * @param {import("N/types").EntryPoints.UserEvent.beforeLoadContext} context Before load script context
		 */
		beforeLoad: (context) => {
			const { newRecord } = context;
			const bridgeItemRecord = new BridgeItemRecord(newRecord);
			const bridgeSubsidiaries =
				bridgeHelperFunctionsLib.getBridgeSubsidiaries();
			const isBridgeSubsidiary = bridgeHelperFunctionsLib.isBridgeSubsidiary(
				bridgeItemRecord.subsidiaries,
				bridgeSubsidiaries
			);

			if (!isBridgeSubsidiary) {
				return;
			}

			bridgeItemRecord.setSubsidiaryToBridgeManagementOnly();
			if (
				context.type === context.UserEventType.CREATE ||
				context.type === context.UserEventType.COPY
			) {
				bridgeItemRecord.setDefaultValues(context);
			}
			bridgeItemRecord.setSyncToLightSpeedFields();
			bridgeItemRecord.setMatchBillToReceipt();
		},

		/**
		 * Validate sales price against last purchase price
		 * Calculates and sets the marked up price for sale
		 * Validate UPC Code used on the item
		 *
		 * @param {import("N/types").EntryPoints.UserEvent.beforeSubmitContext} context Before submit script context
		 * @returns {void}
		 */
		beforeSubmit: (context) => {
			const { newRecord } = context;
			const bridgeItemRecord = new BridgeItemRecord(newRecord);
			const bridgeSubsidiaries =
				bridgeHelperFunctionsLib.getBridgeSubsidiaries();
			const isBridgeSubsidiary = bridgeHelperFunctionsLib.isBridgeSubsidiary(
				bridgeItemRecord.subsidiaries,
				bridgeSubsidiaries
			);

			if (!isBridgeSubsidiary) {
				return;
			}

			if (bridgeItemRecord.type === "inventoryitem") {
				// We can't have an LPP if it's a new item, no PO in the system for this item yet
				if (context.type === context.UserEventType.EDIT) {
					const lastPurchasePriceObj = updateItemFieldsLib.getLastPurchasePrice(
						bridgeItemRecord.id,
						bridgeItemRecord.type
					);
					updateItemFieldsLib.setLastPurchasePricesFields(
						newRecord,
						lastPurchasePriceObj
					);
					const messageText = updateItemFieldsLib.validateSalesPrice(
						lastPurchasePriceObj,
						newRecord
					);

					if (messageText) {
						throw error.create({
							name: "ITEM_SALES_PRICE_ERROR",
							message:
								"This item has a sales price lower then the last purchase price: " +
								messageText,
						});
					}
				}

				// LDEV-738: Commenting out until operations is ready for the change.
				//	bridgeItemRecord.setOtherPriceFieldsToNull();
			}

			bridgeItemRecord.validateUpcCode();
			bridgeItemRecord.validateItemNumber();
			bridgeItemRecord.setSubsidiaryToBridgeManagementOnly();
			if (
				context.type === context.UserEventType.CREATE ||
				context.type === context.UserEventType.COPY
			) {
				bridgeItemRecord.setDefaultValues(context);
			}
			bridgeItemRecord.setSyncToLightSpeedFields();
			bridgeItemRecord.setMatchBillToReceipt();
		},

		/**
		 *
		 * @param {import("N/types").EntryPoints.UserEvent.afterSubmitContext} context After submit script context
		 * @returns {void}
		 */
		afterSubmit: (context) => {
			const { newRecord } = context;
			/** @type {import("../Classes/brdg_item_record").BridgeItemRecord} */
			const bridgeItemRecord = new BridgeItemRecord(newRecord);
			const bridgeSubsidiaries =
				bridgeHelperFunctionsLib.getBridgeSubsidiaries();
			const isBridgeSubsidiary = bridgeHelperFunctionsLib.isBridgeSubsidiary(
				bridgeItemRecord.subsidiaries,
				bridgeSubsidiaries
			);

			if (!isBridgeSubsidiary) {
				return;
			}

			if (!bridgeItemRecord.ignoreMarkupCalculation) {
				bridgeItemRecord.getParentProductCategoryInfo();
				bridgeItemRecord.calculateMarkupPercentageObj();
				bridgeItemRecord.setMarkupValues();
			}
		},
	};
});
