/**
 * @NApiVersion 2.1
 * @NScriptType scheduledscript
 * @NAmdConfig /SuiteScripts/config.json
 */

//@ts-ignore
define(["N/log", "N/record", "N/search", "GetRepToAssignToLib"] /*
        TODO: fix regex
        TODO: see if need to remove duplicates from emails arr - if saved search pulls it up
        */, function (log, record, search, getRepToAssignToLib) {
	function execute() {
		var emailsArr = getNewEmailsWithinSevenDays();
		var emailsAlreadyProcessedArr = getEmailsAlreadyProcessed();

		emailsArr = emailsArr.filter((email) =>
			emailsAlreadyProcessedArr.includes(email)
		);

		emailsArr.forEach((loggedEmail) => {
			var assignTaskTo = getRepToAssignToLib.getRepBasedOffOfTaskRatio();
			var messageText = getMessageText(loggedEmail);
			createNewTask(loggedEmail, assignTaskTo, messageText);
			setEmailAsProcessed(loggedEmail.internalId);
		});

		function getNewEmailsWithinSevenDays() {
			var searchObj = search.create({
				type: "message",
				filters: [
					[
						"formulanumeric: INSTR({message}, '<!-- Created with SuperSync Email for Outlook -->')",
						"greaterthan",
						"0",
					],
					"AND",
					["formulanumeric: {now}-{messagedate}", "lessthan", "7"],
					// "AND",
					// ["internalid", "anyof", "2399121"], //TODO : Remove
				],
				columns: [
					search.createColumn({ name: "messagedate", label: "Date" }),
					search.createColumn({ name: "authoremail", label: "Author Email" }),
					search.createColumn({ name: "recipientemail", label: "To" }),
					search.createColumn({ name: "cc", label: "Cc" }),
					search.createColumn({
						name: "companyname",
						join: "customer",
						label: "Company Name",
					}),
					search.createColumn({
						name: "internalid",
						join: "customer",
						label: "Internal ID",
					}),
					search.createColumn({ name: "subject", label: "Subject" }),
					search.createColumn({ name: "message", label: "Message" }),

					search.createColumn({
						name: "recipient",
						label: "Primary Recipient",
					}),
					search.createColumn({ name: "recipients", label: "Recipients" }),
				],
			});
			var emails = [];
			searchObj.run().each(function (result) {
				emails.push({
					internalId: result.id,
					messageDate: result.getValue(searchObj.columns[0]),
					emailAuthor: result.getValue(searchObj.columns[1]),
					recipient: result.getValue(searchObj.columns[2]),
					cc: result.getValue(searchObj.columns[3]),
					customerName: result.getValue(searchObj.columns[4]),
					customerInternalId: result.getValue(searchObj.columns[5]),
					subject: result.getValue(searchObj.columns[6]),
					message: result.getValue(searchObj.columns[7]),
				});
				return true;
			});

			var firstElement = emails[0];
			return [firstElement];
			//return emails;
		}

		function getEmailsAlreadyProcessed() {
			var searchObj = search.create({
				type: "customrecord_spl_prcsd_logged_emails",
				columns: [
					search.createColumn({
						name: "custrecord_spl_lgd_eml_prcsd",
						label: "Internal ID",
					}),
				],
			});

			var processedEmails = [];

			searchObj.run().each(function (result) {
				processedEmails.push({
					internalId: result.getValue(searchObj.columns[0]),
				});
				return true;
			});

			return processedEmails;
		}

		function getMessageText(loggedEmail) {
			// var regExp = /<!-.*?<\/head>/g;
			var regExp1 = /<[^<>]*>/gm;
			var regExp2 = /v\\:.*?shape.*?VML\);}/gm;
			var messageText = loggedEmail.message.replace(regExp1, "");

			return `Date: ${loggedEmail.messageDate}
            Customer: ${loggedEmail.customerName}
            Customer's Email: ${loggedEmail.emailAuthor}
            Recipient: ${loggedEmail.recipient}
            Cc: ${loggedEmail.recipient}
            
            Subject: ${loggedEmail.subject}
            
            Message: ${messageText}`;
		}

		function createNewTask(loggedEmail, assignTaskTo, messageText) {
			var task = record.create({
				type: record.Type.TASK,
			});

			task.setValue({
				fieldId: "customform",
				value: 103, //Supplyline Task Form
			});

			task.setValue({
				fieldId: "title",
				value: `${loggedEmail.customerName}: New Quote Request`,
			});

			task.setValue({
				fieldId: "assigned",
				value: 3288, //TODO: Change to assignTaskTo,
			});

			task.setValue({
				fieldId: "company",
				value: 3111, //TODO: change to loggedEmail.customerInternalId,
			});

			task.setValue({
				fieldId: "custevent_spl_task_type",
				value: 25, //New Quote Request
			});

			task.setValue({
				fieldId: "status",
				value: "NOTSTART",
			});

			task.setValue({
				fieldId: "message",
				value: messageText,
			});

			task.save();
		}

		function setEmailAsProcessed(internalId) {
			var emailIsProcessed = record.create({
				type: "customrecord_spl_prcsd_logged_emails",
			});

			emailIsProcessed.setValue("custrecord_spl_lgd_eml_prcsd", internalId);
			emailIsProcessed.save();
		}
	}

	return {
		execute: execute,
	};
});
