/**
 * @NApiVersion 2.0
 * @NScriptType Suitelet
 */

//@ts-ignore
define(["N/log", "N/search", "N/redirect"], function (log, search, redirect) {
	return {
		onRequest: function (context) {
			var request = context.request;

			if (request.method === "GET") {
				var customerId = context.request.parameters["custId"];
				var customerName = context.request.parameters["custName"];

				log.debug({
					title: "Customer ID",
					details: customerId,
				});

				var mySearch = search.create({
					type: "transaction",
					title: "Payments for " + customerName,
					filters: [
						["type", "anyof", "CustPymt", "Deposit"],
						"AND",
						[
							["name", "anyof", customerId],
							"OR",
							["appliedtotransaction.name", "anyof", customerId],
						],
						"AND",
						["mainline", "is", "F"],
					],
					columns: [
						search.createColumn({ name: "entity", label: "Payment By" }),
						search.createColumn({
							name: "trandate",
							sort: search.Sort.DESC,
							label: "Date",
						}),
						search.createColumn({ name: "type", label: "Type" }),
						search.createColumn({ name: "tranid", label: "Document Number" }),
						search.createColumn({
							name: "appliedtotransaction",
							label: "Applied To Transaction",
						}),
						search.createColumn({
							name: "amount",
							join: "appliedToTransaction",
							label: "Amount",
						}),
					],
				});

				var searchId = mySearch.save();

				redirect.toSavedSearchResult({
					id: searchId,
				});

				search.delete({ id: searchId });
			} else {
				return true;
			}
		},
	};
});
