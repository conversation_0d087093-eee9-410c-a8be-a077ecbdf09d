/******************************************************************************************************
	Script Name - AVA_SUT_TwoWayIMSBatchDetails.js
	Company -     Avalara Technologies Pvt Ltd.
******************************************************************************************************/

/**
 *@NApiVersion 2.0
 *@NScriptType Suitelet
 */

define(['N/ui/serverWidget', 'N/search', 'N/record', 'N/redirect', 'N/url', 'N/task', './utility/AVA_Library'],
	function(ui, search, record, redirect, url, task, ava_library){
		function onRequest(context){
			try{
				var checkServiceSecurity = ava_library.mainFunction('AVA_CheckService', 'TaxSvc');
				if(checkServiceSecurity == 0){
					checkServiceSecurity = ava_library.mainFunction('AVA_CheckSecurity', 36);
				}

				if(checkServiceSecurity == 0){
					if(context.request.method === 'GET'){
						var avaIMSForm = ui.createForm({
							title: 'View Details'
						});
						avaIMSForm.clientScriptModulePath = './AVA_CLI_TwoWayIMSBatchDetails.js';
						
						var batchId = context.request.parameters.twowayimsbatchid;
						if(batchId == null || batchId == ''){
							context.response.write({
								output: ava_library.mainFunction('AVA_NoticePage', 'Batch Name is Missing.')
							});
						}
						else{
							var batchRecordObj = record.load({
								type: 'customrecord_avatwowayimsbatch',
								id: batchId
							});
						
							addFormFields(context, avaIMSForm, batchRecordObj);
							addFormSublist(avaIMSForm);
							setSublistFieldValues(context, avaIMSForm, batchRecordObj.id);
							avaIMSForm.addButton({
								id: 'custpage_twowayimsexportcsv',
								label: 'Export CSV',
								functionName: 'AVA_TwoWayIMSExportCSV()'
							});
							avaIMSForm.addPageLink({
								title: 'Create Batch',
								type: ui.FormPageLinkType.CROSSLINK,
								url: url.resolveScript({
									scriptId: 'customscript_ava_2wayimscreatebatch_suit',
									deploymentId: 'customdeploy_ava_2wayimscreatebatch_suit'
								})
							});
							context.response.writePage({
								pageObject: avaIMSForm
							});
						}
					}
				}
				else{
					context.response.write({
						output: checkServiceSecurity
					});
				}
			}
			catch(e){
				log.error('ERROR', 'ERROR NAME = ' + e.name + ', ERROR TYPE = ' + e.type + ', ERROR MESSAGE = ' + e.message);
			}
		}

		function setSublistFieldValues(context, avaIMSForm, batchId){
			try{
				var itemExistValue = '';
				if(context.request.parameters.itemexist == 'T' || context.request.parameters.itemexist == 'F'){
					itemExistValue = context.request.parameters.itemexist;
				}
				
				var j = 0, twoWayIMSDetails = [];
				
				var avaIMSSearchObj = search.create({
					type: "customrecord_avatwowayimsdetails",
					filters: [
						["isinactive", "is", "F"],
						"AND",
						["custrecord_ava_twowayimsbatchreference", "anyof", batchId],
						"AND", 
						["custrecord_ava_twowayimspresentinnetsuit","is",itemExistValue]
					],
					columns: [
						"custrecord_ava_twowayimsitemcode",
						"custrecord_ava_twowayimstaxcode",
						"custrecord_ava_twowayimspresentinnetsuit"
					]
				});
				
				avaIMSSearchObj = avaIMSSearchObj.run();
				var searchResult = avaIMSSearchObj.getRange({
					start: 0,
					end: 1000
				});
				
				while(searchResult != null && searchResult.length > 0){
					for(var i = 0; i < searchResult.length; i++){
						twoWayIMSDetails.push(searchResult[i]);
						j++;
					}
					
					if(searchResult.length == 1000){
						searchResult = avaIMSSearchObj.getRange({
							start: j,
							end: j + 1000
						});
					}
					else{
						break;
					}
				}
							
				var imsSublist = avaIMSForm.getSublist({
					id: 'custpage_twowayimsbatchsublist'
				});
				
				for(var k = 0; k < twoWayIMSDetails.length; k++){
					var twoWayIMSItemCode = twoWayIMSDetails[k].getValue('custrecord_ava_twowayimsitemcode');
					var twoWayIMSITaxCode = twoWayIMSDetails[k].getValue('custrecord_ava_twowayimstaxcode');
					var twoWayIMSPresentInNS = twoWayIMSDetails[k].getValue('custrecord_ava_twowayimspresentinnetsuit');
					
					imsSublist.setSublistValue({
						id: 'twowayimsitemname',
						line: k,
						value: twoWayIMSItemCode
					});
					
					if(twoWayIMSITaxCode){
						imsSublist.setSublistValue({
						id: 'twowayimsitemtaxcode',
							line: k,
							value: twoWayIMSITaxCode
						});
					}
					
					if(twoWayIMSPresentInNS){
						imsSublist.setSublistValue({
							id: 'twowayimsexistinnetsuite',
							line: k,
							value: 'T'
						});
					}
				}
			}
			catch(e){
				log.error('setSublistFieldValues', 'ERROR NAME = ' + e.name + ', ERROR TYPE = ' + e.type + ', ERROR MESSAGE = ' + e.message);
			}
		}
		
		function addFormFields(context, avaIMSForm, batchRecordObj){
			try{
				var batchid = avaIMSForm.addField({
					id: 'ava_batchid',
					label: 'Batch Name',
					type: ui.FieldType.SELECT,
					source: 'customrecord_avatwowayimsbatch'
				});
				batchid.updateDisplayType({
					displayType: ui.FieldDisplayType.INLINE
				});
				batchid.defaultValue = batchRecordObj.id;
				
				var batchStatus = batchRecordObj.getValue('custrecord_ava_twowayimsstatus');
				var batchStatus1 = avaIMSForm.addField({
					id: 'ava_status',
					label: 'Status',
					type: ui.FieldType.TEXT
				});
				batchStatus1.updateDisplayType({
					displayType: ui.FieldDisplayType.INLINE
				});
				batchStatus1.defaultValue = batchStatus;
				
				var batchStartDate = batchRecordObj.getValue('custrecord_ava_twowayimsstartdate');
				if(batchStartDate != null && batchStartDate.toString().length > 0){
					var startDate = avaIMSForm.addField({
						id: 'ava_startdate',
						label: 'Start Date',
						type: ui.FieldType.DATE
					});
					startDate.updateDisplayType({
						displayType: ui.FieldDisplayType.INLINE
					});
					startDate.defaultValue = batchStartDate;
				}
				
				var batchEndDate = batchRecordObj.getValue('custrecord_ava_twowayimsenddate');
				if(batchEndDate != null && batchEndDate.toString().length > 0){
					var endDate = avaIMSForm.addField({
						id: 'ava_enddate',
						label: 'End Date',
						type: ui.FieldType.DATE
					});
					endDate.updateDisplayType({
						displayType: ui.FieldDisplayType.INLINE
					});
					endDate.defaultValue = batchEndDate;
				}
				
				var sublistCriteria = avaIMSForm.addField({
					id: 'custpage_itemexistcheck',
					label: 'Sublist Criteria',
					type: ui.FieldType.SELECT
				});
				sublistCriteria.addSelectOption({
					value : 'T',
					text: 'Exists Items in NetSuite'
				});
				sublistCriteria.addSelectOption({
					value : 'F',
					text: 'Non Exists Items in NetSuite'
				});
				sublistCriteria.addSelectOption({
					value : 'both',
					text: 'Both'
				});
				
				if(context.request.parameters.itemexist){
					sublistCriteria.defaultValue = context.request.parameters.itemexist;
				}
				else{
					sublistCriteria.defaultValue = 'both';
				}
				
				var totalItems = batchRecordObj.getValue('custrecord_ava_twowayimstotalitems');
				var existItems = batchRecordObj.getValue('custrecord_ava_twowayexistsitems');
				var nonExistItems = batchRecordObj.getValue('custrecord_ava_twowayimsnonexistitems');
				
				try{
					var temphtml = '<span style="padding: 10px 25px; display: inline-block; position: relative; left: -20px; margin-bottom: 5px;" class="bgmd"><img src="/images/x.gif" class="totallingTopLeft"><img src="/images/x.gif" class="totallingTopRight"><img src="/images/x.gif" class="totallingBottomLeft"><img src="/images/x.gif" class="totallingBottomRight"><table cellspacing="0" cellpadding="0" border="0" class="totallingtable">';
					temphtml += '<tbody><tr><td><span class="smallgraytextnolink">Total Items  &nbsp;&nbsp;</span></td><td ALIGN="right"><span class="smallgraytextnolink">' + totalItems + '</span></td></tr><tr><td>&nbsp;&nbsp;</td><td>&nbsp;&nbsp;</td></tr>';
					temphtml += '<tr><td><span class="smallgraytextnolink">Exists Items in NetSuite &nbsp;&nbsp;</span></td><td ALIGN="right"><span class="smallgraytextnolink">' + existItems + '</span></td></tr><tr><td>&nbsp;&nbsp;</td><td>&nbsp;&nbsp;</td></tr>';
					temphtml += '<tr><td><span class="smallgraytextnolink">Non Exists Items in NetSuite &nbsp;&nbsp;</span></td><td ALIGN="right"><span class="smallgraytextnolink">' + nonExistItems + '</span></td></tr></tbody></table></span>';
					
					var stats = avaIMSForm.addField({
						id: 'ava_stats',
						label: ' ',
						type: ui.FieldType.INLINEHTML  
					});
					stats.updateLayoutType({
						layoutType: ui.FieldLayoutType.NORMAL
					});
					stats.updateBreakType({
						breakType: ui.FieldBreakType.STARTCOL
					});
					stats.defaultValue = temphtml;			
				}
				catch(err){
					log.error('addFormFields - Summary', 'ERROR NAME = ' + err.name + ', ERROR TYPE = ' + err.type + ', ERROR MESSAGE = ' + err.message);
				}						
			}
			catch(e){
				log.error('addFormFields', 'ERROR NAME = ' + e.name + ', ERROR TYPE = ' + e.type + ', ERROR MESSAGE = ' + e.message);
			}
		}

		function addFormSublist(avaIMSForm){
			try{
				var imsSublist = avaIMSForm.addSublist({
					id: 'custpage_twowayimsbatchsublist',
					label: 'Results',
					type: ui.SublistType.LIST
				});
				imsSublist.addField({
					id: 'twowayimsitemname',
					label: 'Item Name',
					type: ui.FieldType.TEXT
				});
				imsSublist.addField({
					id: 'twowayimsitemtaxcode',
					label: 'Tax Code',
					type: ui.FieldType.TEXT
				});
				var twoWayIMSPresentInNetsuite = imsSublist.addField({
					id: 'twowayimsexistinnetsuite',
					label: 'Exists In NetSuite',
					type: ui.FieldType.CHECKBOX
				});
				twoWayIMSPresentInNetsuite.updateDisplayType({
					displayType: ui.FieldDisplayType.DISABLED
				});
				return avaIMSForm;
			}
			catch(e){
				log.error('addFormSublist', 'ERROR NAME = ' + e.name + ', ERROR TYPE = ' + e.type + ', ERROR MESSAGE = ' + e.message);
			}
		}
		
		return{
			onRequest: onRequest
		};
	}
);