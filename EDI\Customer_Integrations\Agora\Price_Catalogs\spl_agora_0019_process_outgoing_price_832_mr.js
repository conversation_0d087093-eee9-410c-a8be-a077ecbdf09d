/**
 * @NApiVersion 2.1
 * @NScriptType MapReduceScript
 * @NAmdConfig /SuiteScripts/config.json
 */

define([
	"require",
	"N/log",
	"../../../../EDI/Libraries/Process_EDI_File/spl_process_outgoing_832_price_lib",
], function (require) {
	/**@type {import ('../../../../EDI/Libraries/Process_EDI_File/spl_process_outgoing_832_price_lib')} */
	const process832PriceLib = require("../../../../EDI/Libraries/Process_EDI_File/spl_process_outgoing_832_price_lib");

	const customerObj = {
		accountNumber: "0019",
		customerName: "Agora",
		internalId: 428,
	};

	const dataObj = {
		prodGuidBool: true,
		prodDirectoryBool: true,
		decodeContent: true,
		prodGUID: "e0d75c59808740f1a5f5a3c983916854",
		sandboxGUID: "",
		prodDirectory: `/users/Agora/OUT/832`,
		testDirectory: "/users/Agora/Test",
		transactionType: "Price Catalog",
		purchasingSoftware: "Agora",
		customerName: customerObj.customerName,
	};

	function getInputData(context) {
		return process832PriceLib.getPriceList(customerObj.internalId);
	}

	function map(context) {
		try {
			const parsedItemRow = JSON.parse(context.value);

			const itemObj = process832PriceLib.getItemObj(
				parsedItemRow,
				customerObj.accountNumber
			);

			const itemString = process832PriceLib.getItemAsString(itemObj);

			context.write(customerObj.accountNumber, itemString);
		} catch (e) {
			log.error("Error Mapping Script", e);
		}
	}

	function summarize(summary) {
		try {
			process832PriceLib.processEnd(
				summary,
				customerObj.accountNumber,
				dataObj,
				customerObj.customerName
			);
		} catch (e) {
			log.error("Error Processing Summarize", e);
		}
	}

	return {
		getInputData,
		map,
		summarize,
	};
});
