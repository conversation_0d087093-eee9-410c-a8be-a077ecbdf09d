/**
 * @description Pulls EDI 856 files from the Drive folder and creates IF records in NS
 * <AUTHOR>
 * @module spl_drive_process_incoming_856_mr
 * @NApiVersion 2.1
 * @NScriptType MapReduceScript
 * @NAmdConfig /SuiteScripts/config.json
 */

/**@param {import ("N/types")} */

define([
	"require",
	"GetEdiFileContents",
	"GetEdiPartnerValuesLib",
	"ProcessIncoming856Lib",
	"MoveFileLib",
	"ProcessIncoming856EndLib",
	"EdiDataObject",
], function (
	require,
	getEdiFileContentsLib,
	getEdiPartnerValuesLib,
	processAsnLib,
	moveFileLib,
	processEnd,
	EdiDataObject
) {
	const log = require("N/log");
	const error = require("N/error");

	/** @typedef {import("../Classes/vlmd_edi_transaction").EdiDataObject} EdiDataObject*/
	const ediDataObj = new EdiDataObject({
		prodGuidBool: true,
		prodDirectoryBool: true,
		decodeContent: true,
		prodGUID: "e14e40445bbb4869bf0cc9a92acd9fe6",
		sandboxGUID: "",
		prodDirectory: "/users/Drive/IN/856",
		referenceDirectory: "/EDI Reference Files/Drive/IN/856",
		testDirectory: "",
		documentType: "Tracking",
		documentTypeId: 4,
		purchasingSoftware: "Drive",
		purchasingSoftwareId: 10,
	});

	/**@type {Object} */
	const partnerValues = getEdiPartnerValuesLib.getDriveValues();

	if (!partnerValues) {
		throw `Partner values not gotten correctly for ${ediDataObj.purchasingSoftware}.`;
	}

	/**
	 * Get all new files in the Drive 856 folder
	 *
	 * @returns {Array< { continueProcessing: boolean, fileContents: Array }>} of new files
	 */
	function getInputData(context) {
		return getEdiFileContentsLib.getEdiFileContents(ediDataObj).fileContents;
	}

	/**
	 * Process EDI file and create Item Fulfillment record if applicable
	 *
	 * @param {import("N/types").EntryPoints.MapReduce.map} context
	 */
	function map(context) {
		/**@type {JSON} */
		const ediFile = JSON.parse(context.value);

		let {
			errorLog,
			processingLog,
			itemFulfillmentInternalId,
			itemFulfillmentName,
			isTrackingForDropShipOrder,
			subsidiary,
			fileMovedSuccessfully,
		} = processAsnLib.processASN(ediDataObj, partnerValues, ediFile);

		if (!fileMovedSuccessfully) {
			const moveErrorLog = moveFileLib.moveFile(
				ediDataObj,
				ediFile.fileName,
				false //Not processed successfully
			);

			if (moveErrorLog.length > 0) {
				log.error("Error Moving File to Error Folder", moveErrorLog);
				errorLog = errorLog.concat(errorLog, moveErrorLog);
			}
		}

		if (errorLog.length > 0) {
			throw error.create({ name: ediFile?.fileName, message: errorLog.join() });
		}

		let contextMessage = `${subsidiary}, ${
			processingLog.length <= 0 ? "Success" : "Created With Errors"
		},${itemFulfillmentInternalId}, ${itemFulfillmentName}, ${
			processingLog.length > 0 ? processingLog.join(", ") : ""
		}`;

		context.write({
			key: isTrackingForDropShipOrder ? "DropShip Order" : "Warehouse Order",
			value: contextMessage,
		});
	}

	/**
	 * Create an EDI transaction record with processing details
	 *
	 * @param {import("N/types").EntryPoints.MapReduce.summarize} context
	 */

	function summarize(context) {
		const ediTransactionRecordObj = processEnd.getEdiTransactionObj(
			ediDataObj,
			context
		);

		if (ediTransactionRecordObj) {
			const ediTransactionRecordId = processEnd.createEdiTransactionRecord(
				ediTransactionRecordObj
			);

			log.debug(
				"EDI Transaction Record",
				ediTransactionRecordId
					? '<a href="https://5802576.app.netsuite.com/app/common/custom/custrecordentry.nl?rectype=707&id=' +
							ediTransactionRecordId +
							'&selectedtab=custom592"> Record Link </a>'
					: `No record created`
			);
		}
	}

	return {
		getInputData,
		map,
		summarize,
	};
});
