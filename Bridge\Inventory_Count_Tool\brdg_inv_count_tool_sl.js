/**
* @NApiVersion 2.1
* @NScriptType Suitelet
* @NModuleScope Public

* @description a SL for inventory counts at the store
* <AUTHOR> ChayaDrillick
*/

/**Define global variables, needs to be var vs. let for scope */
var scriptURL,
  subsidiaryOptions,
  log,
  https,
  record,
  search,
  email,
  serverWidget,
  url,
  redirect,
  runtime,
  query,
  file,
  message,
  format,
  recordModuleHelperLib,
  sessionScope,
  subsidiaryId;

define([
  "require",
  "N/log",
  "N/query",
  "N/file",
  "N/ui/serverWidget",
  "N/https",
  "N/record",
  "N/search",
  "N/email",
  "N/url",
  "N/redirect",
  "N/runtime",
  "N/ui/message",
  "N/format",
  "../../Helper_Libraries/vlmd_record_module_helper_lib",
], main);

function main(require, logModule, queryModule, fileModule) {
  log = logModule;
  query = queryModule;
  file = fileModule;
  serverWidget = require("N/ui/serverWidget");
  https = require("N/https");
  record = require("N/record");
  search = require("N/search");
  email = require("N/email");
  url = require("N/url");
  redirect = require("N/redirect");
  runtime = require("N/runtime");
  message = require("N/ui/message");
  format = require("N/format");
  recordModuleHelperLib = require("../../Helper_Libraries/vlmd_record_module_helper_lib");
  sessionScope = runtime.getCurrentSession();
  subsidiaryId;

  return {
    onRequest: function (context) {
      scriptURL = url.resolveScript({
        scriptId: runtime.getCurrentScript().id,
        deploymentId: runtime.getCurrentScript().deploymentId,
        returnExternalURL: false,
      });

      subsidiaryId = sessionScope.get({ name: "subsidiaryId" });

      if (context.request.method == "GET") {
        handleGetRequest(context);
      } else {
        handlePostRequest(context);
      }
    },
  };
}

function getItemsPartOfScan() {
  let jsonRecordId = sessionScope.get({ name: "parentRecordId" });
  let parentRecordId = JSON.parse(jsonRecordId);

  const sqlQuery =
    /*sql*/
    `SELECT
  custrecord_inv_count_parent_sub as subsidiary, 
  custrecord_brdg_table_data as itemdata,
  custrecord_count_quantity as countquantity, 
  segmentrecord.id as recordid,
  custrecord_comments_entered as comment,
  FROM 
    customrecord_brdg_inv_count_segment segmentrecord
  INNER JOIN 
    customrecord_brdg_inventory_count parentRecord
  ON 
    segmentrecord.custrecord_inv_count = parentRecord.id
  WHERE
      custrecord_inv_count = ${parentRecordId}`;

  let existingItemsScanned = query
    .runSuiteQL({
      query: sqlQuery,
    })
    .asMappedResults();

  const arrAllScannedItems = existingItemsScanned.flatMap((itemObj) => {
    //If there's only one item so far, parsed items is an object, otherwise it's an array
    let parsedItems = JSON.parse(itemObj.itemdata);
    parsedItems = Array.isArray(parsedItems) ? parsedItems : [parsedItems];
    //Adding the segment record id and quantity to the results
    return parsedItems.map((item) => ({
      ...item,
      recordId: itemObj.recordid,
      countQuantity: itemObj.countquantity,
      comment: itemObj.comment,
    }));
  });

  return arrAllScannedItems;
}

function addItemObjToArray(currentItemData) {
  const arrAllScannedItems = getItemsPartOfScan();

  const existingScannedItemObj = arrAllScannedItems.find(
    (item) => item.item_internal_id === currentItemData.item_internal_id
  );

  /*Note: if the quantity is off, check that this checking/updating existing items is working as expected */

  if (existingScannedItemObj) {
    try {
      record.submitFields({
        type: "customrecord_brdg_inv_count_segment",
        id: existingScannedItemObj.recordId,
        values: {
          custrecord_count_quantity: (
            (parseFloat(existingScannedItemObj.countQuantity) || 0) +
            (parseFloat(currentItemData.quantity) || 0)
          ).toString(),
          custrecord_comments_entered: existingScannedItemObj.comment
            ? `${existingScannedItemObj.comment}\n${currentItemData.comment}`.trim()
            : currentItemData.comment,
          custrecord_item_received_last_week: currentItemData.receivedLastWeek,
        },
      });
    } catch (e) {
      throw "Error updating existing item quantity! " + e.message;
    }
  } else {
    createRecordInventorySegment(currentItemData);
  }

  return getItemsPartOfScan();
}

function addScan(context, requestPayload) {
  var responsePayload;

  let { currentItemData, nextAction } = JSON.parse(
    requestPayload.paramsJsonObj
  );

  currentItemData = JSON.parse(currentItemData);

  const records = addItemObjToArray(currentItemData);

  switch (nextAction) {
    case "scanAnother":
      responsePayload = { submitted: true };

      break;
    case "viewList":
      responsePayload = { records };
      break;
    case "btnAddCompleteCount":
      responsePayload = { submitted: true };

      break;
    default:
      log.error({
        title: "Invalid nextAction",
        details: nextAction,
      });
  }

  context.response.write(JSON.stringify(responsePayload, null, 5));
}

function viewListNew(context) {
  var records = getItemsPartOfScan() ?? [];
  var responsePayload = {
    records:
      records && records.length > 0
        ? records
        : { error: "No Items in List Yet" },
  };
  context.response.write(JSON.stringify(responsePayload, null, 5));
}

function completeCount(context) {
  let records = getItemsPartOfScan();

  try {
    //If change the order of the header names or add/remove - be sure to change it when building the rows for the CSV.
    const headers = [
      "Count Date",
      "Store Name",
      "Subsidiary ID",
      "Item ID",
      "Item Name",
      "Stock Unit Name",
      "Last Purchase Price",
      "Sales Price",
      "Gross Profit",
      "QTY in NS",
      "Count Number",
      "QTY to Adjust",
      "Received Last Week",
      "Is Allocated Item",
      "Comments Entered",
      "IA Memo",
      "IA External ID",
      "Inventory Count Type",
    ];

    const csvData = [];
    const chosenType = sessionScope.get({ name: "inventoryCountType" });
    csvData.push(headers.join(","));

    // Loop through the records and add each object as a row in the CSV data
    for (const item of records) {
      const row = [
        sessionScope.get({
          name: "inventoryCountDate",
        }),
        `"${item["store_name"]}"`,
        sessionScope.get({
          name: "subsidiaryId",
        }),
        item["item_internal_id"],
        `"${item["item_name"]}"`,
        item["stock_unit"],
        `"${item["lpp"]}"`,
        item["sale_price"],
        item["sale_price"] / item["lpp"] / item["lpp"],
        item["available"],
        item["countQuantity"],
        " ", //Qty to adjust
        item["receivedLastWeek"],
        item["is_allocated_item"],
        `"${(item["comment"] || "").replace(/"/g, '""').replace(/\n/g, " ")}"`,
        " ", //IA Memo
        " ", //IA External Id
        chosenType,
      ];

      csvData.push(row.join(","));
    }

    const storeName = records[0]["store_name"];
    const csvContent = csvData.join("\n");

    const csvFile = file.create({
      name: `Inventory_Count_${new Date().toISOString().split("T")[0]}.csv`,
      fileType: file.Type.CSV,
      contents: csvContent,
      folder: 6521151, //7403874,
    });

    const fileId = csvFile.save();

    let parentRecordId = JSON.parse(
      sessionScope.get({ name: "parentRecordId" })
    );

    record.submitFields({
      type: "customrecord_brdg_inventory_count",
      id: parentRecordId,
      values: {
        custrecord_brdg_inventory_count_file: file.load({
          id: fileId,
        }).url,
        custrecord_count_completed: "T",
      },
    });

    sessionScope.set({ name: "parentRecordId", value: "" });

    email.send({
      author: 223244, //Requests
      body: `Please find the results of the inventory count attached. 
		<a href='/app/common/custom/custrecordentry.nl?rectype=2669&id=${parentRecordId}'> Click here to go to inventory count record.</a>`,
      recipients: [314184, 84342], //Leah Pinter, Diana Bolivar
      subject: `${storeName} ${chosenType} Count: ${
        new Date().toISOString().split("T")[0]
      }`,
      attachments: [csvFile],
      relatedRecords: {
        customRecord: {
          id: parentRecordId,
          recordType: "customrecord_brdg_inventory_count",
        },
      },
    });

    responsePayload = { parentRecordId };

    context.response.write(JSON.stringify(responsePayload, null, 5));
  } catch (e) {
    log.error("Error Completing Count", e);
  }
}

function createNewParentRecord() {
  try {
    let inventoryDate =
      sessionScope.get({ name: "chosenDate" }) ??
      new Date().toLocaleDateString();

    const inventoryParentRecord = record.create({
      type: "customrecord_brdg_inventory_count",
      isDynamic: false,
    });

    const inventoryParentRecordObj = {
      name: runtime.getCurrentUser().name + ": " + inventoryDate,
      custrecord_inv_count_parent_sub: sessionScope.get({
        name: "subsidiaryId",
      }),
      custrecord_inv_count_parent_date: new Date(inventoryDate),
    };

    recordModuleHelperLib.setBodyValues(
      inventoryParentRecordObj,
      inventoryParentRecord
    );

    const parentRecordId = inventoryParentRecord.save();

    sessionScope.set({
      name: "parentRecordId",
      value: parentRecordId,
    });

    sessionScope.set({
      name: "records",
      value: null,
    });
  } catch (e) {
    log.error({
      title: "Error creating parent inventory record.",
      details: e,
    });
  }
}

function createRecordInventorySegment(currentItemData) {
  var currentDateTime = format.format({
    value: new Date(),
    type: format.Type.DATETIME,
    timezone: format.Timezone.AMERICA_NEW_YORK,
  });

  let parentRecordId = JSON.parse(sessionScope.get({ name: "parentRecordId" }));

  const custRecSegment = record.create({
    type: "customrecord_brdg_inv_count_segment",
    isDynamic: false,
  });

  const custRecSegmentObj = {
    name: runtime.getCurrentUser().name + ": " + currentDateTime,
    custrecord_brdg_employee: runtime.getCurrentUser().id,
    custrecord_brdg_timestamp: new Date(),
    custrecord_brdg_table_data: JSON.stringify(currentItemData),
    custrecord_inv_count: parentRecordId,
    custrecord_count_quantity: currentItemData.quantity,
    custrecord_item_internal_id: currentItemData.item_internal_id,
  };

  recordModuleHelperLib.setBodyValues(custRecSegmentObj, custRecSegment);

  try {
    custRecSegment.save();
  } catch (e) {
    log.error({
      title: "Error creating inventory segment record.",
      details: e,
    });
    throw "Error saving item to NS!";
  }
}

function enterUpcView() {
  return /*html*/ `
	<div id='enterUpcDiv' class='container mt-4'>
    <div class='row justify-content-center'>
      <div class='col-12 col-md-8 col-lg-6'>
      <h1 class='text-center mb-3'><label for="upc" id="scanLabel">SCAN BARCODE</label></h1>
        <div class="btn-group w-100 mb-3">
          <button type="button" class="btn btn-primary active" id="autoScanBtn" onclick="toggleScanMode('auto')">
            <i class="fas fa-barcode"></i> Auto Scan
          </button>
          <button type="button" class="btn btn-outline-secondary" id="manualEntryBtn" onclick="toggleScanMode('manual')" style="opacity: 0.6;">
            <i class="fas fa-keyboard"></i> Manual Entry
          </button>
        </div>
        <input type="text" id="upc" class="form-control form-control-lg mb-3" required minlength="12" maxlength="13" placeholder="Enter UPC Code">
        <button type="button" class="btn btn-success btn-lg btn-block" onclick="scanItem()" accesskey="r">Scan</button>
        <button type="button" class="btn btn-success btn-lg btn-block" onclick="viewListNew()">
          View List
        </button>
      </div>
    </div>
	</div>

  <script>
  const upcInput = document.getElementById('upc');
  let typingTimer;
  let isAutoScan = true;

  function toggleScanMode(mode) {
    const autoBtn = document.getElementById('autoScanBtn');
    const manualBtn = document.getElementById('manualEntryBtn');
    const scanLabel = document.getElementById('scanLabel');
    const upcInput = document.getElementById('upc');

    isAutoScan = mode === 'auto';

    // Update button states
    if (mode === 'auto') {
      // Auto scan active state
      autoBtn.classList.add('active', 'btn-primary');
      autoBtn.classList.remove('btn-outline-secondary');
      autoBtn.style.opacity = '1';
      
      // Manual entry inactive state
      manualBtn.classList.remove('active', 'btn-primary');
      manualBtn.classList.add('btn-outline-secondary');
      manualBtn.style.opacity = '0.6';
      
      scanLabel.textContent = 'SCAN BARCODE';
      upcInput.placeholder = 'Enter UPC Code';
    } else {
      // Manual entry active state
      manualBtn.classList.add('active', 'btn-primary');
      manualBtn.classList.remove('btn-outline-secondary');
      manualBtn.style.opacity = '1';
      
      // Auto scan inactive state
      autoBtn.classList.remove('active', 'btn-primary');
      autoBtn.classList.add('btn-outline-secondary');
      autoBtn.style.opacity = '0.6';
      
      scanLabel.textContent = 'ENTER UPC';
      upcInput.placeholder = 'Manually Enter UPC';
    }
  }

  upcInput.addEventListener('input', function(e) {
    if (isAutoScan) {
      if (this.value.length === 13) {
        scanItem();
      } else {
        clearTimeout(typingTimer);
        typingTimer = setTimeout(scanItem, 1000);
      }
    }
  });
</script>
`;
}

function generateHTML() {
  return /*html*/ `
		<link rel="stylesheet" href="https://stackpath.bootstrapcdn.com/bootstrap/4.5.2/css/bootstrap.min.css">
		<script src="https://stackpath.bootstrapcdn.com/bootstrap/4.5.2/js/bootstrap.min.js"></script>
    <script src="https://code.jquery.com/jquery-3.5.1.min.js"></script>
<script src="https://cdn.jsdelivr.net/npm/bootstrap@4.6.0/dist/js/bootstrap.bundle.min.js"></script>

		<link rel="stylesheet" type="text/css" href="https://cdn.datatables.net/1.10.25/css/jquery.dataTables.css">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.2.1/css/all.min.css" rel="stylesheet"/>
		<script type="text/javascript" charset="utf8" src="https://cdn.datatables.net/1.10.25/js/jquery.dataTables.js"></script>

		<style type="text/css">
			input[type="text"],
			input[type="search"],
			textarea,
			button {
				outline: none;
				box-shadow: none !important;
				border: 1px solid #ccc !important;
			}

			p,
			pre {
				font-size: 10pt;
			}

			td,
			th {
				font-size: 10pt;
				border: 3px;
			}

			th {
				text-transform: lowercase;
				font-weight: bold;
			}
    @media (max-width: 767px) {
  .modal-dialog {
    margin: 0.5rem;
    max-width: none;
  }
  
  .modal-content {
    height: 90vh; /* This makes the modal take up 90% of the viewport height */
  }

  .modal-body {
    max-height: calc(90vh - 120px); /* Adjust based on your header and footer height */
    overflow-y: auto;
  }
}
@media (max-height: 500px) {
  #startScreenDiv {
    justify-content: flex-start !important;
    padding-top: 2rem !important;
  }
}

@media (max-width: 576px) {
  #startScreenDiv h1 {
    font-size: 1.5rem;
  }
  
  #startScreenDiv .btn {
    width: 100%;
    max-width: 300px;
  }
}
.d-none-important {
  display: none !important;
}
#resultsTable th {
    text-transform: none !important;
}
@media (max-width: 767px) {
    .table-responsive {
        overflow-x: auto;
    }
    .th-sm, .td-sm {
        padding: 0.3rem !important;
        font-size: 0.8rem;
        white-space: nowrap;
    }
    .btn-sm {
        padding: 0.2rem 0.4rem;
        font-size: 0.7rem;
    }
}



		</style>

		${htmlUI()}

		<div id="currentItemData" style="max-width: 100%; margin-top: 12px; display: none; overflow: auto; overflow-y: hidden;"></div>

		<script>
			var
				queryResponsePayload,
				fileLoadResponsePayload;

			window.jQuery = window.$ = jQuery;

	
			$('#startScreenDiv').show();
			$('#newCountDiv').hide();
     	$('#loadPreviousCountDiv').hide();
			$('#enterUpcDiv').hide();

			${jsFunctionBackToScan()}    
			${jsFunctionCompleteCount()} 
			${jsFunctionContinueCount()} 
			${jsFunctionContinueWithScan()}			
			${jsFunctionEnterScan()}    
			${jsFunctionGenerateTable()}   
			${jsFunctionLoadPreviousCount()}
			${jsFunctionNewCount()}
      ${jsFunctionViewListNew()}
			${jsFunctionScanItem()}
			${jsFunctionStartCount()}			
			${jsFunctionSaveContinueLater()}
		</script>`;
}

function getDataForUpc(context, requestPayload) {
  var responsePayload;
  let upcCode = JSON.parse(requestPayload.paramsJsonObj).upcCode;

  let records = getItemValues(upcCode);
  records =
    records && records.length > 0
      ? records
      : [{ error: "No item found for this UPC code!" }];

  responsePayload = { records };
  context.response.write(JSON.stringify(responsePayload, null, 5));
}

function getItemValues(upcCode) {
  try {
    const itemTypeQuery = `
        SELECT 
        item.itemtype AS itemtype
        FROM
        item
        LEFT OUTER JOIN
            inventoryItemLocations 
            ON inventoryItemLocations.item = item.id 
        LEFT OUTER JOIN
            Location 
            ON inventoryItemLocations.location = Location.id 
        LEFT OUTER JOIN
            LocationSubsidiaryMap 
            ON Location.id = LocationSubsidiaryMap.location 
        WHERE
            item.upccode = '${upcCode}'
            AND item.isinactive = 'F' `;

    let resultsItemType = query
      .runSuiteQL({
        query: itemTypeQuery,
      })
      .asMappedResults();
    if (resultsItemType.length == 0 || !resultsItemType) {
      return null;
    }

    let itemType = resultsItemType[0].itemtype;

    const lppFieldId = `
        SELECT
        custrecord_last_purchase_price_by_store 
        FROM
        customrecord_bridge_store 
        WHERE
        custrecord_brdg_store_subsidiary = ${subsidiaryId}`;

    let resultsLppFieldId = query
      .runSuiteQL({
        query: lppFieldId,
      })
      .asMappedResults();

    if (itemType == "Kit") {
      const sqlQuery = /*sql*/ `
      SELECT 
      store.name as store_name, 
      item.id as item_internal_id, 
      item.itemid AS item_number, 
      item.displayname AS item_name, 
      BUILTIN.DF(member.stockunit) AS stock_unit, 
      member.${resultsLppFieldId[0]["custrecord_last_purchase_price_by_store"]} as lpp, 
      CASE WHEN promo.custrecord_dollar_promo_off IS NULL THEN unitprice WHEN custrecord_active_promotion_tag_internal in (
        'custitem_ltspd_ggn_prmtn_tag', 
        'custitem_ltspd_ggs_prmtn_tag', 
        'custitem_ltspd_lol_prmtn_tag', 
        'custitem_ltspd_wg_prmtn_tag', 
        'custitem_ltspd_vyb_prmtn_tag', 
        'custitem_ltspd_vye_prmtn_tag'
      ) THEN unitprice - promo.custrecord_dollar_promo_off WHEN custrecord_active_promotion_tag_internal IS NULL 
      OR custrecord_active_promotion_tag_internal NOT IN (
        'custitem_ltspd_ggn_prmtn_tag', 
        'custitem_ltspd_ggs_prmtn_tag', 
        'custitem_ltspd_lol_prmtn_tag', 
        'custitem_ltspd_wg_prmtn_tag', 
        'custitem_ltspd_vyb_prmtn_tag', 
        'custitem_ltspd_vye_prmtn_tag'
      ) THEN unitprice ELSE null END AS sale_price, 
      inventoryItemLocations.quantityavailable available, 
      member.custitem_brdg_cost_allocated_item as is_allocated_item, 
    FROM 
      item 
      INNER JOIN KitItemMember ON (
        KitItemMember.ParentItem = item.ID
      ) 
      INNER JOIN Item AS member ON (member.ID = KitItemMember.Item) 
      LEFT OUTER JOIN inventoryItemLocations ON inventoryItemLocations.item = member.id 
      LEFT OUTER JOIN customrecord_bridge_store store ON store.custrecord_brdg_store_subsidiary = ${subsidiaryId} 
      INNER JOIN pricing on pricing.pricelevel = store.custrecord_brdg_store_price_level 
      and item.id = pricing.item 
      LEFT OUTER JOIN customrecord_brdg_promtion_tag promo ON CASE WHEN promo.id = item.custitem_ltspd_ggn_prmtn_tag 
      and store.custrecord_active_promotion_tag_internal = 'custitem_ltspd_ggn_prmtn_tag' THEN 1 WHEN promo.id = item.custitem_ltspd_ggs_prmtn_tag 
      and store.custrecord_active_promotion_tag_internal = 'custitem_ltspd_ggs_prmtn_tag ' THEN 1 WHEN promo.id = item.custitem_ltspd_lol_prmtn_tag 
      and store.custrecord_active_promotion_tag_internal = 'custitem_ltspd_lol_prmtn_tag ' THEN 1 WHEN promo.id = item.custitem_ltspd_wg_prmtn_tag 
      and store.custrecord_active_promotion_tag_internal = 'custitem_ltspd_wg_prmtn_tag ' THEN 1 WHEN promo.id = item.custitem_ltspd_vyb_prmtn_tag 
      and store.custrecord_active_promotion_tag_internal = 'custitem_ltspd_vyb_prmtn_tag' THEN 1 WHEN promo.id = item.custitem_ltspd_vye_prmtn_tag 
      and store.custrecord_active_promotion_tag_internal = 'custitem_ltspd_vye_prmtn_tag' THEN 1 ELSE 0 END = 1 
    WHERE 
      item.upccode = '${upcCode}' 
      AND item.isinactive = 'F' 
      and inventoryItemLocations.location = custrecord_brdg_store_location    
`;

      let results = query
        .runSuiteQL({
          query: sqlQuery,
        })
        .asMappedResults();
      return results.length > 0 ? results : null;
    } else {
      const sqlQuery = /*sql*/ `
      SELECT 
      store.name as store_name, 
      item.id as item_internal_id, 
      item.itemid AS item_number, 
      item.displayname AS item_name, 
      BUILTIN.DF(item.stockunit) AS stock_unit, 
      item.${resultsLppFieldId[0]["custrecord_last_purchase_price_by_store"]} as lpp, 
      CASE WHEN promo.custrecord_dollar_promo_off IS NULL THEN unitprice WHEN custrecord_active_promotion_tag_internal in (
        'custitem_ltspd_ggn_prmtn_tag', 
        'custitem_ltspd_ggs_prmtn_tag', 
        'custitem_ltspd_lol_prmtn_tag', 
        'custitem_ltspd_wg_prmtn_tag', 
        'custitem_ltspd_vyb_prmtn_tag', 
        'custitem_ltspd_vye_prmtn_tag'
      ) THEN unitprice - promo.custrecord_dollar_promo_off WHEN custrecord_active_promotion_tag_internal IS NULL 
      OR custrecord_active_promotion_tag_internal NOT IN (
        'custitem_ltspd_ggn_prmtn_tag', 
        'custitem_ltspd_ggs_prmtn_tag', 
        'custitem_ltspd_lol_prmtn_tag', 
        'custitem_ltspd_wg_prmtn_tag', 
        'custitem_ltspd_vyb_prmtn_tag', 
        'custitem_ltspd_vye_prmtn_tag'
      ) THEN unitprice ELSE null END AS sale_price, 
      inventoryItemLocations.quantityavailable/conversionrate available,
      item.custitem_brdg_cost_allocated_item as is_allocated_item, 
    FROM 
      item 
      INNER JOIN unitstypeuom saleunit ON saleunit.internalid = item.saleunit
      LEFT OUTER JOIN inventoryItemLocations ON inventoryItemLocations.item = item.id 
      LEFT OUTER JOIN customrecord_bridge_store store ON store.custrecord_brdg_store_subsidiary = ${subsidiaryId} 
      INNER JOIN pricing on pricing.pricelevel = store.custrecord_brdg_store_price_level 
      and item.id = pricing.item 
      LEFT OUTER JOIN customrecord_brdg_promtion_tag promo ON CASE WHEN promo.id = item.custitem_ltspd_ggn_prmtn_tag 
      and store.custrecord_active_promotion_tag_internal = 'custitem_ltspd_ggn_prmtn_tag' THEN 1 WHEN promo.id = custitem_ltspd_ggs_prmtn_tag 
      and store.custrecord_active_promotion_tag_internal = 'custitem_ltspd_ggs_prmtn_tag ' THEN 1 WHEN promo.id = custitem_ltspd_lol_prmtn_tag 
      and store.custrecord_active_promotion_tag_internal = 'custitem_ltspd_lol_prmtn_tag ' THEN 1 WHEN promo.id = custitem_ltspd_wg_prmtn_tag 
      and store.custrecord_active_promotion_tag_internal = 'custitem_ltspd_wg_prmtn_tag ' THEN 1 WHEN promo.id = custitem_ltspd_vyb_prmtn_tag 
      and store.custrecord_active_promotion_tag_internal = 'custitem_ltspd_vyb_prmtn_tag' THEN 1 WHEN promo.id = custitem_ltspd_vye_prmtn_tag 
      and store.custrecord_active_promotion_tag_internal = 'custitem_ltspd_vye_prmtn_tag' THEN 1 ELSE 0 END = 1 
    WHERE 
      item.upccode = '${upcCode}' 
      AND item.isinactive = 'F' 
      and inventoryItemLocations.location = custrecord_brdg_store_location`;

      let results = query
        .runSuiteQL({
          query: sqlQuery,
        })
        .asMappedResults();

      return results;
    }
  } catch (e) {
    return [{ error: e.message }];
  }
}

function getParentInventoryRecordsObj() {
  let sqlQuery = /*sql*/ `
	SELECT 
		name, 
		id,
		TO_CHAR(created, 'HH12:MI') AS created_datetime,
    BUILTIN.DF(custrecord_inv_count_parent_sub) AS subsidiary,
	FROM 
		customrecord_brdg_inventory_count parent
	WHERE
		custrecord_count_completed = 'F'
 		AND EXISTS (
			SELECT 1
			FROM customrecord_brdg_inv_count_segment s
			WHERE s.custrecord_inv_count = parent.id
    	)
    ORDER BY created desc
    `;

  return query
    .runSuiteQL({
      query: sqlQuery,
    })
    .asMappedResults();
}

function getSubsidiaryObjs() {
  let sqlQuery = /*sql*/ `
	SELECT
		subsidiary.id AS id,
		subsidiary.name AS name 
	FROM
		subsidiary 
	JOIN
		subsidiary parent ON subsidiary.parent = parent.id 
	WHERE
		(subsidiary.parent = 16 OR parent.parent = 16)
		AND subsidiary.id NOT IN (26,27,22) --Vineyard parent, RE 105, RE 1380
	ORDER BY
		subsidiary.name`;

  return query
    .runSuiteQL({
      query: sqlQuery,
    })
    .asMappedResults();
}

function handleGetRequest(context) {
  /**Coming to this page for the first time, load the start screen */
  subsidiaryOptions = getSubsidiaryObjs();
  inventoryCountOptions = getParentInventoryRecordsObj();
  var form = serverWidget.createForm({
    title: `Inventory Count Tool`,
    hideNavBar: true,
  });

  var htmlField = form.addField({
    id: "custpage_field_html",
    type: serverWidget.FieldType.INLINEHTML,
    label: "HTML",
  });

  htmlField.defaultValue = generateHTML();
  context.response.writePage(form);
}

function handlePostRequest(context) {
  var requestPayload = JSON.parse(context.request.body);
  context.response.setHeader("Content-Type", "application/json");

  switch (requestPayload["function"]) {
    case "setNewCountVariables":
      log.audit("setNewCountVariables", requestPayload);
      return setSubsidiaryAndDateSessionScope(requestPayload);

    case "getDataForUpc":
      return getDataForUpc(context, requestPayload);

    case "viewListNew":
      log.audit("viewListNew", requestPayload);
      return viewListNew(context);

    case "setPreviousCountVariables":
      log.audit("setPreviousCountVariables", requestPayload);
      return setPreviousCountVariables(requestPayload);

    case "addScan":
      return addScan(context, requestPayload);

    case "updateNetsuiteRecords":
      log.audit("updateNetsuiteRecords", requestPayload);
      return updateNetsuiteRecords(requestPayload);

    case "completeCount":
      log.audit("completeCount", requestPayload);

      return completeCount(context, requestPayload);

    case "saveContinueLater":
      log.audit("saveContinueLater", requestPayload);

      return saveContinueLater(context);

    default:
      log.error({
        title: "Payload - Unsupported Function",
        details: requestPayload["function"],
      });
  }
}

function htmlUI() {
  return /*html*/ `
		<div class="container" id="inventoryCountToolUI" style="text-align: left;">	
				${startScreenView()}
				${newCountView()}
        ${loadPreviousCountView()}
				${enterUpcView()}
				<div id='enterQuantityDiv' class='col'></div>
				<div id="listView" class="container"></div>
		</div>`;
}

function loadPreviousCountView() {
  return /*html*/ `
  <div class="modal fade"  
  tabindex="-1" role="dialog" aria-labelledby="exampleModalLabel" id="loadPreviousCountDiv">
	<div class="modal-dialog" role="document">
	  <div class="modal-content">
		<div class="modal-header">
		  <h1 class="modal-title" id="exampleModalLabel">Load Previous Count</h1>
		  <button type="button" class="close" data-dismiss="modal" aria-label="Close">
			<span aria-hidden="true">&times;</span>
		  </button>
		</div>
		<div class="modal-body">
		<div class="form-group" >
				<h2><label for="countSelect">Inventory Count</label></h2>
				<select class="form-control select2 select2-hidden-accessible" id="countSelect" style="width: 100%;">
					<option selected value=""></option>
					${inventoryCountOptions.map(
            (
              inventoryCountOption
            ) => `<option value="${inventoryCountOption.id}">${inventoryCountOption.name} | ${inventoryCountOption.created_datetime} |  ${inventoryCountOption.subsidiary}</option>
						`
          )}
				</select>
			</div>
		</div>
		<div class="modal-footer">
		  <button type="button" class="btn btn-secondary" data-dismiss="modal">Back</button>
		  <button type="button" class="btn btn-primary"  onclick="continueCount()" accesskey="r" data-dismiss="modal">Continue Count</button>
		</div>
	  </div>
	</div>
  </div>`;
}

function newCountView() {
  return /*html*/ `
    <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no">
    <div class="modal fade"  
  tabindex="-1" role="dialog" aria-labeledby="exampleModalLabel" aria-hidden="true" id="newCountDiv">
  <div class="modal-dialog modal-dialog-centered modal-lg" role="document">
	  <div class="modal-content">
		<div class="modal-header">
    <h1 class="modal-title w-100 text-center" id="newCountModalLabel">New Count</h1>
    <button type="button" class="close" data-dismiss="modal" aria-label="Close">
			<span aria-hidden="true">&times;</span>
		  </button>
		</div>
		<div class="modal-body">
		<div class="form-group" >
				<h2><label for="subsidiarySelect">Store:</label></h2>
				<select class="form-control select2 select2-hidden-accessible" id="subsidiarySelect" style="width: 100%;">
					<option selected value=""></option>
					${subsidiaryOptions.map(
            (
              subsidiaryObj
            ) => `<option value="${subsidiaryObj.id}">${subsidiaryObj.name}</option>
						`
          )}
				</select>
			</div>
      <div class="form-group">
      <h2>Type of Count:</h2>
      <div class="d-flex justify-content-center mt-2">
          <div class="form-check form-check-inline mx-4">
              <input class="form-check-input" type="radio" name="countType" id="cycleCount" value="Cycle" checked>
              <label class="form-check-label h5 mb-0" for="cycleCount">
                  Cycle Count
              </label>
          </div>
          <div class="form-check form-check-inline mx-4">
              <input class="form-check-input" type="radio" name="countType" id="weeklyCount" value="Weekly">
              <label class="form-check-label h5 mb-0" for="weeklyCount">
                  Weekly Count
              </label>
          </div>
      </div>
  </div>
			<div class="form-group">
				<h2><label for="datePicker">Date:</label></h2>
				<input type="date" id="datepicker" class="form-control" value="${
          new Date().toISOString().split("T")[0]
        }" >
			</div>
		</div>
    <div class="modal-footer d-flex justify-content-center">
    <button type="button" class="btn btn-secondary btn-lg mx-2" data-dismiss="modal">Back</button>
          <button type="button" class="btn btn-primary btn-lg mx-2" onclick="startCount()" accesskey="r" data-dismiss="modal">Start Count</button>
		</div>
	  </div>
	</div>
  </div>`;
}

function updateNetsuiteRecords(requestPayload) {
  let { itemUpc, task, comment, receivedLastWeek } = JSON.parse(
    requestPayload.paramsJsonObj
  );

  try {
    const arrAllScannedItems = getItemsPartOfScan();
    const matchingRecordId =
      arrAllScannedItems.find((item) => item.upc == itemUpc)?.recordId ?? null;

    matchingRecordId &&
      task == "delete" &&
      record.delete({
        type: "customrecord_brdg_inv_count_segment", // Replace with your actual record type
        id: matchingRecordId,
      });

    matchingRecordId &&
      task == "update" &&
      record.submitFields({
        type: "customrecord_brdg_inv_count_segment",
        id: matchingRecordId,
        values: {
          custrecord_comments_entered: comment,
          custrecord_item_received_last_week: receivedLastWeek,
        },
      });
  } catch (e) {
    log.error({
      title: "Error updating segment record.",
      details: e,
    });
  }
}

function saveContinueLater(context) {
  const responsePayload = {
    success: true,
  };

  context.response.write(JSON.stringify(responsePayload, null, 5));
}

function setPreviousCountVariables(requestPayload) {
  let { parentInventoryId } = JSON.parse(requestPayload.paramsJsonObj);

  sessionScope.set({
    name: "parentRecordId",
    value: parentInventoryId,
  });

  const arrAllScannedItems = getItemsPartOfScan();
  sessionScope.set({
    name: "records",
    value: JSON.stringify(arrAllScannedItems),
  });
}

function setSubsidiaryAndDateSessionScope(requestPayload) {
  try {
    let { chosenSubsidiaryId, chosenDate, chosenType } = JSON.parse(
      requestPayload.paramsJsonObj
    );

    sessionScope.set({
      name: "subsidiaryId",
      value: chosenSubsidiaryId,
    });

    sessionScope.set({
      name: "inventoryCountDate",
      value: chosenDate,
    });

    sessionScope.set({
      name: "inventoryCountType",
      value: chosenType,
    });

    createNewParentRecord();
  } catch (e) {
    log.error({
      title: "Error setting subsidiary and date session scope",
      details: e,
    });
    return;
  }
}

function startScreenView() {
  return /*html*/ `
		<div id="startScreenDiv" class="container  d-flex flex-column pt-4 py-5">
    <div class="d-flex flex-column align-items-center">
    <div class="container">
  <div class="row">
    <div class="col-12">
      <button type="button" class="btn btn-primary btn-lg mb-3 w-100" id='loadCountButton' onclick="loadCount()" accesskey="r" data-toggle="modal"
        data-target=${
          inventoryCountOptions.length >= 1 && "#loadPreviousCountDiv"
        }>
        Load Previous Count
      </button>
    </div>
    <div class="col-12">
      <button type="button" class="btn btn-success btn-lg mb-3 w-100" id='newCountButton' onclick="newCount()" accesskey="r" data-toggle="modal" data-target="#newCountDiv">
        New Count
      </button>
    </div>
  </div>
</div>

	</div>
      </div>
`;
}

function jsFunctionBackToScan() {
  return `	
	function backToScan() {
		document.getElementById('upc').value = ''; 
        $('#listView').hide();
        $('#enterUpcDiv').show();
        document.getElementById('upc').focus();

	}
`;
}

function jsFunctionCompleteCount() {
  return `	
	function completeCountClicked() { 
			let paramsJsonObj = JSON.stringify({				
			});

        var requestPayload = { 
				'function': 'completeCount' , 
				'paramsJsonObj': paramsJsonObj,
			}
	
			var xhr = new XMLHttpRequest();
		
			xhr.open( 'POST', '${scriptURL}', true );
			
			xhr.setRequestHeader( 'Accept', 'application/json' );		
		
			xhr.send( JSON.stringify( requestPayload ) );
		
			xhr.onload = function() {
				if( xhr.status === 200 ) {	
					try {	
						var responseObj = JSON.parse( xhr.response );
          				var parentRecordId = responseObj.parentRecordId;						
					} catch( e ) {	
						alert( 'Unable to parse the response.' );
						return;					
					}
			
					if ( parentRecordId ) {
var successMessage = 
  '<div class="container mt-5">' +
    '<div class="row justify-content-center">' +
      '<div class="col-md-8">' +
        '<div class="card border-success" style="box-shadow: 0 4px 8px rgba(0,0,0,0.1);">' +
          '<div class="card-body text-center" style="padding: 3rem;">' +
            '<i class="fas fa-check-circle text-success" style="font-size: 5rem; margin-bottom: 1rem;"></i>' +
            '<h2 class="card-title" style="font-size: 2.5rem; font-weight: bold; margin-bottom: 1.5rem;">Success!</h2>' +
            '<p class="card-text" style="font-size: 1.25rem; margin-bottom: 2rem;">Your count has been successfully sent to the back office!</p>' +
          '</div>' +
        '</div>' +
      '</div>' +
    '</div>' +
  '</div>';

document.getElementById('inventoryCountToolUI').innerHTML = successMessage;

          } else {					
						alert( 'Error: ' + xhr.response );									
					}																																		
				} else {				
					alert( 'Error: ' + xhr.status );									
				}
			}
		}`;
}

function jsFunctionContinueCount() {
  return `
		function continueCount() {			
			parentInventoryId = document.getElementById('countSelect').value;

            if ( parentInventoryId == '' ) { 
                alert( 'Please choose an existing count or new count.' );
                return; 
            }

            $('#newCountDiv').hide();
            $('#loadPreviousCountDiv').hide();
document.getElementById('startScreenDiv').className += ' d-none-important';
            $('#enterUpcDiv').show();
            document.getElementById('upc').focus();


            let paramsJsonObj = JSON.stringify({
				parentInventoryId : parentInventoryId
			});

            var requestPayload = { 
				'function': 'setPreviousCountVariables' , 
				'paramsJsonObj': paramsJsonObj,
			};

	
			var xhr = new XMLHttpRequest();
		
			xhr.open( 'POST', '${scriptURL}', true );
			
			xhr.setRequestHeader( 'Accept', 'application/json' );		
		
			xhr.send( JSON.stringify( requestPayload ) );
	
		}`;
}

function jsFunctionContinueWithScan() {
  return `	
	function continueWithScan() {
        $('#enterUpcDiv').hide();
        $('#enterQuantityDiv').show();
                    
            let itemData = queryResponsePayload.records[0]; 
            
            document.getElementById('currentItemData').innerHTML = JSON.stringify(itemData);
            
            let scanDivHTML= '<div class="card mb-4">'+
    '<div class="card-header bg-primary text-white text-center py-3">'+
      '<h3 class="mb-0 fw-bold"><i class="fas fa-barcode me-2"></i> UPC Code: '+ document.getElementById('upc').value +'</h3>'+
    '</div>'+
    '<div class="card-body">'+
      '<div class="mb-3">'+
        '<h5 class="card-subtitle mb-2 text-muted"><i class="fas fa-tag"></i> Item Name</h5>'+
        '<p class="card-text fw-bold mt-1 p-2 bg-light rounded">'+ itemData.item_name +'</p>'+
      '</div>'+
      '<div class="mb-3">'+
        '<h5 class="card-subtitle mb-2 text-muted"><i class="fas fa-database"></i> Quantity in Netsuite</h5>'+
        '<p class="card-text h5 mt-2 p-2 bg-light rounded">'+ itemData.available +'</p>'+
      '</div>'+
      '<div class="mb-3">'+
        '<h5 class="card-subtitle mb-2 text-muted"><i class="fas fa-ruler-combined"></i> Item UOM</h5>'+
        '<p class="card-text h5 mt-2 p-2 bg-light rounded">'+ itemData.stock_unit +'</p>'+
      '</div>'+
      '<div class="mb-3">'+
        '<h5 class="card-subtitle mb-2 text-muted"><i class="fas fa-comment"></i> Comment</h5>'+
        '<input type="text" class="form-control" placeholder="Enter comment" id="comment">'+
      '</div>'+
      '<div class="mb-3 form-check">'+
        '<input type="checkbox" class="form-check-input" id="receivedLastWeek">'+
        '<label class="form-check-label" for="receivedLastWeek">Item Received Last Week</label>'+
        '</div>'+
      '<div class="mb-3">'+
        '<h5 class="card-subtitle mb-2 text-muted"><i class="fas fa-clipboard-list"></i> Quantity in Store</h5>'+
        '<input class="form-control" type="number" id="fldQtyStore" required placeholder="Enter quantity">'+
      '</div>'+
    '</div>'+
  '</div>'+
  
'<div class="d-flex flex-column align-items-center">'+
    '<button type="button" class="btn btn-success btn-lg mb-2 w-75" id="scanAnother" onclick="enterScan(this.id)">'+
      '<i class="fas fa-barcode me-2"></i> Add and Scan Another'+
    '</button>'+
    '<button type="button" class="btn btn-success btn-lg mb-2 w-75" onclick="backToScan()"><i class="fas fa-arrow-left me-2"></i> Back to Scan</button>'+
    '<button type="button" class="btn btn-success btn-lg mb-2 w-75" id="viewList" onclick="enterScan(this.id)">'+
      '<i class="fas fa-list-ul me-2"></i> Add and View List'+
    '</button>'+
    
    '<button type="button" class="btn btn-success btn-lg mb-2 w-75" id="btnAddCompleteCount" onclick="enterScan(this.id)">'+
      '<i class="fas fa-check-circle me-2"></i> Add and Complete Count'+
    '</button>'+
  '</div>'+

'</div>';

            
            document.getElementById('enterQuantityDiv').innerHTML = scanDivHTML;    
            document.getElementById('fldQtyStore').focus();
    }	
`;
}

function jsFunctionEnterScan() {
  return `
		function enterScan(nextAction) {	
			let quantity = document.getElementById('fldQtyStore').value 			
			
            if ( quantity == '' ) { 
                alert( 'Please enter a quantity' );
                return; 
            }

			let currentItemData = JSON.stringify({...JSON.parse(document.getElementById('currentItemData').innerHTML), ...{
					quantity, 
					upc: document.getElementById('upc').value,
					comment: document.getElementById('comment').value,
          receivedLastWeek: document.getElementById('receivedLastWeek').checked,
				} })
				
			let paramsJsonObj = JSON.stringify({
				currentItemData,
				nextAction
			});

            var requestPayload = { 
				'function': 'addScan' , 
				'paramsJsonObj': paramsJsonObj,
			}
	
			var xhr = new XMLHttpRequest();
		
			xhr.open( 'POST', '${scriptURL}', true );
			
			xhr.setRequestHeader( 'Accept', 'application/json' );		
		
			xhr.send( JSON.stringify( requestPayload ) );
		
			xhr.onload = function() {
		
				if( xhr.status === 200 ) {	
					try {	
						queryResponsePayload = JSON.parse( xhr.response );						
					} catch( e ) {	
						alert( 'Unable to parse the response in enter scan.'+e.message );
						return;					
					}
			
					if ( queryResponsePayload['error'] == undefined ) {				
						switch (nextAction) {
							case "scanAnother":
        $('#enterQuantityDiv').hide();
        $('#enterUpcDiv').show();
        document.getElementById('upc').focus();
        document.getElementById('upc').value = ''; 

								break;
							case "viewList":
								$('#enterQuantityDiv').hide();
								generateTable(); 

								break;

							case "btnAddCompleteCount":
								completeCountClicked();

								break;
							default:
								log.error({
									title: "Invalid Next Action",
									details: nextAction,
								});
						}
					} else {					
						alert( 'Error: ' + queryResponsePayload.error.message );									
					}																																		
				} else {				
					alert( 'Error: ' + xhr.status );									
				}
			}
		}`;
}

function jsFunctionGenerateTable() {
  return `
  function generateTable() {
		try{
			let records = queryResponsePayload.records;
			
			if ( records.length > 0 ) {	
		

        let thead = '<thead class="thead-light">';
                
                thead += '<tr>' +
        '<th class="th-sm">UPC</th>' +
        '<th class="th-sm">Item</th>' +
        '<th class="th-sm">Qty</th>' +
        '<th class="th-sm">Actions</th>' +
    '</tr>';
                
                let tbody = '<tbody>';	
				
				  records.forEach((obj) => {
        tbody += '<tr>' +
            '<td class="td-sm">' + obj['upc'] + '</td>' +
            '<td class="td-sm">' + obj['item_name'] + '</td>' +
            '<td class="td-sm">' + obj['countQuantity'] + '</td>' +
            '<td class="td-sm">' +
                '<button type="button" class="btn btn-sm btn-danger delete-btn mb-1 me-1">Delete</button>' +
                '<button type="button" class="btn btn-sm btn-info details-btn mb-1">Details</button>' +
            '</td>' +
        '</tr>';
    });
    tbody += '</tbody>';

		var content = ''; 
    content += '<div class="container-fluid mt-3">';
    content += '<p class="mb-3">Counted ' + records.length + ' UPCs.</p>';
    content += '<div class="table-responsive">';
    content += '<table class="table table-striped table-sm table-hover" id="resultsTable">';
    content += thead;
    content += tbody;                
    content += '</table>';
    content += '</div>';
    content += '<div class="d-flex flex-column align-items-center mt-3">';
    content += '<button type="button" class="btn btn-success mb-2 w-100" onclick="completeCountClicked()"><i class="fas fa-check-circle me-2"></i> Complete Count</button>';
    content += '<button type="button" class="btn btn-success mb-2 w-100" onclick="backToScan()"><i class="fas fa-arrow-left me-2"></i> Back to Scan</button>';
    content += '<button type="button" class="btn btn-success mb-2 w-100" onclick="saveContinueLater()"><i class="fas fa-save me-2"></i> Save and Continue Later</button>';
    content += '</div>';
    content += '</div>';
				
				document.getElementById('listView').innerHTML = content;
$('#resultsTable').DataTable({
    responsive: true,
    scrollX: false,
    autoWidth: true,
    columnDefs: [
        { responsivePriority: 1, targets: 0 },
        { responsivePriority: 2, targets: -1 },
        { responsivePriority: 3, targets: 2 },
        { responsivePriority: 4, targets: 1 }
    ]
});

            
				$('#listView').show();
        
			} else {			
				document.getElementById('listView').innerHTML = '<h5 class="text-warning">Error getting table view.</h5>';
			}

      $(".details-btn").on("click", function () {  
    var row = $(this).closest('tr');
    var upc = row.find('td:eq(0)').text();
    var item = records.find(function(record) { return record.upc === upc; });


    if (item) {
        var detailsHTML = '<div class="modal fade" id="detailsModal" tabindex="-1" role="dialog" aria-labelledby="detailsModalLabel">'+
            '<div class="modal-dialog" role="document">'+
                '<div class="modal-content">'+
                    '<div class="modal-header">'+
                        '<h5 class="modal-title" id="detailsModalLabel">Item Details</h5>'+
                        
    		  '<button type="button" class="close close-modal-btn" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>'+
                    '</div>'+
                    '<div class="modal-body">'+
                        '<p><strong>UPC:</strong> '+ item.upc +'</p>'+
                        '<p><strong>Quantity:</strong> '+ item.countQuantity +'</p>'+
                        '<p><strong>Stock UOM:</strong> '+ item.stock_unit +'</p>'+
                        '<p><strong>Is Allocated Item:</strong> '+ item.is_allocated_item +'</p>'+
                        '<p><strong>Sale Price:</strong> '+ item.sale_price +'</p>'+
                         '<p><strong>Item Received Last Week:</strong> <input type="checkbox" id="receivedLastWeek" data-upc="'+ item.upc +'"' + (item.received_last_week ? ' checked' : '') + '></p>'+
                        '<p><strong>Comment:</strong> '+ (item.comment || 'N/A') +'</p>'+
                          '<p><strong>Add/Edit Comment:</strong></p>'+
                        '<textarea class="form-control" id="modalComments" rows="3">'+ (item.comment || '') +'</textarea>'+
                    '</div>'+
                    '<div class="modal-footer">'+
                         '<button type="button" class="btn btn-primary save-updates-btn">Save and Update</button>'+
                        '<button type="button" class="btn btn-secondary close-modal-btn">Back</button>'+
                    '</div>'+
                '</div>'+
            '</div>'+
        '</div>';

                document.addEventListener('click', function(event) {
    if (event.target.classList.contains('save-updates-btn')) {
        var modalContent = event.target.closest('.modal-content');
        var newComment = modalContent.querySelector('#modalComments').value.trim();
        
        // Update the displayed comment in the modal
        var existingCommentsElement = Array.from(modalContent.querySelectorAll('p')).find(p => p.textContent.includes('Comment:'));
        if (existingCommentsElement) {
            existingCommentsElement.innerHTML = '<strong>Comment:</strong> ' + newComment;
        }
        // Find the checkbox within this modal
        var receivedLastWeekCheckbox = modalContent.querySelector('#receivedLastWeek');
        var receivedLastWeekItem = receivedLastWeekCheckbox ? receivedLastWeekCheckbox.checked : false;
        // Get the UPC from the checkbox's data attribute
        var itemUpc = receivedLastWeekCheckbox ? receivedLastWeekCheckbox.getAttribute('data-upc') : null;
        if (itemUpc) {
    
         let paramsJsonObj = JSON.stringify({
				itemUpc : itemUpc,
       	task: 'update',
        comment: newComment,
        receivedLastWeek: receivedLastWeekItem,
			  });

				var requestPayload = { 
					'function': 'updateNetsuiteRecords' , 
					'paramsJsonObj': paramsJsonObj,
				};
        
                                var xhr = new XMLHttpRequest();                        
                                xhr.open( 'POST', '${scriptURL}', true );                                
                                xhr.setRequestHeader( 'Accept', 'application/json' );                                        
                                xhr.send( JSON.stringify( requestPayload ) );
        $('#detailsModal').remove();
    }
}
});

        $('#detailsModal').remove();

        $('body').append(detailsHTML);
        $('.close-modal-btn').on('click', function() {
    $('#detailsModal').remove();;
});




        var modalElement = document.getElementById('detailsModal');
    if (modalElement) {
        modalElement.style.display = 'block';
        modalElement.classList.add('show');
    } else {
        alert('Item details not found');
    }
    } else {
        alert('Item details not found');
    }
});

			
			$(".delete-btn").on("click", function () {
        const row = jQuery(this).closest("tr");
         row.hide();
		    const itemUpc = row.find("td:eq(0)").text();
			
        let paramsJsonObj = JSON.stringify({
				itemUpc : itemUpc,
       	task: 'delete',
			  });

				var requestPayload = { 
					'function': 'updateNetsuiteRecords' , 
					'paramsJsonObj': paramsJsonObj,
				};

	
				var xhr = new XMLHttpRequest();			
				xhr.open( 'POST', '${scriptURL}', true );				
				xhr.setRequestHeader( 'Accept', 'application/json' );					
				xhr.send( JSON.stringify( requestPayload ) );
     		});

       
		}catch(e){
			alert(e)
		}
		
	}`;
}

function jsFunctionLoadPreviousCount() {
  return `
		function loadCount() {			
			${
        inventoryCountOptions.length == 0 &&
        `alert("No matching inventory counts found. Select new count to begin.")`
      }
			${inventoryCountOptions.length > 0 && `$('#loadPreviousCountDiv').show();`}
		}`;
}

function jsFunctionNewCount() {
  return `function newCount() {			
			  $('#newCountDiv').show();
		}`;
}

function jsFunctionSaveContinueLater() {
  return `
    function saveContinueLater(){

            var requestPayload = { 
				'function': 'saveContinueLater' , 
			};
	
			var xhr = new XMLHttpRequest();
    		xhr.open( 'POST', '${scriptURL}', true );
			xhr.setRequestHeader( 'Accept', 'application/json' );		
			xhr.send( JSON.stringify( requestPayload ) );
		
			xhr.onload = function() {
				if( xhr.status === 200 ) {	
					try {	
						var responseObj = JSON.parse( xhr.response );
         			} catch( e ) {	
						alert( 'Unable to parse the response.' );
						return;					
					}
			
					if ( responseObj.success ) {
          document.getElementById('inventoryCountToolUI').innerHTML = 
    '<div class="container mt-5">' +
    '  <div class="row justify-content-center">' +
    '    <div class="col-md-8">' +
    '      <div class="card border-success">' +
    '        <div class="card-body text-center">' +
    '          <i class="fas fa-check-circle text-success" style="font-size: 4rem;"></i>' +
    '          <h2 class="card-title mt-3 mb-4">Success!</h2>' +
    '          <p class="card-text lead">Your count has been successfully saved.</p>' +
    '        </div>' +
    '      </div>' +
    '    </div>' +
    '  </div>' +
    '</div>';


					} else {					
						alert( 'Error: ' + xhr.response );									
					}																																		
				} else {				
					alert( 'Error: ' + xhr.status );									
				}
			}
}
  `;
}

function jsFunctionScanItem() {
  return `
		function scanItem() {	
			let upc = document.getElementById('upc').value 
			
            if ( upc == '' ) { 
                alert( 'Please enter a UPC' );
                return; 
            }
	
			let paramsJsonObj = JSON.stringify({
      			upcCode: upc,
			});
	
            var requestPayload = { 
				'function': 'getDataForUpc', 
				'paramsJsonObj': paramsJsonObj,
			}
	
			var xhr = new XMLHttpRequest();
		
			xhr.open( 'POST', '${scriptURL}', true );
			
			xhr.setRequestHeader( 'Accept', 'application/json' );		
		
			xhr.send( JSON.stringify( requestPayload ) );
		
			xhr.onload = function() {

				if( xhr.status === 200 ) {	
					try {			
						queryResponsePayload = JSON.parse( xhr.response );

									if(queryResponsePayload.records[0].hasOwnProperty('error')){
							alert('Could not find matching item with this upc code!');	
							let upc = document.getElementById('upc');

							upc.value = '';
							upc.focus();
							return;	
						}				
					} catch( e ) {	
           alert(e.message);
           alert("UPC "+upc);
           alert(queryResponsePayload);
						alert( 'Unable to parse the response in trying to scan.'+e.message+JSON.stringify(queryResponsePayload) );
						return;					
					}
			
					if ( queryResponsePayload['error'] == undefined ) {				
                        continueWithScan()
					} else {					
						alert( 'Error: ' + queryResponsePayload.error.message );									
					}																																		
				} else {				
					alert( 'Error: ' + xhr.status );									
				}
			}
	
		
		}`;
}

function jsFunctionViewListNew() {
  return `
		function viewListNew() {	
        var requestPayload = { 
				'function': 'viewListNew' , 
			}
	
			var xhr = new XMLHttpRequest();
		
			xhr.open( 'POST', '${scriptURL}', true );
			
			xhr.setRequestHeader( 'Accept', 'application/json' );		
		
			xhr.send( JSON.stringify( requestPayload ) );
		
			xhr.onload = function() {
		
				if( xhr.status === 200 ) {	
					try {	
						queryResponsePayload = JSON.parse( xhr.response );	
					} catch( e ) {	
						alert( 'Unable to parse the response in view list new. '+e.message );
						return;					
					}
			
					if ( queryResponsePayload['records'].error == undefined ) {				
          				$('#enterQuantityDiv').hide();
                  $('#enterUpcDiv').hide();
								generateTable(); 
						}
                else{
                alert(queryResponsePayload['records'].error);
                }
                }
	                  }
}`;
}

function jsFunctionStartCount() {
  return `
		function startCount() {		
			subsidiaryId = document.getElementById('subsidiarySelect').value;
      const dateChosen = document.getElementById('datepicker').value;
      const selectedCountType = document.querySelector('input[name="countType"]:checked').value;

            if ( subsidiaryId == '' ) { 
                alert( 'Please choose a store. ' );
                return; 
            }

            $('#newCountDiv').hide();
            $('#loadPreviousCountDiv').hide();
            document.getElementById('startScreenDiv').className += ' d-none-important';
            $('#enterUpcDiv').show();
            document.getElementById('upc').focus();


            let paramsJsonObj = JSON.stringify({
                  chosenSubsidiaryId : subsidiaryId,
                  chosenDate: dateChosen,
                  chosenType: selectedCountType,
            });

            var requestPayload = { 
              'function': 'setNewCountVariables' , 
              'paramsJsonObj': paramsJsonObj,
            };

	    			var xhr = new XMLHttpRequest();
		
            xhr.open( 'POST', '${scriptURL}', true );
            
            xhr.setRequestHeader( 'Accept', 'application/json' );		
          
            xhr.send( JSON.stringify( requestPayload ));	
          }`;
}
