/******************************************************************************************************
	Script Name - AVA_MAP_TwoWayItemMasterSync.js
	Company -     Avalara Technologies Pvt Ltd.
******************************************************************************************************/

/**
 *@NApiVersion 2.0
 *@NScriptType MapReduceScript
 */

define(['N/record', 'N/search', 'N/runtime', 'N/https', './utility/AVA_Library',  './utility/AVA_CommonServerFunctions'],
	function (record, search, runtime, https, ava_library, ava_commonFunction){
		function getInputData(context){
			try{
				var nextLink = ''
				var responseBodyValue = [];
				
				var parameterDetails = runtime.getCurrentScript().getParameter({
					name: 'custscript_ava_twowayimsbatchdetails'
				});
				log.debug('getInputData', 'parameterDetails = ' + parameterDetails);
				parameterDetails = JSON.parse(parameterDetails);
				
				if(parameterDetails.newbatchid){
					var twoWayIMSBatchId = record.submitFields({
						type: 'customrecord_avatwowayimsbatch',
						id: parameterDetails.newbatchid,
						values:{
							custrecord_ava_twowayimsstatus: 'In Progress'
						},
						options:{
							enableSourcing: false,
							ignoreMandatoryFields: true
						}
					});
				}
				
				var startDate = ava_library.mainFunction('AVA_ConvertDate', parameterDetails.startdate);
				var endDate = ava_library.mainFunction('AVA_ConvertDate', parameterDetails.enddate);
				startDate = startDate + 'T00:00:00.000';
				endDate = endDate + 'T23:59:59.999';
				var filterDetails = "?$filter=createdDate ge " + startDate + " and createdDate le " + endDate;
				
				var details;
				var defCompanyId = ava_library.mainFunction('AVA_GetDefaultCompanyCode', parameterDetails.subsidiary);
				var avaConfigObjRec = ava_library.mainFunction('AVA_LoadValuesToGlobals', '');
				
				if(avaConfigObjRec.AVA_AdditionalInfo3 != null && avaConfigObjRec.AVA_AdditionalInfo3.length > 0){
					details = avaConfigObjRec.AVA_AdditionalInfo3;
				}
				else if(avaConfigObjRec.AVA_AdditionalInfo != null && avaConfigObjRec.AVA_AdditionalInfo.length > 0){
					details = ava_commonFunction.mainFunction('AVA_General', (avaConfigObjRec.AVA_AccountValue + '+' + avaConfigObjRec.AVA_AdditionalInfo + '+' + avaConfigObjRec.AVA_AdditionalInfo1 + '+' + avaConfigObjRec.AVA_AdditionalInfo2));
				}
				
				var avaTaxDetails = ava_library.mainFunction('AVA_InitSignatureObject', avaConfigObjRec.AVA_ServiceUrl);
				
				do{
					var retrieveItemsForCompany = new avaTaxDetails.retrieveItemsForCompany(details, defCompanyId[1], filterDetails, nextLink, avaConfigObjRec.AVA_AdditionalInfo3);
					
					var response = https.get({
						url: retrieveItemsForCompany.url,
						body: retrieveItemsForCompany.data,
						headers: retrieveItemsForCompany.headers
					});
					
					if(response.code == 200){
						var responseBody = JSON.parse(response.body);
						responseBodyValue = responseBodyValue.concat(responseBody.value);
						nextLink = responseBody['@nextLink'];
					}
					else{
						log.debug('getInputData', 'response.code = ' + response.code);
					}
				}
				while(nextLink != null && nextLink.length > 0);
				
				log.debug('getInputData', 'Item Length Count = ' + responseBodyValue.length);
				return responseBodyValue;
			}
			catch(e){
				log.error('getInputData', 'ERROR NAME = ' + e.name + ', ERROR TYPE = ' + e.type + ', ERROR MESSAGE = ' + e.message);
			}
		}

		function map(context){
			try{		
				var parameterDetails = runtime.getCurrentScript().getParameter({
					name: 'custscript_ava_twowayimsbatchdetails'
				});
				parameterDetails = JSON.parse(parameterDetails);
				
				var itemDetailsObj = context.value;
				itemDetailsObj = JSON.parse(itemDetailsObj);
				var itemDetails = AVA_SearchItemCodeInNetSuite(itemDetailsObj.itemCode);
				if(itemDetails['itemid']){
					var imsRecordId = AVA_SearchIMSRecord(itemDetails['itemid']);
					AVA_CreateUpdateIMSDetailsRec(imsRecordId, itemDetails['itemid'], itemDetailsObj);
					AVA_CreateTwoWayIMSDetailsRec(itemDetailsObj, true, parameterDetails.newbatchid);
					AVA_UpdateItemDetails(itemDetails, itemDetailsObj);
				}
				else{
					AVA_CreateTwoWayIMSDetailsRec(itemDetailsObj, false, parameterDetails.newbatchid);
				}
			}
			catch(e){
				log.error('map', 'ERROR NAME = ' + e.name + ', ERROR TYPE = ' + e.type + ', ERROR MESSAGE = ' + e.message);
			}
		}
		
		function summarize(context){
			try{
				var parameterDetails = runtime.getCurrentScript().getParameter({
					name: 'custscript_ava_twowayimsbatchdetails'
				});
				parameterDetails = JSON.parse(parameterDetails);
				
				var j = 0, twoWayIMSDetails = [];
				
				var avaIMSSearchObj = search.create({
					type: "customrecord_avatwowayimsdetails",
					filters: [
						["isinactive", "is", "F"],
						"AND",
						["custrecord_ava_twowayimsbatchreference", "anyof", parameterDetails.newbatchid]
					],
					columns: [
						"custrecord_ava_twowayimspresentinnetsuit"
					]
				});
				
				avaIMSSearchObj = avaIMSSearchObj.run();
				var searchResult = avaIMSSearchObj.getRange({
					start: 0,
					end: 1000
				});
				
				while(searchResult != null && searchResult.length > 0){
					for(var i = 0; i < searchResult.length; i++){
						twoWayIMSDetails.push(searchResult[i]);
						j++;
					}
					
					if(searchResult.length == 1000){
						searchResult = searchRecord.getRange({
							start: j,
							end: j + 1000
						});
					}
					else{
						break;
					}
				}
				
				var totalRecords = twoWayIMSDetails.length;
				var existRecords = 0;
				var nonExistRecords = 0;
				
				for(var k = 0; k < twoWayIMSDetails.length; k++){
					if(twoWayIMSDetails[k].getValue('custrecord_ava_twowayimspresentinnetsuit')){
						existRecords++;
					}
					else{
						nonExistRecords++;
					}
				}
				
				record.submitFields({
					type: 'customrecord_avatwowayimsbatch',
					id: parameterDetails.newbatchid,
					values: {
						custrecord_ava_twowayimsstatus: 'Completed', custrecord_ava_twowayimstotalitems: totalRecords, custrecord_ava_twowayexistsitems: existRecords, custrecord_ava_twowayimsnonexistitems: nonExistRecords
					},
					options: {
						enableSourcing: false,
						ignoreMandatoryFields: true
					}
				});
			}
			catch(e){
				log.error('summarize', 'ERROR NAME = ' + e.name + ', ERROR TYPE = ' + e.type + ', ERROR MESSAGE = ' + e.message);
			}
		}
		
		function AVA_SearchItemCodeInNetSuite(itemCode){
			var itemDetails = [];
			itemDetails['itemid'] = '';
			itemDetails['itemtype'] = '';
			try{
				var itemSearchObj = search.create({
				   type: "item",
				   filters:
				   [
					  ["name","is",itemCode]
				   ],
				   columns:
				   [
					  search.createColumn({name: "itemid"})
				   ]
				});
				itemSearchObj.run().each(function(result){
				   itemDetails['itemid'] = result.id;
				   itemDetails['itemtype'] = result.recordType;
				   return true;
				});
			}
			catch(e) {
				log.error('AVA_SearchItemCodeInNetSuite - ERROR', 'ERROR NAME = ' + e.name + ', ERROR TYPE = ' + e.type + ', ERROR MESSAGE = ' + e.message);
			}
			return itemDetails;
		}
		
		function AVA_CreateTwoWayIMSDetailsRec(itemDetailsObj, presentInNetSuiteFlag, twoWayIMSBatchId){
			try{
				var twoWayIMSDetailsObj = record.create({
				   type: 'customrecord_avatwowayimsdetails'                      
				});
				twoWayIMSDetailsObj.setValue({
					fieldId: 'custrecord_ava_twowayimsitemcode',
					value: itemDetailsObj.itemCode
				});
				twoWayIMSDetailsObj.setValue({
					fieldId: 'custrecord_ava_twowayimsdescription',
					value: itemDetailsObj.description
				});
				twoWayIMSDetailsObj.setValue({
					fieldId: 'custrecord_ava_twowayimstaxcode',
					value: itemDetailsObj.taxCode
				});
				twoWayIMSDetailsObj.setValue({
					fieldId: 'custrecord_ava_twowayimspresentinnetsuit',
					value: presentInNetSuiteFlag
				});
				twoWayIMSDetailsObj.setValue({
					fieldId: 'custrecord_ava_twowayimsdetailsstatus',
					value: 'Success'
				});
				twoWayIMSDetailsObj.setValue({
					fieldId: 'custrecord_ava_twowayimsbatchreference',
					value: twoWayIMSBatchId
				});
				var twoWayIMSDetailsId = twoWayIMSDetailsObj.save({
					enableSourcing: true,
					ignoreMandatoryFields: true
				});
			}
			catch(e){
				log.error('AVA_CreateTwoWayIMSDetailsRec - ERROR', 'ERROR NAME = ' + e.name + ', ERROR TYPE = ' + e.type + ', ERROR MESSAGE = ' + e.message);
			}
		}
		
		function AVA_UpdateItemDetails(itemDetails, itemDetailsObj){
			try{
				if(itemDetails['itemtype'] == 'assemblyitem' || itemDetails['itemtype'] == 'kititem' || itemDetails['itemtype'] == 'lotnumberedassemblyitem' || itemDetails['itemtype'] == 'serializedassemblyitem'){
					var itemRecId = record.submitFields({
						type: itemDetails['itemtype'],
						id: itemDetails['itemid'],
						values: {
							custpage_ava_taxcodemapping: itemDetailsObj.taxCode, description: itemDetailsObj.description, upccode: itemDetailsObj.upc
						},
						options: {
							enableSourcing: false,
							ignoreMandatoryFields : true
						}
					});
				}
				else{
					var itemRecId = record.submitFields({
						type: itemDetails['itemtype'],
						id: itemDetails['itemid'],
						values: {
							custpage_ava_taxcodemapping: itemDetailsObj.taxCode, salesdescription: itemDetailsObj.description, upccode: itemDetailsObj.upc
						},
						options: {
							enableSourcing: false,
							ignoreMandatoryFields : true
						}
					});
				}						
			}
			catch(e){
				log.error('AVA_UpdateItemDetails - ERROR', 'ERROR NAME = ' + e.name + ', ERROR TYPE = ' + e.type + ', ERROR MESSAGE = ' + e.message);
			}
		}
		
		function AVA_SearchIMSRecord(itemInternalId){
			var imsRecordId = 'null';
			
			try{
				var avaimsdetailsSearchObj = search.create({
				   type: "customrecord_avaimsdetails",
				   filters:
				   [
					  ["custrecord_ava_imsiteminternalid", "equalto", itemInternalId]
				   ]
				});
				avaimsdetailsSearchObj.run().each(function(result){
				   imsRecordId = result.id;
				   return true;
				});
			}
			catch(e){
				log.error('AVA_SearchIMSRecord', 'ERROR NAME = ' + e.name + ', ERROR TYPE = ' + e.type + ', ERROR MESSAGE = ' + e.message);
			}
			
			return imsRecordId;
		}
		
		function AVA_CreateUpdateIMSDetailsRec(imsRecordId, itemInternalId, itemDetailsObj){
			try{
				if(imsRecordId == 'null' || !imsRecordId){
					var imsDetailsRecObj = record.create({
						type: 'customrecord_avaimsdetails'
					});
					imsDetailsRecObj.setValue({
						fieldId: 'custrecord_ava_imsitem',
						value: itemInternalId
					});
					imsDetailsRecObj.setValue({
						fieldId: 'custrecord_ava_imsiteminternalid',
						value: itemInternalId
					});
				}
				else{
					var imsDetailsRecObj = record.load({
						type: 'customrecord_avaimsdetails',
						id: imsRecordId
					});
				}
				
				imsDetailsRecObj.setValue({
					fieldId: 'custrecord_ava_imsitemid',
					value: itemDetailsObj.id
				});
				imsDetailsRecObj.setValue({
					fieldId: 'custrecord_ava_imscontexttype',
					value: 'MapReduce'
				});
				var imsDetailsRecId = imsDetailsRecObj.save({
					enableSourcing: true,
					ignoreMandatoryFields: true
				});
			}
			catch(e){
				log.error('AVA_CreateUpdateIMSDetailsRec - ERROR', 'ERROR NAME = ' + e.name + ', ERROR TYPE = ' + e.type + ', ERROR MESSAGE = ' + e.message);
			}
		}
		
		return{
			getInputData: getInputData,
			map: map,
			summarize: summarize
		};
	}
);