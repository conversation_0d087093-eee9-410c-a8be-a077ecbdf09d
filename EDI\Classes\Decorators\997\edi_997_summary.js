/**
 * @description Class containing functions to post-process 997
 *
 * @NApiVersion 2.1
 * <AUTHOR> <<EMAIL>>
 */

define([
    "exports",
    "require",
    "../edi_summary",
    "../../../../Classes/vlmd_custom_error_object",
], function (/** @type {any} */ exports, /** @type {any} */ require) {
    const { EDISummary } = require("../edi_summary");
    /** @type {CustomErrorObject} */
    const CustomErrorObject = require("../../../../Classes/vlmd_custom_error_object");

    /**
     * 997 Processing Summary Class
     *
     * @class
     * @typedef {import("../../../../Classes/vlmd_custom_error_object").CustomErrorObject} CustomErrorObject
     * @extends {EDISummary}
     */
    class EDI997Summary extends EDISummary {
        /** @param {{[key:string]: any}} params */
        constructor (params) {
            super(params);
        }
    }

    exports.EDI997Summary = EDI997Summary;
});