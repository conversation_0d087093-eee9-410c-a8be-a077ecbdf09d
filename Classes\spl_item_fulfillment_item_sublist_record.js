/**
 * Item Sublist Record class
 * that represents the line in the item sublist
 * of an Item Fulfillment
 *
 * @NApiVersion 2.1
 * <AUTHOR>
 */

define(["N/search"], (search) => {

	/**
	 * Item Fulfillment Transaction Record class
	 *
	 * @class
	 * @type {import("./spl_item_sublist_record").ItemFulfillmentItemSublistRecord}
	 */
	class ItemFulfillmentItemSublistRecord {
		constructor(transaction, index) {
			this.Column = {
				itemIsShipByTruck: "custitem_spl_item_ship_by_truck"
			};
			this.Field = {
				item: "item",
				itemName: "itemname"
			};
			this.Sublist = {
				item: "item"
			};

			this.itemId = transaction.getSublistValue({
				sublistId: this.Sublist.item,
				fieldId: this.Field.item,
				line: index
			});
	
			this.itemName = transaction.getSublistText({
				sublistId: this.Sublist.item,
				fieldId: this.Field.itemName,
				line: index
			});

			const itemLookup = this.itemId && search.lookupFields({
				type: search.Type.ITEM,
				id: this.itemId,
				columns: [this.Column.itemIsShipByTruck]
			});
	
			this.itemIsShipByTruck = itemLookup && itemLookup[this.Column.itemIsShipByTruck]; //Boolean value if checkbox is checked off
		}
	}

	return ItemFulfillmentItemSublistRecord;
});