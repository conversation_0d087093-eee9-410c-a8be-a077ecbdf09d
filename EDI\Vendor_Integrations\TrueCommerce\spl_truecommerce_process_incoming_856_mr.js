/**
 * @description Pulls EDI 856 files from the TrueCommerce folder and creates IF records in NS
 * <AUTHOR>
 * @module spl_truecommerce_process_incoming_856_mr
 * @NApiVersion 2.1
 * @NScriptType MapReduceScript
 * @NAmdConfig /SuiteScripts/config.json
 */

define([
  "require",
  "GetEdiFileContents",
  "GetEdiPartnerValuesLib",
  "ProcessIncoming856Lib",
  "ProcessIncoming856EndLib",
  "EdiDataObject",
], function (
  require,
  getEdiFileContentsLib,
  getEdiPartnerValuesLib,
  processAsnLib,
  processEnd,
  EdiDataObject
) {
  const log = require("N/log");
  const error = require("N/error");

  /** @typedef {import("../Classes/vlmd_edi_transaction").EdiDataObject} EdiDataObject*/
  const dataObj = new EdiDataObject({
    prodGuidBool: true,
    prodDirectoryBool: true,
    decodeContent: true,
    prodGUID: "5a2a8c654ee24859a82db7dfa104ae1c",
    sandboxGUID: "",
    prodDirectory: "/edi/prod/vendor/true_commerce/in/856",
    referenceDirectory: "/edi/reference/vendor/true_commerce/in/856",
    testDirectory: "/edi/test/vendor/true_commerce/in/856",
    documentType: "Tracking",
    documentTypeId: 4,
    purchasingSoftware: "True Commerce",
    purchasingSoftwareId: 12,
  });

  /**@type {Object} */
  const partnerValues = getEdiPartnerValuesLib.getTrueCommerceValues();

  if (!partnerValues) {
    throw `Partner values not gotten correctly for ${dataObj.purchasingSoftware}.`;
  }

  /**
   * Get all new files in the TrueCommerce 856 folder
   *
   * @returns {Array< { continueProcessing: boolean, fileContents: Array }>} of new files
   */
  function getInputData() {
    return getEdiFileContentsLib.getEdiFileContents(dataObj).fileContents;
  }

  /**
   * Process EDI file and create Item Fulfillment record if applicable
   *
   * @param {import("N/types").EntryPoints.MapReduce.map} context
   */
  function map(context) {
    /**@type {JSON} */
    const ediFile = JSON.parse(context.value);

    let {
      errorLog,
      processingLog,
      itemFulfillmentInternalId,
      itemFulfillmentName,
      isTrackingForDropShipOrder,
      subsidiary,
    } = processAsnLib.processASN(dataObj, partnerValues, ediFile);

    let sourcePath = `/edi/prod/vendor/true_commerce/in/856/${ediFile.fileName}`;
    let destinationPath = `/edi/reference/vendor/true_commerce/in/856/${ediFile.fileName}`;

    try {
      var connection = getEdiFileContentsLib.createConnection(
        dataObj,
        false, //appendToDirectory
        false, //referenceDirectory
        true //So that the "createConnectionToRoot" param will be true
      );

      connection.move({
        from: sourcePath,
        to: destinationPath,
      });
    } catch (err) {
      log.error(
        "Error Moving File",
        `Source path: ${sourcePath}\nDestination path: ${destinationPath}\nError: ${err}`
      );
      errorLog.push(err);
    }

    if (errorLog.length > 0) {
      log.error(`Error Processing File ${ediFile?.fileName}`, errorLog.join());
      throw error.create({ name: ediFile?.fileName, message: errorLog.join() });
    }

    let contextMessage = `${subsidiary}, ${
      processingLog.length <= 0 ? "Success" : "Created With Errors"
    },${itemFulfillmentInternalId}, ${itemFulfillmentName}, ${
      processingLog.length > 0 ? processingLog.join(", ") : ""
    }`;

    context.write({
      key: isTrackingForDropShipOrder ? "DropShip Order" : "Warehouse Order",
      value: contextMessage,
    });
  }

  /**
   * Create an EDI transaction record with processing details
   *
   * @param {import("N/types").EntryPoints.MapReduce.summarize} context
   */

  function summarize(context) {
    const ediTransactionRecordObj = processEnd.getEdiTransactionObj(
      dataObj,
      context
    );

    if (ediTransactionRecordObj) {
      const ediTransactionRecordId = processEnd.createEdiTransactionRecord(
        ediTransactionRecordObj
      );

      log.debug(
        "EDI Transaction Record",
        ediTransactionRecordId
          ? '<a href="https://5802576.app.netsuite.com/app/common/custom/custrecordentry.nl?rectype=707&id=' +
              ediTransactionRecordId +
              '&selectedtab=custom592"> Record Link </a>'
          : `No record created`
      );
    }
  }

  return {
    getInputData,
    map,
    summarize,
  };
});
