/******************************************************************************************************
	Script Name - AVA_SUT_SuiteCommerceCertCapture.js
	Company -     Avalara Technologies Pvt Ltd.
******************************************************************************************************/

/**
 *@NApiVersion 2.0
 *@NScriptType Suitelet
*/

define(['N/url', 'N/runtime', 'N/search','N/https', 'N/record', './utility/AVA_Library.js', './utility/AVA_CommonServerFunctions.js', 'N/log'],
	function (url, runtime, search,https, record, ava_library, ava_commonFunction, log){
		function onRequest(context){
			try{
				var request = context.request;
				var requestObject = request.parameters;
				var customer = requestObject.customer;
				var address_line = requestObject.address_line;
				var functionName = requestObject.op;
				var email = requestObject.email;
				var crecord = record.load({
					type: record.Type.CUSTOMER,
					id: customer
				});
				
				switch(functionName){
					case 'initiate':
						var result = AVA_AddCustomerToCertCapture(crecord, address_line, email);
						break;
						
					default:
						break;
				}
				
				context.response.write(JSON.stringify(result));
			}
			catch(error){
				log.debug("error", JSON.stringify(error));
			}
		}
		
		function AVA_AddCustomerToCertCapture(crecord, address_line, email){
			try{
				var crecord = crecord;
				var defCompanyId = ava_library.mainFunction('AVA_GetDefaultCompanyCode', crecord.getValue('subsidiary'));
				var avaConfigObjRecvd = ava_library.mainFunction('AVA_LoadValuesToGlobals', '');
				
				if(avaConfigObjRecvd.AVA_CustomerCode != null && avaConfigObjRecvd.AVA_CustomerCode > 8){
					var result = {};
					result.code = 'error';
					result.message = "Customer cannot be created. " + ava_library.mainFunction('AVA_ErrorCodeDesc', 24);
					return result;
				}
				
				if(defCompanyId[1] == null || defCompanyId[1].length == 0){
					var result = {};
					result.code = 'error';
					result.message = 'Please re-run the Avalara Configuration and map Default Company Code to respective subsidiary.';
					return result;
				}
				
				var details;
				var customerCode = AVA_GetCustomerInfo(crecord, avaConfigObjRecvd);
				
				if(avaConfigObjRecvd.AVA_AdditionalInfo3 != null && avaConfigObjRecvd.AVA_AdditionalInfo3.length > 0){
					details = avaConfigObjRecvd.AVA_AdditionalInfo3;
				}
				else{
					details = ava_commonFunction.mainFunction('AVA_General', (avaConfigObjRecvd.AVA_AccountValue + '+' + avaConfigObjRecvd.AVA_AdditionalInfo + '+' + avaConfigObjRecvd.AVA_AdditionalInfo1 + '+' + avaConfigObjRecvd.AVA_AdditionalInfo2));
				}
				
				var avaTax = ava_library.mainFunction('AVA_InitSignatureObject', avaConfigObjRecvd.AVA_ServiceUrl);
				var CertCapture = new avaTax.certCapture();
				var customers = AVA_CreateCustomerAvaCertBody(customerCode, crecord, address_line, CertCapture);
				var result = AVA_CreateCustomerAvaCert(crecord, address_line, email, defCompanyId, customerCode, details, CertCapture, customers, avaConfigObjRecvd.AVA_AdditionalInfo3);
				return result;
			}
			catch(err){
				var result = {};
				result.code = 'error';
				result.message = 'Adding Customer on CertCapture was not successful. ' + err.message;
				return result;
			}
		}
		
		function AVA_CreateCustomerAvaCertBody(customerCode, crecord, address_line, CertCapture){
			var customers = [], phone, attention, addr1, addr2, city, state, zip, country;
			var customer = new CertCapture.customer();
			
			for(var i = 0; i < crecord.getLineCount('addressbook'); i++){
				var internalid = crecord.getSublistValue('addressbook', 'internalid', i);
				
				if(internalid == address_line){
				    phone = crecord.getSublistValue('addressbook', 'phone_initialvalue', i);
					attention = crecord.getSublistValue('addressbook', 'attention_initialvalue', i);
					addr1 = crecord.getSublistValue('addressbook', 'addr1_initialvalue', i);
					addr2 = crecord.getSublistValue('addressbook', 'addr2_initialvalue', i);
					city = crecord.getSublistValue('addressbook', 'city_initialvalue', i);
					state = crecord.getSublistValue('addressbook', 'state_initialvalue', i);
					zip = crecord.getSublistValue('addressbook', 'zip_initialvalue', i);
					country = crecord.getSublistValue('addressbook', 'country_initialvalue', i);
					break;
				}
			}
			
			var phone = (phone != null && phone.length > 0) ? phone : crecord.getValue('phone');
			phone = phone.replace(/\(|\)/gi, '');
			
			var fax = crecord.getValue('fax');
			fax = fax.replace(/\(|\)/gi, '');
			
			var email = crecord.getValue('email');
			
			customer.phoneNumber = phone;
			customer.faxNumber = fax;
			customer.emailAddress = email;
			customer.customerCode = (customerCode[0] != null ? customerCode[0].substring(0, 49) : '');
			customer.name = (customerCode[1] != null ? customerCode[1].substring(0, 49) : '');
			customer.attnName = attention;
			customer.line1 = addr1;
			customer.line2 = addr2;
			customer.city = city;
			customer.region = state;
			customer.postalCode = zip;
			var returnCountryName = ava_library.mainFunction('AVA_CheckCountryName', country);
			customer.country = returnCountryName[1];
			customer.contactName = (customerCode[1] != null ? customerCode[1].substring(0, 49) : '');
			
			customers.push(customer);
			return customers;
		}
		
		function AVA_CreateCustomerAvaCert(crecord, address_line, email, defCompanyId, customerCode, details, CertCapture, customers, addInfo){
			var customerSave = CertCapture.customerSave(details, defCompanyId[1], customers, addInfo);
			
			try{
				var response = https.post({
					url: customerSave.url,
					body: customerSave.data,
					headers: customerSave.headers
				});
				var responseBody = JSON.parse(response.body);
				
				if(response.code == 201){
					var result = AVA_InitiateExemptCert(crecord, address_line, email, defCompanyId, customerCode, details, CertCapture, addInfo);
					return result;

				}
				else if(responseBody.error.code == 'DuplicateEntry'){
					var result = AVA_UpdateCustomerDetails(crecord, address_line, email, defCompanyId, customerCode, details, CertCapture, customers, addInfo);
					return result;
				}
				else{
					var result = {};
					result.code = 'error';
					result.message = responseBody.error.message;
					return result;
				}
			}
			catch(err){
				var result = {};
				result.code = 'error';
				result.message = 'Adding Customer on CertCapture was not successful. ' + err.message;
				return result;
			}
		}

		function AVA_UpdateCustomerDetails(crecord, address_line, email, defCompanyId, customerCode, details, CertCapture, customers, addInfo){
			var customerUpdate = CertCapture.customerUpdate(details, defCompanyId[1], customerCode[0], customers[0], addInfo);
			
			try{
				var response = https.put({
					url: customerUpdate.url,
					body: customerUpdate.data,
					headers: customerUpdate.headers
				});
				var responseBody = JSON.parse(response.body);
				
				if(response.code == 200){
					var result = AVA_InitiateExemptCert(crecord, address_line, email, defCompanyId, customerCode, details, CertCapture, addInfo);
					return result;
				}
				else{
					var result = {};
					result.code = 'error';
					result.message = responseBody.error.message
					return result;
				}
			}
			catch(err){
				var result = {};
				result.code = 'error';
				result.message = 'Updating Customer on CertCapture was not successful. ' + err.message;
				return result;
			}
		}
		
		function AVA_InitiateExemptCert(crecord, address_line, email, defCompanyId, customerCode, details, CertCapture, addInfo){
			var invitations = AVA_InitiateExemptCertInvitationBody(crecord, address_line, email, CertCapture);
			var certCapture = CertCapture.certificateRequestInitiate(details, defCompanyId[1], customerCode[0], invitations, addInfo);
			
			try{
				var response = https.post({
					url: certCapture.url,
					body: certCapture.data,
					headers: certCapture.headers
				});
				var responseBody = JSON.parse(response.body);
				
				if(response.code == 201){
					var result = {};
					result.code = 'success';
					result.message = "An email was sent to " + email + " that contains instructions for submitting your exemption certificates. If you have not received an email after 15 minutes, please be sure to check any spam or junk folders as well.";
					return result;
				}
				else{
					var result = {};
					result.code = 'error';
					result.message = responseBody.error.message;
					return result;
				}
			}
			catch(err){
				var result = {};
				result.code = 'success';
				result.message = 'Initiating Exemption Certificate on CertCapture was not successful. ' + err.message;
				return result
			}
		}
		
		function AVA_InitiateExemptCertInvitationBody(crecord, address_line, email, CertCapture){
			var invitations = [];
			var invitation = new CertCapture.invitation();
			invitation.deliveryMethod = 'EMAIL';
			invitation.recipient = email;
			invitation.coverLetterTitle = 'STANDARD_REQUEST';
			invitations.push(invitation);
			return invitations;
		}

		function AVA_GetCustomerInfo(crecord, avaConfigObjRecvd){
			var entityId;
			var customerCode = new Array(); // 0-Customer/Partner ID, 1-Customer/Partner Name

			switch(avaConfigObjRecvd.AVA_CustomerCode){
				case '0':
					entityId = search.lookupFields({
						type: search.Type.CUSTOMER,
						id: crecord.getValue('id'),
						columns: ['entityid']
					});
					customerCode[0] = entityId.entityid;
					customerCode[1] = (crecord.getValue('isperson') == 'T') ? (crecord.getValue('firstname') + ((crecord.getValue('middlename') != null && crecord.getValue('middlename').length > 0) ? (' ' + crecord.getValue('middlename') + ' ') : ' ') + crecord.getValue('lastname')) : (crecord.getValue('companyname'));
					break;
					
				case '1':
					var customerName = (crecord.getValue('isperson') == 'T') ? (crecord.getValue('firstname') + ((crecord.getValue('middlename') != null && crecord.getValue('middlename').length > 0) ? (' ' + crecord.getValue('middlename') + ' ') : ' ') + crecord.getValue('lastname')) : (crecord.getValue('companyname'));
					customerCode[0] = customerName;
					customerCode[1] = customerName;
					break;
					
				case '2':
					var customerName = (crecord.getValue('isperson') == 'T') ? (crecord.getValue('firstname') + ((crecord.getValue('middlename') != null && crecord.getValue('middlename').length > 0) ? (' ' + crecord.getValue('middlename') + ' ') : ' ') + crecord.getValue('lastname')) : (crecord.getValue('companyname'));
					customerCode[0] = crecord.id.toString();
					customerCode[1] = customerName;
					break;
					
				case '3':
					if(crecord.type == 'partner'){
						var partnerName = (crecord.getValue('isperson') == 'T') ? (crecord.getValue('firstname') + ((crecord.getValue('middlename') != null && crecord.getValue('middlename').length > 0) ? (' ' + crecord.getValue('middlename') + ' ') : ' ') + crecord.getValue('lastname')) : (crecord.getValue('companyname'));
						entityId = search.lookupFields({
							type: search.Type.CUSTOMER,
							id: crecord.getValue('id'),
							columns: ['entityid']
						});
						customerCode[0] = entityId.entityid;
						customerCode[1] = partnerName;
					}
					else{
						if(runtime.isFeatureInEffect({feature: 'MULTIPARTNER'}) != true && crecord.getValue('partner') != null && crecord.getValue('partner').length > 0){
							var response = https.get({
								url: url.resolveScript({
									scriptId: 'customscript_ava_recordload_suitelet',
									deploymentId: 'customdeploy_ava_recordload',
									returnExternalUrl: true,
									params: {
										type: 'partner',
										id: crecord.getValue('partner'),
										recordopr: 'search'
									}
								})
							});
							var fieldValues = response.body.split('+');
							customerCode[0] = fieldValues[5];
							customerCode[1] = (fieldValues[0] == true) ? (fieldValues[1] + ((fieldValues[2] != null && fieldValues[2].length > 0) ? ('' + fieldValues[2] + '') : '') + fieldValues[3]) : (fieldValues[4]);
						}
						else{
							var customerName = (crecord.getValue('isperson') == 'T') ? (crecord.getValue('firstname') + ((crecord.getValue('middlename') != null && crecord.getValue('middlename').length > 0) ? (' ' + crecord.getValue('middlename') + ' ') : ' ') + crecord.getValue('lastname')) : (crecord.getValue('companyname'));
							entityId = search.lookupFields({
								type: search.Type.CUSTOMER,
								id: crecord.getValue('id'),
								columns: ['entityid']
							});
							customerCode[0] = entityId.entityid;
							customerCode[1] = customerName;
						}
					}
					break;
					
				case '4':
					if(crecord.type == 'partner'){
						var partnerName = (crecord.getValue('isperson') == 'T') ? (crecord.getValue('firstname') + ((crecord.getValue('middlename') != null && crecord.getValue('middlename').length > 0) ? (' ' + crecord.getValue('middlename') + ' ') : ' ') + crecord.getValue('lastname')) : (crecord.getValue('companyname'));
						customerCode[0] = partnerName;
						customerCode[1] = partnerName;
					}
					else{
						if(runtime.isFeatureInEffect({feature: 'MULTIPARTNER'}) != true && crecord.getValue('partner') != null && crecord.getValue('partner').length > 0){
							var response = https.get({
								url: url.resolveScript({
									scriptId: 'customscript_ava_recordload_suitelet',
									deploymentId: 'customdeploy_ava_recordload',
									returnExternalUrl: true,
									params: {
										type: 'partner',
										id: crecord.getValue('partner'),
										recordopr: 'search'
									}
								})
							});
							var fieldValues = response.body.split('+');
							customerCode[0] = (fieldValues[0] == true) ? (fieldValues[1] + ((fieldValues[2] != null && fieldValues[2].length > 0) ? ('' + fieldValues[2] + '') : '') + fieldValues[3]) : (fieldValues[4]);
							customerCode[1] = (fieldValues[0] == true) ? (fieldValues[1] + ((fieldValues[2] != null && fieldValues[2].length > 0) ? ('' + fieldValues[2] + '') : '') + fieldValues[3]) : (fieldValues[4]);
						}
						else{
							var customerName = (crecord.getValue('isperson') == 'T') ? (crecord.getValue('firstname') + ((crecord.getValue('middlename') != null && crecord.getValue('middlename').length > 0) ? (' ' + crecord.getValue('middlename') + ' ') : ' ') + crecord.getValue('lastname')) : (crecord.getValue('companyname'));
							customerCode[0] = customerName;
							customerCode[1] = customerName;
						}
					}
					break;
					
				case '5':
					if(crecord.type == 'partner'){
						var partnerName = (crecord.getValue('isperson') == 'T') ? (crecord.getValue('firstname') + ((crecord.getValue('middlename') != null && crecord.getValue('middlename').length > 0) ? (' ' + crecord.getValue('middlename') + ' ') : ' ') + crecord.getValue('lastname')) : (crecord.getValue('companyname'));
						customerCode[0] = crecord.id.toString();
						customerCode[1] = partnerName;
					}
					else{
						if(runtime.isFeatureInEffect({feature: 'MULTIPARTNER'}) != true && crecord.getValue('partner') != null && crecord.getValue('partner').length > 0){
							var response = https.get({
								url: url.resolveScript({
									scriptId: 'customscript_ava_recordload_suitelet',
									deploymentId: 'customdeploy_ava_recordload',
									returnExternalUrl: true,
									params: {
										type: 'partner',
										id: crecord.getValue('partner'),
										recordopr: 'search'
									}
								})
							});
							var fieldValues = response.body.split('+');
							customerCode[0] = crecord.getValue('partner');
							customerCode[1] = (fieldValues[0] == true) ? (fieldValues[1] + ((fieldValues[2] != null && fieldValues[2].length > 0) ? ('' + fieldValues[2] + '') : '') + fieldValues[3]) : (fieldValues[4]);
						}
						else{
							var customerName = (crecord.getValue('isperson') == 'T') ? (crecord.getValue('firstname') + ((crecord.getValue('middlename') != null && crecord.getValue('middlename').length > 0) ? (' ' + crecord.getValue('middlename') + ' ') : ' ') + crecord.getValue('lastname')) : (crecord.getValue('companyname'));
							customerCode[0] = crecord.id.toString();
							customerCode[1] = customerName;
						}
					}
					break;
					
				case '6':
					customerCode[0] = crecord.getValue('entitytitle');
					customerCode[1] = (crecord.getValue('isperson') == 'T') ? (crecord.getValue('firstname') + ((crecord.getValue('middlename') != null && crecord.getValue('middlename').length > 0) ? (' ' + crecord.getValue('middlename') + ' ') : ' ') + crecord.getValue('lastname')) : (crecord.getValue('companyname'));
					break;
					
				case '7':
					if(crecord.type == 'partner'){
						var partnerName = (crecord.getValue('isperson') == 'T') ? (crecord.getValue('firstname') + ((crecord.getValue('middlename') != null && crecord.getValue('middlename').length > 0) ? (' ' + crecord.getValue('middlename') + ' ') : ' ') + crecord.getValue('lastname')) : (crecord.getValue('companyname'));
						customerCode[0] = crecord.getValue('entitytitle');
						customerCode[1] = partnerName;
					}
					else{
						if(runtime.isFeatureInEffect({feature: 'MULTIPARTNER'}) != true && crecord.getValue('partner') != null && crecord.getValue('partner').length > 0){
							var response = https.get({
								url: url.resolveScript({
									scriptId: 'customscript_ava_recordload_suitelet',
									deploymentId: 'customdeploy_ava_recordload',
									returnExternalUrl: true,
									params: {
										type: 'partner',
										id: crecord.getValue('partner'),
										recordopr: 'search'
									}
								})
							});
							var fieldValues = response.body.split('+');
							customerCode[0] = fieldValues[8];
							customerCode[1] = (fieldValues[0] == true) ? (fieldValues[1] + ((fieldValues[2] != null && fieldValues[2].length > 0) ? ('' + fieldValues[2] + '') : '') + fieldValues[3]) : (fieldValues[4]);
						}
						else{
							var customerName = (crecord.getValue('isperson') == 'T') ? (crecord.getValue('firstname') + ((crecord.getValue('middlename') != null && crecord.getValue('middlename').length > 0) ? (' ' + crecord.getValue('middlename') + ' ') : ' ') + crecord.getValue('lastname')) : (crecord.getValue('companyname'));
							customerCode[0] = crecord.getValue('entitytitle');
							customerCode[1] = customerName;
						}
					}
					break;
					
				case '8':
					if(crecord.getValue('externalid') != null && crecord.getValue('externalid').length > 0){
						customerCode[0] = crecord.getValue('externalid');
						customerCode[1] = (crecord.getValue('isperson') == 'T') ? (crecord.getValue('firstname') + ((crecord.getValue('middlename') != null && crecord.getValue('middlename').length > 0) ? (' ' + crecord.getValue('middlename') + ' ') : ' ') + crecord.getValue('lastname')) : (crecord.getValue('companyname'));
					}
					else{
						entityId = search.lookupFields({
							type: search.Type.CUSTOMER,
							id: crecord.getValue('id'),
							columns: ['entityid']
						});
						customerCode[0] = entityId.entityid;
						customerCode[1] = (crecord.getValue('isperson') == 'T') ? (crecord.getValue('firstname') + ((crecord.getValue('middlename') != null && crecord.getValue('middlename').length > 0) ? (' ' + crecord.getValue('middlename') + ' ') : ' ') + crecord.getValue('lastname')) : (crecord.getValue('companyname'));
					}
					break;
					
				default:
					customerCode = 0;
					break;
			}
			
			return customerCode;
		}
		
		return{
			onRequest: onRequest
		};
	}
);