//@ts-ignore
define(["N/log", "N/record"], function (log, record) {
	function getEdiTransactionObj(ediData, summaryContext) {
		const messageText = getEmailMessage(summaryContext);

		if (!messageText) {
			return;
		}

		const ediTransactionRecord = {};
		ediTransactionRecord.emailSubject = `Processing Results for ${ediData.documentType} for ${ediData.purchasingSoftware}`;
		ediTransactionRecord.emailMessage = messageText;
		ediTransactionRecord.documentTypeId = ediData.documentTypeId;
		ediTransactionRecord.purchasingSoftwareId = ediData.purchasingSoftwareId;

		return ediTransactionRecord;

		function getEmailMessage(summaryContext) {
			let errorText = ``;
			summaryContext.mapSummary.errors.iterator().each(function (key, value) {
				errorText += `${key}: ${JSON.parse(value).message}
				`;

				return true;
			});

			if (errorText) {
				errorText = `Processing Errors:
				${errorText}
				`;
			}

			let summaryText = "";
			summaryContext.output.iterator().each(function (key, value) {
				summaryText += `${key}: ${value}
				`;

				return true;
			});

			if (summaryText) {
				summaryText = `Processing Results:
				${summaryText}`;
			}

			return errorText + summaryText;
		}
	}

	function createEdiTransactionRecord(ediTransactionRecordObj) {
		try {
			var newRecord = record.create({
				type: "customrecord_spl_edi_transaction",
			});

			newRecord.setValue(
				"custrecord_spl_edi_document_type",
				ediTransactionRecordObj.documentTypeId
			);

			newRecord.setValue(
				"custrecord_spl_edi_email_subject",
				ediTransactionRecordObj.emailSubject
			);

			newRecord.setValue(
				"custrecord_spl_edi_email_message",
				ediTransactionRecordObj.emailMessage
			);

			newRecord.setValue(
				"custrecord_spl_edi_purchasing_software",
				ediTransactionRecordObj.purchasingSoftwareId
			);

			return newRecord.save();
		} catch (e) {
			log.error(
				"Error Pushing EDI Transaction to DB",
				`Error: ${e} ${e.stack}`
			);
		}
	}

	return {
		getEdiTransactionObj,
		createEdiTransactionRecord,
	};
});
