/**
 * @NApiVersion 2.1
 * @NScriptType scheduledscript
 * @NAmdConfig /SuiteScripts/config.json
 */

//@ts-ignore
define([
	"N/log",
	"N/sftp",
	"N/record",
	"N/file",
	"N/search",
	"N/email",
	"GetEdiPartnerValuesLib",
	"GetPurchaseOrderAddressLib",
	"WriteOutgoing850Lib",
	"PushEdiEmailInfoToDBLib",
	"Moment",
], function (
	log,
	sftp,
	record,
	file,
	search,
	email,
	getEdiPartnerValuesLib,
	getPurchaseOrderAddress,
	writePoLib,
	pushEdiEmailInfoToDBLib,
	moment
) {
	function execute(context) {
		var transactionsProcessedSuccessfully_Valmar = [];
		var transactionsProcessedSuccessfully_SPL = [];

		var processingLog_Valmar = [];
		var processingLog_SPL = [];

		var processedSuccesfully = true;
		var noErrorsOnAllTransactions = true;

		var vendorData = {
			vendorInternalId: 742,
			documentTypeId: 5,
			pushEmailToDB: true,
		};

		var medlineConnection = getMedlineConnection();
		var supplylineConnection = getSupplylineConnection();
		var partnerValues = getEdiPartnerValuesLib.getMedlineValues();
		var poIds = getInternalIds();
		if (poIds.length > 0) {
			processPurchaseOrders();
			processEnd();
		} else {
			log.debug("Script ran, no results found");
		}

		function getMedlineConnection() {
			var hostKey =
				"AAAAB3NzaC1yc2EAAAABIwAAAIEA2YIvZPcsWA3jP6yq5J" +
				"5XYunNySNspUZUhtzxXoePDZEhxJFIF/am9o+15CqO4hlyEDni0m+VW4" +
				"LVSoiUnj31xJis91EcYrUeCewIcSwof4Xs05ghth/2SKn0cITsR5hOeST" +
				"l4jFsMf9oK0+b2gr4jdV4fria4E/eF2u0qQArWuc=";

			return sftp.createConnection({
				username: "FTPadmin",
				passwordGuid: "b2fe4b88a49443d79c464a9f23d797cf", //production account
				url: "************",
				hostKey: hostKey,
				directory: "/users/medlineprod/OUT", //PROD
			});
		}

		function getSupplylineConnection() {
			var hostKey =
				"AAAAB3NzaC1yc2EAAAABIwAAAIEA2YIvZPcsWA3jP6yq5J" +
				"5XYunNySNspUZUhtzxXoePDZEhxJFIF/am9o+15CqO4hlyEDni0m+VW4" +
				"LVSoiUnj31xJis91EcYrUeCewIcSwof4Xs05ghth/2SKn0cITsR5hOeST" +
				"l4jFsMf9oK0+b2gr4jdV4fria4E/eF2u0qQArWuc=";

			return sftp.createConnection({
				username: "FTPadmin",
				passwordGuid: "b2fe4b88a49443d79c464a9f23d797cf", //production account
				url: "************",
				hostKey: hostKey,
				directory: "/EDI Reference Files/Medline/OUT/850",
			});
		}

		function getInternalIds() {
			var poSearch = search.create({
				type: "purchaseorder",
				filters: [
					["type", "anyof", "PurchOrd"],
					"AND",
					["name", "anyof", "742"],
					"AND",
					["custbody_spl_po_edi_trans_cntrl_number", "isempty", ""],
					"AND",
					["mainline", "is", "T"],
					"AND",
					["createdfrom.number", "isnotempty", ""], //TAKE OUT WHEN ADD NON DROP SHIP
					"AND",
					[
						"datecreated",
						"onorafter",
						"1/5/2021 12:00 am",
						"1/5/2021 12:00 am",
					], //CHANGE TIME SO DONT HAVE DUPLICATES
					"AND",
					["status", "anyof", "PurchOrd:B"],
				],
			});

			var results = [];

			poSearch.run().each((result) => {
				results.push(result.id);
				return true;
			});

			return results;
		}

		function processPurchaseOrders() {
			poIds.forEach((id) => {
				processedSuccesfully = true;
				var NSPurchaseOrder = getPurchaseOrder();
				var subsidiary = NSPurchaseOrder.getValue("subsidiary");
				var purchaseOrderNumber = NSPurchaseOrder.getText("tranid");
				var controlNumber = setControlNumber();

				var purchaseOrder = getPurchaseOrderValuesObj();
				purchaseOrder.items = getItemsArr();

				var ediFile = writePurchaseOrderAsEdiFile();
				if (processedSuccesfully && ediFile.success) {
					var fileToUpload = createFile();
				}
				if (processedSuccesfully) {
					uploadFileToMedline();
					uploadFileToSupplyline();
				}
				if (processedSuccesfully) {
					addControlNumberToPurchaseOrder();
					if (subsidiary == 2) {
						//POs Processed for Valmar
						transactionsProcessedSuccessfully_Valmar.push(purchaseOrderNumber);
					} else if (subsidiary == 1) {
						//POs Processed for SPL
						transactionsProcessedSuccessfully_SPL.push(purchaseOrderNumber);
					}
				}

				//*******Process Purchase Order Helper Functions*******
				function processFail(logMessage, hardError) {
					processedSuccesfully = false;
					noErrorsOnAllTransactions = false;
					if (subsidiary == 2) {
						//Errors Captured for Valmar
						processingLog_Valmar.push(logMessage);
					} else if (subsidiary == 1) {
						//Errors Captured for SPL
						processingLog_SPL.push(logMessage);
					}
					return false;
				}

				function getPurchaseOrder() {
					try {
						return record.load({
							type: record.Type.PURCHASE_ORDER,
							id: id,
						});
					} catch (error) {
						processFail(
							"Purchase order record not loaded for internal ID " + id,
							false
						);
					}
				}

				function setControlNumber() {
					try {
						var controlNumberToSlice =
							purchaseOrderNumber.replace("PO", "") +
							moment().format("MMDDYYYY");
						var controlNumber = controlNumberToSlice.slice(0, 9);
						return controlNumber;
					} catch (error) {
						processFail(
							"Control number not set for " + purchaseOrderNumber + "."
						);
					}
				}

				function getPurchaseOrderValuesObj() {
					//var isDropShip = checkIfDropShip();
					try {
						const isDropShip = true; //isDropShip;
						const orderType = getOrderType();
						const date = NSPurchaseOrder.getValue("trandate");
						const number = purchaseOrderNumber;
						const pONumber = NSPurchaseOrder.getText("otherrefnum");
						const customer = getCustomer();
						const shipToAccount = getShippingAccount();
						const address = getShippingAddress();
						const total = NSPurchaseOrder.getValue("amountremaining");

						return {
							isDropShip,
							orderType,
							controlNumber,
							date,
							number,
							pONumber,
							customer,
							shipToAccount,
							address,
							total,
						};
					} catch (error) {
						processFail(
							"Values not gotten correctly for purchase order " +
								purchaseOrderNumber +
								". Error: " +
								error
						);
						return {};
					}
				}

				function getItemsArr() {
					var itemsLineCount = NSPurchaseOrder.getLineCount({
						sublistId: "item",
					});

					var itemsArr = [];

					for (var x = 0; x < itemsLineCount; x++) {
						try {
							var name = NSPurchaseOrder.getSublistText({
								sublistId: "item",
								fieldId: "vendorname",
								line: x,
							});

							const description = NSPurchaseOrder.getSublistText({
								sublistId: "item",
								fieldId: "description",
								line: x,
							});

							let quantity = NSPurchaseOrder.getSublistText({
								sublistId: "item",
								fieldId: "quantity",
								line: x,
							});

							quantity = Math.round(quantity);

							const rate = NSPurchaseOrder.getSublistText({
								sublistId: "item",
								fieldId: "rate",
								line: x,
							});

							const uom = getParsedUOM();

							function getParsedUOM() {
								var uomToParse = NSPurchaseOrder.getSublistText({
									sublistId: "item",
									fieldId: "units",
									line: x,
								});

								var unitConversionRate = NSPurchaseOrder.getSublistText({
									sublistId: "item",
									fieldId: "unitconversionrate",
									line: x,
								});

								/*Get last 2 element - when primary units type is Each
									Example: 	5000 EA / CS, 	12 EA / DZ
								Get first 2 elements - when the primary units type is not each
									PK/200EA, 	BX/10EA
								*/

								let uom =
									unitConversionRate == 1 //The primary units type is 'Each'
										? uomToParse.slice(-2) //Get the last 2 elements in the array
										: uomToParse.slice(0, 2); //Get the first 2 elements in the array
								uom = uom.toUpperCase();

								return uom;
							}

							let lineNumber = x + 1;

							itemsArr.push({
								name,
								description,
								quantity,
								rate,
								uom,
								lineNumber,
							});
						} catch (error) {
							processFail(
								"Item " +
									name +
									" not gotten correctly for purchase order " +
									purchaseOrderNumber +
									". \nError: " +
									error
							);
						}
					}

					if (itemsArr.length <= 0) {
						processFail(
							"No items added for " + purchaseOrderNumber + ".",
							false
						);
					}

					return itemsArr;
				}

				function writePurchaseOrderAsEdiFile() {
					try {
						return writePoLib.getPurchaseOrderAsEDI(
							partnerValues,
							purchaseOrder
						);
					} catch (error) {
						processFail(
							"EDI file not gotten for " + purchaseOrderNumber + ".",
							false
						);
					}
				}

				function createFile() {
					try {
						return file.create({
							name: purchaseOrder.controlNumber + ".850",
							fileType: file.Type.PLAINTEXT,
							contents: ediFile.value,
						});
					} catch (error) {
						processFail(
							"File to upload not created for purchase order " +
								purchaseOrderNumber +
								". Error: " +
								error,
							false
						);
					}
				}

				function uploadFileToMedline() {
					try {
						medlineConnection.upload({
							file: fileToUpload,
							replaceExisting: true,
						});
					} catch (error) {
						processFail(
							"File not uploaded to Medline for " +
								purchaseOrderNumber +
								". Error: " +
								error,
							false
						);
					}
				}

				function uploadFileToSupplyline() {
					try {
						supplylineConnection.upload({
							file: fileToUpload,
							replaceExisting: true,
						});
					} catch (error) {
						processFail(
							"File not uploaded to Supplyline for " +
								purchaseOrderNumber +
								". Error: " +
								error,
							false
						);
					}
				}

				function addControlNumberToPurchaseOrder() {
					try {
						NSPurchaseOrder.setValue(
							"custbody_spl_po_edi_trans_cntrl_number",
							controlNumber
						);

						NSPurchaseOrder.save({
							enableSourcing: true,
							ignoreMandatoryFields: true,
						});

						var controlNumberSaved = NSPurchaseOrder.getValue(
							"custbody_spl_edi_trans_cntrl_num"
						);
						if (!controlNumberSaved) {
							return false;
						}
					} catch (error) {
						processFail(
							"EDI transaction control number not saved for " +
								purchaseOrderNumber +
								".",
							false
						);
					}
				}

				//*******Set Purchase Order Values Helper Functions*******
				//function checkIfDropShip() {
				//    try {
				//        return NSPurchaseOrder.getValue('createdfrom') !== "";
				//    } catch (error) {
				//        processFail('Drop-ship value not gotten successfully for purchase order ' + purchaseOrderNumber + '.');
				//    }
				//};

				function getOrderType() {
					//if (isDropShip) {
					return "DS";
					//}
					//return 'NE';
				}

				function getCustomer() {
					//if (isDropShip) {
					return NSPurchaseOrder.getText("shipto")
						.replace(/^(.*):/, "")
						.trim();
					//}
					//return 'Supplyline Medical';
				}

				function getShippingAccount() {
					try {
						let shippingAccountID = NSPurchaseOrder.getSublistValue({
							sublistId: "item",
							fieldId: "custcol_spl_ship_account",
							line: 0,
						});

						if (!shippingAccountID) {
							let customerId = NSPurchaseOrder.getValue("shipto");

							let parentId = search.lookupFields({
								type: record.Type.CUSTOMER,
								id: customerId,
								columns: ["parent"],
							})["parent"][0].value;

							if (parentId == 13264) {
								//Revival
								shippingAccountID = 3;
							} else {
								processFail(
									"No shipping account entered for " + purchaseOrderNumber + "."
								);

								return;
							}
						}

						validateShipAccountName(shippingAccountID);

						var shippingAccount = search.lookupFields({
							type: "customrecord_spl_shipping_account",
							id: shippingAccountID,
							columns: ["custrecord_spl_ship_account_number"],
						})["custrecord_spl_ship_account_number"];

						if (shippingAccount == "") {
							processFail(
								"No shipping account found for object for " +
									purchaseOrderNumber +
									"."
							);
							return;
						}

						return shippingAccount;
					} catch (error) {
						processFail(
							"Ship account not gotten for " + purchaseOrderNumber + "."
						);
					}

					function validateShipAccountName(shippingAccountID) {
						var itemsLineCount = NSPurchaseOrder.getLineCount({
							sublistId: "item",
						});

						for (var x = 0; x < itemsLineCount; x++) {
							try {
								var itemShipAccountID = NSPurchaseOrder.getSublistValue({
									sublistId: "item",
									fieldId: "custcol_spl_ship_account",
									line: x,
								});

								if (shippingAccountID != itemShipAccountID) {
									processFail(
										"Shipping accounts don't match for purchase order " +
											purchaseOrderNumber +
											"."
									);
									return false;
								}
							} catch (error) {
								processFail(
									"Item " +
										name +
										" not gotten correctly to validate ship account for " +
										purchaseOrderNumber +
										". Error: " +
										error
								);
							}
						}
					}
				}

				function getShippingAddress() {
					var address = getPurchaseOrderAddress.getPurchaseOrderAddress(
						true,
						id
					); //isDropShip
					if (!address.address1) {
						processFail(
							"Shipping address not gotten for " + purchaseOrderNumber + "."
						);
					}
					return address;
				}
			});
		}

		function processEnd() {
			var resultsData = setResultsDataAndSendEmail();
			logEndResult();

			if (vendorData.pushEmailToDB) {
				pushEdiEmailInfoToDBLib.pushEdiEmailInfoToDB(vendorData, resultsData);
			}

			function setResultsDataAndSendEmail() {
				var data = {};
				var recipientValmar = ["<EMAIL>"];
				var recipientSPL = ["<EMAIL>"];

				var errorTextValmar = "";
				var errorTextSPL = "";

				processingLog_Valmar.forEach(function (error) {
					errorTextValmar += error + "\n\n";
				});

				processingLog_SPL.forEach(function (error) {
					errorTextSPL += error + "\n\n";
				});

				var transactionsProcessed_Valmar = "";
				transactionsProcessedSuccessfully_Valmar.forEach(function (
					transaction
				) {
					transactionsProcessed_Valmar += transaction + "\n";
				});

				var transactionsProcessed_SPL = "";
				transactionsProcessedSuccessfully_SPL.forEach(function (transaction) {
					transactionsProcessed_SPL += transaction + "\n";
				});

				if (noErrorsOnAllTransactions) {
					processCreatedSuccessfully(); //Call processCreatedSuccessfully() if All Transactions are processed with No Errors
					sendEmail();
				} else {
					//Call processCreatedWithErrors() if some of the transactions are succesful and if some captured an error
					if (
						transactionsProcessedSuccessfully_Valmar.length > 0 ||
						transactionsProcessedSuccessfully_SPL.length > 0
					) {
						processCreatedWithErrors(
							recipientValmar,
							transactionsProcessed_Valmar,
							errorTextValmar
						);
						if (transactionsProcessed_Valmar != "" || errorTextValmar != "") {
							sendEmail();
						}
						processCreatedWithErrors(
							recipientSPL,
							transactionsProcessed_SPL,
							errorTextSPL
						);
						if (transactionsProcessed_SPL != "" || errorTextSPL != "") {
							sendEmail();
						}
					} else {
						//Call processFailedData() if all of the transactions captured an error
						processFailedData(recipientValmar, errorTextValmar);
						if (errorTextValmar != "") {
							sendEmail();
						}
						processFailedData(recipientSPL, errorTextSPL);
						if (errorTextSPL != "") {
							sendEmail();
						}
					}
				}

				return data;

				function processCreatedSuccessfully() {
					data.subject =
						"Success: Medline EDI Purchase Orders Processed Successfully";
					data.recipients = ["<EMAIL>"];
					data.body =
						"The following Medline purchase orders were processed successfully via EDI:\n\n" +
						transactionsProcessed_Valmar +
						"\n\n" +
						transactionsProcessed_SPL;
					data.logTitle = "Processed Successfully";
					data.processingStatus = 1; //Processed With No Errors
				}

				function processCreatedWithErrors(
					emailRecipient,
					transactionsProcessed,
					errorText
				) {
					data.subject =
						"Errors: Please Review and Correct Medline EDI Purchase Orders";
					data.recipients = ["<EMAIL>"];
					data.cc = emailRecipient;
					data.body =
						"The following Medline purchase orders were processed successfully via EDI:\n\n" +
						transactionsProcessed +
						"Please review the errors below.\n\n" +
						errorText;
					data.logTitle = "Created with Errors";
					data.processingStatus = 2; //Processed With Errors
				}

				function processFailedData(emailRecipient, errorText) {
					data.subject =
						"Failure: Medline EDI purchase orders not processed successfully.";
					data.recipients = emailRecipient;
					data.cc = ["<EMAIL>"];
					data.body = "Please review the errors below.\n\n" + errorText;
					data.logTitle = "Purchased Orders Not Processed";
					data.processingStatus = 4; //Document Not Created
				}

				function sendEmail(salesOrderId) {
					try {
						email.send({
							author: 262579, //EDI
							recipients: data.recipients,
							cc: data.cc,
							subject: data.subject,
							body: data.body,
						});
					} catch (error) {
						throw "Medline EDI Purchase Order email not sent.";
					}
				}
			}

			function logEndResult() {
				log.debug({
					title: resultsData.logTitle,
					details: resultsData.logDetails,
				});
			}
		}
	}

	return {
		execute: execute,
	};
});
