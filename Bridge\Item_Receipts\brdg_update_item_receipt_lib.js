/**
 *
 * @NApiVersion 2.1
 * @NAmdConfig /SuiteScripts/config.json
 *
 * <AUTHOR>
 * @module brdg_update_item_receipt_lib

 * @description Helper lib for update item receipt scripts
 */

//@ts-ignore
define([
	"N/log",
	"N/record",
	"N/runtime",
	"N/query",
	"N/email",
	"N/file",
	"N/format",
], function (log, record, runtime, query, email, file, format) {
	/**
	 * @description This function gets the script parameters from the script deployment
	 * @param {void}
	 * @returns {Object} Start Date, End Date, Subsidiary, Bill ID Override
	 */
	function getScriptParameters() {
		const currentScript = runtime.getCurrentScript();

		let startDate = currentScript.getParameter({
			name: "custscript_update_ir_start_date",
		});

		startDate =
			startDate && startDate != "null"
				? format.format({
						value: new Date(startDate),
						type: format.Type.DATE,
				  })
				: null;

		let endDate = currentScript.getParameter({
			name: "custscript_update_ir_end_date",
		});

		endDate =
			endDate && endDate != "null"
				? format.format({
						value: new Date(endDate),
						type: format.Type.DATE,
				  })
				: null;

		const subsidiary = currentScript.getParameter({
			name: "custscript_update_ir_subsidiary",
		});

		const billIdOverride = currentScript.getParameter({
			name: "custscript_brdg_bill_id_override",
		});

		log.audit(
			"Script Params",
			`Start Date: ${startDate} End Date: ${endDate} Subsidiary: ${subsidiary} Bill ID: ${billIdOverride}`
		);

		return {
			startDate,
			endDate,
			subsidiary,
			billIdOverride,
		};
	}

	/**
	 *
	 * @description This function runs a SQL query to pull the item receipt info for a specific vendor bill.
	 * @param {String | Number} billId
	 * @returns {Array}
	 */
	function runSqlQuery(billId) {
		const selectClause = `SELECT 
			MAX(BUILTIN.DF(BILL.id)) AS vendorbill,
			MAX(BUILTIN.DF(itemreceipt.id)) as itemreceipt, 
			MAX(itemreceipt.id) as itemreceiptid, 
			MAX(vendorbillitem.memo) AS itemname, 
			MAX(itemreceiptitem.item) AS item,
			MIN(vendorbillitem.rate) AS vendorbillitemrate, 
			MAX(itemreceiptitem.rate) AS itemreceiptitemrate, 
			MAX(itemreceiptitem.linesequencenumber) AS itemreceiptlinenumber, 
			MAX(itemreceiptitem.uniquekey) AS itemreceiptuniquekey,
			MAX(itemreceiptitem.units) AS units,
			MAX(vendorbillunit.conversionrate) as vendorbillconversionrate,
			MAX(itemreceiptunit.conversionrate) as itemreceiptconversionrate `;

		const fromClause = `FROM transaction AS BILL `;

		const joinClause = `INNER JOIN transactionLine AS vendorbillitem ON BILL.id = vendorbillitem.transaction 
				  INNER JOIN unitsTypeUom AS vendorbillunit ON vendorbillitem.units = vendorbillunit.internalid
			      INNER JOIN PreviousTransactionLineLink AS PTLL ON BILL.id = PTLL.nextdoc 
			      JOIN transaction AS itemreceipt ON itemreceipt.id = PTLL.previousdoc 
			      JOIN transactionLine AS itemreceiptitem ON itemreceipt.id = itemreceiptitem.transaction 
				  JOIN unitsTypeUom AS itemreceiptunit ON itemreceiptitem.units = itemreceiptunit.internalid `;

		const whereClause = `WHERE 
			      BILL.id = ${billId} 
			      AND PTLL.linktype = 'RcptBill' 
			      AND vendorbillitem.mainline NOT LIKE 'T' 
			      AND itemreceiptitem.mainline NOT LIKE 'T' 
				  AND vendorbillitem.units = itemreceiptitem.units 
			      AND vendorbillitem.item = itemreceiptitem.item 
				  AND vendorbillitem.rate != itemreceiptitem.rate `;

		const groupByClause = `GROUP BY  itemreceiptitem.linesequencenumber`;

		const sqlQuery = `${selectClause}${fromClause}${joinClause}${whereClause}${groupByClause}`;

		try {
			const sqlResults = query
				.runSuiteQL({
					query: sqlQuery,
				})
				.asMappedResults();

			return sqlResults;
		} catch (e) {
			log.error("Script Failure: Error running SQL query", e);
		}
	}

	/**
	 *
	 * @description This function goes through the array of items and updates the rates using the UOM conversion rate.
	 * @param {Array} itemLinesToUpdate
	 * @returns {Array} itemLinesToUpdate
	 */
	function modifyRates(itemLinesToUpdate) {
		itemLinesToUpdate.forEach((itemLineObj) => {
			itemLineObj.vendorbillitemrate = Number(
				(
					itemLineObj.vendorbillitemrate * itemLineObj.vendorbillconversionrate
				).toFixed(8)
			);

			itemLineObj.itemreceiptitemrate = Number(
				(
					itemLineObj.itemreceiptitemrate *
					itemLineObj.itemreceiptconversionrate
				).toFixed(8)
			);
		});

		return itemLinesToUpdate;
	}

	/**
	 *
	 * @description This function runs a query to pull any duplicate items on a specific item receipt.
	 * @param {void}
	 * @returns {Array} SQL results of the duplicate items
	 */
	function getDuplicateItems(itemReceiptId) {
		const duplicateItemsQuery = `SELECT  
			a.linesequencenumber,
			a.uniquekey,
			a.item,
			FROM transactionLine a
			JOIN (SELECT units, item,  COUNT(*) 
			FROM transactionLine 
			WHERE transaction = ?   
			GROUP BY units, item 
			HAVING COUNT(*) > 1 ) b
			ON a.units = b.units
			AND a.item = b.item
			WHERE transaction = ? 
			ORDER BY a.item`;

		return query
			.runSuiteQL({
				query: duplicateItemsQuery,
				params: [itemReceiptId, itemReceiptId],
			})
			.asMappedResults();
	}

	/**
	 *
	 * @description This function loads each record once, loops through the items, checks if they match any items to update and updates any relevant items.
	 * @param {Number} itemReceiptId
	 *  @param {Array} itemLinesToUpdate
	 * @returns {Object}
	 */
	function updateItemReceipt(itemReceiptId, itemLinesToUpdate) {
		const resultsLog = [];

		try {
			//Adjust the rates to use the transaction line's UOM before updating the item receipts
			itemLinesToUpdate = modifyRates(itemLinesToUpdate);

			const itemReceiptRecord = record.load({
				type: record.Type.ITEM_RECEIPT,
				id: itemReceiptId,
				isDynamic: false, //Must be standard mode or else need to use select line and commit line
			});

			//For each NS record, loop through the items,check for duplicates and update the items on that record

			const itemLineCount = itemReceiptRecord.getLineCount({
				sublistId: "item",
			});

			const duplicateItems = getDuplicateItems(itemReceiptId);

			for (
				let currentLineCount = 0;
				currentLineCount < itemLineCount;
				currentLineCount++
			) {
				const currentLineItem = itemReceiptRecord.getSublistValue({
					sublistId: "item",
					fieldId: "item",
					line: currentLineCount,
				});

				const currentLineUOM = itemReceiptRecord.getSublistValue({
					sublistId: "item",
					fieldId: "units",
					line: currentLineCount,
				});

				//Find index of the array where the current line's item and UOM matches the item line to update.
				const index = itemLinesToUpdate.findIndex(
					(lineToUpdate) =>
						currentLineItem == lineToUpdate.item &&
						currentLineUOM == lineToUpdate.units
				);

				//Set the rate of the current line item to be the rate of the indexed line to update.
				if (index !== -1) {
					//If this line was found
					if (
						//If this is a duplicate item, don't udpate and add to error log
						duplicateItems.some(
							(duplicate) =>
								duplicate.uniquekey ==
								itemLinesToUpdate[index].itemreceiptuniquekey
						)
					) {
						resultsLog.push({
							BILL: itemLinesToUpdate[index].vendorbill,
							ITEMRECEIPT: itemLinesToUpdate[index].itemreceipt.includes(
								"Item Receipt #"
							)
								? itemLinesToUpdate[index].itemreceipt.slice(14)
								: itemLinesToUpdate[index].itemreceipt, //Remove "Item Receipt #" if needed
							ITEMNAME: itemLinesToUpdate[index].itemname,
							UOM: itemLinesToUpdate[index].units,
							PREVIOUSPRICE: itemLinesToUpdate[index].itemreceiptitemrate,
							UPDATEDPRICE: itemLinesToUpdate[index].vendorbillitemrate,
							ERROR: `${itemLinesToUpdate[index].itemname} is a duplicate item. Cannot update this item.`,
						});

						continue;
					}

					itemReceiptRecord.setSublistValue({
						sublistId: "item",
						fieldId: "rate",
						line: currentLineCount,
						value: itemLinesToUpdate[index].vendorbillitemrate,
					});

					resultsLog.push({
						BILL: itemLinesToUpdate[index].vendorbill,
						ITEMRECEIPT: itemLinesToUpdate[index].itemreceipt,
						ITEMNAME: itemLinesToUpdate[index].itemname,
						UOM: itemLinesToUpdate[index].units,
						PREVIOUSPRICE: itemLinesToUpdate[index].itemreceiptitemrate,
						UPDATEDPRICE: itemLinesToUpdate[index].vendorbillitemrate,
					});
				}
			}

			try {
				itemReceiptRecord.save();
			} catch (e) {
				resultsLog.push({
					BILL:
						isArray(itemLinesToUpdate) && itemLinesToUpdate[0]
							? itemLinesToUpdate[0].vendorbill
							: "N/A",
					ITEMRECEIPT:
						isArray(itemLinesToUpdate) && itemLinesToUpdate[0]
							? itemLinesToUpdate[0].itemreceipt
							: "N/A",
					ITEMNAME: "N/A",
					UOM: "N/A",
					PREVIOUSPRICE: "N/A",
					UPDATEDPRICE: "N/A",
					ERROR: `Error saving item receipt: ${e}`,
				});
			}
		} catch (e) {
			resultsLog.push({
				BILL:
					isArray(itemLinesToUpdate) && itemLinesToUpdate[0]
						? itemLinesToUpdate[0].vendorbill
						: "N/A",
				ITEMRECEIPT:
					isArray(itemLinesToUpdate) && itemLinesToUpdate[0]
						? itemLinesToUpdate[0].itemreceipt
						: "N/A",
				ITEMNAME: "N/A",
				UOM: "N/A",
				PREVIOUSPRICE: "N/A",
				UPDATEDPRICE: "N/A",
				ERROR: `Error updating items: ${e}. Item lines to update: ${JSON.stringify(
					itemLinesToUpdate
				)}`,
			});
		} finally {
			return resultsLog;
		}
	}

	/**
	 *
	 * @description This function goes through the context of results that were updated and formats for later use.
	 * @param {Object} context
	 * @returns {Object}
	 */
	function getItemReceiptsResults(context) {
		const resultsLogToPrint = []; //All results objects to write on file
		const errorLog = []; //Error objects to display on email
		const noIRtoUpdate = []; //Count of bills with no item receipts to update

		context.output.iterator().each(function (key, value) {
			switch (key) {
				case "Results":
				case "Error getting item receipt info: ":
					const parsedObj = JSON.parse(value); //The values are passed in as a string, turning back into object

					//Formatting
					if (parsedObj.ITEMNAME && parsedObj.ITEMNAME != "N/A") {
						//If there is a name but it's not N/A - if there was an IR error, there won't be an item name
						parsedObj.ITEMNAME = '"' + parsedObj.ITEMNAME + '"'; //To include commas in the item name w/o messing up CSV parsing
						parsedObj.BILL.includes("Bill #") ? parsedObj.BILL.slice(6) : null; //If contains "Bill #", remove it
						parsedObj.ITEMRECEIPT.includes("Item Receipt #")
							? (parsedObj.ITEMRECEIPT = parsedObj.ITEMRECEIPT.slice(14))
							: null; //If contains "Item Receipt #", remove it
					}

					if (parsedObj.ERROR) {
						parsedObj.ERROR = '"' + parsedObj.ERROR + '"'; //To include commas/other characters in the error
						errorLog.push(parsedObj); //Add this error to error array
					}

					resultsLogToPrint.push(parsedObj); //Item receipts updated successfully and with errors will both be added to the CSV file.
					break;

				case "No item receipts to update":
					noIRtoUpdate.push(value);
					break;
			}

			return true; //To continue iterating the context.output
		});

		const itemReceiptsUpdatedSuccessfully = [
			...new Set(
				resultsLogToPrint
					.filter((result) => !result.ERROR)
					.map((result) => result.ITEMRECEIPT)
			),
		];

		const itemReceiptsWithErrors = [
			...new Set(errorLog.map((error) => error.ITEMRECEIPT)),
		];

		return {
			errorLog,
			itemReceiptsUpdatedSuccessfully,
			itemReceiptsWithErrors,
			noIRtoUpdate,
			resultsLogToPrint,
		};
	}

	/**
	 *
	 * @description This function goes through the formatted results of what was updated and creates a CSV file to be emailed to the users.
	 * @param {Array} resultsLogToPrint
	 * @returns {File}
	 */
	function createCSVResultFile(resultsLogToPrint) {
		const updateIRresultsFile = file.create({
			name: `Update_Item_Receipt_Results ${new Date().toLocaleDateString()}.csv`,
			fileType: file.Type.CSV,
			contents:
				"BILL,ITEM RECEIPT,ITEM NAME,UOM,PREVIOUS PRICE,UPDATED PRICE,ERROR\n",
		});

		resultsLogToPrint.forEach((result) => {
			updateIRresultsFile.appendLine({
				value: Object.values(result),
			});
		});

		return updateIRresultsFile;
	}

	/**
	 *
	 * @description This function sends the results of the map reduce to the users.
	 * @returns {void}
	 */
	function sendResultsEmail(parameters, updateIRresultsFile, errorLog) {
		const { startDate, endDate, subsidiary, billIdOverride } = parameters;

		let emailText = `Please see the attached results from the BRDG Update Item Receipt MR script.<br /><br />
		Script Parameters: Start Date: ${startDate} End Date: ${endDate} Subsidiary: ${subsidiary} Bill ID: ${billIdOverride}<br /><br />`;

		if (errorLog.length > 0) {
			let errorTable = `<table>
				<tr>
				<th  style = "border: 1px solid black; border-collapse: collapse">BILL</th>
				<th  style = "border: 1px solid black; border-collapse: collapse">ITEM RECEIPT</th>
				<th  style = "border: 1px solid black; border-collapse: collapse">ITEM NAME</th>
				<th  style = "border: 1px solid black; border-collapse: collapse">UOM</th>
				<th  style = "border: 1px solid black; border-collapse: collapse">PREVIOUS PRICE</th>
				<th   style = "border: 1px solid black; border-collapse: collapse">PRICE TO UPDATE</th>
				<th  style = "border: 1px solid black; border-collapse: collapse">ERROR</th>
				</tr>`;

			for (i = 0; i < errorLog.length; i++) {
				("<tr>");
				errorTable +=
					`<td style = "border: 1px solid black; border-collapse: collapse">${errorLog[i].BILL}
						</td>
						<td style = "border: 1px solid black; border-collapse: collapse">` +
					errorLog[i].ITEMRECEIPT +
					`</td>
						<td style = "border: 1px solid black; border-collapse: collapse">` +
					errorLog[i].ITEMNAME +
					`</td>
						<td style = "border: 1px solid black; border-collapse: collapse">` +
					errorLog[i].UOM +
					`</td>
						<td style = "border: 1px solid black; border-collapse: collapse">` +
					errorLog[i].PREVIOUSPRICE +
					`</td>
						<td style = "border: 1px solid black; border-collapse: collapse">` +
					errorLog[i].UPDATEDPRICE +
					`</td>
						<td style = "border: 1px solid black; border-collapse: collapse">` +
					errorLog[i].ERROR +
					`</td>
						</tr><tr>`;
			}
			errorTable += "</table>";

			emailText += `

				Please note: the following items were not updated. Please review and update the item receipts accordingly.<br /><br />${errorTable}`;
		}

		email.send({
			author: 223244, //Requests
			recipients: 314184, //<EMAIL> (temp switched from Miri's)
			cc: [],
			body: emailText,
			subject: `Results from Update Item Receipts MR ${new Date().toLocaleDateString()}`,
			attachments: [updateIRresultsFile],
		});
	}

	return {
		getScriptParameters,
		runSqlQuery,
		modifyRates,
		updateItemReceipt,
		getItemReceiptsResults,
		createCSVResultFile,
		sendResultsEmail,
	};
});
