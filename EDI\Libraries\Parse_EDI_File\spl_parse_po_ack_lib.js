/**
 * @NApiVersion 2.1
 */

define(["Numeral"], function (numeral) {
	function parse855(ediFile, partnerValues) {
		var errorLog = [];

		var fieldDelimiter = partnerValues.formattingInfo[0].partnerValue;
		var segmentDelimiter = partnerValues.formattingInfo[1].partnerValue;

		try {
			var poAckObj = {
				transactionControlNumber: getTransactionControlNumber(),
				purchaseOrderNumber: getPurchaseOrderNumber(),
				items: getItems(),
				shippingAddress: getAckShippingAddress(),
			};
		} catch (e) {
			errorLog.push(e);
		}

		return {
			errorLog,
			poAckObj,
		};

		/**********Parse PO Acknowledgment File Helper Functions**********/
		function getSegment(segmentTitle) {
			var regExp = new RegExp(segmentTitle + ".*?" + segmentDelimiter);
			try {
				return ediFile.match(regExp)[0].replace(segmentDelimiter, "");
			} catch (error) {
				return false;
			}
		}

		function getTransactionControlNumber() {
			var isaSegment = getSegment("ISA");
			return isaSegment.split(fieldDelimiter)[13];
		}

		function getPurchaseOrderNumber() {
			var bakSegment = getSegment("BAK");
			return bakSegment.split(fieldDelimiter)[3];
		}

		function getItems() {
			var delimiterEscaped = `\\${fieldDelimiter}`;

			var regExp = new RegExp(
				"PO1" +
					delimiterEscaped +
					".*?" +
					segmentDelimiter +
					"ACK" +
					".*?" +
					segmentDelimiter,
				"gs"
			);
			var arr = [];
			var items = ediFile.match(regExp);

			items.forEach(function (item) {
				var po1SegmentArr = item
					.split(segmentDelimiter)[0]
					.split(fieldDelimiter);
				var ackIndex = item.split(segmentDelimiter).length - 2; //Need variable because some venders send PID, some dont
				var ackSegmentArr = item
					.split(segmentDelimiter)
					[ackIndex].split(fieldDelimiter);

				var itemToAdd = {
					itemName: po1SegmentArr[7],
					quantity: numeral(po1SegmentArr[2]).format("0.00"),
					rate: numeral(po1SegmentArr[4]).format("0.00"),
					ack: ackSegmentArr[1],
				};
				if (ackSegmentArr.length >= 5) {
					itemToAdd.expectedDate = ackSegmentArr[5];
				}
				arr.push(itemToAdd);
			});
			return arr;
		}

		function getAckShippingAddress() {
			var addressSegment = getSegment("N3");
			if (addressSegment) {
				return addressSegment.split(fieldDelimiter)[1];
			} else {
				return "";
			}
		}
	}

	return {
		parse855: parse855,
	};
});
