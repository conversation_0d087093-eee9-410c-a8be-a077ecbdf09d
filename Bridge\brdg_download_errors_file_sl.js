/**
 * @description SL to consolidate the daily error logs in the payload
 
 * @NApiVersion 2.1
 * @NScriptType Suitelet
 * @NAmdConfig /SuiteScripts/config.json
 *
 * @param {import ("N/types")}
 * <AUTHOR>
 * @module brdg_download_errors_file_sl
 */

define([
  "require",
  "Moment",
  "N/log",
  "N/ui/serverWidget",
  "N/file",
  "N/redirect",
  "N/url",
  "N/https",
  "N/query",
  "../Classes/vlmd_custom_error_object",
], function (require, moment) {
  const log = require("N/log");
  const serverWidget = require("N/ui/serverWidget");
  const file = require("N/file");
  const redirect = require("N/redirect");
  const url = require("N/url");
  const https = require("N/https");
  const query = require("N/query");

  const CustomErrorObject = require("../Classes/vlmd_custom_error_object");
  const customErrorObject = new CustomErrorObject();

  var helperFunctions = (function () {
    /**
     * Gets all LS voided transactions names that meet the given params
     *
     * @param {string} startDate
     * @param {string} endDate
     * @param {string} storeDomain
     * @returns {Array<invoice_number|null>}
     */
    function _getQueueObjsForLsVoidedTransactions(
      startDate,
      endDate,
      storeDomain
    ) {
      try {
        const startDateUtc = moment.utc(startDate).format();
        const endDateUtc = moment.utc(endDate).format();

        const storesObj = {
          thevineyardsouth: "lsxs_pt_kDbGuYeOVZNz5UGAQyMPmi7wFWp8FK6X",
          thevineyardmadison: "lsxs_pt_RD5JY4e2A8AUIF7OXerflq4aF5Z3grrl",
          thevineyardexpress: "lsxs_pt_FmH7n1LeAcSYxcy03j7N1zy4kNVOBPbZ",
          thevineyardbergenfield: "lsxs_pt_N3ob7fzftAE9u18xiyjEOSvztcBkJP4r",
          thevineyardwestgate: "lsxs_pt_TWolKV1BtFTHpJdKjMGI5Nh0gjbkrYnq",
        };

        var header = {
          accept: "application/json",
          authorization: `Bearer ${storesObj[storeDomain]}`,
        };
        var apiURL = `https://${storeDomain}.retail.lightspeed.app/api/2.0/search?type=sales&status=Voided&date_from=${startDateUtc}&date_to=${endDateUtc}`;
        var responseObj = https.request({
          method: https.Method.GET,
          url: apiURL,
          headers: header,
        });

        const bodyData = JSON.parse(responseObj.body);
        const orderArr = bodyData.data;

        return orderArr && orderArr.length > 0
          ? orderArr.map((element) => `'${element.invoice_number}'`)
          : false;
      } catch (e) {
        throw customErrorObject.updateError({
          errorType: customErrorObject.ErrorTypes.NO_VALUE_RETURNED,
          summary: "ERROR_GETTING_VOIDED_ORDERS",
          details: `Error getting voided orders via the LightSpeed API: ${e}`,
        });
      }
    }

    /**
     * Gets all error queues that meet the given params
     *
     * @param {string} startDate
     * @param {string} endDate
     * @param {string} storeDomain
     * @returns {Array<{date:string, orderNumber:string, vendId: string, storeDomain:string, payload:JSON }|null>}
     */

    function getQueueObjsForTransactionsWithErrors(
      startDate,
      endDate,
      storeDomain
    ) {
      try {
        const voidedLsOrderInvoiceNumbers =
          _getQueueObjsForLsVoidedTransactions(startDate, endDate, storeDomain);

        const sqlQuery = `SELECT
   custrecord_in8_vend_log_datetime date,
   custrecord_in8_vend_order_num orderNumber,
   custrecord_in8_vend_log_vend_id vendId,
   custrecord_in8_vend_domain_prefix_ref storeDomain,
   custrecord_in8_vend_log_payload payload,
   from
   customrecord_in8_vend_log
   WHERE
   custrecord_in8_vend_domain_prefix_ref like '%${storeDomain}%'
   AND
   custrecord_in8_vend_log_status like 'error%'
   AND
   custrecord_in8_vend_log_payload not like '%voided%'
   AND
   created BETWEEN ('${startDate}') AND ('${endDate}')
   ${
     voidedLsOrderInvoiceNumbers
       ? `AND custrecord_in8_vend_order_num NOT IN (${voidedLsOrderInvoiceNumbers})`
       : ""
   }
   `;

        var queueObjsArr = query
          .runSuiteQL({
            query: sqlQuery,
            params: [],
          })
          .asMappedResults();

        if (!queueObjsArr || queueObjsArr.length <= 0) {
          log.debug("No error queues found");
        }

        return queueObjsArr;
      } catch (e) {
        throw customErrorObject.updateError({
          errorType: customErrorObject.ErrorTypes.NO_VALUE_RETURNED,
          summary: "ERROR_QUERYING_ERRRORS",
          details: `Error querying the error queues for params: ${e}`,
        });
      }
    }

    /**
   *Iterate over items in the payload for each obj
    and push a separate obj to the arr for each line item
   * @param {Array<Object>} queueObjsArr
   * @returns {Array<Object>}
   */
    function flattenObjs(queueObjsArr) {
      const flattenedArr = [];

      queueObjsArr.forEach((queueObj) => {
        try {
          let parsedItems = JSON.parse(queueObj.payload).register_sale_products;

          if (!parsedItems) {
            /*This payload follows a new format*/
            parsedItems = JSON.parse(queueObj.payload).line_items;
          }

          if (!parsedItems) {
            parsedItems = JSON.parse(queueObj.payload).register_sale_products;

            flattenedArr.push({
              downloadError: `No sales products found in this payload.`,
              payload: JSON.stringify(queueObj).split(",").join("-"),
            });
          } else {
            parsedItems.forEach((item) => {
              if (!item.sku) {
                /*This payload follows the new format which is missing information
                              -> query for the needed info from NS*/
                //                               const itemQuery = `SELECT
                //  custrecord_in8_vend_ids_item item_internal_id,
                //  itemid item_id,
                //  displayname description,
                // FROM
                //  customrecord_in8_vend_ids
                //  JOIN
                //     item
                //     ON item.id = custrecord_in8_vend_ids_item
                // WHERE
                //  custrecord_in8_vend_ids_id = '${item["product_id"]}'`;

                //                               let result = query
                //                                   .runSuiteQL({
                //                                       query: itemQuery,
                //                                       params: [],
                //                                   })
                //                                   .asMappedResults()[0];

                //                               log.debug("results", result);
                //                               item.sku = result["item_id"];
                //                               item.name = result["description"];
                //                               item.handle = result["item_internal_id"];
                item.sku = `Missing item SKU - do a vlookup with the Vend product ID ${item["product_id"]}`;
                item.name = "Missing item name - do a vlookup";
                item.handle = "Missing item handle - do a vlookup";
              }

              flattenedArr.push({
                date: queueObj.date,
                orderNumber: queueObj.orderNumber ?? queueObj["ordernumber"],
                vendId: queueObj.vendId ?? queueObj.vendid,
                storeDomain: queueObj.storedomain,
                product_id: item["product_id"],
                sku: item.sku,
                description: `"${item.name}"`,
                quantity: item.quantity,
                price: item.price ?? item["price_total"],
                nsInternalId: item.handle,
              });

              return true;
            });
          }
        } catch (e) {
          flattenedArr.push({
            downloadError: "Error Parsing/Flattening items",
            payload: "Error Parsing/Flattening items",
          });

          log.error(
            "Error Parsing/Flattening items",
            `Queue Obj: ${JSON.stringify(queueObj)}, Error: ${e.message}`
          );

          throw customErrorObject.updateError({
            errorType: customErrorObject.ErrorTypes.INVALID_DATA,
            summary: "ERROR_FLATTENING_OBJS",
            details: `Error flatting queue object: ${queueObj}, Error: ${e}`,
          });
        }

        return true;
      });

      return flattenedArr;
    }

    /**
     *
     * @param {Object} sampleItemObj
     * @returns {string} header row string composed of concatenated obj keys
     */
    function getHeaderRow(sampleItemObj) {
      try {
        return Object.keys(sampleItemObj).join(",");
      } catch (e) {
        throw customErrorObject.updateError({
          errorType: customErrorObject.ErrorTypes.UNHANDLED_ERROR,
          summary: "ERROR_GETTING_HEADER_ROW",
          details: `Error getting header row: ${e}`,
        });
      }
    }

    /**
     *
     * @param {Array<Object>} flattenedObjsArr
     * @returns {Array <string>}
     */
    function mapToStringsArr(flattenedObjsArr) {
      try {
        return flattenedObjsArr.map((itemObj) =>
          Object.values(itemObj).join(",")
        );
      } catch (e) {
        throw customErrorObject.updateError({
          errorType: customErrorObject.ErrorTypes.UNHANDLED_ERROR,
          summary: "ERROR_MAPPING_TO_STRINGS_ARR",
          details: `Error mapping to array of strings: ${e}`,
        });
      }
    }

    function createFile(fileString, startDate, endDate, storeDomain, folderId) {
      try {
        return file.create({
          name: `${storeDomain}_${startDate}_${endDate}_${moment().format(
            "MMDDYYHHmmss"
          )}.csv`,
          fileType: file.Type.CSV,
          contents: fileString,
          folder: folderId,
        });
      } catch (e) {
        throw customErrorObject.updateError({
          errorType: customErrorObject.ErrorTypes.RECORD_NOT_SAVED,
          summary: "ERROR_CREATING_FILEE",
          details: `Error creating file: ${e}`,
        });
      }
    }

    function redirectToSuitelet(fileId, folderId) {
      try {
        var suiteletURL = url.resolveScript({
          scriptId: "customscript_brdg_download_error_payload",
          deploymentId: "customdeploy_brdg_download_error_payload",
          params: {
            redirected_from_sl: true,
            file_id: fileId,
            folder_id: folderId,
          },
        });

        redirect.redirect({ url: suiteletURL });
      } catch (e) {
        throw customErrorObject.updateError({
          errorType: customErrorObject.ErrorTypes.UNHANDLED_ERROR,
          summary: "ERROR_REDIRECTING_TO_SL",
          details: `Error redirecting to SL: ${e}`,
        });
      }
    }

    return {
      getQueueObjsForTransactionsWithErrors,
      flattenObjs,
      getHeaderRow,
      mapToStringsArr,
      createFile,
      redirectToSuitelet,
    };
  })();

  return {
    onRequest: function (context) {
      const folderId = "4257211"; //BRDG In8 Sync Error Logs

      if (context.request.method === "GET") {
        try {
          const form = serverWidget.createForm({
            title: "Download Error Log",
          });
          const redirectedFromSl =
            context.request.parameters["redirected_from_sl"];

          if (redirectedFromSl) {
            form.addField({
              id: "custpage_results",
              label: "View Results",
              type: serverWidget.FieldType.INLINEHTML,
            });

            const file = context.request.parameters["file_id"];
            const folderId = context.request.parameters["folder_id"];

            form.updateDefaultValues({
              custpage_results: file
                ? `<a style='margin: 0px;padding: 20px 50px;font-size: 20px;'href="https://5802576.app.netsuite.com/app/common/media/mediaitemfolders.nl?folder=${folderId}&whence=" target=_blank>View Results</a>`
                : `<h2>No error logs found for the provided dates and store.</h2>`,
            });
          } else {
            form.addField({
              id: "custpage_start_date",
              label: "Start Date",
              type: serverWidget.FieldType.DATE,
            }).defaultValue = moment().subtract(7, "days").format("MM/DD/YYYY");

            form.addField({
              id: "custpage_end_date",
              label: "End Date",
              type: serverWidget.FieldType.DATE,
            }).defaultValue = new Date(); //Today's date

            form.addField({
              id: "custpage_domain",
              label: "Store Domain",
              type: serverWidget.FieldType.TEXT,
            });

            form.updateDefaultValues({ custpage_domain: "thevineyard" });

            form.addSubmitButton({
              label: "Download Error Log",
            });
          }

          context.response.writePage(form);
        } catch (e) {
          customErrorObject.throwError({
            summaryText: "ERROR_IN_GET",
            error: e,
          });
        }
      } else {
        //POST Request
        try {
          const startDate = context.request.parameters.custpage_start_date;
          const endDate = context.request.parameters.custpage_end_date;
          const storeDomain = context.request.parameters.custpage_domain ?? "";

          log.audit(
            "Download BRDG Errors Parameters",
            `Start Date: ${startDate}, End Date: ${endDate}, Store: ${storeDomain}`
          );
          let fileId = "";

          const queueObjsArr =
            helperFunctions.getQueueObjsForTransactionsWithErrors(
              startDate,
              endDate,
              storeDomain
            );

          if (queueObjsArr.length > 0) {
            const flattenedObjsArr = helperFunctions.flattenObjs(queueObjsArr);
            const headerRow = helperFunctions.getHeaderRow(flattenedObjsArr[0]);
            const stringsArr =
              helperFunctions.mapToStringsArr(flattenedObjsArr);
            const fileString = headerRow + "\n" + stringsArr.join("\n");
            const fileToUpload = helperFunctions.createFile(
              fileString,
              startDate,
              endDate,
              storeDomain,
              folderId
            );

            fileId = fileToUpload.save();
          }

          helperFunctions.redirectToSuitelet(fileId, folderId);

          return;
        } catch (e) {
          customErrorObject.throwError({
            summaryText: "ERROR_IN_POST",
            error: e,
          });
        }
      }
    },
  };
});
