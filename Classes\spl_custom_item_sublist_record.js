/**
 * Item Sublist Record class
 * containing custom and searched data
 * that represents the line in the item sublist
 * of a transaction
 *
 * @NApiVersion 2.1
 * <AUTHOR>
 */

define(["N/search", "ItemSublistRecord"], (search, ItemSublistRecord) => {
	/**
	 * Custom Item Sublist Record class
	 *
	 * @class
	 * @type {import("./spl_item_sublist_record").CustomItemSublistRecord}
	 */
	class CustomItemSublistRecord extends ItemSublistRecord {
		constructor(transaction, index) {
			super(transaction, index);

			// Columns will be used for search.lookupFields
			this.Column = {
				dontDropShip: "custitem_spl_dont_drop_ship",
				dontUseItemInNewTransactions: "custitem_spl_dont_use_item_in_new_tran",
				isFinalSaleItem: "custitem_spl_final_sale_item",
				isFinalSaleVendor: "custentity_spl_final_sale_vendor",
				needsEtaUpdate: "custitem_spl_requires_eta_update",
				noMinimumMarkup: "custitem_spl_no_min_markup_alert",
				pricingGroup: "pricinggroup",
				vendor: "vendor",
				productCategory: "class",
			};

			/**
			 * Get the value from a search lookup object array
			 *
			 * @param {object} searchLookup Search lookup object
			 * @param {string} field Field in the lookup object
			 * @param {any} defaultValue Default value to set if field is not in the lookup object
			 * @returns {any} Value from the lookup object
			 */
			const getLookupArrayValue = (searchLookup, field, defaultValue) => {
				return (
					(searchLookup &&
						searchLookup[field] &&
						Array.isArray(searchLookup[field]) &&
						searchLookup[field][0] &&
						searchLookup[field][0].value) ||
					defaultValue
				);
			};

			this.itemLookup =
				this.itemId &&
				search.lookupFields({
					type: search.Type.ITEM,
					id: this.itemId,
					columns: [
						this.Column.dontDropShip,
						this.Column.dontUseItemInNewTransactions,
						this.Column.isFinalSaleItem,
						this.Column.needsEtaUpdate,
						this.Column.noMinimumMarkup,
						this.Column.pricingGroup,
						this.Column.vendor,
						this.Column.productCategory,
					],
				});

			this.vendorId = getLookupArrayValue(
				this.itemLookup,
				this.Column.vendor,
				false
			);

			if (this.itemLookup) {
				this.dontDropShip = this.itemLookup[this.Column.dontDropShip];
				this.dontUseItemInNewTransactions =
					this.itemLookup[this.Column.dontUseItemInNewTransactions];
				this.isFinalSaleItem = Boolean(
					this.itemLookup[this.Column.isFinalSaleItem]
				);
				this.noMinimumMarkupAlert =
					this.itemLookup[this.Column.noMinimumMarkup];
				this.priceGroup = getLookupArrayValue(
					this.itemLookup,
					this.Column.pricingGroup,
					""
				);
			}
			if (!this.isFinalSaleItem) {
				const vendorLookup =
					this.vendorId &&
					search.lookupFields({
						type: search.Type.VENDOR,
						id: this.vendorId,
						columns: [this.Column.isFinalSaleVendor],
					});
				this.isFinalSaleItem = Boolean(
					vendorLookup && vendorLookup[this.Column.isFinalSaleVendor]
				);
			}
		}

		/**
		 * Retrieve markup percentage
		 *
		 * @returns {string} Minimum markup percentage
		 */
		getMinimumMarkupPercentage() {
			const largeDMEItemPriceGroupId = "32";

			return this.priceGroup === largeDMEItemPriceGroupId ? "10%" : "15%";
		}

		/**
		 * Retrieve markup amount
		 *
		 * @returns {number} Minimum markup amount
		 */
		getMinimumMarkupAmount() {
			return this.priceGroup === "32" //Excluding Large DME items
				? this.itemCost * 1.1
				: this.itemCost * 1.15;
		}

	}

	return CustomItemSublistRecord;
});
