/**
 * @NApiVersion 2.1
 * @NScriptType Suitelet
 * @NAmdConfig /SuiteScripts/config.json
 * <AUTHOR>
 * @description SL to print item label on scan
 * @module brdg_print_item_label_sl
 */

define([
  "require",
  "N/ui/serverWidget",
  "N/url",
  "N/redirect",
  "N/render",
  "N/https",
  "N/file",
  "N/query",
  "N/format",
  "N/runtime",
  "N/log",
  "../../Classes/vlmd_custom_error_object",
], function (require) {
  const serverWidget = require("N/ui/serverWidget");
  const url = require("N/url");
  const redirect = require("N/redirect");
  const render = require("N/render");
  const https = require("N/https");
  const file = require("N/file");
  const query = require("N/query");
  const format = require("N/format");
  const runtime = require("N/runtime");
  const log = require("N/log");

  let apiKeyEncoded =
    "ZkNpQi1KRW1FdWQ4a0ktYWtJQ3hJMEhvQXVLbkxKbC1hQUlvYmJ0UV9aTQ==";
  var currentScript = runtime.getCurrentScript();
  let folderId = 7291625; //SB2 Folder:  6553749

  /** @type {import("../../Classes/vlmd_custom_error_object").CustomErrorObject} */

  const CustomErrorObject = require("../../Classes/vlmd_custom_error_object");
  const customErrorObject = new CustomErrorObject();

  var helperFunctions = (function () {
    function addInlineHTML(form, subsidiaryText) {
      try {
        var htmlSkuField = form.addField({
          id: "custpage_html",
          label: "HTML",
          type: serverWidget.FieldType.INLINEHTML,
        });

        htmlSkuField.defaultValue = /*html*/ `
	<style>
		  .outer_container {
			height: 1000px;
			position: relative;
			text-align: center;
		  }
		  
		  .inner_container {
			margin: 0;
			position: absolute;
			top: 50%;
			left: 50%;
			-ms-transform: translate(-50%, -50%);
			transform: translate(-50%, -50%);
		  }
	  </style>
	  
	  <body>   
	  	<div class="outer_container"> 
			<div class="inner_container">
	  			<label for="scanUPC" style = "font-size:80px; text-align:center"><b>SCAN UPC</b></label><br/>
				<span style= "font-size: 35px;">${subsidiaryText}</span><br/><br/>
		  		<input type="number" style="height:200px; width: 700px; font-size:80px; text-align: center;" name="scanUPC" autofocus  ><br><br><br><br><br><br>
	  			<input type="submit" style="height:175px; width: 700px; font-size:80px; background-color:green;" value="PRINT LABEL">
	  		</div>
		</div>
	  </body>`;
      } catch (err) {
        throw customErrorObject.updateError({
          errorType: customErrorObject.ErrorTypes.VALUE_NOT_SET,
          summary: "PARAMETER_FIELDS_NOT_SET",
          details: `Errors setting details when hit GET via link: ${err}`,
        });
      }
    }

    function getLabelInfo(itemUpc, subsidiary) {
      const sqlQuery = /*sql*/ `
		  SELECT upccode,
      SUBSTR(description, 1, INSTR(description, ',') - 1) description,
       unitprice,
       CASE
       WHEN custrecord_active_promotion_tag_internal in  ('custitem_ltspd_ggn_prmtn_tag',
        'custitem_ltspd_ggs_prmtn_tag',
        'custitem_ltspd_lol_prmtn_tag',
        'custitem_ltspd_wg_prmtn_tag',
        'custitem_ltspd_vyb_prmtn_tag',
        'custitem_ltspd_vye_prmtn_tag')
 THEN unitprice - promo.custrecord_dollar_promo_off
         ELSE 0
       END AS saleprice
FROM   pricing
       LEFT OUTER JOIN customrecord_bridge_store store
                    ON store.custrecord_brdg_store_subsidiary = '${subsidiary}'
       INNER JOIN item
               ON item.id = pricing.item
       LEFT OUTER JOIN customrecord_brdg_promtion_tag promo
                    ON CASE
                         WHEN promo.id = item.custitem_ltspd_ggn_prmtn_tag and store.custrecord_active_promotion_tag_internal = 'custitem_ltspd_ggn_prmtn_tag' THEN
                         1
                         WHEN promo.id = custitem_ltspd_ggs_prmtn_tag and store.custrecord_active_promotion_tag_internal = 'custitem_ltspd_ggs_prmtn_tag' THEN 1
                         WHEN promo.id = custitem_ltspd_lol_prmtn_tag and store.custrecord_active_promotion_tag_internal = 'custitem_ltspd_lol_prmtn_tag' THEN 1
                         WHEN promo.id = custitem_ltspd_wg_prmtn_tag and store.custrecord_active_promotion_tag_internal = 'custitem_ltspd_wg_prmtn_tag' THEN 1
                         WHEN promo.id = custitem_ltspd_vyb_prmtn_tag and store.custrecord_active_promotion_tag_internal = 'custitem_ltspd_vyb_prmtn_tag' THEN  1
                         WHEN promo.id = custitem_ltspd_vye_prmtn_tag and store.custrecord_active_promotion_tag_internal = 'custitem_ltspd_vye_prmtn_tag' THEN 1
                         ELSE 0
                       END = 1
WHERE  (upccode = '${itemUpc}' or custitem_brdg_additional_upc_ = '${itemUpc}')
       AND pricelevel = store.custrecord_brdg_store_price_level`;

      return query
        .runSuiteQL({
          query: sqlQuery,
        })
        .asMappedResults()[0];
    }

    function printLabel(printerName, labelInfoObj) {
      var apiURL = "https://api.printnode.com/printers";

      var listOfPrintersResponse = https.get({
        url: apiURL,
        headers: { Authorization: `Basic ${apiKeyEncoded}` },
      });

      if (!listOfPrintersResponse || !listOfPrintersResponse.body) {
        throw customErrorObject.updateError({
          errorType: customErrorObject.ErrorTypes.NO_VALUE_RETURNED,
          summary: "NO_PRINTERS_FOUND",
          details: `No printers found for this store`,
        });
      }

      listOfPrintersResponse = JSON.parse(listOfPrintersResponse.body);

      var labelPrinterObj = listOfPrintersResponse.filter((obj) => {
        return obj.name === printerName;
      });

      var labelPrinterId =
        labelPrinterObj && labelPrinterObj[0] && labelPrinterObj[0].id;

      if (!labelPrinterId) {
        throw customErrorObject.updateError({
          errorType: customErrorObject.ErrorTypes.NO_VALUE_RETURNED,
          summary: "NO_PRINTER_ID",
          details: `No corresponding printer, ${printerName} found for this store`,
        });
      }

      const pdfObj = _generatePdfFile(labelInfoObj);

      if (!pdfObj) {
        throw customErrorObject.updateError({
          errorType: customErrorObject.ErrorTypes.NO_VALUE_RETURNED,
          summary: "PDF_NOT_GENERATED",
          details: `PDF label not generated`,
        });
      }

      const printJobObj = {
        printerId: labelPrinterId,
        title: "Print Job",
        contentType: "pdf_uri",
        content: pdfObj.url.includes("https")
          ? pdfObj.url
          : "https://" +
            url.resolveDomain({ hostType: url.HostType.APPLICATION }) +
            pdfObj.url,
        source: "api documentation!",
        options: { rotate: 90 },
        expireAfter: 600,
      };

      var responseObjPrint = https.post({
        url: "https://api.printnode.com/printjobs",
        headers: { Authorization: `Basic ${apiKeyEncoded}` },
        body: printJobObj,
      });

      const statusCode = responseObjPrint.code;

      if (statusCode == 201) {
        //Success
        return true;
      } else {
        throw customErrorObject.updateError({
          errorType: customErrorObject.ErrorTypes.INVALID_DATA,
          summary: "LABEL_COULD_NOT_PRINT.",
          details: `Error printing label! ${responseObjPrint}`,
        });
      }
    }

    function _generatePdfFile(labelInfoObj) {
      const escapedHtmlDescription = labelInfoObj.description.replace(
        /&/g,
        "&amp;"
      );

      const xmlLabelPdfDoc = `
	  <pdf>
    <head>
        <link name="mytimes" type="font" subtype="opentype" src="calibri.ttf" bytes="1"/> 
</head>
	  <body style="font-size:95px; margin: auto; text-align:center;" padding="0in 0in 0in 0in" size="Letter-LANDSCAPE">
    <table style=" margin-left: auto; margin-right: auto; align: center; vertical-align:middle">
  <tr><td>
    <p style="text-align:center; font-family=calibri;">
	  			<br/>${escapedHtmlDescription}<br/><span style="font-size: 180px; font-weight:bold">
			${
        labelInfoObj.saleprice != labelInfoObj.unitprice &&
        labelInfoObj.saleprice
          ? `Sale`
          : ""
      } $${format.format({
        value: labelInfoObj.saleprice ?? labelInfoObj.unitprice,
        type: format.Type.CURRENCY,
      })}</span>
			<br/> <span style = "font-size: 80px; font-weight:bold">${
        labelInfoObj.saleprice != labelInfoObj.unitprice &&
        labelInfoObj.saleprice
          ? `<s>REG. PRICE: $${format.format({
              value: labelInfoObj.unitprice,
              type: format.Type.CURRENCY,
            })}</s>`
          : ""
      }
		</span>
    </p></td></tr>
      </table>
	  </body>
	  </pdf>`;

      var pdfFile = render.xmlToPdf({
        xmlString: xmlLabelPdfDoc,
      });

      pdfFile.name = "Item Label";
      pdfFile.folder = folderId;
      pdfFile.isOnline = true;

      var fileId = pdfFile.save();

      return file.load({ id: fileId });
    }

    return {
      printLabel,
      addInlineHTML,
      getLabelInfo,
    };
  })();

  return {
    onRequest: function (context) {
      let printerName = currentScript.getParameter(
        "custscript_label_printer_name"
      );
      let subsidiaryId = currentScript.getParameter("custscript_subsidiary_id");
      let subsidiaryText = currentScript.getParameter(
        "custscript_subsidiary_text"
      );
      let deploymentId =
        currentScript.getParameter("custscript_deployment_id") ??
        "customdeploy_brdg_print_item_label_sl";
      let suiteletURL = url.resolveScript({
        scriptId: "customscript_brdg_print_item_label_sl",
        deploymentId: deploymentId,
      });
      if (!printerName || !subsidiaryId || !subsidiaryText) {
        throw "Error printing label! No subsidiary/printer found for this store!";
      }
      var form = serverWidget.createForm({
        title: " ",
        hideNavBar: true,
      });

      if (context.request.method === "GET") {
        helperFunctions.addInlineHTML(form, subsidiaryText);

        context.response.writePage(form);
      } else {
        try {
          const upcCode = context.request.parameters.scanUPC;

          const labelInfoObj = helperFunctions.getLabelInfo(
            upcCode,
            subsidiaryId
          );

          if (
            !labelInfoObj ||
            !labelInfoObj.description ||
            !labelInfoObj.unitprice
          ) {
            var missingInfoField = form.addField({
              id: "custpage_html_2",
              label: "HTML",
              type: serverWidget.FieldType.INLINEHTML,
            });

            missingInfoField.defaultValue = /*html*/ `
            <html>
            <div style="display: flex; justify-content: center; background-color: #f8d7da; align-items: center; height: 100vh;">
  <p style="font-size: 4rem; text-align: center;">
    <strong>No information found for this UPC code/missing information.</strong>
 <br/><br/>
    UPC Code: ${upcCode} <span id="upcCodeDisplay">
  <br/><br/>
  <input type="button" style="background-color: #721c24; color: #fff; height:150px; width: 675px; font-size:80px;
  border: none; padding: 10px 20px; border-radius: 5px; cursor: pointer;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1), 0 1px 3px rgba(0, 0, 0, 0.08);
  transform: translateY(-2px);" onclick="window.location.href='${suiteletURL}';" value="SCAN ANOTHER">
</p>
</div>
</html>`;

            context.response.writePage(form);
            return;
          }

          log.audit("Label Info", labelInfoObj);

          try {
            const printingStatus = helperFunctions.printLabel(
              printerName,
              labelInfoObj
            );

            printingStatus &&
              redirect.toSuitelet({
                scriptId: "customscript_brdg_print_item_label_sl",
                deploymentId: deploymentId,
              });
          } catch (e) {
            throw customErrorObject.updateError({
              errorType: customErrorObject.ErrorTypes.INVALID_DATA,
              summary: "LABEL_COULD_NOT_PRINT.",
              details: `Error printing label! ${e.message}`,
            });
          }
        } catch (e) {
          customErrorObject.throwError({
            summaryText: `ERROR_IN_SL`,
            error: e,
          });
        }
      }
    },
  };
});
