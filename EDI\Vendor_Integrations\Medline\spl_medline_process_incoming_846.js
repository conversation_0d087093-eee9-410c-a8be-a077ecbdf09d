/**
 * @NApiVersion 2.1
 * @NScriptType scheduledscript
 * @NAmdConfig /SuiteScripts/config.json
 */

define([
    'N/sftp',
    "GetEdiPartnerValuesLib",
    "ProcessInventoryInquiryLib"
],
    function (
        sftp,
        getEdiPartnerValuesLib,
        parseInventoryLib,
        processInventoryLib
    ) {
        function execute(context) {
            //create all items as inventory
            //most medline items could probably be inventory - 
            //so can email mr.sil<PERSON><PERSON> list of all medline inv items to see what can be non - inv

            var vendorData = {
                prodGUID: '',
                sandboxGUID: '********************************',
                testDirectory: '/users/medlinetest/IN/Inventory_Inquiries',
                prodDirectory: '/users/medlineprod/IN/846',
                vendorName: 'Medline',
            }

            var hostKey = "AAAAB3NzaC1yc2EAAAABIwAAAIEA2YIvZPcsWA3jP6yq5J" +
                "5XYunNySNspUZUhtzxXoePDZEhxJFIF/am9o+15CqO4hlyEDni0m+VW4" +
                "LVSoiUnj31xJis91EcYrUeCewIcSwof4Xs05ghth/2SKn0cITsR5hOeST" +
                "l4jFsMf9oK0+b2gr4jdV4fria4E/eF2u0qQArWuc=";

            var connection = createConnection();
            var directoryList = getDirectoryList();
            var fileName = getFileName();
            if (fileName) {
                var downloadConnection = getDownloadConnection()
                var fileData = getFileContents();
                processInventory();
            } else {
                log.debug('Script ran, no results found');
            }

            function createConnection() {
                return sftp.createConnection({
                    username: 'FTPadmin',
                    //passwordGuid: vendorData.prodGUID, //production account
                    passwordGuid: vendorData.sandboxGUID, //sandbox account, 
                    url: '************',
                    hostKey: hostKey,
                    directory: vendorData.testDirectory
                });
            }

            function getDirectoryList() {
                return connection.list({
                    path: ''
                });
            }

            function getFileName() {
                try {
                    var file = directoryList.find(name => !name.directory); //.name
                    return file.name;
                } catch (error) {
                    throw vendorData.vendorName + ' Inventory: ' + error;
                }
            }

            function getDownloadConnection() {
                return sftp.createConnection({
                    username: 'FTPadmin',
                    //passwordGuid: vendorData.prodGUID, //production account
                    passwordGuid: vendorData.sandboxGUID, //sandbox account, 
                    url: '************',
                    hostKey: hostKey
                });
            }

            function getFileContents() {
                try {
                    var downloadedFile = downloadConnection.download({
                        directory: vendorData.testDirectory,
                        filename: fileName
                    });
                    var content = downloadedFile.getContents();

                    return {
                        content: content,
                        name: fileName
                    };
                } catch (error) {
                    throw vendorData.vendorName + ' Inventory: ' + error;
                }
            }

            function processInventory() {
                log.debug({
                    title: 'file data',
                    details: fileData
                });
                var partnerValues = getEdiPartnerValuesLib.getMedlineValues();
                var inventoryObj = parseInventoryLib.parse846(fileData.content, partnerValues);
                log.debug({
                    title: 'inv obj',
                    details: inventoryObj
                });
                //try {
                inventoryObj.items.forEach(itemObj => processInventoryLib.processItem(itemObj, fileData.name))
                //} catch (error) {
                //    log.debug({
                //        title: 'Error processing ' + vendorData.vendorName + ' inventory',
                //        details: error
                //    });
                //}
            }
        }

        return {
            execute: execute
        }
    });
