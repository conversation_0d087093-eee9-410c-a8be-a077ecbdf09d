/**
 * @description Class representing the object we display as notification from the UI
 *
 * @NApiVersion 2.1
 * <AUTHOR>
 */

define(["require", "exports", "N/log"], (
	/** @type {any} */ require,
	/** @type {any} */ exports
) => {
	const log = require("N/log");

	/**
	 * UI Notification Object class
	 *
	 * @type {import("./spl_ui_notification_object").UINotification}
	 */
	class UINotification {
		constructor(scriptName) {
			this.warnings = [];
			this.errors = [];
			this.scriptName = scriptName ?? "";
		}

		/**
		 * Log the messages in the script deployment
		 *
		 * @param {"debug"|"audit"|"error"} level Log level
		 * @param {"warnings"|"errors"} messageType Notification type
		 * @returns {void}
		 */
		logNotifications(level, messageType) {
			log[level]("UI Notifications", this[messageType].join("\n"));
		}

		/**
		 * Add a new notification to the list of messages
		 *
		 * @param {"warnings"|"errors"} messageType Notification type
		 * @param {string} notification Warning message
		 * @returns {void}
		 */
		addNotification(messageType, notification) {
			this[messageType].push(notification);
		}

		/**
		 * Display an alert dialog box containing warning and error messages
		 * To be used in UI context only
		 *
		 * @returns {void}
		 */
		displayNotifications() {
			if (this.warnings.length > 0 || this.errors.length > 0) {
				alert(
					`${this.warnings.join("\n")}\n${this.errors.join(
						"\n"
					)} (Error from internal script ${this.scriptName})`
				);
			}
		}

		/**
		 * Allow/prevent save based on whether there are errors
		 *
		 * @returns {boolean}
		 */
		allowSave() {
			//If only warnings and no errors, return true and display soft alert. Otherwise, return false and prevent save.
			return this.errors.length <= 0;
		}
	}

	exports.UINotification = UINotification;

	return UINotification;
});
