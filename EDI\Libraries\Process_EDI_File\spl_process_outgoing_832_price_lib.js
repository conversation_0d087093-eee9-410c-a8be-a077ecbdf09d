/**
 * @NApiVersion 2.1
 * @NAmdConfig /SuiteScripts/config.json
 *
 */

define([
  "require",
  "Moment",
  "../../../EDI/Libraries/Customer_Integration_Libraries/Outgoing_832_Libs/spl_get_result_obj_for_832_lib",
  "../../../Libraries/Misc_Libs/spl_get_edi_file_contents_lib",
  "N/log",
  "N/query",
  "N/file",
], (require, moment) => {
  const log = require("N/log");
  const query = require("N/query");
  const file = require("N/file");

  const getResultObjFor832Lib = require("../../../EDI/Libraries/Customer_Integration_Libraries/Outgoing_832_Libs/spl_get_result_obj_for_832_lib");
  const getEdiFileContentsLib = require("../../../Libraries/Misc_Libs/spl_get_edi_file_contents_lib");
  /** @type {import("../../../Libraries/Misc_Libs/spl_get_edi_file_contents_lib")}*/

  function getPriceList(customerInternalId) {
    function _getOnyxItemIds() {
      const itemIdQuery = `	
			AND (item.id = 5344 OR item.id = 3396 OR item.id = 3477 OR item.id = 8326 OR item.id = 5819 OR item.id = 38759 OR item.id = 15625 OR item.id = 38028 OR item.id = 15626 OR item.id = 44708 OR item.id = 4737 OR item.id = 4574 OR item.id = 4354 OR item.id = 2928 OR item.id = 3867 OR item.id = 43335 OR item.id = 43349 OR item.id = 43507 OR item.id = 42119)
			`;
      return itemIdQuery;
    }

    const itemPricingQuery = `SELECT
		BUILTIN.DF(customeritempricing.item) as item,
		customeritempricing.price as price,
		item.saleunit as unit,
		item.itemtype as type,
		CASE
			WHEN
				item.itemtype = 'Kit'
			THEN
				CASE WHEN item.custitem_spl_uom_abbreviation IS NOT NULL THEN item.custitem_spl_uom_abbreviation ELSE 'EA' END
			ELSE
				utu.abbreviation
			END abbreviation, 
	FROM
		customeritempricing 
	INNER JOIN
		   item 
		   ON item.id = customeritempricing.item 
		   AND item.custitem_spl_apprvd_for_itm_ctlg = 'T' 
		   AND item.isinactive = 'F' 
	LEFT OUTER JOIN
			unitsTypeUom utu
			ON item.saleunit = utu.internalid
	 WHERE
	 	customeritempricing.customer = ?
		${customerInternalId == 13253 ? _getOnyxItemIds() : ""}`;

    const groupPricingQuery = `SELECT
    BUILTIN.DF(pricingwithcustomers.item) as item,
    pricingwithcustomers.unitprice as price,
    pricingwithcustomers.saleunit as unit, 
	item.itemtype as type,
	CASE
			WHEN
				item.itemtype = 'Kit'
			THEN
				CASE WHEN item.custitem_spl_uom_abbreviation IS NOT NULL THEN item.custitem_spl_uom_abbreviation ELSE 'EA' END
			ELSE
				utu.abbreviation
			END abbreviation, 
FROM
   pricingwithcustomers 
INNER  JOIN
     item 
     ON item.id = pricingwithcustomers.item 
     AND item.custitem_spl_apprvd_for_itm_ctlg = 'T' 
     AND item.isinactive = 'F' 
LEFT OUTER JOIN
	unitsTypeUom utu
	ON item.saleunit = utu.internalid
WHERE
   pricingwithcustomers.customer = ? 
   AND pricingwithcustomers.assignedpricelevel = 'T'
   ${customerInternalId == 13253 ? _getOnyxItemIds() : ""}`;

    const itemsArr = [
      ..._getSuiteQlResultsObj(itemPricingQuery, customerInternalId),
      ..._getSuiteQlResultsObj(groupPricingQuery, customerInternalId),
    ];

    if (itemsArr.length <= 0) {
      throw Error({
        name: "ERROR_GETTING_QUERY_RESULTS",
        message: `No results returned for the price catalog for ${
          customerInternalId ?? "Undefined"
        }.`,
      });
    }

    return itemsArr;
  }

  function _getSuiteQlResultsObj(sqlQuery, customerInternalId) {
    try {
      const resultIterator = query
        .runSuiteQL({
          query: sqlQuery,
          params: [customerInternalId],
        })
        .asMappedResults();

      return resultIterator;
    } catch (e) {
      throw `${e}: ${sqlQuery}`;
    }
  }

  function getItemObj(itemRow, accountNumber) {
    return getResultObjFor832Lib.getForPriceCatalog(itemRow, accountNumber);
  }

  function getItemAsString(itemObj) {
    return `${itemObj["SUPPLIER_ID"]},${itemObj["PRICE_LEVEL_ID"]},${itemObj["FILE_TYPE"]},${itemObj["ITEM_NUM"]},${itemObj["UM"]},${itemObj["PRICE"]},${itemObj["IS_AUDITED"]},${itemObj["START_DATE"]},${itemObj["END_DATE"]},\n`;
  }

  function processEnd(summary, accountNumber, dataObj, customerName) {
    const headerRow = _getHeaderRow();
    let catalogString = headerRow + _getAllItemRowsAsString(summary);

    const fileToUpload = _getFileToUpload(accountNumber, catalogString);

    _uploadFileToTheirServer(fileToUpload, dataObj);
    _uploadFileToSupplyLineServer(
      fileToUpload,
      accountNumber,
      dataObj,
      customerName
    );

    log.audit(`File Uploaded Successfully for ${accountNumber}`);
  }

  function getFileAsString(paramsObj, customErrorObject) {
    const { context, accountNumber } = paramsObj;
    const headerRow = _getHeaderRow();
    let catalogString = headerRow + _getAllItemRowsAsString(context);

    const fileToUpload = _getFileToUpload(accountNumber, catalogString);
    return fileToUpload;
  }

  function _getHeaderRow() {
    return `SUPPLIER_ID,PRICE_LEVEL_ID,FILE_TYPE,ITEM_NUM,UM,PRICE,IS_AUDITED,START_DATE,END_DATE,\n`;
  }

  function _getAllItemRowsAsString(summary) {
    let itemRowsString = "";
    summary.output.iterator().each(function (key, itemRow) {
      itemRowsString += itemRow;

      return true;
    });

    return itemRowsString;
  }

  function _getFileToUpload(accountNumber, csvFile) {
    return file.create({
      name: `${accountNumber}_Price_Catalog_${moment().format(
        "YYYY_MM_DD"
      )}.csv`,
      fileType: file.Type.CSV,
      contents: csvFile,
    });
  }

  function _uploadFileToTheirServer(fileToUpload, dataObj) {
    const connection = getEdiFileContentsLib.createConnection(dataObj);

    connection.upload({
      file: fileToUpload,
      directory: "",
      replaceExisting: true,
    });
  }

  function _uploadFileToSupplyLineServer(
    fileToUpload,
    accountNumber,
    dataObj,
    customerName
  ) {
    try {
      const referenceDirectory = `/EDI Reference Files/${dataObj.purchasingSoftware}/${accountNumber}/OUT/832`;

      const supplyLineConnection = getEdiFileContentsLib.createConnection(
        dataObj,
        "",
        referenceDirectory
      );

      supplyLineConnection.upload({
        file: fileToUpload,
        replaceExisting: true,
      });
    } catch (e) {
      throw `${customerName} ${dataObj.transactionType}:  Could not upload file to SupplyLine Server. Error: ${e}`;
    }
  }

  return {
    getPriceList,
    getItemObj,
    getItemAsString,
    processEnd,
    getFileAsString,
  };
});
