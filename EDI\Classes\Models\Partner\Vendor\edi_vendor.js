/**
 * @description Vendor class implementation
 *
 * @NApiVersion 2.1
 * <AUTHOR> <<EMAIL>>
 */

define([
    "exports",
    "require",
    "../edi_partner"
], (/** @type {any} */ exports, /** @type {any} */ require) => {
    const { EDIPartner } = require("../edi_partner");

    /**
     * EDI Partner class representing vendors
     *
     * @class
     * @typedef {import("../../../Interfaces/Models/Partner/edi_partner").EDIVendorInterface} EDIVendorInterface
     * @implements {EDIVendorInterface}
     * @extends {EDIPartner}
     */
    class EDIVendor extends EDIPartner {
        constructor() {
            super();
        }
    }

    exports.EDIVendor = EDIVendor;
});