/**
 * @NApiVersion 2.1
 */

//@ts-ignore
define(["N/search"], function (search) {
	function getTaskRepArr(department) {
		//sorted by internal id
		var searchObj = search.create({
			type: "employee",
			filters: [["department", "anyof", department]],
		});

		var resultSet = searchObj.run();

		var resultsArr = resultSet
			.getRange({
				start: 0,
				end: 10, //PULLS RESULTS OF UP TO 10 REPS, CAN CHANGE
			})
			.map((result) => result.id);

		return resultsArr;
	}

	function getCaseRepArr() {
		//sorted by internal id

		var searchObj = search.create({
			type: "supportcase",
			filters: [
				["assigned", "anyof", "9975", "12215"], //Devorah and Sara
			],
			columns: [
				search.createColumn({ name: "assigned", label: "Assigned To" }),
			],
		});

		var resultSet = searchObj.run();

		return resultSet
			.getRange({
				start: 0,
				end: 3,
			})
			.map((result) => {
				return result.getValue(searchObj.columns[0]);
			});
	}

	function getNewQuotesTasksRepArr() {
		//sorted by internal id

		var searchObj = search.create({
			type: "task",
			filters: [
				["assigned", "anyof", "9975", "12215"],
				// "AND",
				// ["custevent_spl_task_type", "anyof", "25"], //New Quote Requests
			],
			columns: [
				search.createColumn({ name: "assigned", label: "Assigned To" }),
			],
		});

		var resultSet = searchObj.run();

		return resultSet
			.getRange({
				start: 0,
				end: 3,
			})
			.map((result) => {
				return result.getValue(searchObj.columns[0]);
			});
	}

	function getRepBasedOffOfLastAssigned(
		repArr,
		repAssignedLastTask,
		scriptDeployment
	) {
		//When CSR -
		//Cycle through the list of CSR reps, Devorah gets 2 consecutive tasks, all other reps get just one per cycle.
		if (repAssignedLastTask == 9975) {
			//Devorah
			var repAssignedTwoTasksAgo = scriptDeployment.getValue(
				"custscript_spl_hb_rep_asgnd_two_tsks_ago"
			);

			if (repAssignedTwoTasksAgo != 9975) {
				//Devorah only got one task this cycle so assign the next task to her as well.
				return 9975;
			}
		}

		//Cycle through list of CSR/Offshore reps as regular
		var lastRepIndex = repArr.findIndex((rep) => rep == repAssignedLastTask);
		var nextRep;

		if (lastRepIndex == repArr.length - 1) {
			//Up to the last rep in the cycle -> go back to the beggining
			nextRep = repArr[0];
		} else {
			//Return next rep in the cycle
			nextRep = repArr[lastRepIndex + 1];
		}

		return nextRep;
	}

	function getRepBasedOffOfCaseRatio(repAssignedLastTask) {
		//Changed this from a ratio of 3(Sara):1(Devora) to 1:1
		if (repAssignedLastTask == 9975) {
			//Devorah
			return 12215; //Sarah
		} else if (repAssignedLastTask == 12215) {
			//Sarah
			return 9975; //Devorah
		} else {
			throw "Task not assigned to Sara or Devorah";
		}
	}

	function getRepBasedOffOfTaskRatio(repArr) {
		var repArr = getNewQuotesTasksRepArr();
		var devorahAssignedWithinLastThree = repArr.find((rep) => rep == "9975");

		return devorahAssignedWithinLastThree ? "12215" : "9975"; //3 (Sara) : 1 (Devorah)  ratio.
		//If Devorah was assigned a task within the last 3 tasks -> assign it to Sara, If the last 3 cases were assigned to Sara, then should be assigned to Devorah
	}

	return {
		getTaskRepArr: getTaskRepArr,
		getCaseRepArr: getCaseRepArr,
		getRepBasedOffOfLastAssigned: getRepBasedOffOfLastAssigned,
		getRepBasedOffOfCaseRatio: getRepBasedOffOfCaseRatio,
		getRepBasedOffOfTaskRatio: getRepBasedOffOfTaskRatio,
	};
});
