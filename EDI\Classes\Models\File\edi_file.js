/**
 * @description Abstract EDI File Class to be extended by Incoming and Outgoing classes
 *
 * @NApiVersion 2.1
 * <AUTHOR>
 */

define([
    "exports",
    "require",
    "N/log",
    "N/record",
    "../../../../Classes/vlmd_custom_error_object",
], (/** @type {any} */ exports, /** @type {any} */ require) => {
    const log = require("N/log");
    const record = require("N/record");
    /** @type {import("../../../../Classes/vlmd_custom_error_object").CustomErrorObject} */
    const CustomErrorObject = require("../../../../Classes/vlmd_custom_error_object");

    const DocumentType = {
        INCOMING_850: 1,
        INCOMING_855: 2,
        INCOMING_810: 3,
        INCOMING_856: 4,
        OUTGOING_850: 5,
        OUTGOING_810: 6,
        OUTGOING_855: 7,
        OUTGOING_ITEM_832: 8,
        OUTGOING_PRICE_832: 9,
        OUTGOING_856: 10,
        OUTGOING_997: 11,
    };

    /**
     * EDI File Class
     *
     * @class
     * @typedef {import("../../Interfaces/Models/File/edi_file").EDIFileInterface} EDIFileInterface
     * @typedef {import("../../Interfaces/Models/File/edi_file").EDITransactionData} EDITransactionData
     * @typedef {import("../../Interfaces/Models/Server/edi_server").EDIServerInterface} EDIServerInterface
     * @typedef {import("../../Interfaces/Models/Server/edi_server").EDIMFTServerInterface} EDIMFTServerInterface
     * 
     * @implements {EDIFileInterface}
     */
    class EDIFile {
        constructor() {
            /** @type {typeof CustomErrorObject} */
            this.customError = new CustomErrorObject();
            /** @type {string} */
            this.ediType = "";
            /** @type {any} */
            this.parser = null;
            /** @type {any} */
            this.processor = null;
            /** @type {EDIServerInterface | EDIMFTServerInterface} */
            this.server;
            /** @type {string} */
            this.type = "";
            /** @type {number} */
            this.typeId = 0;
        }

        /**
         * Reset the Custom Error Object
         *
         * @returns {void}
         */
        resetCustomError() {
            this.customError = new CustomErrorObject();
        }

        /**
         * Create a NetSuite EDI Transaction record
         *
         * @param {EDITransactionData} data Email object to log
         * @returns {void} 
         */
        createEDITransactionRecord(data) {

            //Check if missing values needed
            //Moving 5 - Outgoing 850s to here until control number subrecord functionality is added
            if (
                [
                    DocumentType.INCOMING_850,
                    DocumentType.OUTGOING_850,
                    DocumentType.OUTGOING_810,
                    DocumentType.OUTGOING_855,
                    DocumentType.OUTGOING_856
                ].includes(this.typeId)
                && (!data || !data.email)
            ) {
                log.error(
                    "Missing Parameter",
                    `data: ${data}, email: ${data.email}`
                );
            } else if (
                [
                    DocumentType.INCOMING_855,
                    DocumentType.INCOMING_810,
                    DocumentType.INCOMING_856
                ].includes(this.typeId)
                && (
                    !data
                    || !data.email
                    || !data.transactionControlNumber
                    || !data.purchaseOrderReferenceNumber
                )
            ) {
                log.error({
                    title: "Missing Parameter",
                    details: `data: ${data}, ` +
                        `email: ${data.email}, ` +
                        `transactionControlNumber: ${data.transactionControlNumber}, ` +
                        `purchaseOrderReferenceNumber: ${data.purchaseOrderReferenceNumber}`
                });
            }

            try {
                const ediTransactionRecord = record.create({
                    type: "customrecord_spl_edi_transaction",
                    isDynamic: true
                });

                //Set body fields
                ediTransactionRecord.setValue({
                    fieldId: "custrecord_spl_edi_document_type",
                    value: this.typeId,
                });

                ediTransactionRecord.setValue({
                    fieldId: "custrecord_spl_processing_status",
                    value: data.email.processingStatus,
                });

                ediTransactionRecord.setValue({
                    fieldId: "custrecord_spl_edi_email_subject",
                    value: data.email.subject,
                });

                ediTransactionRecord.setValue({
                    fieldId: "custrecord_spl_edi_email_message",
                    value: data.email.body,
                });

                ediTransactionRecord.setValue({
                    fieldId: "custrecord_spl_edi_email_recipient",
                    value: data.email.recipients[0],
                });

                if (data.email.cc) {
                    ediTransactionRecord.setValue({
                        fieldId: "custrecord_spl_edi_email_cc_recipient",
                        value: data.email.cc[0],
                    });
                }

                ediTransactionRecord.setValue({
                    fieldId: "custrecord_spl_edi_trnsctn_cntrl_nmbr",
                    value: data.transactionControlNumber,
                });

                ediTransactionRecord.setValue({
                    fieldId: "custrecord_spl_edi_po_ref_nmbr",
                    value: data.purchaseOrderReferenceNumber,
                });

                ediTransactionRecord.setValue({
                    fieldId: "custrecord_spl_edi_purchasing_software",
                    value: data.purchasingSoftwareId,
                });

                ediTransactionRecord.setValue({
                    fieldId: "custrecord_spl_edi_vendor",
                    value: data.vendorInternalId
                });

                const ediTransactionRecordId = ediTransactionRecord.save();

                if (!ediTransactionRecordId) {
                    throw "EDI transaction not saved to database.";
                }

                //Set EDI transaction record id on the document control number record
                if (
                    data.email.documentControlNumbers &&
                    data.email.documentControlNumbers.length > 0
                ) {
                    data.email.documentControlNumbers.forEach((controlId) => {
                        if (controlId && parseInt(controlId)) {
                            //Load customrecord_edi_dcn_doc_ctrl_num record and set value of ediTransaction record to link them.
                            const documentCtrlNumberRecObj = record.load({
                                type: "customrecord_edi_dcn_doc_ctrl_num",
                                id: controlId,
                            });

                            if (!documentCtrlNumberRecObj) {
                                throw `documentCtrlNumberRecObj not gotten for ${controlId}`;
                            }

                            documentCtrlNumberRecObj.setValue({
                                fieldId: "custrecord_edi_dcn_edi_tran_rec",
                                value: ediTransactionRecordId,
                            });

                            documentCtrlNumberRecObj.save();
                        }
                    });
                }
            } catch (e) {
                log.error({
                    title: "Error Pushing EDI Transaction to DB",
                    details: `Transaction Title: ${data.email.subject ?? ""} Error: ${e}`
                });
            }
        }
    }

    exports.EDIFile = EDIFile;
    exports.DocumentType = DocumentType;
});