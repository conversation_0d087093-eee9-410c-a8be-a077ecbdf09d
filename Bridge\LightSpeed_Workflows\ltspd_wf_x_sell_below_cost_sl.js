/**
 * @NApiVersion 2.1
 * @NScriptType Suitelet
 * <AUTHOR>
 * @description SL that gets triggered on "Ready for Payment" sale event in Lightspeed.
 *  Validates that sales price > purchase price.
 *  Prevents the sale from being saved if any items are below cost.
 */

define([
  "require",
  "N/query",
  "N/email",
  "N/format",
  "N/log",
  "../../Classes/vlmd_custom_error_object",
], function (require) {
  const query = require("N/query");
  const email = require("N/email");
  const format = require("N/format");
  const log = require("N/log");

  /** @type {import("../../Classes/vlmd_custom_error_object").CustomErrorObject} */

  const CustomErrorObject = require("../../Classes/vlmd_custom_error_object");
  const customErrorObject = new CustomErrorObject();

  return {
    onRequest: function (context) {
      var helperFunctions = (function () {
        function _getProductNameById(productId) {
          const sqlQuery = /*sql*/ `
          SELECT displayname FROM customrecord_in8_vend_ids vend
          LEFT OUTER JOIN item ON item.id = vend.custrecord_in8_vend_ids_item
          WHERE custrecord_in8_vend_ids_id = '${productId}'`;
          return query
            .runSuiteQL({
              query: sqlQuery,
            })
            .asMappedResults()[0].displayname;
        }

        function getItemsBelowCost(lineItemsArr) {
          return lineItemsArr
            .filter((lineItem) => {
              const lppPriceObj = lineItem.product.custom_fields.find(
                (field) => field.name === "lpp_price"
              );
              return (
                lppPriceObj &&
                parseFloat(lppPriceObj.string_value) >
                  parseFloat(lineItem.price)
              );
            })
            .map((lineItem) => {
              const lppPriceObj = lineItem.product.custom_fields.find(
                (field) => field.name === "lpp_price"
              );
              return {
                salePrice: lineItem.price,
                productId: lineItem.product_id,
                lppPrice: lppPriceObj ? lppPriceObj.string_value : null,
                productName: _getProductNameById(lineItem.product_id),
              };
            });
        }

        function sendEmail(errorText, username, storeString) {
          var currentDateTime = format.format({
            value: new Date(),
            type: format.Type.DATETIME,
            timezone: format.Timezone.AMERICA_NEW_YORK,
          });

          const timeParts = currentDateTime.split(" ");
          const currentTime = `${timeParts[1]} ${timeParts[2]}`;

          email.send({
            author: 223244, // Requests
            recipients: [1121, 46503, 43398, 3046, 314184], //Mr. Silberberg and Nina and Miri and Esther (temp) and Leah (temp)
            subject: `Sale Attempt Below Cost at${storeString} at ${currentTime}`,
            body: `The following items were attempted to be sold below cost by ${username} in ${storeString}: ${errorText}`,
          });
        }

        return {
          getItemsBelowCost,
          sendEmail,
        };
      })();

      if (context.request.method === "POST") {
        const json = JSON.parse(context.request.body);
        const username = json["user"]["username"];

        const orderStatus = json["sale"]["status"];
        if (orderStatus == "ONACCOUNT") return;

        //Splitting the store domain name into a normal string
        //thevineyardmadison > The Vineyard Madison
        const storeDomain = json["retailer"]["domain_prefix"];
        const regex = new RegExp(`(the|vineyard)`, "gi");
        const parts = storeDomain.split(regex);
        const storeString = parts
          .map((word) => {
            return word.charAt(0).toUpperCase() + word.slice(1);
          })
          .join(" ");

        // if (
        //   username == "<EMAIL>" ||
        //   username == "Chaya Kass"
        // ) {
        // }

        const lineItemsArr = json["sale"]["line_items"];

        try {
          const itemsBelowCost =
            helperFunctions.getItemsBelowCost(lineItemsArr);

          //No items with errors, break out of script
          if (!itemsBelowCost || itemsBelowCost.length <= 0) {
            return;
          }

          const errorText = itemsBelowCost.map((lineItem) => {
            return `\nProduct: ${lineItem.productName}, Cost Price: $${lineItem.lppPrice}`;
          });

          helperFunctions.sendEmail(errorText, username, storeString);

          var returnStatementObj = {
            actions: [
              {
                type: "stop",
                title: "Cannot sell items below cost",
                message: `Please update pricing for the items with a sales price below cost: ${errorText}`,
                dismiss_label: "OK",
              },
            ],
          };

          context.response.write(JSON.stringify(returnStatementObj));
        } catch (e) {
          customErrorObject.throwError({
            summaryText: `ERROR_IN_SL`,
            error: e,
          });
        }
      }
    },
  };
});
