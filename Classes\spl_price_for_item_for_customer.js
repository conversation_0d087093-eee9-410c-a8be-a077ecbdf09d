/**
 * @description Class containing functions to get the price for an item for a specific customer
 *
 * @NApiVersion 2.1
 * <AUTHOR>
 */

define(["N/record", "N/query", "N/log"], (record, query, log) => {
  /**
   * Price for Item for Customer Class
   *
   * @class
   */
  class PriceForItemForCustomer {
    /** @param {{[key:string]: any}} params */
    constructor(transaction) {
      /** @type {string[]} */
      this.warnings = [];

      /** @type {number} */
      this.itemId = transaction.getCurrentSublistValue({
        sublistId: "item",
        fieldId: "item",
      });

      /** @type {string} */
      this.itemName = transaction.getCurrentSublistText({
        sublistId: "item",
        fieldId: "item",
      });

      /** @type {number} */
      this.customerId = transaction.getValue("entity");

      /** @type {string} */
      this.customerName = transaction.getText("entity");

      /** @type {number} */
      this.transactionId = transaction.getValue("id");

      /** @type {string} */
      this.transactionName = transaction.getValue("tranid");

      /** @type {number} */
      this.quantity = transaction.getCurrentSublistText({
        sublistId: "item",
        fieldId: "quantity",
      });

      /** @type {number} */
      this.purchaserId;

      /** @type {decimal} */
      this.rate;

      /** @type {number} */
      this.priceLevelId;

      /** @type {string} */
      this.priceLevelName;

      /** @type {number} */
      this.priceGroupId;

      /** @type {string} */
      this.priceGroupName;
    }

    /**
     * Consolidate error logs for the Bill class
     *
     * @param {Object} args Parameters
     * @param {string} args.functionName Function identifier
     * @param {string} args.logMessage Error message to log
     * @returns {void}
     */
    addWarning({ functionName, logMessage }) {
      log.debug({
        title: functionName,
        details: logMessage,
      });

      this.warnings.push(logMessage);
    }

    displayNotifications() {
      if (this.warnings.length > 0) {
        alert(this.warnings.join(" \n"));
      }
    }

    /**
     * Set the value of the purchaser id for this customer
     */
    getPurchaserId() {
      try {
        let sqlQuery = /*SQL */ `
			 SELECT
			 	integration.custrecord_spl_prchsng_fclty purchaser_id,
				parent.id parent_id,
				customer.id customer_id,
			 FROM
				customer 
				LEFT JOIN
				   customer parent 
				   ON customer.parent = parent.id 
				LEFT JOIN
				   customrecord_vlmd_edi_integration integration 
				   ON (parent.custentity_spl_edi_integration_record = integration.id 
				   OR customer.custentity_spl_edi_integration_record = integration.id ) 
			 WHERE
				customer.id = ${this.customerId}`;

        let queryResults = query
          .runSuiteQL({
            query: sqlQuery,
          })
          .asMappedResults()?.[0];

        if (!queryResults) {
          this.addWarning({
            functionName: "getPurchaserId",
            logMessage: `No purchaser ID found for ${this.customerName}. No rate was set.`,
          });

          return false;
        }

        this.purchaserId =
          queryResults["purchaser_id"] ??
          queryResults["parent_id"] ??
          queryResults["customer_id"];
      } catch (/** @type {any} */ err) {
        let errorText = `Error getting purchaser id: ${err}`;
        log.error("PriceForItemForCustomer (getPurchaserId)", errorText);
      }
    }

    /**
     * Set the value of the price for this item for this purchaser ID
     */
    getPriceValues() {
      //TODO: test, see if come up with more than 1 price - specifically test individual etc.
      try {
        if (!this.purchaserId) {
          return false;
        }

        var itemPricingQuery = /*SQL*/ `
                SELECT
                    ip.price rate,
                    IP.level price_level_id,
                    BUILTIN.DF(IP.level) price_level_name,
                    i.pricinggroup price_group_id,
                    BUILTin.DF(pricinggroup) price_group_name,
                FROM
                    customerItemPricing AS IP 
                    JOIN
                    item i 
                    ON i.id = ip.item 
                WHERE
                    IP.customer = ${this.purchaserId} 
                    AND IP.item = ${this.itemId} 
                UNION
                SELECT
                    p.unitPrice rate,
                    GP.level price_level_id,
                    BUILTIN.DF( GP.level) price_level_name,
                    GP.GROUP price_group_id,
                    BUILTIN.DF( GP.GROUP) price_group_name,
                FROM
                    CustomerGroupPricing AS gp 
                    INNER JOIN
                    item AS i 
                    ON i.pricinggroup = gp.GROUP 
                    LEFT JOIN
                    pricing p 
                    ON p.pricelevel = gp.level 
                    AND p.item = i.id 
                WHERE
                    gp.customer = ${this.purchaserId}  
                    AND i.id = ${this.itemId}
				ORDER BY 
					price_level_id `;

        var resultIterator = query.runSuiteQL({
          query: itemPricingQuery,
        });

        if (resultIterator.results.length <= 0) {
          this.addWarning({
            functionName: "getPriceValues",
            logMessage: `No price found for ${this.itemName}. No rate was set.`,
          });

          return false;
        }

        if (resultIterator.results.length > 1) {
          this.addWarning({
            functionName: "getPriceValues",
            logMessage: `More than one rate returned for ${this.itemName}. The individual pricing was used.`,
          });
        }

        this.rate = resultIterator.results[0].values[0];
        this.priceLevelId = resultIterator.results[0].values[1];
        this.priceLevelName = resultIterator.results[0].values[2];
        this.priceGroupId = resultIterator.results[0].values[3];
        this.priceGroupName = resultIterator.results[0].values[4];
        this.amount = this.rate * this.quantity;
      } catch (/** @type {any} */ err) {
        let errorText = `Error getting price values: ${err}`;
        log.error("PriceForItemForCustomer (getPriceValues)", errorText);
      }
    }

    setPriceValuesBasedOnCustomerPricingCS(transaction) {
      try {
        log.audit(
          "Instance Data (setPriceValuesBasedOnCustomerPricingCS)",
          JSON.stringify(this)
        );

        transaction.setCurrentSublistValue({
          sublistId: "item",
          fieldId: "price",
          value: -1, //Set price level to custom
          ignoreFieldChange: true,
        });

        transaction.setCurrentSublistValue({
          sublistId: "item",
          fieldId: "custcol_rate_based_on_price_level",
          value: this.priceLevelId,
          ignoreFieldChange: true,
        });

        transaction.setCurrentSublistValue({
          sublistId: "item",
          fieldId: "rate",
          value: this.rate,
          ignoreFieldChange: true,
        });

        transaction.setCurrentSublistValue({
          sublistId: "item",
          fieldId: "amount",
          value: this.amount,
          ignoreFieldChange: true,
        });
      } catch (/** @type {any} */ err) {
        let errorText = `Error setting price values: ${err}\nJSON: ${JSON.stringify(
          this
        )}`;

        log.error(
          "PriceForItemForCustomer (setPriceValuesBasedOnCustomerPricingCS)",
          errorText
        );
      }
    }
  }

  return PriceForItemForCustomer;
});
