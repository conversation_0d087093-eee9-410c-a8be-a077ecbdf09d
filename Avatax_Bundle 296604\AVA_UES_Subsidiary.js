/******************************************************************************************************
	Script Name - 	AVA_UES_Subsidiary.js
	Company - 		Avalara Technologies Pvt Ltd.
******************************************************************************************************/

/**
 *@NApiVersion 2.0
 *@NScriptType UserEventScript
*/

define(['N/record', 'N/search'],
	function(record, search){
		function AVA_SubsidiaryAfterSubmit(context){
			var rec, AVA_Flag = 'F';
			var nRecord = context.newRecord;
			var active = nRecord.getValue({
				fieldId: 'isinactive'
			});
			
			if(active == false){
				if(context.type == context.UserEventType.CREATE){
					rec = record.create({
						type: 'customrecord_avasubsidiaries'
					});
					rec.setValue({
						fieldId: 'custrecord_ava_iscompanyaddr',
						value: true
					});
					AVA_Flag = 'T';
				}
				else{
					var searchRecord = search.create({
						type: 'customrecord_avasubsidiaries',
						filters: ['custrecord_ava_subsidiary', 'anyof', nRecord.id]
					});
					var searchresult = searchRecord.run();
					searchresult = searchresult.getRange({
						start: 0,
						end: 5
					});
					
					if(context.type == context.UserEventType.EDIT){
						if (searchresult == null || searchresult.length == 0){
							rec = record.create({
								type: 'customrecord_avasubsidiaries'
							});
							rec.setValue({
								fieldId: 'custrecord_ava_iscompanyaddr',
								value: true
							});
							AVA_Flag = 'T';
						}
						else{
							rec = record.load({
								type: 'customrecord_avasubsidiaries',
								id: searchresult[0].id
							});
							AVA_Flag = 'T';
						}
					}
					else if(context.type == context.UserEventType.DELETE && searchresult != null && searchresult.length > 0){
						record.delete({
							type: 'customrecord_avasubsidiaries',
							id: searchresult[0].id
						});
					}
				}
				
				if(AVA_Flag == 'T'){
					rec.setValue({
						fieldId: 'custrecord_ava_subsidiary',
						value: nRecord.id
					});
					rec.setValue({
						fieldId: 'custrecord_ava_companyaddr',
						value: nRecord.getValue({
							fieldId: 'mainaddress_text'
						})
					});
					rec.setValue({ // Fix for CONNECT-5757
						fieldId: 'custrecord_ava_shipaddr',
						value: nRecord.getValue({
							fieldId: 'shippingaddress_text'
						})
					});
					rec.setValue({
						fieldId: 'custrecord_ava_subname',
						value: nRecord.getValue({
							fieldId: 'name'
						})
					});
					
					var nexuses = '';
					var nexusCount = nRecord.getLineCount({
					    sublistId: 'nexus'
					});
					for(var i = 0; i < nexusCount; i++){
						var nexus =  nRecord.getSublistValue({
						    sublistId: 'nexus',
						    fieldId: 'country',
						    line: i
						});
						nexuses += nexus + ',';
					}
					rec.setValue({
						fieldId: 'custrecord_ava_subnexuses',
						value: nexuses
					});
					rec.save({
					});
				}
			}
		}
		
		return{
			afterSubmit: AVA_SubsidiaryAfterSubmit
        };
	}
);