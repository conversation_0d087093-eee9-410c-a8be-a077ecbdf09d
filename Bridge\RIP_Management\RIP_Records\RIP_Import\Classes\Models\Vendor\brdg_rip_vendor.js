/**
 * @description Bridge RIP Vendor class
 *
 * @NApiVersion 2.1
 * <AUTHOR>
 */

define([
    "exports",
    "require"
  ], function (/** @type {any} */ exports, /** @type {any} */ require) {
    /**
     * Bridge RIP Vendor Class
     *
     * @typedef {import("../../../../../../../Classes/vlmd_custom_error_object").CustomErrorObject} CustomErrorObject
     */
    class BridgeRIPVendor {
      /** @param {{[key:string]: any}} props Constructor params */
      constructor(props){
        /** @type {CustomErrorObject} */
        this.customErrorObject = props.customErrorObject;
      }
  
      /**
       * Split the row string from the CSV file and attach the line index
       *
       * @param {import("N/file").File} ripFile NetSuite File
       * @returns {any[]} Row strings split by a delimiter
       */
      splitLines(ripFile) {
        return [];
      }

      /**
       * Create an object from the line extracted from the CSV file
       *
       * @param {any[]} fields Array of column values
       * @returns {{[key:string]: any}} Key-value pairs of column values
       */
      parseLine(fields) {
        return {};
      }

      /**
       * Merge RIP Levels into a single row
       *
       * @param {string[]} levels RIP levels in JSON String format
       * @returns {any[]} Merged levels 
       */
      mergeLevels(levels) {
        return [];
      }
    }
  
    exports.BridgeRIPVendor = BridgeRIPVendor;
  });