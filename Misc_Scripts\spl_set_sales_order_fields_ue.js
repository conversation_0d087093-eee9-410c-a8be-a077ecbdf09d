/**
 * @description Sets the email address and the warehouse timestamp on Sales Orders
 *
 * </br><b>Deployed On:</b> SalesOrder
 * </br><b>Execution Context:</b> ALL
 * </br><b>Event Type/Mode:</b> beforeLoad- <PERSON><PERSON> not CREATE, beforeSubmit- <PERSON><PERSON> not CREATE
 * </br><b>Entry Points:</b>  beforeLoad, beforeSubmit
 *
 * @NApiVersion 2.1
 * @NScriptType UserEventScript
 * @NAmdConfig /SuiteScripts/config.json
 *
 * <AUTHOR>
 * @module spl_set_sales_order_fields_ue
 */

define([
	"require",
	"./../Libraries/Misc_Libs/spl_get_order_email_address_lib",
	"./../Classes/vlmd_custom_error_object",
	"N/query",
], (/** @type {any} */ require) => {
	const getOrderEmailAddress = require("./../Libraries/Misc_Libs/spl_get_order_email_address_lib");
	/** @type {import("./../Classes/vlmd_custom_error_object").CustomErrorObject} */
	const CustomErrorObject = require("./../Classes/vlmd_custom_error_object");
	const customErrorObject = new CustomErrorObject();
	const query = require("N/query");

	const helperFunctions = (function () {
		function getEmailString(customerId) {
			const emailsArr =
				getOrderEmailAddress.getSalesOrderEmailAddress(customerId);

			return emailsArr.join(";");
		}

		function checkIfCommittedInventory(recordId) {
			const sqlQuery = `select SUM(commitinventory) as qtycommitted from transactionline where transaction = ?`;

			const resultIterator = query
				.runSuiteQL({
					query: sqlQuery,
					params: [recordId],
				})
				.asMappedResults()[0].qtycommitted;
			return resultIterator > 0;
		}
		return {
			getEmailString,
			checkIfCommittedInventory,
		};
	})();

	return {
		beforeLoad: (context) => {
			try {
				if (context.type !== context.UserEventType.CREATE) {
					const salesOrder = context.newRecord;

					if (salesOrder.getValue("subsidiary") != 1) {
						//Supplyline
						return;
					}

					const customerId = salesOrder.getValue("entity");

					if (!customerId) {
						throw customErrorObject.updateError({
							errorType: customErrorObject.ErrorTypes.NO_VALUE_RETURNED,
							summary: `NO_CUSTOMER_ID_FOUND`,
							details: `Cannot access customer id on the sales order! Sales Order: ${salesOrder.id}`,
						});
					}

					const emailsString = helperFunctions.getEmailString(customerId);

					if (emailsString) {
						salesOrder.setValue("email", emailsString);
					}
				}
			} catch (error) {
				customErrorObject.throwError({
					summaryText: `ERROR_UPDATING_SALES_ORDER`,
					error: error,
					recordId: newRecord.id,
					recordType: `SALES_ORDER`,
					errorWillBeGrouped: true,
				});
			}
		},

		beforeSubmit: (context) => {
			const { oldRecord, newRecord } = context;

			if (newRecord.getValue("subsidiary") != 1) {
				//Supplyline
				return;
			}

			try {
				const oldStatus = oldRecord ? oldRecord.getValue("orderstatus") : null;
				const newStatus = newRecord.getValue("orderstatus");

				if ((!oldStatus || !newStatus) && context.type !== "create") {
					throw customErrorObject.updateError({
						errorType: customErrorObject.ErrorTypes.NO_VALUE_RETURNED,
						summary: `NO_STATUS_VALUES_FOUND`,
						details: `Missing old or new order status! Old Status: ${oldStatus}, New Status: ${newStatus}`,
					});
				}

				const customerId = newRecord.getValue("entity");

				if (!customerId) {
					throw customErrorObject.updateError({
						errorType: customErrorObject.ErrorTypes.NO_VALUE_RETURNED,
						summary: `NO_CUSTOMER_ID_FOUND`,
						details: `Cannot access customer id on the sales order! Sales Order: ${salesOrder.id}`,
					});
				}

				const emailsString = helperFunctions.getEmailString(customerId);

				if (!emailsString) {
					throw customErrorObject.updateError({
						errorType: customErrorObject.ErrorTypes.NO_VALUE_RETURNED,
						summary: `NO_EMAIL_RETURNED`,
						details: `No email address was returned. Sales Order: ${salesOrder.id}`,
					});
				}

				newRecord.setValue("email", emailsString);

				let hasCommittedInventory =
					context.type !== "create"
						? helperFunctions.checkIfCommittedInventory(newRecord.id)
						: false;

				//If this status just got updated to be "Pending Fulfillment" and there is inventory
				if (
					oldStatus != newStatus &&
					newStatus == "B" &&
					hasCommittedInventory
				) {
					newRecord.setValue(
						"custbody_pending_fulfillment_timestamp",
						new Date()
					);
				}
			} catch (error) {
				customErrorObject.throwError({
					summaryText: `ERROR_UPDATING_SALES_ORDER`,
					error: error,
					recordId: newRecord.id,
					recordType: `SALES_ORDER`,
					errorWillBeGrouped: true,
				});
			}
		},
	};
});
