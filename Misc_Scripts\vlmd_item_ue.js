/**
 * @description Sets and validates values:
 * - <b>Set item name: on CREATE, COPY for Supplyline and Valmar items </b>
 *
 * </br><b>Deployed On: Items </b>
 * </br><b>Execution Context: ALL </b>
 * </br><b>Event Type/Mode: ALL </b>
 * </br><b>Entry Points: BeforeSubmit </b>
 *
 * @NApiVersion 2.1
 * @NScriptType UserEventScript
 * @NAmdConfig /SuiteScripts/config.json
 *
 * <AUTHOR>
 * @module vlmd_item_ue
 */

define([
	"require",
	"../Classes/spl_item_record",
	"../Classes/vlmr_item_record",
	"../Classes/vlmd_custom_error_object",
	"N/log",
	"N/record",
], (/** @type {any} */ require) => {
	/** @type {import("../Classes/spl_item_record").SplItemRecord} */
	const SplItemRecord = require("../Classes/spl_item_record");

	/** @type {import("../Classes/vlmr_item_record").VlmrItemRecord} */
	const VlmrItemRecord = require("../Classes/vlmr_item_record");

	/** @type {import("../Classes/vlmd_custom_error_object").CustomErrorObject} */
	const CustomErrorObject = require("../Classes/vlmd_custom_error_object");

	const log = require("N/log");
	const record = require("N/record");

	const customErrorObject = new CustomErrorObject();

	const helperFunctions = (function () {
		return {
			/**
			 *
			 *Generates item name for SPL or VLMR items - if either new or top level product category was changed.
			 * @param {object} context - The script context
			 * @param {string} subsidiaryType - "spl" or "vlmr"
			 * @returns {void}
			 */
			generateItemName(context, subsidiaryType) {
				try {
					const newCategory = context.newRecord.getValue("class");
					if (!newCategory) {
						return; //Don't regenerate item name if category was removed
					}

					// Create the appropriate item record based on subsidiary type
					let itemRecord;
					if (subsidiaryType === "spl") {
						itemRecord = new SplItemRecord(context.newRecord);
					} else if (subsidiaryType === "vlmr") {
						itemRecord = new VlmrItemRecord(context.newRecord);
					} else {
						return; // Exit if not a supported subsidiary type
					}

					const oldCategory = context.oldRecord
						? context.oldRecord.getValue("class")
						: null;

					const originalCategoryParent = oldCategory
						? itemRecord.getParentCategoryId(oldCategory)
						: null; //If adding a category (was previously empty), set the previous category parent to be null

					const newCategoryParent = itemRecord.getParentCategoryId(newCategory);

					if (
						itemRecord.dontAutomateItemName ||
						newCategoryParent == originalCategoryParent //Top Level Category was not changed
					) {
						return;
					}

					const { parentCategoryId, itemNumber, itemName } =
					itemRecord.generateItemName();
					if (!parentCategoryId) {
						throw customErrorObject.updateError({
							errorType: customErrorObject.ErrorTypes.MISSING_VALUE,
							summary: `MISSING_PRODUCT_CATEGORY`,
							details: `No parent product category found.`,
						});
					}

					// For Valmar items, add "V" prefix if not already present
					let finalItemName = itemName;
					if (subsidiaryType === "vlmr" && !finalItemName.startsWith("V")) {
						finalItemName = "V" + finalItemName;
					}

					context.newRecord.setValue({
						fieldId: "itemid",
						value: finalItemName,
					});
					try {
						record.submitFields({
							type: record.Type.CLASSIFICATION,
							id: parentCategoryId,
							values: {
								custrecord_last_item_name: itemNumber,
							},
						});
					} catch (e) {
						customErrorObject.throwError({
							summaryText: `UPDATE_CATEGORY_LAST_ITEM_NAME`,
							error: e,
							errorWillBeGrouped: true,
						});
					}
				} catch (e) {
					customErrorObject.throwError({
						summaryText: `GENERATE_ITEM_NAME`,
						error: e,
						errorWillBeGrouped: true,
					});
				}
			},
		};
	})();

	return {
		/**
		 * @param {import("N/types").EntryPoints.UserEvent.beforeSubmitContext} context Before submit script context
		 * @returns {void}
		 */
		beforeSubmit: (context) => {
			try {
				const subsidiaryId = context.newRecord.getValue("subsidiary");
				
				// Check for Supplyline (ID: 1)
				const hasSplSubsidiary = (Array.isArray(subsidiaryId) && subsidiaryId.includes("1")) ||
					(typeof subsidiaryId == "string" && subsidiaryId == "1");
				
				// Check for Valmar (ID: 2)
				const hasVlmrSubsidiary = (Array.isArray(subsidiaryId) && subsidiaryId.includes("2")) ||
					(typeof subsidiaryId == "string" && subsidiaryId == "2");
				
				// Determine subsidiary type - prioritize Supplyline if both are present
				let subsidiaryType = null;
				if (hasSplSubsidiary) {
					subsidiaryType = "spl";
				} else if (hasVlmrSubsidiary) {
					subsidiaryType = "vlmr";
				} else {
					return; // Exit if not a supported subsidiary
				}
				
				// Process item name generation for both subsidiary types
				if (
					context.type == "create" || //Copy mode is logged as create
					(context.type == "edit" &&
						context.oldRecord &&
						(context.newRecord.getValue("class") !=
							context.oldRecord.getValue("class") ||
							context.newRecord.getValue("subsidiary") !=
								context.oldRecord.getValue("subsidiary")))
				) {
					helperFunctions.generateItemName(context, subsidiaryType);
				}
			} catch (e) {
				customErrorObject.throwError({
					summaryText: `BEFORE_SUBMIT`,
					error: e,
					recordId: context.newRecord.id,
					recordName: context.newRecord.displayname,
					recordType: "Item",
					errorWillBeGrouped: true,
				});
			}
		},
	};
});
