/**
 * @description Class containing functions specific to processing NetSuite records into an Outgoing 856 EDI File
 * @NApiVersion 2.1
 * <AUTHOR> <<EMAIL>>
 */

define([
    "exports",
    "require",
    "N/file",
    "N/log",
    "../../Decorators/edi_processor"
], (/** @type {any} */ exports, /** @type {any} */ require) => {
    const file = require("N/file");
    const log = require("N/log");
    const { EDIProcessor } = require("../../Decorators/edi_processor"); 

    /**
     * 856 Processor Class
     * 
     * @typedef {import("../../Interfaces/Decorators/856/edi_856_processor").EDI856ProcessorInterface} EDI856ProcessorInterface
     * @typedef {import("../../Interfaces/Models/File/edi_outgoing").SuiteQLObjectReference} SuiteQLObjectReference
     * @implements {EDI856ProcessorInterface}
     * @class
     */
    class EDI856Processor extends EDIProcessor {
        constructor(){
            super();
        }

        /**
         * Return the query string or Search object to load the records
         *
         * @param {{[key:string]: any}} [params] Parameters
         * @returns {SuiteQLObjectReference} Query string or Search object to retrieve transaction records
         */
        load(params) {
            log.error({ title: "EDI 856 Processor", details: "Error: Parent load function needs to be implemented by child class." });
            return { type: "", query: "", params: [] };
        }

        /**
         * Process the invoice to generate the string to store on the EDI File
         *
         * @returns {string} EDI File as string
         */
        process() {
            log.error({ title: "EDI 856 Processor", details: "Error: Parent process function needs to be implemented by child class." });
            return "";
        }

        /**
         * Create the EDI File
         *
         * @param {{[key:string]: any}} [params] Parameters
         * @returns {file.File|null} EDI File
         */
        create(params) {
            log.error({ title: "EDI 856 Processor", details: "Error: Parent process function needs to be implemented by child class." });
            return null;
        }

        /**
         * Mark the transaction as complete to avoid reprocessing
         *
         * @returns {number} Document Control Number record ID
         */
        complete() {
            log.error({ title: "EDI 856 Processor", details: "Error: Parent complete function needs to be implemented by child class." });

            return 0;
        }
    }

    exports.EDI856Processor = EDI856Processor;
});