/**
 * @NApiVersion 2.1
 */
define([
	"CreatePurchaseOrderObjLib",
	"WriteOutgoing850Lib",
	"ProcessOutgoing850EmailLib",
], function (createPurchaseOrderObjLib, writePoLib, processEnd) {
	function process850(purchaseOrderId, vendorData, partnerValues) {
		var helperFunctions = (function () {
			function _processFail(logMessage) {
				processedSuccesfully = false;
				processingLog.push(logMessage);
				return false;
			}
			function getPurchaseOrder() {
				try {
					return record.load({
						type: record.Type.PURCHASE_ORDER,
						id: id,
					});
				} catch (error) {
					processFail(
						"Purchase order record not loaded for internal ID " + id,
						false
					);
				}
			}
			return { getPurchaseOrder: getPurchaseOrder };
		})();
		var processedSuccesfully = true;
		var processingLog = [];
		var NSPurchaseOrder = helperFunctions.getPurchaseOrder();
		var purchaseOrderNumber = NSPurchaseOrder.getText("tranid");
		var purchaseOrder = createPurchaseOrderObjLib.getPurchaseOrderObj();
		var ediFile = writePurchaseOrderAsEdiFile();
		if (processedSuccesfully && ediFile.success) {
			var fileToUpload = createFile();
		}
		if (processedSuccesfully) {
			uploadFileToMedline();
			uploadFileToSupplyline();
		}
		if (processedSuccesfully) {
			addControlNumberToPurchaseOrder();
			transactionsProcessedSuccessfully.push(purchaseOrderNumber);
		}
		function writePurchaseOrderAsEdiFile() {
			try {
				return writePoLib.getPurchaseOrderAsEDI(partnerValues, purchaseOrder);
			} catch (error) {
				processFail(
					"EDI file not gotten for " + purchaseOrderNumber + ".",
					false
				);
			}
		}
		function createFile() {
			try {
				return file.create({
					name: purchaseOrder.controlNumber + ".850",
					fileType: file.Type.PLAINTEXT,
					contents: ediFile.value,
				});
			} catch (error) {
				processFail(
					"File to upload not created for purchase order " +
						purchaseOrderNumber +
						". Error: " +
						error,
					false
				);
			}
		}
		function uploadFileToMedline() {
			try {
				medlineConnection.upload({
					file: fileToUpload,
					replaceExisting: true,
				});
			} catch (error) {
				processFail(
					"File not uploaded to Medline for " +
						purchaseOrderNumber +
						". Error: " +
						error,
					false
				);
			}
		}
		function uploadFileToSupplyline() {
			try {
				supplylineConnection.upload({
					file: fileToUpload,
					replaceExisting: true,
				});
			} catch (error) {
				processFail(
					"File not uploaded to Supplyline for " +
						purchaseOrderNumber +
						". Error: " +
						error,
					false
				);
			}
		}
		function addControlNumberToPurchaseOrder() {
			try {
				NSPurchaseOrder.setValue(
					"custbody_spl_po_edi_trans_cntrl_number",
					controlNumber
				);
				NSPurchaseOrder.save({
					enableSourcing: true,
					ignoreMandatoryFields: true,
				});
				var controlNumberSaved = NSPurchaseOrder.getValue(
					"custbody_spl_edi_trans_cntrl_num"
				);
				if (!controlNumberSaved) {
					return false;
				}
			} catch (error) {
				processFail(
					"EDI transaction control number not saved for " +
						purchaseOrderNumber +
						".",
					false
				);
			}
		}
		function processEnd() {
			var resultsData = setResultsDataAndSendEmail();
			logEndResult();
			if (vendorData.pushEmailToDB) {
				pushEdiEmailInfoToDBLib.pushEdiEmailInfoToDB(vendorData, resultsData);
			}
			function setResultsDataAndSendEmail() {
				var data = {};
				var errorText = "";
				processingLog.forEach(function (error) {
					errorText += error + "\n\n";
				});
				var transactionsProcessed = "";
				transactionsProcessedSuccessfully.forEach(function (transaction) {
					transactionsProcessed += transaction + "\n";
				});
				if (processedSuccesfully) {
					processCreatedSuccessfully();
				} else {
					if (transactionsProcessedSuccessfully.length > 0) {
						processCreatedWithErrors();
						sendEmail();
					} else {
						processFailedData();
						sendEmail();
					}
				}
				return data;
				function processCreatedSuccessfully() {
					data.subject =
						"Success: Medline EDI Purchase Orders Processed Successfully";
					data.recipients = ["<EMAIL>"];
					data.body =
						"The following Medline purchase orders were processed successfully via EDI:\n\n" +
						transactionsProcessed;
					data.logTitle = "Processed Successfully";
					data.processingStatus = 1; //Processed With No Errors
				}
				function processCreatedWithErrors() {
					data.subject =
						"Errors: Please Review and Correct Medline EDI Purchase Orders";
					data.recipients = ["<EMAIL>"];
					data.cc = ["<EMAIL>"];
					data.body =
						"The following Medline purchase orders were processed successfully via EDI:\n\n" +
						transactionsProcessed +
						"Please review the errors below.\n\n" +
						errorText;
					data.logTitle = "Created with Errors";
					data.processingStatus = 2; //Processed With Errors
				}
				function processFailedData() {
					data.subject =
						"Failure: Medline EDI purchase orders not processed successfully.";
					data.recipients = ["<EMAIL>"];
					data.cc = ["<EMAIL>"];
					data.body = "Please review the errors below.\n\n" + errorText;
					data.logTitle = "Purchased Orders Not Processed";
					data.processingStatus = 4; //Document Not Created
				}
				function sendEmail(salesOrderId) {
					try {
						email.send({
							author: 262579, //EDI
							recipients: data.recipients,
							cc: data.cc,
							subject: data.subject,
							body: data.body,
						});
					} catch (error) {
						throw "Medline EDI Purchase Order email not sent.";
					}
				}
			}
			function logEndResult() {
				log.debug({
					title: resultsData.logTitle,
					details: resultsData.logDetails,
				});
			}
		}
		processEnd();
		var processingLog = [];
		var transactionsProcessedSuccessfully = [];
		var processedSuccesfully = true;
		var errorLog = [];
		var processedSuccessfully = true;
		try {
			var helperFunctions = (function () {
				function _processFail(logMessage) {
					processedSuccessfully = false;
					throw logMessage;
				}
				return {};
			})();
			var transactionId = transactionObj.transactionId,
				transactionType = transactionObj.transactionType;
			var customerName = customer.customerName,
				customerId = customer.customerId,
				accountNumber = customer.accountNumber;
			var purchasingSoftware = dataObj.purchasingSoftware,
				isInvoice = dataObj.isInvoice;
		} catch (e) {
			processedSuccessfully = false;
			errorLog.push("".concat(e));
		}
		return { processedSuccessfully: processedSuccessfully, errorLog: errorLog };
	}
	return {
		process850: process850,
	};
});
