import {Query, Result} from "N/query"

export interface ConstructorParams {
	queryId?: string;
	queryString?: string;
	columns?: string[];
	threshold?: number
	description: string;
}

export type QueryResult {
	results: Result[];
	columns: {label:string}[];
};

export interface QueryEmail {
	new(constructorParams): QueryEmail;
	queryId: string;
	queryString?: string;
	queryObjectFromId: Query|null;
	queryResult: QueryResult;
	threshold: number;
	generateQueryResults: () => QueryResult;
	buildResultHtml: () => string;
	sendResults: ({ /** @type {number[]} */ recipients, /** @type {string} */ subject }) => void;
}