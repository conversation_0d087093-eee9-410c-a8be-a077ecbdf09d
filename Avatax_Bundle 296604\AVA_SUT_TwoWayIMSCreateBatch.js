/******************************************************************************************************
	Script Name - AVA_SUT_TwoWayIMSCreateBatch.js
	Company -     Avalara Technologies Pvt Ltd.
******************************************************************************************************/

/**
 *@NApiVersion 2.0
 *@NScriptType Suitelet
 */

define(['N/ui/serverWidget', 'N/url', 'N/search', 'N/task', 'N/record', 'N/redirect', './utility/AVA_Library'],
	function(ui, url, search, task, record, redirect, ava_library){
		function onRequest(context){
			try{
				var checkServiceSecurity = ava_library.mainFunction('AVA_CheckService', 'TaxSvc');
				if(checkServiceSecurity == 0){
					checkServiceSecurity = ava_library.mainFunction('AVA_CheckSecurity', 34);
				}

				if(checkServiceSecurity == 0){
					if(context.request.method === 'GET'){
						var searchRecord = search.create({
							type: 'customrecord_avaconfig',
							columns: ['custrecord_ava_additionalinfo3', 'custrecord_ava_configflag']
						});
						var searchresult = searchRecord.run();
						searchresult = searchresult.getRange({
							start: 0,
							end: 5
						});
						
						if(searchresult != null && searchresult.length > 0){
							var additionalInfo3 = searchresult[0].getValue('custrecord_ava_additionalinfo3');
							var configFlag = searchresult[0].getValue('custrecord_ava_configflag');
							
							if(additionalInfo3 != null && additionalInfo3.length > 0 && configFlag == true){
								var avaIMSForm = ui.createForm({
									title: 'Create Batch'
								});
								avaIMSForm.clientScriptModulePath = './AVA_CLI_TwoWayIMSCreateBatch.js';
								addFormFields(avaIMSForm);
								avaIMSForm.addSubmitButton({
									label: 'Submit'
								});
								avaIMSForm.addResetButton({
									label: 'Reset'
								});
								avaIMSForm.addPageLink({
									title: 'View Batch',
									type: ui.FormPageLinkType.CROSSLINK,
									url: url.resolveScript({
										scriptId: 'customscript_ava_twowayimsviewbatch_suit',
										deploymentId: 'customdeploy_ava_twowayimsviewbatch_suit'
									})
								});
								context.response.writePage({
									pageObject: avaIMSForm
								});
							}
							else{
								redirect.toSuitelet({
									scriptId: 'customscript_avaconfig_wizard',
									deploymentId: 'customdeploy_ava_configurewizard'
								});
							}
						}
						else{
							redirect.toSuitelet({
								scriptId: 'customscript_avaconfig_wizard',
								deploymentId: 'customdeploy_ava_configurewizard'
							});
						}
					}
					else{
						var imsBatchId = createTwoWayIMSBatchRecord(context);
						if(imsBatchId){
							var imsBatchData = {
								batchname: context.request.parameters.ava_batchname,
								subsidiary: context.request.parameters.ava_subsidiary,
								startdate: context.request.parameters.ava_startddate,
								enddate: context.request.parameters.ava_enddate,
								newbatchid: imsBatchId
							};
							
							var callMapReduce = task.create({
								taskType: task.TaskType.MAP_REDUCE
							});
							callMapReduce.scriptId = 'customscript_ava_twowayitemmastersyn_map';
							callMapReduce.params = {
								custscript_ava_twowayimsbatchdetails: imsBatchData
							};
							callMapReduce.submit();
							
							var param = new Array();
							param['batchid'] = imsBatchId;
							context.response.sendRedirect("SUITELET", "customscript_ava_twowayimsviewbatch_suit", "customdeploy_ava_twowayimsviewbatch_suit", false, param);
						}
					}
				}
				else{
					context.response.write({
						output: checkServiceSecurity
					});
				}
			}
			catch(e){
				log.error('onRequest', 'ERROR NAME = ' + e.name + ', ERROR TYPE = ' + e.type + ', ERROR MESSAGE = ' + e.message);
			}
		}

		function addFormFields(avaIMSForm){
			try{
				avaIMSForm.addFieldGroup({
					id: 'ava_batchdata',
					label: '<b>Batch Information</b>'
				});
				var avaBatchName = avaIMSForm.addField({
					id: 'ava_batchname',
					label: 'Batch Name',
					type: ui.FieldType.TEXT,
					container: 'ava_batchdata'
				});
				avaBatchName.isMandatory = true;
				avaBatchName.updateDisplaySize({
					width: 40,
					height: 0
				});
				var avaSubsidiary = avaIMSForm.addField({
					id: 'ava_subsidiary',
					label: 'Subsidiary',
					type: ui.FieldType.SELECT,
					source: 'subsidiary',
					container: 'ava_batchdata'
				});
				avaSubsidiary.isMandatory = true;
				avaIMSForm.addFieldGroup({
					id: 'ava_createddate',
					label: '<b>Item Created Date</b>'
				});
				var avaStartDate = avaIMSForm.addField({
					id: 'ava_startddate',
					label: 'Start Date',
					type: ui.FieldType.DATE,
					container: 'ava_createddate'
				});
				avaStartDate.isMandatory = true;
				var avaEndDate = avaIMSForm.addField({
					id: 'ava_enddate',
					label: 'End Date',
					type: ui.FieldType.DATE,
					container: 'ava_createddate'
				});
				avaEndDate.isMandatory = true;
			}
			catch(e){
				log.error('addFormFields', 'ERROR NAME = ' + e.name + ', ERROR TYPE = ' + e.type + ', ERROR MESSAGE = ' + e.message);
			}
		}
		
		function createTwoWayIMSBatchRecord(context){
			try{
				var imsBatchObj = record.create({
					type: 'customrecord_avatwowayimsbatch'
				});
				imsBatchObj.setValue({
					fieldId: 'name',
					value: context.request.parameters.ava_batchname
				});
				imsBatchObj.setValue({
					fieldId: 'custrecord_ava_twowayimsbatchname',
					value: context.request.parameters.ava_batchname
				});
				imsBatchObj.setValue({
					fieldId: 'custrecord_ava_twowayimssubsidiary',
					value: context.request.parameters.ava_subsidiary
				});
				imsBatchObj.setValue({
					fieldId: 'custrecord_ava_twowayimsstartdate',
					value: ava_library.AVA_FormatDate(context.request.parameters.ava_startddate)
				});
				imsBatchObj.setValue({
					fieldId: 'custrecord_ava_twowayimsenddate',
					value: ava_library.AVA_FormatDate(context.request.parameters.ava_enddate)
				});
				imsBatchObj.setValue({
					fieldId: 'custrecord_ava_twowayimsstatus',
					value: 'In Queue'
				});
				var imsBatchId = imsBatchObj.save({
					enableSourcing: true,
					ignoreMandatoryFields: true
				});
				return imsBatchId;
			}
			catch(e){
				log.error('createTwoWayIMSBatchRecord', 'ERROR NAME = ' + e.name + ', ERROR TYPE = ' + e.type + ', ERROR MESSAGE = ' + e.message);
			}
		}
		
		return{
			onRequest: onRequest
		};
	}
);