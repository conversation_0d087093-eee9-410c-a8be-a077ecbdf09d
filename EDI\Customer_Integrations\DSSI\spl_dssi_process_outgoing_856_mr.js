/**
 * @NApiVersion 2.1
 * @NScriptType MapReduceScript
 * @NAmdConfig /SuiteScripts/config.json
 */

define([
	"require",
	"N/log",
	"N/error",
	"GetEdiIntegrationsLib",
	"Get856InternalIdsLib",
	"ProcessOutgoing856Lib",
	"GetEdiPartnerValuesLib",
	"ProcessOutgoingEdiFileEmailLib",
	"PushEdiEmailInfoToDBLib",
	"../../../Classes/vlmd_custom_error_object",
	"../../../Classes/vlmd_mr_summary_handling",	
], function (
	require,
	log,
	error,
	getEdiIntegrationsLib,
	getItemFulfillmentInternalIdsLib,
	process856Lib,
	getEdiPartnerValuesLib,
	processOutgoingEdiFileEmailLib,
	pushEdiEmailInfoToDBLib
) {
	/** @type {import("../../../Classes/vlmd_custom_error_object").CustomErrorObject} */
	const CustomErrorObject = require("../../../Classes/vlmd_custom_error_object");

	const noTransactionsToProcess = [];

	const dataObj = {
		prodGuidBool: true,
		prodDirectoryBool: true,
		decodeContent: true,
		prodGUID: `eddc4a665f15459db46c53a9df589a60`,
		sandboxGUID: `********************************`,
		testDirectory: "",
		documentType: "Advance Shipment Notice",
		documentTypeId: 10,
		purchasingSoftware: "DSSI",
		purchasingSoftwareId: 2,
		pushEmailToDB: true,
	};

	function getInputData() {
		const customErrorObject = new CustomErrorObject();
		const transactionObjArr = [];

		try {
		
		const parentCustomerFoldersArr =
			getEdiIntegrationsLib.getInfoForOutgoingDocument(
				dataObj.purchasingSoftwareId,
				dataObj.documentTypeId
			);

		parentCustomerFoldersArr.forEach((parentCustomer) => {
			const partnerValues = getEdiPartnerValuesLib.getDSSIValues(parentCustomer.integrationFolder);

			const itemFulfillment_intID_obj =
				getItemFulfillmentInternalIdsLib.getInternalIds(
					parentCustomer.customerId,
					dataObj.purchasingSoftwareId,
					"",
					customErrorObject
				);

			if (itemFulfillment_intID_obj.internalIds.length <= 0) {
				//No item fulfillment to be processed for this customer
				noTransactionsToProcess.push(parentCustomer.customerName);
			} else {
				itemFulfillment_intID_obj.internalIds.forEach((ifInternalID_Obj) => {
					transactionObjArr.push({
						ifInternalID_Obj,
						parentCustomer,
						partnerValues,
						dataObj,
					});
				});
				return true;
			}
		});

	} catch (err) {
		customErrorObject.throwError({
			summaryText: "GET_INPUT_DATA_ERROR",
			error: err,
		});
	}

		return transactionObjArr;
	}

	function map(context) {
		const customErrorObject = new CustomErrorObject();

		var data = JSON.parse(context.value);

		const { ifInternalID_Obj, parentCustomer, partnerValues, dataObj } = data;
		dataObj.prodDirectory = `/users/DSSI/OUT/856`;
		var internalIDval = ifInternalID_Obj.transactionId;


		try {
		const docCtrlNumRecIdVal = process856Lib.process856(
			internalIDval,
			parentCustomer,
			partnerValues,
			dataObj,
			customErrorObject
		);

		context.write(parentCustomer.customerName, docCtrlNumRecIdVal); 

	} catch (err) {
		customErrorObject.throwError({
			summaryText: `MAP_ERROR`,
			error: err,
			recordId: ifInternalID_Obj?.transactionId,
			recordType: `itemfulfillment`,
			errorWillBeGrouped: true,
		});
	}

	}

	function reduce(context) {
		context.write({
			key: context.key,
			value: context.values,
		});
	}

	function summarize(context) {
		const customErrorObject = new CustomErrorObject();
		try {		
			const sentEmailObj = processOutgoingEdiFileEmailLib.processEmail(
				context,
				[],
				dataObj.purchasingSoftware,
				dataObj.documentType
			);

			if (dataObj.pushEmailToDB) {
				try {
					pushEdiEmailInfoToDBLib.pushEdiEmailInfoToDB(dataObj, sentEmailObj);
				} catch (e) {
					throw `Error pushing EDI email to database: ${e}`;
				}
			}
		} catch (err) {
			customErrorObject.throwError({
				summaryText: `SUMMARIZE_ERROR`,
				error: err,
			});
		}

	}

	return {
		getInputData,
		map,
		reduce,
		summarize,
	};
});
