/**
 * @NApiVersion 2.1
 * @NScriptType MapReduceScript
 * @NAmdConfig /SuiteScripts/config.json
 */

//@ts-ignore
define(["N/error","N/search", "N/record"], function (error, search, record) {
	function getInputData(context) {
		var itemsArr = [];

		var itemSearchObj = search.create({
			type: "item",
			filters: [
				["subsidiary", "anyof", "22"],
				"AND",
				["name", "doesnotcontain", "test"],
			],
		});

		itemSearchObj.run().each(function (result) {
			itemsArr.push(result);
			return true;
		});

		log.debug("itemsArr", itemsArr);

		return itemsArr;
	}

	function map(context) {
		var parsedResult = JSON.parse(context.value);

		try {
			var deletedId = record.delete({
				type: record.Type.INVENTORY_ITEM,
				id: parsedResult.id,
			});
			log.debug("deletedId", deletedId);
		} catch (e) {
			context.write(customer.customerName, transactionObj.transactionId);

			throw error.create({
				message: e,
				name: "Map Error",
			});
		}
	}
	function reduce(context) {
		context.write({
			key: context.key,
			value: context.values,
		});
	}

	function summarize(context) {}
	
	return {
		getInputData: getInputData,
		map: map,
		reduce: reduce,
		summarize: summarize,
	};
});
