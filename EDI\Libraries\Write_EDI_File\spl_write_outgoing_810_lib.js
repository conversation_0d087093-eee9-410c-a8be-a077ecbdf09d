/**
 * @NApiVersion 2.1
 */

//@ts-ignore
define(["N/log", "Moment", "Numeral"], function (log, moment, numeral) {
  function getInvoiceAsEDI(
    partnerValues,
    documentObj,
    isInvoice,
    purchasingSoftware
  ) {
    var ediFile = `ISA*00*          *00*          *SenderQualifier*ISASenderId*ReceiverQualifier*ISAReceiverId*ISADate*Time*U*EdiVersion*ControlNumber*0*P*>~
GS*IN*GSSenderId*GSReceiverId*GSDate*Time*ControlNumber*X*EdiVersion0~
ST*810*0001~
BIG*DocumentDate*DocumentNumber*PoDate*PoNumber***TransactionType~NTE*ZZZ*Note~
REF*OI*OriginalInvoice~${
      purchasingSoftware == "DSSI"
        ? ""
        : `
N1*ST*CustomerName*92*CustomerID~
N2*~
N3*StreetAddress~
N4*City*State*Zip~`
    }
N1*OB*CustomerName*92*CustomerID~
N1*EN*CustomerName*92*CustomerID~
DTM*011*ShipDate~
`; //N103 - 91: Assigned by seller, 92: assigned by buyer
    //do en - ship to

    let itemText;

    switch (purchasingSoftware) {
      case "DSSI":
        itemText = `IT1*LineNumber*Quantity*UOM*Rate**VN*ItemName~
`;
        break;
      case "Agora":
        itemText = `IT1*LineNumber*Quantity*UOM*Rate*UOM*VN*ItemName*VN*IsReplacementFor~
QTY*01*Quantity*UOM~
CTP********Amount~
`;
        break;
      default:
        itemText = `IT1*LineNumber*Quantity*UOM*Rate*UOM*VN*ItemName~
QTY*01*Quantity*UOM~
CTP********Amount~
`;
        break;
    }

    itemText += `PID*F****ItemDescription~
`;

    var taxAndTotalText = `TDS*Total~
TXI*TaxType*TaxAmount*4~
`; //**CD*TaxJurisdictionCode****TIN~'

    var sacText = `SAC*C*SACType***SACAmount${
      purchasingSoftware == "DSSI" ? "" : "*****SACQuantity*****SACDescription"
    }~
`;

    var endOfFile = `CTT*TotalLineItems~
SE*NumberOfSegments*0001~
GE*1*ControlNumber~
IEA*1*ControlNumber~`;

    formatDateTimeEdiVersion();
    formatSender();
    formatReceiver();
    formatInvoice();
    formatNote();
    formatNameAddress();
    formatItems();
    formatShipDate();

    return ediFile;

    function formatFormatting() {
      //called at the end of formatItems
      var formattingInfo = partnerValues.formattingInfo;

      formattingInfo.forEach(function (x) {
        ediFile = ediFile.split(x.templateValue).join(x.partnerValue);
      });

      formatSegmentNumber();
    }

    function formatDateTimeEdiVersion() {
      var dateTimeInfo = partnerValues.isaGsInfo;

      dateTimeInfo.forEach(function (x) {
        ediFile = ediFile.split(x.name).join(x.value);
      });
    }

    function formatSender() {
      var senderInfo = partnerValues.senderInfo;

      senderInfo.forEach(function (x) {
        ediFile = ediFile.split(x.name).join(x.value);
      });
    }

    function formatReceiver() {
      var receiverInfo = partnerValues.receiverInfo;

      receiverInfo.forEach(function (x) {
        ediFile = ediFile.split(x.name).join(x.value);
      });
    }

    function formatInvoice() {
      var invoiceInfo = [
        {
          name: "DocumentDate",
          value: moment(documentObj.documentInfo.date).format("YYYYMMDD"),
        },
        {
          name: "DocumentNumber",
          value: documentObj.documentInfo.documentNumber,
        },
        {
          name: "DocumentDate",
          value: moment(documentObj.documentInfo.date).format("YYYYMMDD"),
        },
        {
          name: "PoDate",
          value: documentObj.documentInfo.purchaseOrderDate
            ? moment(documentObj.documentInfo.purchaseOrderDate).format(
                "YYYYMMDD"
              )
            : "",
        },
        {
          name: "PoNumber",
          value: documentObj.documentInfo.purchaseOrderNumber,
        },
        {
          name: "TransactionType",
          value: isInvoice ? "DI" : "CR", //Debit Invoice vs. Credit Memo
        },
        {
          name: "OriginalInvoice",
          value: documentObj.documentInfo.originalInvoice,
        },
      ];

      invoiceInfo.forEach(function (x) {
        ediFile = ediFile.replace(x.name, x.value);
      });
    }

    function formatNote() {
      var freeFormInfo = [
        {
          name: "NTE*ZZZ*Note~",
          value: `
NTE*ZZZ*${documentObj.note}~`,
        },
      ];

      freeFormInfo.forEach(function (x) {
        if (documentObj.note) {
          ediFile = ediFile.replace(x.name, x.value);
        } else {
          ediFile = ediFile.replace(x.name, "");
        }
      });
    }

    function formatNameAddress() {
      var nameAddressInfo = [
        {
          name: "NameQualifier",
          value: "OB", //Ordered by, ST: Ship to, BT: Bill to.
        },
        {
          name: "CustomerName",
          value: documentObj.customer.customerNameInTheirSystem,
        },
        {
          name: "CustomerID", //check that this is what CustomerNumber(AccountNumber) is
          value: documentObj.customer.accountNumber,
        },
        {
          name: "StreetAddress",
          value: documentObj.address.streetAddress,
        },
        {
          name: "City",
          value: documentObj.address.city,
        },
        {
          name: "State",
          value: documentObj.address.state,
        },
        {
          name: "Zip",
          value: documentObj.address.zip,
        },
      ];

      nameAddressInfo.forEach(function (x) {
        ediFile = ediFile.split(x.name).join(x.value);
      });
    }

    function formatItems() {
      const specialCharactersPattern = [
        "[,.]", // Basic punctuation
        "[\u0027\u2018\u2019\u201A\u201B\u2032\u2035]", // Single quotes (various Unicode versions)
        "[\u0022\u201C\u201D\u201E\u201F\u2033\u2036]", // Double quotes (various Unicode versions)
        "[\u0082\u0091\u0092\u0084\u0093\u0094]", // Special character quotation marks
      ].join("|");

      let regexp = new RegExp(specialCharactersPattern, "g");

      documentObj.items.forEach(function (item) {
        var text = itemText;

        var itemInfo = [
          {
            name: "Quantity",
            value: numeral(item.quantity).format("0,0"),
          },
          {
            name: "UOM",
            value: item.uom,
          },
          {
            name: "Rate",
            value: numeral(item.rate).format("00.00"),
          },
          {
            name: "ItemName",
            value: item.name,
          },
          {
            name: "Amount",
            value: numeral(item.amount).format("00.00"),
          },
          {
            name: "ItemDescription",
            value: item.description.slice(0, 80).replace(regexp, ""),
          },
          { name: "LineNumber", value: item.lineNumber },
          {
            name: "IsReplacementFor",
            value: item.isReplacementFor != false ? item.isReplacementFor : "",
          },
        ];

        itemInfo.forEach(function (x) {
          text = text.split(x.name).join(x.value);
        });

        ediFile += text;
      });
      formatTaxAndTotal();
      formatCharges();
    }

    function formatTaxAndTotal() {
      var taxAndTotalInfo = [
        {
          name: "TaxType",
          value: documentObj.taxType,
        },
        {
          name: "TaxAmount",
          value: numeral(documentObj.taxAmount).format("00.00"),
        },
        {
          name: "Total",
          value: numeral(documentObj.total * 100).format("00"),
        },
      ];

      taxAndTotalInfo.forEach(function (x) {
        taxAndTotalText = taxAndTotalText.replace(x.name, x.value);
      });
      ediFile += taxAndTotalText;
    }

    function formatCharges() {
      documentObj.sacItems.forEach(function (sac) {
        var text = sacText;
        var sacInfo = [
          {
            name: "SACType",
            value: sac.sacType,
          },
          {
            name: "SACQuantity",
            value: numeral(sac.quantity).format("0,0"),
          },
          {
            name: "SACAmount",
            value: numeral(sac.amount * 100).format("00"),
          },
          {
            name: "SACDescription",
            value: sac.description,
          },
        ];

        sacInfo.forEach(function (x) {
          text = text.split(x.name).join(x.value);
        });

        ediFile += text;
      });
      formatEnd();
      formatFormatting();
      formatControlNumber();
    }

    function formatShipDate() {
      var shipDateInfo = [
        {
          name: "ShipDate",
          value: moment(documentObj.documentInfo.shipDate).format("YYYYMMDD"),
        },
      ];

      shipDateInfo.forEach(function (x) {
        ediFile = ediFile.replace(x.name, x.value);
      });
    }

    function formatSegmentNumber() {
      var segments = ediFile.match(/[\n]/gm);
      var numberOfSegments = segments.length - 3;
      ediFile = ediFile.replace("NumberOfSegments", numberOfSegments);
    }

    function formatControlNumber() {
      //Called at the end of formatItems
      ediFile = ediFile
        .split("ControlNumber")
        .join(documentObj.documentInfo.controlNumber);
    }

    function formatEnd() {
      var endInfo = [
        {
          name: "TotalLineItems",
          value: documentObj.items.length,
        },
      ];

      endInfo.forEach(function (x) {
        endOfFile = endOfFile.replace(x.name, x.value);
      });
      ediFile += endOfFile;
    }
  }

  return {
    getInvoiceAsEDI,
  };
});
