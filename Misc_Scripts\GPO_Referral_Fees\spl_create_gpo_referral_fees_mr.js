//TODO: Add handling for when saved search param is passed in

/**
 * @NApiVersion 2.1
 * @NScriptType MapReduceScript
 * @NAmdConfig /SuiteScripts/config.json
 * @param {import ("N/types")}
 * <AUTHOR>
 * @description MR to create GPO transacion records and associated JEs
 * @module spl_create_gpo_referral_fees_mr
 */

//@ts-ignore
define([
	"require",
	"CreateReferralFeeLib",
	"MapReduceSummaryStageHandling",
	"../../Classes/vlmd_custom_error_object",
	"N/log",
	"N/error",
	"N/runtime",
	"N/query",
], function (require, createReferralFeeLib, MapReduceSummaryStageHandling) {
	const log = require("N/log");
	const runtime = require("N/runtime");

	/**@type {import ("../../Classes/vlmd_custom_error_object").CustomErrorObject}} */
	const CustomErrorObject = require("../../Classes/vlmd_custom_error_object");
	const customErrorObject = new CustomErrorObject();

	function getInputData(context) {
		try {
			const currentScript = runtime.getCurrentScript();

			const queryString = currentScript.getParameter({
				name: "custscript_suiteql_query",
			});

			if (queryString) {
				return {
					type: "suiteql",
					query: queryString,
				};
			}

			const gpoId = currentScript.getParameter({
				name: "custscript_gpo_id",
			});

			const startDate = currentScript.getParameter({
				name: "custscript_gpo_start_date",
			});

			const endDate = currentScript.getParameter({
				name: "custscript_gpo_end_date",
			});

			/**Important: Do not change this order, doing so will cause the map stage to fail. */
			const selectClause = `SELECT
			SUM (trnsctnln.netAmount * - 1) as subtotal,
			SUM (trnsctnln.netAmount * gpo.custrecord_gpo_referral_fee * -1) as gpo_amount,
			gpo.custrecord_gpo_referral_fee as gpo_fee_percentage,			
			prnt.custentity_mhi_supplyline_gpo AS gpo_id,
			gpo.custrecord_spl_gpo_rfrl_fee_credit_acnt as accounts_payable_account,
			gpo.custrecord_spl_gpo_rfrl_fee_debit_acnt as cogs_account,
			transaction.id AS transaction_internal_id,
			transaction.tranid AS transaction_name,
			transaction.type AS transaction_type,
			transaction.trandate AS transaction_date,
			trnsctnln.subsidiary as subsidiary,
			cus.id AS customer_id,
			cus.companyname AS customer_name,
			prnt.id as parent_id,
			prnt.companyname as parent_name
		`;

			const fromClause = `FROM
			transaction 
			INNER JOIN
			   customer AS cus 
			   ON cus.id = transaction.entity 
			LEFT OUTER JOIN
			   customer AS prnt 
			   ON prnt.id = cus.parent 
			INNER JOIN
			   customrecord_spl_gpo AS GPO 
			   ON (prnt.custentity_mhi_supplyline_gpo = GPO.id OR cus.custentity_mhi_supplyline_gpo = GPO.id )
			INNER JOIN
			   transactionLine AS trnsctnln 
			   ON transaction .id = trnsctnln .transaction 
			LEFT OUTER JOIN
			customrecord_spl_trnsctn_gpo_fee_record as gporecord 
			   on gporecord.custrecord_spl_gpo_fee_created_for = transaction.id 
			   `;

			let whereClause = `	WHERE
			(
				transaction.type = 'CustInvc' 
				OR transaction.type = 'CustCred' 
			 )
			 --Filter to only include transactions with a subtotal amount on the transaction
			 AND (
				SELECT
				   COUNT (*) 
				FROM
				   transactionline 
				WHERE
				   transaction.id = transactionline.transaction 
				   AND transactionline.netamount > 0 
			 ) > 0 
			 AND trnsctnln.accountinglinetype = 'INCOME' 
			 AND gpo.custrecord_spl_gpo_charge_referral_fee = 'T' 
			 AND transaction.trandate >= gpo.custrecord_spl_commission_start_date 
			 AND transaction.trandate BETWEEN to_date('${startDate}') AND to_date('${endDate}')
 --Filter to only include invoice lines that the invoice doesn't have any GPO JE associated with it. 
 AND (
		 SELECT 
			 COUNT(*)
		 FROM 
			 customrecord_spl_trnsctn_gpo_fee_record 
		   WHERE
			 customrecord_spl_trnsctn_gpo_fee_record.custrecord_spl_gpo_fee_created_for = transaction.id 
			 AND gporecord.custrecord_spl_referral_journal_entry IS NOT NULL
	 ) <=0
 
 
 --And has no JE created already for it
 AND transaction.id NOT IN(select custbody_gpo_created_for_record from transaction where type = 'Journal' AND custbody_gpo_created_for_record IS NOT NULL)
			`;

			whereClause += gpoId
				? `AND (prnt.custentity_mhi_supplyline_gpo = ${gpoId} OR cus.custentity_mhi_supplyline_gpo = ${gpoId})`
				: "";

			const groupClause = `
		GROUP BY
			gpo.custrecord_gpo_referral_fee,
			prnt.custentity_mhi_supplyline_gpo,
			gpo.custrecord_spl_gpo_rfrl_fee_credit_acnt,
			gpo.custrecord_spl_gpo_rfrl_fee_debit_acnt,
			transaction.id,
			transaction.tranid,
			transaction.type,
			transaction.trandate,
			trnsctnln.subsidiary,
			cus.id,
			cus.companyname,
			prnt.id,
			prnt.companyname`;

			const gpoQueryString = [
				...selectClause,
				...fromClause,
				...whereClause,
				...groupClause,
			].join("");

			log.audit("Query String", gpoQueryString);

			return {
				type: "suiteql",
				query: gpoQueryString,
				params: [],
			};
		} catch (err) {
			customErrorObject.throwError({
				summaryText: "GET_INPUT_DATA_ERROR",
				error: err,
			});
		}
	}

	function map(context) {
		const mapErrorObject = new CustomErrorObject();

		const invoiceValuesArr = JSON.parse(context.value).values;

		const subtotal = invoiceValuesArr[0];
		const gpoReferralAmount = invoiceValuesArr[1];
		const gpoFeePercentage = invoiceValuesArr[2];
		const gpoId = invoiceValuesArr[3];
		const accountsPaybleAccountId = invoiceValuesArr[4];
		const cogsAccountId = invoiceValuesArr[5];
		const transactionId = invoiceValuesArr[6];
		const transactionName = invoiceValuesArr[7];
		const transactionType = invoiceValuesArr[8];
		const transactionDate = invoiceValuesArr[9];
		const subsidiary = invoiceValuesArr[10];
		const customerId = invoiceValuesArr[11];
		const customerName = invoiceValuesArr[12];
		const parentId = invoiceValuesArr[13];
		const parentName = invoiceValuesArr[14];

		try {
			var { referralInternalId, journalEntryId } =
				createReferralFeeLib.createGpoReferral({
					mapErrorObject,
					subtotal,
					gpoFeePercentage,
					gpoReferralAmount,
					accountsPaybleAccountId,
					cogsAccountId,
					subsidiary,
					transactionId,
					transactionType,
					transactionDate,
					customerId,
				});
		} catch (err) {
			mapErrorObject.throwError({
				summaryText: `MAP_ERROR`,
				error: err,
				recordId: transactionId,
				recordName: transactionName,
				recordType: transactionType,
				errorWillBeGrouped: true,
			});
		}

		context.write(
			parentName ?? customerName,
			`${gpoId}, ${transactionName}, ${transactionId}, ${transactionType},${referralInternalId},${journalEntryId}`
		);
	}

	function reduce(context) {
		context.write({
			key: context.key,
			value: context.values,
		});
	}

	function summarize(context) {
		const stageHandling = new MapReduceSummaryStageHandling(context);
		stageHandling.printScriptProcessingSummary();
		stageHandling.printRecordsProcessed();
		stageHandling.printErrors({ groupErrors: true });
	}

	return {
		getInputData,
		map,
		reduce,
		summarize,
	};
});
