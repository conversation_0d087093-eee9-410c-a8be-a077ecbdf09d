/**
 * @description EDI Outgoing File Class
 *
 * @NApiVersion 2.1
 * <AUTHOR> <<EMAIL>>
 */

define([
    "exports",
    "require",
    "./edi_file",
], (/** @type {any} */ exports, /** @type {any} */ require) => {
    const { EDIFile } = require("./edi_file");

    /**
     * EDI Outgoing File Class
     *
     * @class
     * @typedef {import("N/file").File} File
     * @typedef {import("../../Interfaces/Models/File/edi_outgoing").EDIOutgoingInterface} EDIOutgoingInterface
     * @typedef {import("../../Interfaces/Models/File/edi_outgoing").SuiteQLObjectReference} SuiteQLObjectReference
     * @implements {EDIOutgoingInterface}
     * @extends {EDIFile}
     */
    class EDIOutgoing extends EDIFile {
        constructor() {
            super();
            /** @type {string} */
            this.ediType = "EDI Outgoing";
            /** @type {File | null} */
            this.file = null;
            /** @type {string} */
            this.fileName = "";
            /** @type {number} */
            this.fileId = 0;
            /** @type {number} */
            this.folderId = 0;
            /** @type {string} */
            this.emailSubject = ""
        }

        /**
         * Create the NetSuite File Record
         *
         * @param {{[key:string]: any}} [params] Parametrised object
         * @returns {void}
         */
        create(params) { return; }

        /**
         * Return the query string or Search object to load the records
         *
         * @param {{[key:string]: any}} [params] Parametrised object
         * @returns {any} Query string or Search object to retrieve transaction records
         */
        load(params) { return null; }

        /**
         * Process the record object using the decorator processor
         *
         * @param {{[key:string]: any}} [params] Parametrised object
         * @returns {string}
         */
        process(params) { return ""; }

        /**
         * Save the NetSuite File record to File Cabinet
         *
         * @param {{[key:string]: any}} [params] Parametrised object
         * @returns {void}
         */
        save(params) { return; }

        /**
         * Send the EDI File as an E-mail attachment
         *
         * @param {{[key:string]: any}} [params] Parametrised object
         * @returns {void}
         */
        email(params) { return; }

        /**
         * Upload the EDI File to the Partner's SFTP Server
         *
         * @param {{[key:string]: any}} [params] Parametrised object
         * @returns {void}
         */
        upload(params) { return; }

        /**
         * Mark the transaction as processed
         *
         * @param {{[key:string]: any}} [params] Parametrised object
         * @returns {number} Document Control Number record ID
         */
        complete(params) { return 0; }
    }

    exports.EDIOutgoing = EDIOutgoing;
});