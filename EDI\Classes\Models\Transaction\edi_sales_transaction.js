/**
 * @description EDI Transaction Class for Sales subgroup
 *
 * @NApiVersion 2.1
 * <AUTHOR> <<EMAIL>>
 */

define([
    "exports",
    "require",
    "N/format",
    "N/log",
    "N/record",
    "N/search",
    "./edi_transaction",
], function (/** @type {any} */ exports, /** @type {any} */ require) {
    const format = require("N/format");
    const log = require("N/log");
    const record = require("N/record");
    const search = require("N/search");
    const { EDITransaction } = require("./edi_transaction");

    /**
     * Sales Transaction Class
     *
     * @extends {EDITransaction}
     * @class
     */
    class EDISalesTransaction extends EDITransaction {
        /** @param {{[key:string]: any}} params Constructor params */
        constructor(params){
            super(params);
            /** @type {string} */
            this.customerEntityId = params.customerEntityId;
            /** @type {number} */
            this.customerParent = params.customerParent || 0;
            /** @type {string} */
            this.customerName = params.customerFullName?.replace(/^(.*):\s/, "");
            /** @type {string} */
            this.customerGLN = params.customerGLN;
            /** @type {number} */
            this.lineCount = 0;
            /** @type {number} */
            this.amount = 0;
            /** @type {string} */
            this.purchaseOrderNumber = params.otherrefnum || "";
            /** @type {Date} */
            this.purchaseOrderDate;
            /** @type {string} */
            this.purchaseOrderDateFormatted = "";
        }

        /**
         * @param {Date} date Date to format
         */
        formatToYYYYMMDD(date) {
            const year = date.getFullYear();
            const month = (date.getMonth() + 1).toString().padStart(2, '0');
            const day = date.getDate().toString().padStart(2, '0');

            return `${year}${month}${day}`;
        }

        /**
         * Retrieve PO # from the transaction
         * If not found, return the tranid
         *
         * @returns {string} Purchase order number
         */
        getPurchaseOrderNumber() {
            try {
                if (!this.purchaseOrderNumber && [record.Type.INVOICE, record.Type.ITEM_FULFILLMENT].includes(this.type) && this.createdfrom) {
                    this.purchaseOrderNumber = search.lookupFields({
                        type: search.Type.SALES_ORDER,
                        id: this.createdfrom,
                        columns: ["otherrefnum"],
                    })["otherrefnum"]?.toString() || "";
                }

                return this.purchaseOrderNumber || this.documentNumber;
            } catch (/** @type {any} */err) {
                log.error("EDI Sales Transaction (getPurchaseOrderNumber)", err);
                throw this.customError.updateError({
                    errorType: this.customError.ErrorTypes.NO_VALUE_RETURNED,
                    summary: "NO_PURCHASE_ORDER_NUMBER_RETRIEVED",
                    details: `Error getting purchase order number from the parent Sales Order: ${err}`,
                });
            }
        }
        
        /**
         * Retrieve the Purchase Order Date saved as trandate of the NS Sales Order
         *
         * @returns {string} Formatted Purchase Order Date
         */
        getPurchaseOrderDate() {
            try {
                if ([record.Type.INVOICE, record.Type.ITEM_FULFILLMENT].includes(this.type) && this.createdfrom) {
                    const nsDate = search.lookupFields({
                        type: search.Type.SALES_ORDER,
                        id: this.createdfrom,
                        columns: ["trandate"],
                    })["trandate"]?.toString();

                    if (nsDate) {
                        const parsedDate = format.parse({
                            value: nsDate,
                            type: format.Type.DATE
                        });
                        this.purchaseOrderDate = new Date(parsedDate);
                        this.purchaseOrderDateFormatted = this.formatToYYYYMMDD(this.purchaseOrderDate);
                    }
                }

                return this.purchaseOrderDateFormatted;
            } catch (/** @type {any} */err) {
                log.error("EDI Sales Transaction (getPurchaseOrderDate)", err);
                throw this.customError.updateError({
                    errorType: this.customError.ErrorTypes.NO_VALUE_RETURNED,
                    summary: "NO_PURCHASE_ORDER_DATE_RETRIEVED",
                    details: `Error getting transaction date from the parent Sales Order: ${err}`,
                });
            }
        }
    }

    exports.EDISalesTransaction = EDISalesTransaction;
});

