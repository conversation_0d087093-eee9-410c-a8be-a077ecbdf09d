//Returns the rep based on who the last task was assigned to and then 2:1 ratio.

/**
 * @NApiVersion 2.1
 * @NScriptType workflowactionscript
 * @NAmdConfig /SuiteScripts/config.json
 */

//@ts-ignore
define(["N/log", "N/record", "GetRepToAssignToLib"], function (
	log,
	record,
	getRepToAssignToLib
) {
	function onAction(context) {
		var scriptDeployment = record.load({
			type: record.Type.SCRIPT_DEPLOYMENT,
			id: "3178",
		});

		var repAssignedLastTask = scriptDeployment.getValue(
			"custscript_spl_last_tsk_assngd_to_csr"
		);

		var repArr = getRepToAssignToLib.getTaskRepArr("8"); //Lakewood CSR
		repArr = repArr.filter((repId) => repId != 811);
		//Remove Hindy from arr so that tasks will only go to Sara and Devorah

		var nextRepToAssignTo = getRepToAssignToLib.getRepBasedOffOfLastAssigned(
			repArr,
			repAssignedLastTask,
			scriptDeployment
		);

		updateLastRepScripParam();
		return nextRepToAssignTo;

		function updateLastRepScripParam() {
			scriptDeployment.setValue({
				fieldId: "custscript_spl_last_tsk_assngd_to_csr",
				value: nextRepToAssignTo,
			});

			scriptDeployment.setValue({
				fieldId: "custscript_spl_hb_rep_asgnd_two_tsks_ago",
				value: repAssignedLastTask,
			});

			scriptDeployment.save();
		}
	}

	return {
		onAction: onAction,
	};
});
