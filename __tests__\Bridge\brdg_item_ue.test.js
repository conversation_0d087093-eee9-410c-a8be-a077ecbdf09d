// @ts-nocheck
import error from "N/error";
import runtime from "N/runtime";
import search from "N/search";
import query from "N/query";
import log from "N/log";
import email from "N/email";
import record from "N/record";

import bridgeHelperFunctionsLib from "../../Bridge/Libraries/brdg_helper_functions_lib";
import updateItemFieldsLib from "../../Bridge/Libraries/brdg_update_item_lib";
import brdg_item_ue from "../../Bridge/brdg_item_ue";

beforeEach(() =>  {
	jest.resetModules();
	jest.resetAllMocks();
	jest.clearAllMocks();
});

jest.mock("LoDash", () => ({
	isEmpty: () => false
}), { virtual: true });

import _ from "LoDash";

describe("brdg_item_ue", () => {
	let context;
	beforeEach(() => {
		context = {
			UserEventType: {
				EDIT: "EDIT",
				COPY: "COPY"
			},
			newRecord: {
				id: "foo",
				type: "inventoryitem",
				getValue: ({fieldId}) => {
					const dummyValues = {
						subsidiary: ["16"],
						custitem_brdg_dont_use_markup: false,
						cost: 10,
						custitem_brdg_dont_sync_to_ls: false
					};
					return dummyValues[fieldId] !== undefined ? dummyValues[fieldId] : fieldId;
				},
				setValue: jest.fn(),
				getFields: jest.fn().mockReturnValue([
					"taxschedule",
					"includechildren",
					"matchbilltoreceipt",
					"costestimatetype",
					"overallquantitypricingtype",
					"costingmethod",
					"tracklandedcost",
					"custitem_brdg_markup_percentage",
					"custitem_in8_sync_vend",
					"custitem_sync_to_lol",
					"custitem_sync_to_vs",
					"custitem_sync_to_vb",
					"custitem_sync_to_vexpress",
					"custitem_sync_to_vnyrd_westgate"
				]),
				setSublistValue: jest.fn(),
				getLineCount: () => 15
			}
		};
		jest.spyOn(error, "create").mockImplementation((error) => error);
		jest.replaceProperty(runtime, "executionContext", "csvimport");
		jest.spyOn(bridgeHelperFunctionsLib, "isBridgeSubsidiary").mockReturnValue(true);
		jest.spyOn(search, "create").mockReturnValue({
			run: () => ({
				getRange: () => [
					{
						id: "foo"
					}
				]
			})
		});
		jest.spyOn(log, "error");
		jest.spyOn(record, "submitFields");
	});
	
	describe("beforeLoad", () => {

		beforeEach(() => {
			context.type = "COPY"
		});

		it("returns early when subsidiary is not Bridge", () => {
			jest.spyOn(bridgeHelperFunctionsLib, "getBridgeSubsidiaries").mockReturnValue([]);
			jest.spyOn(bridgeHelperFunctionsLib, "isBridgeSubsidiary").mockReturnValue(false);
			jest.spyOn(query, "runSuiteQL");
			brdg_item_ue.beforeLoad(context);
			expect(query.runSuiteQL).not.toHaveBeenCalled();
		});

		it("sets subsidiary to Bridge only", () => {
			brdg_item_ue.beforeLoad(context);
			expect(context.newRecord.setValue).toHaveBeenCalledWith({
				fieldId: "subsidiary",
				value: ["16"]
			});
		});

		it("sets default values", () => {
			brdg_item_ue.beforeLoad(context);
			expect(context.newRecord.setValue).toHaveBeenCalledWith({
				fieldId: "taxschedule",
				value: 4 // tax schedule 3
			});
			expect(context.newRecord.setValue).toHaveBeenCalledWith({
				fieldId: "includechildren",
				value: true
			});
			expect(context.newRecord.setValue).toHaveBeenCalledWith({
				fieldId: "matchbilltoreceipt",
				value: true
			});
			expect(context.newRecord.setValue).toHaveBeenCalledWith({
				fieldId: "costestimatetype",
				value: "LASTPURCHPRICE"
			});
			expect(context.newRecord.setValue).toHaveBeenCalledWith({
				fieldId: "costingmethod",
				value: "FIFO"
			});
			expect(context.newRecord.setValue).toHaveBeenCalledWith({
				fieldId: "tracklandedcost",
				value: true
			});
			expect(context.newRecord.setValue).toHaveBeenCalledWith({
				fieldId: "custitem_brdg_markup_percentage",
				value: "To Be Generated"
			});
		});

		it("sets sync to Light Speed fields", () => {
			brdg_item_ue.beforeLoad(context);
			expect(context.newRecord.setValue).toHaveBeenCalledWith({
				fieldId: "custitem_in8_sync_vend",
				value: true
			});
			expect(context.newRecord.setValue).toHaveBeenCalledWith({
				fieldId: "custitem_sync_to_lol",
				value: true
			});
			expect(context.newRecord.setValue).toHaveBeenCalledWith({
				fieldId: "custitem_sync_to_vs",
				value: true
			});
			expect(context.newRecord.setValue).toHaveBeenCalledWith({
				fieldId: "custitem_sync_to_vb",
				value: true
			});
			expect(context.newRecord.setValue).toHaveBeenCalledWith({
				fieldId: "custitem_sync_to_vexpress",
				value: true
			});
			expect(context.newRecord.setValue).toHaveBeenCalledWith({
				fieldId: "custitem_sync_to_vnyrd_westgate",
				value: true
			});
		});

	});

	describe("afterSubmit", () => {

		it("returns early when subsidiary is not Bridge", () => {
			jest.spyOn(bridgeHelperFunctionsLib, "getBridgeSubsidiaries").mockReturnValue([]);
			jest.spyOn(bridgeHelperFunctionsLib, "isBridgeSubsidiary").mockReturnValue(false);
			jest.spyOn(query, "runSuiteQL");
			brdg_item_ue.afterSubmit(context);
			expect(query.runSuiteQL).not.toHaveBeenCalled();
		});

		describe("item number is valid, has parent category, has markup amount & parent ignores markup", () => {
			beforeEach(() => {
				jest.spyOn(bridgeHelperFunctionsLib, "getBridgeSubsidiaries").mockReturnValue([]);
				jest.spyOn(bridgeHelperFunctionsLib, "isBridgeSubsidiary").mockReturnValue(true);
				// mock for getParentProductCategoryInfo()
				jest.spyOn(query, "runSuiteQL").mockReturnValueOnce({
					asMappedResults: () => [{parent: 86, ignoremarkup: "T"}]
				});
				// mock for markup amount
				jest.spyOn(query, "runSuiteQL").mockReturnValueOnce({
					results: [{
						values: [0.1, "markuprecordid"]
					}]
				});
			});
			describe.each([
				{
					itemType: "kit"
				},
				{
					itemType: "inventoryitem"
				}
			])("when item is of type $itemType", (itemType) => {
				beforeEach(() => {
					context.newRecord.type = itemType;
				});
				it("sets do not use mark up field to true", () => {
					brdg_item_ue.afterSubmit(context);
					expect(record.submitFields).toHaveBeenCalledWith({
						"id": "foo",
						"options": {
							"enableSourcing": false,
							"ignoreMandatoryFields": true,
						},
						"type": itemType,
						"values": {
							"custitem_brdg_dont_use_markup": true,
						},
					});
				});
			});
		});

		describe("when has no markup amount", () => {

			beforeEach(() => {
				jest.spyOn(bridgeHelperFunctionsLib, "getBridgeSubsidiaries").mockReturnValue([]);
				jest.spyOn(bridgeHelperFunctionsLib, "isBridgeSubsidiary").mockReturnValue(true);
			});

			it("sends a summary email", () => {
				// mock for getParentProductCategoryInfo()
				jest.spyOn(query, "runSuiteQL").mockReturnValueOnce({
					asMappedResults: () => [{parent: 85}]
				});
				jest.spyOn(query, "runSuiteQL").mockReturnValue("dummy");
				jest.spyOn(_, "isEmpty").mockReturnValue(true);
				jest.spyOn(email, "send");
				brdg_item_ue.afterSubmit(context);
				expect(email.send).toHaveBeenCalledWith({
					"author": 3288,
					"body": `This item: itemid was added and it's category 85 does 
							not have a matching markup percentage record. Please update.`,
					"recipients": "<EMAIL>",
					"subject": "Item itemid added without markup percentage!"
				});
			});
		});

	});

	describe("beforeSubmit", () => {

		beforeEach(() => {
			jest.spyOn(bridgeHelperFunctionsLib, "getBridgeSubsidiaries").mockReturnValue([]);
			jest.spyOn(bridgeHelperFunctionsLib, "isBridgeSubsidiary").mockReturnValue(true);
		});
		
		describe("item number is valid, has parent category & has markup amount", () => {
			beforeEach(() => {
				// mock for validateItemNumber()
				jest.spyOn(query, "runSuiteQL").mockReturnValueOnce({
					asMappedResults: () => []
				});
				// mock for markup amount
				jest.spyOn(query, "runSuiteQL").mockReturnValueOnce({
					results: [{
						values: [0.1, "markuprecordid"]
					}]
				});
			});
			describe.each([
				{
					itemType: "kit"
				},
				{
					itemType: "inventoryitem"
				}
			])("when item is of type $itemType", (itemType) => {
				beforeEach(() => {
					context.newRecord.type = itemType;
				});
				it("throws error when UPC code is not available", () => {
					context.newRecord.getValue = ({fieldId}) => {
						const dummyValues = {
							upccode: "",
							custitem_vnyrd_upc_code_not_available: false
						};
						return dummyValues[fieldId] !== undefined ? dummyValues[fieldId] : fieldId;
					};
					try {
						brdg_item_ue.beforeSubmit(context);
						throw "error";
					} catch (e) {
						expect(e).toEqual({
							name: "MISSING_VALUE",
							message: `SUMMARY: Error validating UPC fields:MissingValue\nDETAILS: Please enter a upccode or check off 'UPC Code Not Available' if applicable.\n`
						});
					}
				});
				it("throws a UPC error when UPC code is already used by another item", () => {
					jest.spyOn(search, "create").mockReturnValue({
						run: () => ({
							getRange: () => [
								{
									id: "bar",
									getValue: (fieldId) => fieldId
								}
							]
						}),
						columns: ["itemid", "displayname"]
					});
					try {
						brdg_item_ue.beforeSubmit(context);
						expect(1).toEqual(0);
					} catch (e) {
						expect(e).toEqual({
							name: "INVALID_DATA",
							message: `SUMMARY: Error validating UPC fields:InvalidData\nDETAILS: This UPC code already exists for item/s: itemid - displayname\n`
						});
					}
				});
				it("does not throw a UPC error when the UPC code is unique", () => {
					jest.spyOn(search, "create").mockReturnValue({
						run: () => ({
							getRange: () => []
						})
					});
					brdg_item_ue.beforeSubmit(context);
					expect(error.create).not.toHaveBeenCalled();
				});
				it("does not validate UPC code when code not available is checked", () => {
					context.newRecord.getValue = ({fieldId}) => {
						const dummyValues = {
							upccode: "",
							custitem_vnyrd_upc_code_not_available: true
						}
						return dummyValues[fieldId] !== undefined ? dummyValues[fieldId] : fieldId;
					};
					brdg_item_ue.beforeSubmit(context);
					expect(search.create).not.toHaveBeenCalled();
				});
				it("throws an error when item is not unique", () => {
					jest.spyOn(search, "create").mockImplementation((param) => ({
						run: () => ({
							getRange: () => [{
								id: "bar",
								getValue: (param) => param
							}]
						}),
						columns: param.columns
					}));
					try {
						brdg_item_ue.beforeSubmit(context);
						expect(1).toEqual(0);
					} catch (e) {
						expect(e).toEqual({
							name: "INVALID_DATA",
							message: `SUMMARY: Error validating UPC fields:InvalidData\nDETAILS: This UPC code already exists for item/s: itemid - displayname\n`
						});
					}
				});
			});
	
			describe("when user event type is EDIT and record type is INVENTORY ITEM", () => {
				beforeEach(() => {
					jest.spyOn(updateItemFieldsLib, "getLastPurchasePrice").mockImplementation(() => {});
					jest.spyOn(updateItemFieldsLib, "setLastPurchasePricesFields").mockImplementation(() => {});
					context.type = "EDIT";
				});
				it("returns early when subsidiary is not Bridge", () => {
					jest.spyOn(bridgeHelperFunctionsLib, "isBridgeSubsidiary").mockReturnValue(false);
					jest.spyOn(updateItemFieldsLib, "getLastPurchasePrice");
					brdg_item_ue.beforeSubmit(context);
					expect(updateItemFieldsLib.getLastPurchasePrice).not.toHaveBeenCalled();
				});
				it("throws a sales price error when validateSalesPrice detects an invalid sales price", () => {
					jest.spyOn(updateItemFieldsLib, "validateSalesPrice").mockReturnValue("CODE");
					try {
						brdg_item_ue.beforeSubmit(context);
						expect(1).toEqual(0);
					} catch (e) {
						expect(e).toEqual({
							name: "ITEM_SALES_PRICE_ERROR",
							message: "This item has a sales price lower then the last purchase price: CODE"
						});
					}
				});
				it("exits successfully when validateSalesPrice does not find invalid sales prices", () => {
					jest.spyOn(updateItemFieldsLib, "validateSalesPrice").mockReturnValue("");
					brdg_item_ue.beforeSubmit(context);
					expect(error.create).not.toHaveBeenCalled();
				});
			});
		});

		describe("when item number is invalid", () => {
			it("throws an error", () => {
				jest.spyOn(query, "runSuiteQL").mockReturnValue({
					asMappedResults: () => [{displayname: "Item A"}]
				});
				try {
					brdg_item_ue.beforeSubmit(context);
					expect(1).toEqual(0);
				} catch (err) {
					expect(err).toEqual("VALUE_NOT_SET");
				}
			});
		});

		describe("when parent category is not 86", () => {
			it("does not set do not use markup field to true", () => {
				jest.spyOn(query, "runSuiteQL").mockReturnValueOnce({
					asMappedResults: () => []
				});
				jest.spyOn(query, "runSuiteQL").mockReturnValueOnce({
					asMappedResults: () => [{parent: 85}]
				});
				jest.spyOn(query, "runSuiteQL").mockReturnValue({
					results: [{
						values: [0.1, "markuprecordid"]
					}]
				});
				brdg_item_ue.beforeSubmit(context);
				expect(record.submitFields).not.toHaveBeenCalled();
			});
		});

	});
});