/**
 * Item Record class representing a VLMR item record
 *
 * @NAmdConfig /SuiteScripts/config.json
 * @NApiVersion 2.1
 * <AUTHOR> <PERSON>
 * @module VlmrItemRecord
 */

define([
	"require",
	"exports",
	"../Classes/vlmd_custom_error_object",
	"N/log",
	"N/error",
	"N/query",
], (/** @type {any} */ require, /** @type {any} */ exports) => {
	/** @type {import("../Classes/vlmd_custom_error_object").CustomErrorObject} */
	const CustomErrorObject = require("../Classes/vlmd_custom_error_object");

	const log = require("N/log");
	const error = require("N/error");
	const query = require("N/query");

	/**
	 * VLMR Item Record class
	 *
	 * @class
	 * @type {import("./spl_item_record").VlmrItemRecord}
	 *
	 */
	class VlmrItemRecord {
		/**
		 * Constructor
		 *
		 * @param {import("N/record").Record | import("N/record").ClientCurrentRecord} itemRecord Item record
		 */
		constructor(itemRecord) {
			this.currentRecord = itemRecord;
			this.id = itemRecord.id;
			this.type = itemRecord.type;
			this.subsidiaries = itemRecord.getValue({
				fieldId: "subsidiary",
			});
			this.categoryId = itemRecord.getValue({
				fieldId: "class",
			});
			this.itemNumber = itemRecord.getValue({
				fieldId: "itemid",
			});
			this.vendorCode = itemRecord.getValue({
				fieldId: "vendorname",
			});
			this.dontAutomateItemName = itemRecord.getValue({
				fieldId: "custitem_override_item_name",
			});
			this.displayComponents = itemRecord.getValue({ fieldId: "printitems" });
		}

		getParentCategoryId(categoryId) {
			return query
				.runSuiteQL({
					query: `SELECT
		  CASE
			  WHEN basicCategory.fullname LIKE '%:%:%' THEN parentsCategory.parent
			  WHEN basicCategory.parent IS NULL THEN basicCategory.id
			  ELSE basicCategory.parent
		  END AS toplevelparent,
		  FROM
		  classification basicCategory
		  LEFT OUTER JOIN classification parentsCategory ON parentsCategory.id = basicCategory.parent
		  WHERE
		  basicCategory.id = ${categoryId}`,
				})
				.asMappedResults()[0].toplevelparent;
		}

		/**
		 * Generates item name based on product category
		 *
		 * @returns {object}
		 */
		generateItemName() {
			const customError = new CustomErrorObject();

			try {
				if (!this.categoryId) {
					//Category field was empty
					return;
				}
				const parentCategoryId = this.categoryId
					? this.getParentCategoryId(this.categoryId)
					: null;

				const sqlQuery = `SELECT custrecord_vlmr_last_item_number, custrecord_vlmd_category_abbreviation  
				  FROM classification WHERE id = ${parentCategoryId}`;

				const sqlResults = query
					.runSuiteQL({
						query: sqlQuery,
					})
					.asMappedResults()[0];

				let lastItemNumber = sqlResults.custrecord_vlmr_last_item_number;

				const categoryAbbreviation =
					sqlResults.custrecord_vlmd_category_abbreviation;

				if (!categoryAbbreviation) {
					//No product category abreviation assigned to the parent category
					throw customError.updateError({
						errorType: customError.ErrorTypes.MISSING_VALUE,
						summary: "MISSING_PRODUCT_CATEGORY_ABBREVIATION",
						details: `The top level product category assigned (ID: ${parentCategoryId}) has no abbreviation set. Please assign a product category abbreviation before saving the item. <a href='/app/common/otherlists/classtype.nl?id=${parentCategoryId}'>Click here to update product category.</a>`,
					});
				}

				if (!lastItemNumber) {
					//No last item number assigned to the parent category
					throw customError.updateError({
						errorType: customError.ErrorTypes.MISSING_VALUE,
						summary: "MISSING_LAST_ITEM_NUMBER",
						details: `The top level product category assigned (ID: ${parentCategoryId}) does not have a "vlmr last item number". Please add the last item number for the category before saving the item. <a href='/app/common/otherlists/classtype.nl?id=${parentCategoryId}'>Click here to update product category.</a>`,
					});
				}

				const itemNumber = getNextAvailableItemNumber(
					lastItemNumber,
					categoryAbbreviation
				);

				const itemName = categoryAbbreviation + itemNumber;
				return { parentCategoryId, itemNumber, itemName };
			} catch (e) {
				customError.throwError({
					summaryText: `GENERATE_ITEM_NAME`,
					error: e,
					errorWillBeGrouped: true,
				});
			}
		}
	}

	exports.VlmrItemRecord = VlmrItemRecord;

	function getNextAvailableItemNumber(itemNumber, categoryAbbreviation) {
		var increment = 0;

		function runSql() {
			const sqlQuery = `select itemid from item where itemid = '${
				"V"+categoryAbbreviation + (itemNumber + increment)
			}'`;
			const results = query
				.runSuiteQL({
					query: sqlQuery,
				})
				.asMappedResults()[0];
			if (results) {
				increment++;
				runSql();
			} else {
				return;
			}
		}

		runSql();
		return itemNumber + increment;
	}

	return VlmrItemRecord;
});
