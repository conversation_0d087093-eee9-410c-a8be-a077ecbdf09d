// @ts-nocheck
import CustomErrorObject from "../../Classes/vlmd_custom_error_object";

beforeEach(() => {
	jest.resetModules();
});
global.alert = jest.fn();

describe("vlmd_custom_error_object", () => {
	let customErrorObject;
	beforeEach(() => {
		customErrorObject = new CustomErrorObject("dummy message");
	});

	describe("updateError", () => {
		beforeEach(() => {
			customErrorObject.updateError({
				errorType: "TypeError",
				summary: "summary",
				details: "details",
			});
		});
		it("Sets the error type", () => {
			expect(customErrorObject.ERROR_TYPE).toEqual("TYPE_ERROR");
		});
		it("Sets the error summary", () => {
			expect(customErrorObject.summary).toEqual("summary");
		});
		it("Sets the error details", () => {
			expect(customErrorObject.details).toEqual("details");
		});
	});
});
