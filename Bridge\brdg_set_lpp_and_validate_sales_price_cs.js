/**
 * @description Gets and sets the last purchase price, validates the sales price, adds executive price levels
 *
 * </br><b>Deployed On:</b> Kit & Inventory Items
 * </br><b>Execution Context:</b> ALL
 * </br><b>Event Type/Mode:</b> EDIT
 * </br><b>Entry Points:</b> pageInit, saveRecord
 *
 * @NApiVersion 2.1
 * @NScriptType Clientscript
 * @NAmdConfig /SuiteScripts/config.json
 *
 * <AUTHOR> Boxer
 * @module brdg_set_lpp_and_validate_sales_price_cs
 */

define([
	"require",
	"N/log",
	"../Classes/brdg_item_client_record",
	"../Classes/spl_ui_notification_object",
	"./Libraries/brdg_update_item_lib",
	"./Libraries/brdg_helper_functions_lib",
], function (/** @type {any} */ require) {
	const log = require("N/log");
	/** @type {import("../Classes/brdg_item_record").BridgeItemClientRecord} */
	const BridgeItemClientRecord = require("../Classes/brdg_item_client_record");
	/** @type {import("../Classes/spl_ui_notification_object").UINotification} */
	const UINotification = require("../Classes/spl_ui_notification_object");
	const updateItemFieldsLib = require("./Libraries/brdg_update_item_lib");
	const bridgeHelperFunctionsLib = require("./Libraries/brdg_helper_functions_lib");

	//Page mode is unaccessible on saveRecord so getting it from pageInit
	let /** @type {string} */ pageMode;
	let /** @type {number[]} */ bridgeSubsidiaries = [];

	/**
	 * Save the mode to a global variable
	 *
	 * @param {import("N/types").EntryPoints.Client.pageInitContext} context Page init context
	 * @returns {void}
	 */
	function pageInit(context) {
		bridgeSubsidiaries = bridgeHelperFunctionsLib.getBridgeSubsidiaries();
		pageMode = context.mode;
	}

	/**
	 * Save the mode to a global variable
	 *
	 * @param {import("N/types").EntryPoints.Client.saveRecordContext} context Save record context
	 * @returns {boolean} Returns true when validations pass
	 */
	function saveRecord(context) {
		const item = new BridgeItemClientRecord(context.currentRecord);

		if (
			!bridgeHelperFunctionsLib.isBridgeSubsidiary(
				item.subsidiaries,
				bridgeSubsidiaries
			)
		) {
			return true;
		}

		const uiNotification = new UINotification();

		if (pageMode === "edit") {
			try {
				const lppObj = updateItemFieldsLib.getLastPurchasePrice(
					item.id,
					item.type
				);
				updateItemFieldsLib.setLastPurchasePricesFields(item.record, lppObj);
				const messageText = updateItemFieldsLib.validateSalesPrice(
					lppObj,
					item.record
				);
				const setSecondQuantity = updateItemFieldsLib.checkForQuantityPricing(
					item.record
				);
				updateItemFieldsLib.addExecutivePricingPerStore(
					item.record,
					lppObj,
					setSecondQuantity
				);
				updateItemFieldsLib.addCostPricePerStore(
					item.record,
					lppObj,
					setSecondQuantity
				);
				if (messageText) {
					uiNotification.addNotification(
						"errors",
						"This item has a sales price lower then the last purchase price:" +
							" \n" +
							" \n" +
							messageText +
							"Please correct before saving."
					);
				}
			} catch (e) {
				log.error(
					"Error setting the Lightspeed Last Purchase Price Fields: ",
					e
				);
				throw "Error! " + e;
			}
		}

		uiNotification.displayNotifications();

		return uiNotification.errors.length < 1;
	}

	return {
		pageInit,
		saveRecord,
	};
});
