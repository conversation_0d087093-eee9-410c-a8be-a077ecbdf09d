/**
 * @description Validate the rate entered on the bill (the cost of the item) is below the sales price on the item record for the price level associated with this location
 *
 * </br><b>Deployed On:</b> Vendor Bill
 * </br><b>Exectuion Context:</b> ALL
 * </br><b>Event Type/Mode:</b> ALL
 * </br><b>Entry Points:</b> validateLine, saveRecord,
 *
 * @NApiVersion 2.1
 * @NScriptType ClientScript
 * @NAmdConfig /SuiteScripts/config.json
 *
 * <AUTHOR>
 * @module brdg_validate_sales_price_cs
 */

define([
	"N/log",
	"N/error",
	"N/email",
	"N/runtime",
	"BridgeHelperFunctionsLib",
], function (log, error, email, runtime, bridgeHelperFunctionsLib) {
	const user = runtime.getCurrentUser();
	let /** @type {number[]} */ bridgeSubsidiaries = [];

	function _handleErrors(messageName, messageDetails) {
		alert(messageName + " : " + messageDetails);
	}

	function getItemsInfo(bill) {
		const itemObjArr = [];
		const lineCount = bill.getLineCount({ sublistId: "item" });
		if (!lineCount) {
			return;
		}
		const location = bill.getValue("location");
		if (!location) {
			throw "No location for this item";
		}

		try {
			for (var i = 0; i < lineCount; i++) {
				const itemId = bill.getSublistValue({
					sublistId: "item",
					fieldId: "item",
					line: i,
				});
				const itemCost = bill.getSublistValue({
					sublistId: "item",
					fieldId: "rate",
					line: i,
				});
				const itemDisplay = bill.getSublistValue({
					sublistId: "item",
					fieldId: "item_display",
					line: i,
				});

				//Get the associated price level for this location
				const priceLevelId = bridgeHelperFunctionsLib.getValueByRelatedValue(
					"location",
					location,
					"priceLevel"
				);

				const billPurchaseUnitRate = bill.getSublistValue({
					//The conversion rate from the transaction line
					sublistId: "item",
					fieldId: "unitconversionrate",
					line: i,
				});

				const itemObj = {
					itemId,
					itemCost,
					itemDisplay,
					priceLevelId,
					billPurchaseUnitRate,
				};
				itemObjArr.push(itemObj);
			}
			return itemObjArr;
		} catch (e) {
			_handleErrors("Error getting items objects", e.message);
		}
	}

	/**
	 * Save the Bridge subsidiary IDs to a global variable
	 *
	 * @param {import("N/types").EntryPoints.Client.pageInitContext} context Page init context
	 * @returns {void}
	 */
	function pageInit(context) {
		bridgeSubsidiaries = bridgeHelperFunctionsLib.getBridgeSubsidiaries();
	}

	function validateLine(context) {
		if (context.sublistId != "item") {
			return true;
		}
		const bill = context.currentRecord;
		const subsidiariesArr = bill.getValue({
			fieldId: "subsidiary",
		});
		const isBridgeSubsidiary = bridgeHelperFunctionsLib.isBridgeSubsidiary(
			subsidiariesArr,
			bridgeSubsidiaries
		);

		try {
			if (!isBridgeSubsidiary) {
				return true;
			}

			const itemId = bill.getCurrentSublistValue({
				sublistId: "item",
				fieldId: "item",
			});

			const itemDisplay = bill.getCurrentSublistValue({
				sublistId: "item",
				fieldId: "item_display",
			});

			//When the user presses enter on an empty line
			if (!itemId) {
				return true;
			}

			var itemCost = bill.getCurrentSublistValue({
				//The costing calculation for inventory items
				sublistId: "item",
				fieldId: "rate",
			});

			if (!itemCost) {
				throw "No cost for this item";
			}

			const location =
				bill.getCurrentSublistValue({
					sublistId: "item",
					fieldId: "location",
				}) ?? bill.getValue("location");

			if (!location) {
				throw "No location for this item";
			}

			//Get the associated price level for this location
			const priceLevelId = bridgeHelperFunctionsLib.getValueByRelatedValue(
				"location",
				location,
				"priceLevel"
			);

			if (!priceLevelId) {
				throw "No price level returned";
			}

			const billPurchaseUnitRate = bill.getCurrentSublistValue({
				//The conversion rate from this transaction line
				sublistId: "item",
				fieldId: "unitconversionrate",
				line,
			});

			const salesPrice = bridgeHelperFunctionsLib.getSalesPriceForLocation(
				itemId,
				priceLevelId,
				billPurchaseUnitRate
			);

			if (!salesPrice) {
				throw "No sales price returned";
			}

			if (salesPrice <= itemCost) {
				alert(
					`The sales price $${salesPrice} for this item ${itemDisplay}, is below the cost.`
				);
			} else if (salesPrice >= itemCost) {
			}
		} catch (e) {
			_handleErrors("Error validating sales price", e.message);
		}

		return true;
	}

	function saveRecord(context) {
		try {
			const bill = context.currentRecord;
			const subsidiariesArr = bill.getValue({
				fieldId: "subsidiary",
			});
			const isBridgeSubsidiary = bridgeHelperFunctionsLib.isBridgeSubsidiary(
				subsidiariesArr,
				bridgeSubsidiaries
			);

			if (!isBridgeSubsidiary) {
				return true;
			}

			const vendor = bill.getText("entity");
			const billNumber =
				bill.getText("tranid") != "To Be Generated"
					? bill.getText("tranid")
					: "NEW BILL";

			let itemsObjArr = getItemsInfo(bill);
			if (itemsObjArr) {
				itemsObjArr.forEach((itemObj) => {
					const itemId = itemObj.itemId;
					const priceLevelId = itemObj.priceLevelId;
					const itemDisplay = itemObj.itemDisplay;
					const billPurchaseUnitRate = itemObj.billPurchaseUnitRate;
					const salesPrice = bridgeHelperFunctionsLib.getSalesPriceForLocation(
						itemId,
						priceLevelId,
						billPurchaseUnitRate
					);

					if (!salesPrice) {
						throw "No sales price returned";
					}

					if (salesPrice <= itemObj.itemCost) {
						alert(
							`The sales price $${salesPrice} for this item ${itemDisplay}, , is below the cost.`
						);

						email.send({
							author: 3288,
							recipients: [
								"<EMAIL>",
								"<EMAIL>",
								user.email,
							],
							subject: "Item on Vendor Bill is Below Cost",
							body: `The following item was entered below cost on the vendor bill: <br/><br/>${billNumber} from ${vendor}<br/>${itemDisplay}
							<br/><br/><a href = 'app/accounting/transactions/vendbill.nl?id=${bill.id}'>Click here to go to the bill</a>`,
							relatedRecords: {
								transactionId: bill.id,
							},
						});
					}
				});
			}
			return true;
		} catch (e) {
			_handleErrors("Error validating sales price", e.message);

			return true;
		}
	}

	return {
		pageInit,
		validateLine,
		saveRecord,
	};
});
