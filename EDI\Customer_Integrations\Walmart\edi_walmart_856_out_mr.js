/**
 * @description Generate the Outgoing 856 File (Item Fulfillment) for Walmart
 *
 * @NApiVersion 2.1
 * @NScriptType MapReduceScript
 * @NAmdConfig /SuiteScripts/config.json
 * <AUTHOR> <<EMAIL>>
 */

define([
    "require",
    "N/log",
    "N/record",
    "../../Classes/Models/File/edi_file",
    "../../Classes/Models/File/edi_outgoing",
    "../../Classes/Models/Partner/Customer/edi_walmart",
    "../../Classes/Models/Server/edi_mft_server",
    "../../Classes/Decorators/edi_summary",
    "../../Classes/Decorators/856/edi_856",
    "../../Classes/Decorators/856/edi_856_item_fulfillment_processor",
    "../../Classes/Models/Transaction/edi_item_fulfillment",
    "../../../Classes/vlmd_custom_error_object",
], (/** @type {any} */ require) => {
    const log = require("N/log");
    const record = require("N/record");
    const { DocumentType } = require("../../Classes/Models/File/edi_file");
    const { EDIOutgoing } = require("../../Classes/Models/File/edi_outgoing");
    const { EDIWalmart } = require("../../Classes/Models/Partner/Customer/edi_walmart");
    const { EDIMFTServer } = require("../../Classes/Models/Server/edi_mft_server");
    const { EDISummary } = require("../../Classes/Decorators/edi_summary");
    const { EDI856 } = require("../../Classes/Decorators/856/edi_856");
    const { EDI856ItemFulfillmentProcessor } = require("../../Classes/Decorators/856/edi_856_item_fulfillment_processor");
    const { EDIItemFulfillment } = require("../../Classes/Models/Transaction/edi_item_fulfillment");
    /** @type {import("../../../Classes/vlmd_custom_error_object").CustomErrorObject} */
    const CustomErrorObject = require("../../../Classes/vlmd_custom_error_object");

    /**
     * Retrieve item fulfillment records whose parent is 3729 Walmart
     *
     * @typedef {import("../../Classes/Interfaces/Models/File/edi_outgoing").SuiteQLObjectReference} SuiteQLObjectReference
     * @returns {SuiteQLObjectReference|undefined} Object containing SuiteQL string
     */
    function getInputData() {
        const customError = new CustomErrorObject();
        try {
            const ediFile = new EDIOutgoing();
            new EDI856({
                processor: new EDI856ItemFulfillmentProcessor({
                    customError
                }),
                type: "Item Fulfillment",
                typeId: DocumentType.OUTGOING_856,
            }).decorate(ediFile);

            return ediFile.load({ customerParent: 8454 });
        } catch (/** @type {any} */ err) {
            log.error({ title: err.name, details: err.message });
            customError.throwError({
                summaryText: "GET_INPUT_DATA",
                error: err,
                recordId: null,
                recordName: null,
                recordType: null,
                errorWillBeGrouped: false,
            });
        }
    }

    /**
     * Create the EDI File based on an Item Fulfillment
     *
     * @param {import("N/types").EntryPoints.MapReduce.mapContext} context Map context
     * @returns {void}
     */
    function map(context) {
        const [ index, id, type, otherrefnum, tranid, createdfrom, trandate, shipCarrier, ladingQuantity, shipWeight, trackingInformation, trackingNumbers, street, city, state, zip, customerFullName, controlNumber ] = JSON.parse(context.value).values;
        const customError = new CustomErrorObject();
        try {
            const ediFile = new EDIOutgoing();
            const walmart = new EDIWalmart({
                direction: "out",
                transactionType: "856",
            });
            ediFile.server = new EDIMFTServer({
                partner: walmart,
                customError,
            });
            ediFile.server.connect({
                directory: walmart.referenceDirectory,
                username: "FTPAdmin",
                passwordGuid: "0897309265eb4d3c937733ec311f0627",
            });
            new EDI856({
                processor: new EDI856ItemFulfillmentProcessor({
                    customError,
                    itemFulfillment: new EDIItemFulfillment({
                        customError,
                        index,
                        id,
                        type: record.Type.ITEM_FULFILLMENT,
                        customerParent: 8454,
                        otherrefnum,
                        tranid,
                        createdfrom,
                        trandate,
                        shipCarrier,
                        ladingQuantity,
                        shipWeight,
                        trackingInformation,
                        trackingNumbers,
                        street,
                        city,
                        state,
                        zip,
                        customerFullName,
                        customerEntityId: walmart.code,
                        customerGLN: walmart.gln,
                        controlNumber,
                        supplierName: "Creoh USA, LLC.",
                        supplierStreet: "6595 Delilah Road",
                        supplierCity: "Egg Harbor Township",
                        supplierState: "NJ",
                        supplierZip: "8234",
                    }),
                    parsingInformation: walmart.getParsingInformation({
                        delimiters: walmart.delimiters,
                        direction: "out",
                    }),
                    template: walmart.template.OUT[856],
                    purchasingSoftwareId: walmart.purchasingSoftwareId,
                }),
                type: "Item Fulfillment",
                typeId: DocumentType.OUTGOING_856,
            }).decorate(ediFile);
            ediFile.process();
            ediFile.create();
            ediFile.upload({ targetStationId: walmart.targetStationId });
            ediFile.save(); // TODO: Remove save once we're confident with the MFT and SFTP upload process
            const documentControlNumberId = ediFile.complete();

            context.write(tranid, { id: documentControlNumberId, controlNumber: ediFile.processor.itemFulfillment.controlNumber });
        } catch (/** @type {any} */ err) {
            log.error({
                title: "EDI Outgoing 856 Walmart (MAP)",
                details: err,
            });
            customError.throwError({
                summaryText: "MAP",
                error: err,
                recordId: id,
                recordName: tranid,
                recordType: type,
                errorWillBeGrouped: true,
            });
        }
    }

    /**
     * Log the number of files processed and failed
     *
     * @param {import("N/types").EntryPoints.MapReduce.summarizeContext} context Summarize context
     * @returns {void}
     */
    function summarize(context) {
        const customError = new CustomErrorObject();
        try {
            let transactionsProcessed = 0;
            let transactionsNotProcessed = 0;

            // Update new document control number of EDI Integration record for Walmart
            let newDocumentControlNumber = 0;
            context.output.iterator().each((key, controlNumberObj) => {
                newDocumentControlNumber = Math.max(newDocumentControlNumber, Number(JSON.parse(controlNumberObj).controlNumber));
                return true;
            });
            log.audit("New Control Number:", newDocumentControlNumber);
            newDocumentControlNumber && record.submitFields({
                type: "customrecord_vlmd_edi_integration",
                id: 1076, // 3729 Walmart
                values: {
                    custrecord_document_control_number: newDocumentControlNumber.toString(),
                },
            });
    
            // Create EDI Transaction record for all transactions
            const ediSummary = new EDISummary({
                transactionType: "Item Fulfillment",
                partner: new EDIWalmart({
                    direction: "out",
                    transactionType: "856",
                }),
                documentType: DocumentType.OUTGOING_856
            });
            ediSummary.summarizeProcess(context).pushEdiEmailInfoToDB();

            context.mapSummary.keys.iterator().each((key, executionCount, completionState) => {
                if (completionState === 'COMPLETE'){
                    transactionsProcessed++;
                } else {
                    transactionsNotProcessed++;
                }
                return true;
            });
    
            log.audit({
                title: "EDI Outgoing 856 Walmart (summarize)",
                details: `# of Transactions processed: ${transactionsProcessed}, # of Transactions skipped: ${transactionsNotProcessed}`,
            });
        } catch (/** @type {any} */ err) {
            log.error({
                title: "EDI Outgoing 856 Walmart (SUMMARIZE)",
                details: err,
            });
            customError.throwError({
                summaryText: "SUMMARIZE",
                error: err,
                recordId: null,
                recordName: null,
                recordType: null,
                errorWillBeGrouped: false,
            });
        }
    }

    return {
        getInputData,
        map,
        summarize,
    }
});