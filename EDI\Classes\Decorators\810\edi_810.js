/**
 * @description Represents the Invoice or Bill object used within an EDI context
 *
 * @NApiVersion 2.1
 * <AUTHOR> <<EMAIL>>
 */

define([
    "exports",
    "require",
    "N/error",
    "N/file",
    "N/log",
    "../edi_decorator",
    "../../Models/File/edi_incoming",
    "../../Models/File/edi_outgoing",
    "../../Models/Server/edi_mft_server",
], (/** @type {any} */ exports, /** @type {any} */ require) => {
    const error = require("N/error");
    const file = require("N/file");
    const log = require("N/log");
    const { EDIDecorator } = require("../edi_decorator");
    const { EDIIncoming } = require("../../Models/File/edi_incoming");
    const { EDIOutgoing } = require("../../Models/File/edi_outgoing");
    const { EDIMFTServer } = require("../../Models/Server/edi_mft_server");


    /**
     * 810 Class
     * 
     * @class
     * @typedef {import("../../Interfaces/Models/File/edi_file").EDIFileInterface} EDIFileInterface
     * @typedef {import("../../Interfaces/Models/File/edi_file").EDIPostProcessEmail} EDIPostProcessEmail
     * @typedef {import("../../Interfaces/Models/File/edi_outgoing").SuiteQLObjectReference} SuiteQLObjectReference
     * @typedef {import("../../Interfaces/Models/Server/edi_server").EDIServerInterface} EDIServerInterface
     * @typedef {import("../../Interfaces/Models/Server/edi_server").EDIMFTServerInterface} EDIMFTServerInterface
     * @typedef {import("../../Interfaces/Decorators/810/edi_810").EDI810Interface} EDI810Interface
     * @typedef {import("../../Interfaces/Decorators/edi_decorator").DecoratorParams} DecoratorParams
     * @typedef {import("./edi_810_parser").EDI810Parser} EDI810Parser
     * @typedef {import("./edi_810_processor").EDI810Processor} EDI810Processor
     * @typedef {import("../../../../Classes/vlmd_custom_error_object").CustomErrorObject} CustomErrorObject
     * @implements {EDI810Interface}
     * @extends {EDIDecorator}
     */

    class EDI810 extends EDIDecorator {
        /** @param {DecoratorParams} params */
        constructor(params) {
            super();
            
            //EDI File
            /** @type {CustomErrorObject} */
            this.customError;
            /** @type {EDI810Parser} */
            this.parser = params.parser;
            /** @type {EDI810Processor} */
            this.processor = params.processor;
            /** @type {EDIServerInterface | EDIMFTServerInterface} */
            this.server;
            /** @type {string} */
            this.type = params.type;
            /** @type {number} */
            this.typeId = params.typeId;

            // EDI Outgoing
            /** @type {file.File} */
            this.file;
            /** @type {string} */
            this.fileName = "CRH_810";
            /** @type {number} */
            this.fileId = 0;
            /** @type {number} */
            this.folderId = 6511263;
            /** @type {string} */
            this.emailSubject = ""

            // EDI Incoming
            /** @type {any} */
            this.transaction = null;
        }

        /**
         * Decorate the EDI File with 810 functions
         *
         * @param {EDIFileInterface} ediFile Object to decorate
         */
        decorate(ediFile) {
            log.debug("EDI 810 (decorate)", `Decorating the EDI File: ${ediFile.ediType}`);
            ediFile.type = this.type;
            ediFile.typeId = this.typeId;
            if (ediFile instanceof EDIIncoming) {
                ediFile.parser = this.parser;
                ediFile.parse = this.parse;
                ediFile.transform = this.transform;
                ediFile.summarize = this.summarize;
                ediFile.archive = this.archive;
            } else if (ediFile instanceof EDIOutgoing) {
                ediFile.processor = this.processor;
                ediFile.fileName = this.fileName;
                ediFile.folderId = this.folderId;
                ediFile.emailSubject = this.emailSubject;
                ediFile.load = this.load;
                ediFile.process = this.process;
                ediFile.create = this.create;
                ediFile.save = this.save;
                ediFile.email = this.email;
                ediFile.upload = this.upload;
                ediFile.complete = this.complete;
            }
        }

        /**
         * Parse the incoming EDI File
         *
         * @returns {void}
         */
        parse() {
            this.parser?.parse();
        }

        /**
         * Transform the EDI data to a NetSuite record
         *
         * @returns {void}
         */
        transform() {
            this.parser?.transform();
        }

        /**
         * Save the EDI data as a NetSuite EDI Transaction
         * Override the return type of parent
         *
         * @returns {EDIPostProcessEmail|null}
         */
        summarize() {
            this.parser?.summarize();

            return this.parser?.email ?? null;
        }

        /**
         * Move the EDI File to the Reference directory
         *
         * @param {object} params Archive params
         * @param {string} params.filename File name
         * @param {string} params.source Source directory
         * @param {string} params.target Target directory
         * @param {string} params.content File content to upload
         * @returns {void}
         */
        archive({filename, source, target}) {
            // trace the path to the target relative to the source directory
            // '/edi/reference/810' -> '../../../edi/reference/810'
            const backTrackPath = new Array(source.split("/").length - (source.startsWith("/") ? 1 : 0) - (target.endsWith("/") ? 1 : 0))
                .fill("..")
                .join("/");

            log.debug({
                title: "EDI 810 (archive)",
                details: JSON.stringify({
                    filename, target: `${backTrackPath}${target}/${filename}`,
                })
            });

            this.server?.connection?.move({
                from: filename,
                to: `${backTrackPath}${target}/${filename}`,
            });
        }

        /**
         * Return the query string or Search object to load the NetSuite records
         *
         * @param {{[key:string]: any}} [params] Parameters
         * @returns {SuiteQLObjectReference} Query string to retrieve transaction records
         */
        load(params) {
            return this.processor.load(params);
        };

        /**
         * Process the record object using the decorator processor
         *
         * @returns {string} Invoice or Credit Memo record as EDI File string
         */
        process() { return this.processor.process() };
        
        /**
         * Create the NetSuite File Record
         *
         * @returns {void}
         */
        create() {
            const createdFileObj = this.processor.create({ fileName: this.fileName });
            if (createdFileObj) {
                this.file = createdFileObj; 
            }
        };
    
        /**
         * Save the NetSuite File to File Cabinet
         *
         * @returns {void}
         */
        save() {
            try {
                if (this.file) {
                    log.debug({ title: "EDI 810 (save)", details: `Saving file in Folder ${this.folderId}..` });
                    this.file.folder = this.folderId;
                    this.fileId = this.file.save();
                } else {
                    log.error({ title: "EDI 810 (save)", details: "No file object to save." });
                    throw error.create({
                        name: "MISSING_OBJECT",
                        message: "No file object to save.",
                    });
                }
            } catch (/** @type {any} */ err) {
                log.error({ title: "EDI 810 (save)", details: `Error saving file object: ${err}` });
                throw this.customError?.updateError({
                    errorType: this.customError?.ErrorTypes.FILE_NOT_SAVED,
                    summary: "FAILED_TO_SAVE_OUTGOING_810",
                    details: `Error saving file object: ${err}`,
                });
            }
        };

        /**
         * Send the NetSuite File as an E-mail attachment
         *
         * @param {{[key:string]: any}} [params] Parameters
         * @returns {void}
         */
        email(params) {};

        /**
         * Upload the NetSuite File to the Partner's SFTP Server
         *
         * @param {object} params Parameters
         * @param {string} [params.targetStationId] Target Station
         * @returns {void}
         */
        upload(params) {
            try {
                log.debug({ title: "EDI 810 (upload)", details: `Uploading file to Partner's production directory..` });
                if (this.file && this.server?.connection) {
                    this.server.connection.upload({
                        file: this.file,
                        replaceExisting: true,
                    });
                } else {
                    log.error({ title: "ERROR: EDI 810 (upload)", details: "Cannot upload to partner's server. No connection or file created." })
                }

                if (this.server instanceof EDIMFTServer && params.targetStationId) {
                    const response = this.server.authorize().send({
                        file: this.file,
                        subject: this.file.name,
                        targetStationId: params.targetStationId,
                    });
                    log.audit({ title: "MFT Server Response", details: JSON.stringify(response) });
                } else if (this.server instanceof EDIMFTServer && !params.targetStationId) {
                    log.error({
                        title: "ERROR: EDI 810 (upload)",
                        details: `Cannot upload to partner's server. Target Station ID is not available: ${JSON.stringify(params)}`,
                    });
                }
            } catch (/** @type {any} */ err) {
                log.error({ title: "EDI 810 (upload)", details: `Error uploading file: ${err}` });
                throw this.customError?.updateError({
                    errorType: this.customError.ErrorTypes.FILE_NOT_SAVED,
                    summary: "FILE_NOT_UPLOADED",
                    details: `Error uploading file: ${err}`,
                });
            }
        };

        /**
         * Mark the transaction as processed
         *
         * @returns {number} Document Control Number record ID
         */
        complete() { return this.processor.complete(); };
    }

    exports.EDI810 = EDI810;
});