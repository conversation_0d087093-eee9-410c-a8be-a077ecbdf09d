//@ts-ignore
define(["N/log", "N/search", "N/query"], function (log, search, query) {
	function getIntegrationFolders(purchasingSoftwareId) {
		let sqlQuery = `SELECT
		BUILTIN.DF(integration.custrecord_spl_prchsng_fclty) AS integrationAccountNumber,
		integrationCustomer.companyname,
	 FROM
		customrecord_vlmd_edi_integration AS integration 
		INNER JOIN
		   customer AS integrationCustomer 
		   ON integrationCustomer.id = integration.custrecord_spl_prchsng_fclty 
	 WHERE
		integration.custrecord_edi_intgrtn_prchsng_sftwr = ?`;

		let sqlResults = query.runSuiteQL({
			query: sqlQuery,
			params: [purchasingSoftwareId],
		});

		if (sqlResults.results.length <= 0) {
			log.error("Error - no  SQL results found.", sqlResults.results.length);
		}

		const parsedResultsArr = [];

		sqlResults.results.forEach((resultsObj) => {
			parsedResultsArr.push({
				accountNumber: resultsObj.values[0],
				integrationName: resultsObj.values[1],
			});
		});

		return parsedResultsArr;
	}

	function getIntegrationAccountNumberForParent(parentInternalId) {
		const sqlQuery = `SELECT
		BUILTIN.DF(integration.custrecord_spl_prchsng_fclty) AS integrationFolder,
	 FROM
		customrecord_vlmd_edi_integration AS integration 
		JOIN
		   map_customrecord_vlmd_edi_integration_custrecord_edi_intgrtn_prnt_fclty map 
		   ON map.mapone = integration.id 
		JOIN
		   customer AS parent 
		   ON map.maptwo = parent.id 
	 WHERE
		parent.id = ?`;

		let queryResults = query.runSuiteQL({
			query: sqlQuery,
			params: [parentInternalId],
		});

		if (queryResults.results.length > 0) {
			return queryResults.results[0].values[0];
		}
	}

	/**
	 * Get a list of all parent internal ids, account numbers, company names, and purchasing facility account numbers for this integration, integrated for this document type and if relevant
	 *
	 * @param {number} purchasingSoftwareId
	 * @param {number} documentTypeId
	 * @param {number} integrationCustomerIdParam Optional param from deployment record
	 * @returns
	 */
	function getInfoForOutgoingDocument(
		purchasingSoftwareId,
		documentTypeId,
		integrationCustomerIdParam
	) {
		const sqlQuery = `SELECT
		parent.id as customerId,
		parent.entityid as accountNumber,
		parent.companyname as customerName,
		BUILTIN.DF(integration.custrecord_spl_prchsng_fclty) as integrationFolder, 
		integration.custrecord_spl_prchsng_fclty as purchasingCompanyId,
		integration.id as integrationId
	 FROM
		customrecord_vlmd_edi_integration AS integration 
		JOIN
		   map_customrecord_vlmd_edi_integration_custrecord_edi_intgrtn_prnt_fclty map 
		   ON map.mapone = integration.id 
		JOIN
		   customer AS parent 
		   ON map.maptwo = parent.id 
	 WHERE
		integration.custrecord_edi_intgrtn_prchsng_sftwr =  ${purchasingSoftwareId}
		AND BUILTIN.MNFILTER(integration.custrecord_edi_intgrtn_documents, 'MN_INCLUDE', '', 'FALSE', NULL, '${documentTypeId}') = 'T'
		AND integration.isinactive = 'F'
		${
			integrationCustomerIdParam
				? "AND integration.custrecord_spl_prchsng_fclty.id = " +
				  integrationCustomerIdParam
				: ""
		}`;

		let sqlResults = query.runSuiteQL({
			query: sqlQuery,
		});

		if (sqlResults.results.length <= 0) {
			throw {
				name: "NO_VALUE_RETURNED_FOR_QUERY",
				message: `No parent values were found for this query: \n${sqlQuery}`,
			};
		}

		const parsedResultsArr = [];

		sqlResults.results.forEach((resultsObj) => {
			parsedResultsArr.push({
				customerId: resultsObj.values[0],
				accountNumber: resultsObj.values[1],
				customerName: resultsObj.values[2],
				integrationFolder: resultsObj.values[3],
				purchasingCompanyId: resultsObj.values[4],
				integrationId: resultsObj.values[5],
			});
		});

		return parsedResultsArr;
	}

	return {
		getIntegrationFolders,
		getIntegrationAccountNumberForParent,
		getInfoForOutgoingDocument,
	};
});
