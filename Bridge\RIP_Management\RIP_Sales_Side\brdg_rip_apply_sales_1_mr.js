/**
 * @description Iterates over all BRDG RIP bill credit lines and applies the rates to sales transaction lines to capture accurate COGS
 *
 * </br><b>Schedule:</b> Runs the first of every month
 *
 * @NApiVersion 2.1
 * @NScriptType MapReduceScript
 * @NAmdConfig /SuiteScripts/config.json
 *
 * <AUTHOR>
 * @module brdg_rip_apply_sales_mr
 */

define([
  "require",
  "N/task",
  "N/search",
  "N/record",
  "N/runtime",
  "../../../Classes/vlmd_custom_error_object",
  "../../../Classes/vlmd_mr_summary_handling",
], (/** @type {any} */ require) => {
  const task = require("N/task");
  const search = require("N/search");
  const record = require("N/record");
  const runtime = require("N/runtime");

  /** @type {import("../../../Classes/vlmd_custom_error_object").CustomErrorObject} */

  const CustomErrorObject = require("../../../Classes/vlmd_custom_error_object");
  const customErrorObject = new CustomErrorObject();

  function getInputData() {
    try {
      const specificBillCredit = runtime.getCurrentScript().getParameter({
        name: "custscript_apply_specific_bill_credit",
      });

      //Sql query to pull all RIP bill credit lines that have not been fully applied to a sales transaction line
      const sqlQuery = /*sql*/ `
      SELECT
      billcreditline.custcol_rip_item_link item_id,
      NVL(billcreditline.custcol_rip_quantity_remaining, 
      (
         ABS(billcreditline.quantity) 
      )
      *saleUnit.conversionrate) quantity_remaining,
      billcreditline.rateamount / saleUnit.conversionrate rate,
      billcredit.trandate transaction_date,
      billcredit.id transaction_id,
      (
         ABS(billcreditline.quantity) 
      )
      *saleUnit.conversionrate original_quantity_available,
      billcreditline.subsidiary subsidiary,
      billLine.itemType,
   FROM
      transactionline billcreditline 
      JOIN
         transaction billcredit 
         ON billcredit.id = billcreditline.transaction 
      LEFT JOIN
         transaction bill 
         ON bill.id = billcredit.custbody_associated_vendor_bill 
      LEFT JOIN
         transactionline billLine 
         ON billLine.transaction = bill.id 
         AND billLine.item = billcreditline.custcol_rip_item_link 
      LEFT JOIN
         unitstypeuom saleUnit 
         ON billLine.units = saleUnit.internalid 
   WHERE
      billcredit. custbody_rip_vendor_credit = 'T' 	--custbody_rip_vendor_exclude_from_ar = 'T'
      --SB
      AND billcredit.type = 'VendCred' 
      AND billcreditline.mainline = 'F' 
      AND 
      (
         billcreditline.custcol_item_rip_application_complete IS NULL 
         OR billcreditline.custcol_item_rip_application_complete = 'F' 
      )
      AND 
      (
         billcreditline.custcol_rip_quantity_remaining > 0 
         OR billcreditline.custcol_rip_quantity_remaining IS NULL
      )
      AND EXISTS 
      (
         SELECT
            1 
         FROM
            transaction sales 
            JOIN
               transactionline salesline 
               ON sales.id = salesline.transaction 
         WHERE
            salesline.item = billcreditline.custcol_rip_item_link 
            AND sales.trandate > billcredit.trandate 
            AND sales.type IN 
            (
               'CashSale',
               'Invoice'
            )
            AND salesline.subsidiary = billcreditline.subsidiary 
            AND salesline.custcol_item_rip_bill_credit_applied is null 
            and accountinglinetype = 'INCOME' 
      )
      ${specificBillCredit ? `
      AND billcredit.id = ${specificBillCredit}` : ""}
`;

      return {
        type: "suiteql",
        query: sqlQuery,
        params: [],
      };
    } catch (e) {
      customErrorObject.throwError({
        summaryText: `GET_INPUT_DATA_ERROR`,
        error: e,
      });
    }
  }


  function map(context) {
    const mapErrorObject = new CustomErrorObject();
    try {
      var parsedResultValues = JSON.parse(context.value).values;
      const billCreditObj = {
        itemId: parsedResultValues[0],
        quantityRemaining: parsedResultValues[1],
        rate: parsedResultValues[2],
        transactionDate: parsedResultValues[3],
        transactionId: parsedResultValues[4],
        originalQuantity: parsedResultValues[5],
        subsidiary: parsedResultValues[6],
        itemType: parsedResultValues[7],
      };

      if (!billCreditObj.itemId || !billCreditObj.transactionDate) {
        throw mapErrorObject.updateError({
          errorType: mapErrorObject.ErrorTypes.NO_VALUE_RETURNED,
          summary: "MISSING_BILL_CREDIT_INFO",
          details: `Missing transaction date or item id or quantity from bill credit ${billCreditObj?.transactionId}`,
        });
      }

      if (billCreditObj.itemType === "Group") {
        //TODO: work on the item group part here
        log.debug("Group Item", billCreditObj);
        const billRec = record.load({
          type: record.Type.VENDOR_BILL,
          id: 9697977, //billCreditObj.transactionId
        });

        const parentItemLine = billRec.findSublistLineWithValue({
          sublistId: "item",
          fieldId: "item",
          value: billCreditObj.itemId,
        });
        return;
      }

      context.write({
        key: billCreditObj.transactionId,
        value: billCreditObj,
      });

    } catch (e) {
      mapErrorObject.throwError({
        summaryText: `MAP_ERROR`,
        error: e,
        errorWillBeGrouped: true,
      });
    }
  }

  function reduce(context) {
    const reduceErrorObject = new CustomErrorObject();
    try {
      let billCreditObj = JSON.parse(context.values[0]);
      function checkIfScriptIsStillExecuting() {
        return search
          .create({
            type: "scheduledscriptinstance",
            filters: [
              ["status", "anyof", "RETRY", "RESTART", "PROCESSING", "PENDING"],
              "AND",
              ["script.internalid", "anyof", "4577"],
              //SB: 5455
              //PROD: 4577
              "AND",
              ["scriptdeployment.internalid", "anyof", "10362"],
              //SB: 11954
              //PROD: 10362
            ],
            columns: [search.createColumn({ name: "status", label: "Status" })],
          })
          .runPaged().count;
      }

      do {
        checkIfScriptIsStillExecuting();
      } while (checkIfScriptIsStillExecuting() != 0);

      function passToSecondScript(billCreditObj) {
        const mrTask = task.create({
          taskType: task.TaskType.MAP_REDUCE,
          scriptId: "customscript_brdg_rip_apply_sales_2_mr",
          deploymentId: "customdeploy_brdg_rip_apply_sales_2_mr",
          params: {
            custscript_bill_credit_data: JSON.stringify(billCreditObj),
          },
        });
        const taskId = mrTask.submit();
        context.write(
          "taskId",
          `For ${taskId} with bill ${billCreditObj.transactionId}`
        );
      }
      passToSecondScript(billCreditObj);
    } catch (e) {

      reduceErrorObject.throwError({
        summaryText: `REDUCE_ERROR`,
        error: e,
      });
    }
  }

  function summarize(context) {
    context.output.iterator().each(function (key, value) {
      if (key === "taskId") {
        log.audit({
          title: "MapReduce Task Submitted:",
          details: `Task ID: ${value}`,
        });
      }
      return true;
    });

    /** @type {import("../../../Classes/vlmd_mr_summary_handling").MapReduceSummaryStageHandling} */
    const StageHandling = require("../../../Classes/vlmd_mr_summary_handling");

    const stageHandling = new StageHandling(context);
    stageHandling.printErrors({
      groupErrors: true,
    });
  }
  return {
    getInputData,
    map,
    reduce,
    summarize,
  };
});
