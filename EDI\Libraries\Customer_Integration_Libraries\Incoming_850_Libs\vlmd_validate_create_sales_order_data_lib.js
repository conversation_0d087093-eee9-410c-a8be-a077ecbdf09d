/**
 * @NApiVersion 2.1
 */

//@ts-ignore
define(["N/log", "N/search", "CompareAddressLib", "LoDash"], function (
	log,
	search,
	compareAddressesLib,
	_
) {
	function validateData(
		purchaseOrderObj,
		streetAddress,
		subsidiary,
		referrenceNumber
	) {
		var errorLog = [];
		var continueToCreate = true;

		validateCustomerIdIsInNetsuite();
		if (streetAddress) {
			validateCustomersAddress();
		}
		validateSalesOrderNotDuplicate();
		validateItemsHaveInternalIds();
		validateAtLeastOneLineItemToAdd();

		return {
			errorLog: errorLog,
			continueToCreate: continueToCreate,
			purchaseOrderObj: purchaseOrderObj,
		};

		function processFail(errorText, continueProcessing, programmingError) {
			errorLog.push({
				logMessage: errorText,
				programmingError: programmingError,
			});

			if (!continueProcessing) {
				continueToCreate = false;
			}
		}

		function validateCustomerIdIsInNetsuite() {
			try {
				var customerToValidate = search.lookupFields({
					type: "customer",
					id: purchaseOrderObj.customerId, //The customers internal ID
					columns: ["companyname"],
				});

				if (_.isEmpty(customerToValidate)) {
					processFail(
						`Customer account number ${purchaseOrderObj.customerId} is invalid/missing.`,
						false,
						false
					);
				}
			} catch (e) {
				processFail(`Error validating customer. ${e}`, false, true);
			}
		}

		function validateCustomersAddress() {
			try {
				var addressesMatch = false;

				var searchObj = search.create({
					type: "customer",
					filters: [["internalid", "anyof", purchaseOrderObj.customerId]],
					columns: [
						search.createColumn({ name: "address1", label: "Address 1" }),
					],
				});

				searchObj.run().each(function (result) {
					var addressInNetSuite = result.getValue({
						name: "address1",
					});

					var thisAddressMatches = compareAddressesLib.compareAddresses(
						addressInNetSuite,
						streetAddress
					);

					log.debug("thisAddressMatches", thisAddressMatches);

					if (thisAddressMatches) {
						addressesMatch = true;
						return;
					}

					return true;
				});

				if (!addressesMatch) {
					processFail(
						`The customer's address sent doesn't match the address in NetSuite. 
					Please confirm that ${purchaseOrderObj.customerId} is the correct account number for this customer.`,
						false,
						false
					);
					return;
				}
			} catch (e) {
				processFail(
					`Error validating customer's address. Error: ${e}`,
					false,
					true
				);
			}
		}

		function validateSalesOrderNotDuplicate() {
			try {
				var searchObj = search.create({
					type: "salesorder",
					filters: [
						["type", "anyof", "SalesOrd"],
						"AND",
						["mainline", "is", "T"],
						"AND",
						["subsidiary", "anyof", subsidiary],
						"AND",
						["otherrefnum", "equalto", referrenceNumber],
					],
					columns: [
						search.createColumn({ name: "tranid", label: "Document Number" }),
					],
				});

				var salesOrderArr = searchObj.run().getRange({
					start: 0,
					end: 1000,
				});

				if (salesOrderArr.length > 0) {
					var existingSalesOrder = salesOrderArr[0].getValue(
						salesOrderArr[0].columns[0]
					);
					processFail(
						`${existingSalesOrder} has already been created for PO# ${referrenceNumber}. 
					Please compare the second purchase order submitted with the first purchase order to check for any changes and check with the customer if corrections to the sales order are needed.`,
						false,
						false
					);
				}
			} catch (e) {
				throw e;
			}
		}

		function validateItemsHaveInternalIds() {
			purchaseOrderObj.items.forEach((item) => {
				if (!item.internalId) {
					processFail(
						`${item.itemName} is an incorrect item name or is not in NetSuite and was not added to the sales order.`,
						true,
						false
					);
				}
			});
		}

		function validateAtLeastOneLineItemToAdd() {
			var hasItemWithInternalId = purchaseOrderObj.items.some(function (item) {
				return item.internalId;
			});

			if (!hasItemWithInternalId) {
				processFail("No valid line items to create sales order.", false, false);
			}
		}
	}

	return {
		validateData: validateData,
	};
});
