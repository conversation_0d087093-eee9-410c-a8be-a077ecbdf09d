/**
 * @NApiVersion 2.1
 */
//@ts-ignore
define(["N/record", "N/log"], function (record, log) {
	function createCustomer(purchaseOrderObj) {
		var processingErrors = [];
		var customer = createCustomerRecord();
		setShippingAddress();
		var customerId = saveCustomer();
		customer = loadCustomer();
		setCustomerValues();
		customerId = saveCustomer();

		return {
			customerId: customerId,
			processingErrors: processingErrors,
		};
		function createCustomerRecord() {
			return record.create({
				type: record.Type.CUSTOMER,
				isDynamic: true,
			});
		}
		function setShippingAddress() {
			try {
				var shippingAddressObj = purchaseOrderObj.patient.address;
				customer.selectNewLine({
					sublistId: "addressbook",
				});
				customer.setCurrentSublistValue({
					sublistId: "addressbook",
					fieldId: "defaultshipping",
					value: true,
				});
				customer.setCurrentSublistValue({
					sublistId: "addressbook",
					fieldId: "defaultbilling",
					value: false,
				});
				var shippingAddressSubrecord = customer.getCurrentSublistSubrecord({
					sublistId: "addressbook",
					fieldId: "addressbookaddress",
				});

				shippingAddressSubrecord.setValue({
					fieldId: "addressee",
					value: shippingAddressObj.addressee,
				});
				shippingAddressSubrecord.setValue({
					fieldId: "addr1",
					value: shippingAddressObj.streetAddress,
				});
				shippingAddressSubrecord.setValue({
					fieldId: "city",
					value: shippingAddressObj.city,
				});
				shippingAddressSubrecord.setValue({
					fieldId: "state",
					value: shippingAddressObj.state,
				});
				shippingAddressSubrecord.setValue({
					fieldId: "zip",
					value: shippingAddressObj.zip,
				});
				shippingAddressSubrecord.setValue({
					fieldId: "country",
					value: "US",
				});
				customer.commitLine({
					sublistId: "addressbook",
				});
			} catch (e) {
				throw e;
			}
		}
		function saveCustomer() {
			try {
				return customer.save({
					ignoreMandatoryFields: true,
				});
			} catch (e) {
				processingErrors.push({
					logMessage: "EDI File not saved: ".concat(e.message),
					programmingError: true,
				});
			}
		}
		function loadCustomer() {
			return record.load({
				type: record.Type.CUSTOMER,
				id: customerId,
				isDynamic: true,
			});
		}
		function setCustomerValues() {
			try {
				customer.setValue("customform", 118); //Valmar customer form
				customer.setValue("isperson", "T"); //Customer type is individual
				customer.setValue("entityid", purchaseOrderObj.patient.revivalId);
				customer.setValue("firstname", purchaseOrderObj.patient.name.firstName);
				customer.setValue(
					"middlename",
					purchaseOrderObj.patient.name.middleInitial
				);
				customer.setValue("lastname", purchaseOrderObj.patient.name.lastName);
				customer.setValue(
					"companyname",
					purchaseOrderObj.patient.address.addressee
				);
				customer.setValue(
					"custentity_vlmr_revival_id",
					purchaseOrderObj.patient.revivalId
				);
				customer.setValue("parent", 13264); //Revival Home Health Care (under Valmar subsidiary)
				customer.setValue("subsidiary", 2); //Valmar
				customer.setValue("custentity_spl_purchasing_software", 8); //Homecare Homebase
				customer.setValue("phone", purchaseOrderObj.patient.phoneNumber);
				customer.setValue("emailtransactions", false); //Sets the default of emailing the transaction to the customer to false
			} catch (e) {
				processingErrors.push({
					logMessage: "Customer values not set successfully ".concat(e.message),
					programmingError: true,
				});
			}
		}
	}
	return {
		createCustomer: createCustomer,
	};
});
