/**
 *@NApiVersion 2.1
 *@NScriptType ClientScript
 * @NAmdConfig /SuiteScripts/config.json
 */

//@ts-ignore
define([
	"N/log",
	"N/currentRecord",
	"N/ui/message",
	"N/url",
	"N/ui/dialog",
], function (log, currentRecord, message, url, dialog) {
	function pageInit() {
		try {
			const url = new URL(document.location.href);
			//const pageStatus = url.searchParams.get("page_status");
			const messageText = url.searchParams.get("message_text");
			const depositId = url.searchParams.get("deposit_id");
			const messageTitle = url.searchParams.get("message_title");
			const messageTypeText = url.searchParams.get("message_type");

			showMessage();
			// showDialogBox();

			function showMessage() {
				const resultsMessage = message.create({
					title: messageTitle,
					type: getMessageType(messageTypeText),
					message: messageText,
				});

				resultsMessage.show({ duration: 3000 });
			}

			function getMessageType(messageTypeText) {
				if (messageTypeText == "CONFIRMATION") {
					return message.Type.CONFIRMATION;
				}

				if (messageTypeText == "WARNING") {
					return message.Type.WARNING;
				}

				if (messageTypeText == "ERROR") {
					return message.Type.ERROR;
				}

				if (messageTypeText == "INFORMATION") {
					return message.Type.INFORMATION;
				}
			}

			function showDialogBox() {
				let newDepositButton = {
					label: "Upload Another Bank Deposit",
					value: "noActionNeeded",
				};

				let viewNewDepositButton = {
					label:
						'<a data-auth="NotApplicable" href="https://www.google.com/" rel="noopener noreferrer" target="_blank">' +
						"Link to GOOGLE" +
						"</a>", // "View the New Deposit",
					value: "redirect",
				};

				let options = {
					title: "Bank Deposit Upload Results",
					message: messageText,
					buttons: [newDepositButton, viewNewDepositButton],
				};
				function success(result) {
					if (result == "redirect") {
						console.log("redirect");
						console.log("depositId", depositId);
						// redirect.toRecord({
						// 	type: record.Type.DEPOSIT,
						// 	id: depositId,
						// });
					} else {
						console.log("Success with value " + result);
						// redirect.toSuitelet({
						//     scriptId: '31',
						//     deploymentId: '1',
						//     parameters: {
						//         'custparam_test':'helloWorld'
						//     }
						// });
					}
				}
				function failure(reason) {
					console.log("Failure: " + reason);
				}

				dialog.create(options).then(success).catch(failure);
			}
		} catch (e) {
			console.log("error " + e);
		}
	}

	return {
		pageInit: pageInit,
	};
});
