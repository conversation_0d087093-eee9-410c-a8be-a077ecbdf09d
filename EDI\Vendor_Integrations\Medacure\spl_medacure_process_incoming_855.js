/**
 * @NApiVersion 2.1
 * @NScriptType scheduledscript
 * @NAmdConfig /SuiteScripts/config.json
 */

define([
  "GetEdiFileContents",
  "GetEdiPartnerValuesLib",
  "ParseIncoming855Lib",
  "ProcessIncoming855Lib",
], function (
  getEdiFileContentsLib,
  getEdiPartnerValuesLib,
  parsePoAcknowledgmentLib,
  processPOAcknowledgmentLib
) {
  function execute(context) {
    var vendorData = {
      prodGuidBool: true, //Toggle for Sandbox or Prod
      prodDirectoryBool: true, //Toggle for Sandbox or Prod
      decodeContent: true, //Toggle depending on edi file format type
      prodGUID: "3e852a7d9cb04276a6c8e751f75bc3c2",
      sandboxGUID: "",
      vendorInternalId: 13331,
      integrationStartDate: "2/30/2025 12:00 am",
      testDirectory: "/edi/test/vendor/medacure/in/855",
      prodDirectory: "/edi/prod/vendor/medacure/in/855",
      referenceDirectory: "/edi/reference/vendor/medacure/in/855",
      vendorName: "Medacure",
      documentType: "Purchase Order Acknowledgment",
      documentTypeId: 2,
      pushEmailToDB: true,
      purchasingSoftware: "Medacure",
      purchasingSoftwareId: 13,
    };

    var fileContents =
      getEdiFileContentsLib.getEdiFileContents(vendorData).fileContents;

    if (fileContents.length > 0) {
      var partnerValues = getEdiPartnerValuesLib.getMedacureValues();
      fileContents.forEach((ediFile) => {
        var parsedPoAckObj = parsePoAcknowledgmentLib.parse855(
          ediFile.content,
          partnerValues
        );

        if (parsedPoAckObj.errorLog.length <= 0) {
          processPOAcknowledgmentLib.processPoAck(
            vendorData,
            parsedPoAckObj.poAckObj,
            ediFile.fileName
          );
        }

        let sourcePath = `/edi/prod/vendor/medacure/in/855/${ediFile.fileName}`;
        let destinationPath = `/edi/reference/vendor/medacure/in/855/${ediFile.fileName}`;

        try {
          var connection = getEdiFileContentsLib.createConnection(
            vendorData,
            false, //appendToDirectory
            false, //referenceDirectory
            true //So that the "createConnectionToRoot" param will be true
          );

          connection.move({
            from: sourcePath,
            to: destinationPath,
          });
        } catch (err) {
          log.error(
            "Error Moving File",
            `Source path: ${sourcePath}\nDestination path: ${destinationPath}\nError: ${err}`
          );
        }
      });
    } else {
      log.debug("Script ran, no results found");
    }
  }

  return {
    execute,
  };
});
