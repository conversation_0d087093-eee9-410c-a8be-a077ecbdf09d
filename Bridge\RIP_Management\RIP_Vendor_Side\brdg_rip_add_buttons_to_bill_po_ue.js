/**
 * @description Adds a button to the vendor bill/purchase order 
 * to regenerate RIPS/generate RIPS from multiple bills linked to the same PO
 *
 * </br><b>Deployed On:</b> Vendor Bill, Purchase Order
 * </br><b>Execution Context:</b> ALL
 * </br><b>Event Type/Mode:</b> ALL
 * </br><b>Entry Points:</b> beforeLoad, beforeSubmit, afterSubmit
 *
 * @NApiVersion 2.1
 * @NScriptType UserEventScript
 *
 * <AUTHOR>
 * @module brdg_rip_add_button_to_bill_po_ue
 */

define(["require", "N/log", "../../../Classes/vlmd_custom_error_object","../../Libraries/brdg_helper_functions_lib"], (
  /** @type {any} */ require
) => {
  const log = require("N/log");
  /**@type {import ("../../../Classes/vlmd_custom_error_object").CustomErrorObject}} */
  const CustomErrorObject = require("../../../Classes/vlmd_custom_error_object");

  /**@type {import("../../Libraries/brdg_helper_functions_lib")} */
  const bridgeHelperFunctionsLib = require("../../Libraries/brdg_helper_functions_lib")

  /*
   *
   * @param {import("N/types").EntryPoints.UserEvent.beforeLoadContext} context Before load script context
   */
  function beforeLoad(context) {
    const customErrorObject = new CustomErrorObject();
    try {
      if (context.type != context.UserEventType.CREATE) {

        const netsuiteRecord = context.newRecord;
        const subsidiariesArr = netsuiteRecord.getValue({
          fieldId: "subsidiary"
        });
        const bridgeSubsidiaries = bridgeHelperFunctionsLib.getBridgeSubsidiaries();
        const isBridgeSubsidiary = bridgeHelperFunctionsLib.isBridgeSubsidiary(subsidiariesArr, bridgeSubsidiaries);
  
        if (!isBridgeSubsidiary) {
          return;
        }
        const recordId = netsuiteRecord.id;
        const recordType = netsuiteRecord.type;

        const billApprovalStatus = netsuiteRecord.getValue({
          fieldId: "approvalstatus",
        });
        const poApprovalStatus = netsuiteRecord.getValue({
          fieldId: "orderstatus",
        });
        const isPartofGroupedBills = netsuiteRecord.getValue({
          fieldId: "custbody_is_part_of_grouped_bills",
        });

        log.debug("APPROVAL STATUS",billApprovalStatus);

        //Add the regenerate rips button to bills that are not part of grouped bills and do not have the status of "open"
        recordType == 'vendorbill' && billApprovalStatus != 2 && !isPartofGroupedBills &&
        context.form.addButton({
          id: "custpage_regenerate_rips",
          label: "Calculate RIPS",
          functionName: `confirmAndRegenerateRips(${recordId})`,
        });

        //Add the regenerate rips for multiple bills on POs
        recordType == 'purchaseorder' && poApprovalStatus == "G" &&
        context.form.addButton({
          id: "custpage_regenerate_rips_po",
          label: "Regenerate RIPS Multiple Bills",
          functionName: `confirmAndRegenerateRipsFromPo(${recordId})`,
        });

        context.form.clientScriptFileId = "8905158";
      }
    } catch (e) {
      customErrorObject.throwError({
        summaryText: "ERROR_ADDING_BUTTON",
        error: e,
        recordType: recordType,
        recordId: recordId
      });
    }
  }

  return {
    beforeLoad,
  };
});
