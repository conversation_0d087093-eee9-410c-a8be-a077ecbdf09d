/**
 * @description Helper functions called by various BRDG scripts
 *
 * @NAmdConfig /SuiteScripts/config.json
 * @NApiVersion 2.1
 *
 * <AUTHOR>
 * @module brdg_helper_functions_lib
 */

define([
  "require",
  "exports",
  "../../Classes/brdg_royal_discount",
  "N/log",
  "N/record",
  "N/query",
  "N/error",
], function (
  /** @type {any} */ require,
  /** @type {any} */ exports,
  /** @type {import("../../Classes/brdg_transaction_object").BridgeRoyalDiscount} */ BridgeRoyalDiscount
) {
  const log = require("N/log");
  const record = require("N/record");
  const query = require("N/query");
  const error = require("N/error");

  const bridgeHelperFunctionsLib = exports;

  const storeInfoArr = [
    {
      name: "Vineyard LLC",
      subsidiary: 16,
      location: 25,
      priceLevel: null,
    },
    {
      name: "LOL",
      subsidiary: 25,
      location: 27,
      priceLevel: 28,
    },
    {
      name: "Vineyard Placeholder",
      subsidiary: 22,
      location: 23,
      priceLevel: null,
    },
    {
      name: "Vineyard North",
      subsidiary: 31,
      location: 29,
      priceLevel: 30,
    },
    {
      name: "Vineyard Bergenfield",
      subsidiary: 33,
      location: 37,
      priceLevel: 32,
    },
    {
      name: "Vineyard South",
      subsidiary: 24,
      location: 24,
      priceLevel: 29,
    },
    {
      name: "Vineyard Westgate",
      subsidiary: 32,
      location: 42,
      priceLevel: 34,
    },
    {
      name: "Vineyard Express",
      subsidiary: 30,
      location: 31,
      priceLevel: 33,
    },
  ];
  const royalWineCompanyId = "13366";
  const royalWineCompanyCnRId = "13410";
  const maxNumberOfTiersPerGroup = 5;

  /**
   * Return the value of the property from the storeInfoArr based on the key-value provided
   *
   * @param {"name"|"subsidiary"|"location"|"priceLevel"} keyToSearchBy Search key
   * @param {import("N/record").FieldValue} valueToSearchBy Search value
   * @param {"name"|"subsidiary"|"location"|"priceLevel"} keyToReturn Value of the key being looked up
   * @returns {string|number|null} Property value from storeInfoArr
   */
  exports.getValueByRelatedValue = function (
    keyToSearchBy,
    valueToSearchBy,
    keyToReturn
  ) {
    const objToReturn = storeInfoArr.find((obj) => {
      return obj[keyToSearchBy] == valueToSearchBy;
    });

    if (!objToReturn) {
      throw error.create({
        name: "SEARCH_ERROR",
        message: "No obj found",
      });
    }
    return objToReturn[keyToReturn];
  };

  /**
   * Retrieve sales price based on location
   *
   * @param {import("N/record").FieldValue} itemId
   * @param {string|number|null} priceLevelId
   * @returns {string | number | boolean | null} Query result
   */
  exports.getSalesPriceForLocation = function (
    itemId,
    priceLevelId,
    billPurchaseUnitRate
  ) {
    if (itemId && priceLevelId) {
      //Get the sales price for the specified price level name
      //	(gotten by querying to price level ID)
      //Get the sales price/base unit by dividing by the sales unit conversion rate
      //Multiply by conversion rate for the purchase unit

      const purchaseUnitRate = billPurchaseUnitRate
        ? billPurchaseUnitRate
        : `prchsunit.conversionrate`; //If there is a billPurchaseUnitRate (this query is being called from a bill), use the bill's purchase unit instead of the default item's purchase unit

      const sqlQuery = `SELECT
			( itemprice.price / salesunit.conversionrate)*${purchaseUnitRate},
			FROM
				item AS brdgitem 
				INNER JOIN
					itemprice 
					ON itemprice.item = brdgitem.id 
				INNER JOIN
					unitstypeuom AS prchsunit 
					ON brdgitem.purchaseunit = prchsunit.internalid 
				INNER JOIN
					unitstypeuom AS salesunit 
					ON brdgitem.saleunit = salesunit.internalid 
				JOIN
					pricelevel 
					ON pricelevel.name = itemprice.pricelevelname 
			WHERE
				brdgitem.id = ? 
				AND pricelevel.id = ?`;

      const resultIterator = query.runSuiteQL({
        query: sqlQuery,
        params: [itemId.toString(), priceLevelId.toString()],
      });

      if (
        !resultIterator.results ||
        resultIterator.results.length <= 0 ||
        resultIterator.results[0].values.length <= 0
      ) {
        throw error.create({
          name: "SEARCH_ERROR",
          message: `No results returned for this query. Item ID: ${itemId}, Price Level ID: ${priceLevelId}, Bill Purchase Unit Rate: ${billPurchaseUnitRate}`,
        });
      }

      //Return 0 when no result line is queried

      return resultIterator.results[0].values[0];
    }

    return null;
  };

  /**
   * Check if given subsidiary belongs to the Bridge grouping
   * DEPRECATED: use getBridgeSubsidiaries and isBridgeSubsidiary instead
   *
   * @param {import("N/record").FieldValue} subsidiary
   * @returns {boolean} True if subsidiary is Bridge
   */
  exports.checkIfIsBridgeSubsidiary = function (subsidiary) {
    try {
      //Hard coded the Bridge subsidiaries values temporarily.
      const bridgeSubsidiaries = [
        "16", //Vineyard LLC
        "22", //Vineyard
        "24", //Vineyard South
        "25", //Lots of Liquor Bar and Grill
        "30", //Vineyard Express
        "31", //Vineyard North
        "32", //Vineyard Westgate
        "33", //Vineyard Bergenfield
      ];

      let isBridgeSubsidiary = false;

      if (typeof subsidiary == "string") {
        isBridgeSubsidiary = bridgeSubsidiaries.includes(subsidiary);
      } else if (Array.isArray(subsidiary)) {
        isBridgeSubsidiary = subsidiary.some((element) =>
          bridgeSubsidiaries.includes(element.toString())
        );
      }

      return isBridgeSubsidiary;
    } catch (/** @type {any} */ e) {
      throw error.create({
        name: "Error checking the subsidiary!",
        message: e.message,
      });
    }
  };

  /**
   * Check if the item in the inventory sublist has an est. unit cost
   *
   * @param {import("@hitc/netsuite-types/N/types").EntryPoints.Client.validateLineContext} context Validate line script context
   * @param {number} [line] Index of the sublist line
   * @returns {boolean} Return true if the the line has est. unit cost is not blank or 0
   */
  exports.hasItemEstimatedUnitCost = function (context, line) {
    const { currentRecord, sublistId } = context;

    const unitCost =
      line || line === 0
        ? currentRecord.getSublistValue({
            sublistId,
            fieldId: "unitcost",
            line,
          })
        : currentRecord.getCurrentSublistValue({
            sublistId,
            fieldId: "unitcost",
          });

    return !!unitCost && typeof unitCost === "number" && unitCost !== 0;
  };

  /**
   * Check if the inventory sublist unit costs are valid
   *
   * @param {import("@hitc/netsuite-types/N/types").EntryPoints.Client.validateLineContext} context Validate line script context
   * @returns {string[]} Return true if all lines have est. unit costs
   */
  exports.getItemsWithInvalidUnitCosts = function (context) {
    context["sublistId"] = "inventory";
    const { currentRecord, sublistId } = context;
    const lineCount = currentRecord.getLineCount({ sublistId });
    const invalidItemsArr = [];

    for (let line = 0; line < lineCount; line++) {
      if (!bridgeHelperFunctionsLib.hasItemEstimatedUnitCost(context, line)) {
        try {
          invalidItemsArr.push(
            currentRecord.getSublistText({
              sublistId,
              fieldId: "item",
              line,
            })
          );
        } catch (error) {
          log.audit(
            "WARNING",
            "Error using getSublistText. Using getSublistValue instead"
          );
          const itemId = currentRecord.getSublistValue({
            sublistId,
            fieldId: "item",
            line,
          });
          if (itemId) {
            invalidItemsArr.push(itemId.toString());
          }
        }
      }
    }

    return invalidItemsArr;
  };

  /**
   * Retrieve all Bridge Management subsidiaries
   *
   * @returns {number[]} Bridge subsidiaries
   */
  exports.getBridgeSubsidiaries = function () {
    try {
      const subsidiaryQuery = query.runSuiteQL({
        query: `
					SELECT id, parent, name
					FROM subsidiary
					ORDER BY id
				`,
      });
      const bridgeManagementSubsidiaryId = 16;
      const subsidiariesExclusionArr = [26, 27]; // RE 1380 and RE 105
      const results =
        subsidiaryQuery &&
        subsidiaryQuery.results &&
        Array.isArray(subsidiaryQuery.results) &&
        subsidiaryQuery.results;

      const bridgeSubsidiariesArr =
        (results &&
          results.reduce(
            (subsidiaryIdsArr, result) => {
              const id = result.values[0];
              if (
                Number(id) <= bridgeManagementSubsidiaryId ||
                subsidiariesExclusionArr.includes(Number(id))
              ) {
                return subsidiaryIdsArr;
              }
              const parent = result.values[1];
              if (
                parent === bridgeManagementSubsidiaryId ||
                subsidiaryIdsArr.includes(Number(parent))
              ) {
                return subsidiaryIdsArr.concat([Number(id)]);
              }
              return subsidiaryIdsArr;
            },
            [bridgeManagementSubsidiaryId]
          )) ||
        [];

      return bridgeSubsidiariesArr;
    } catch (/** @type {any} */ error) {
      log.error(error.name, error.message);
      return [];
    }
  };

  /**
   * Checks if the subsidiary or some of the subsidiaries is/are Bridge
   *
   * @param {import("N/record").FieldValue} subsidiaries Single ID or array of subsidiary IDs
   * @param {number[]} bridgeSubsidiaries Array of Bridge subsidiaries
   * @returns {boolean} Return true if one of the subsidiary is Bridge
   */
  exports.isBridgeSubsidiary = function (subsidiaries, bridgeSubsidiaries) {
    if (Array.isArray(subsidiaries)) {
      return subsidiaries.some((subsidiaryId) =>
        bridgeSubsidiaries.includes(parseInt(subsidiaryId.toString(), 10))
      );
    } else if (subsidiaries) {
      return bridgeSubsidiaries.includes(parseInt(subsidiaries.toString(), 10));
    } else {
      return false;
    }
  };

  /**
   * Convert array to (x,y,z) format for SuiteQL
   *
   * @param {number[]} bridgeSubsidiariesArr Bridge subsidiaries
   * @returns {string} Stringified bridge subsidiary list
   */
  exports.getSuiteqlBridgeSubsidiaryList = function (bridgeSubsidiariesArr) {
    if (bridgeSubsidiariesArr) {
      return `(${bridgeSubsidiariesArr.join(",")})`;
    }
    return "()";
  };

  /**
   * Retrieve the transaction dates used for transactions
   * created on a given date
   *
   * @param {String} createdDate Date created
   * @returns {string[]} Formatted transaction dates
   */
  exports.getTransactionDatesFromDateCreated = (createdDate) => {
    const trandateQueryString = `
				SELECT
					BUILTIN_RESULT.TYPE_DATE(transaction.trandate) AS trandate
				FROM
					transaction
				JOIN
					transactionLine ON transactionLine.transaction = transaction.id
				WHERE
					transactionLine.mainline = 'T' 
					AND transaction.type IN ('VendBill')
					AND transaction.entity IN ('${royalWineCompanyId}', '${royalWineCompanyCnRId}')
					AND ${
            createdDate
              ? `'${createdDate}' = TO_CHAR(transaction.createddate)`
              : `TO_CHAR (SYSDATE - 1, 'MM/DD/YYYY') = TO_CHAR (transaction.createddate, 'MM/DD/YYYY')`
          }
					GROUP BY transaction.trandate
			`;

    const /** @type {string[]} */ dateArr = [];
    query
      .runSuiteQLPaged({
        query: trandateQueryString,
        pageSize: 1000,
      })
      .iterator()
      .each((page) => {
        page.value.data.results.forEach((result) => {
          result.values[0] && dateArr.push(result.values[0].toString());
        });
        return true;
      });

    return dateArr;
  };

  /**
   * Generate the comma-separated list of wine and spirit category IDs
   * Sort by fullname, which should contain either "Wine : " or "Spirits : "
   *   for the relevant categories
   * Wine and Spirit category names are set to blank initially
   *   to account for any name change for these main product categories
   *
   * @returns {{[key:string]:string}}} Wine and spirit category IDs in (x,y,z) format for SuiteQL
   */
  exports.getWineAndSpiritCategories = function () {
    let wineProductName = "";
    let spiritsProductName = "";
    const wineProductId = 75;
    const spiritsProductId = 159;
    const windProductIdsArr = [wineProductId];
    const spiritsProductIdsArr = [spiritsProductId];
    const productCategoryQueryString = `
			SELECT id, fullname
			FROM classification
			ORDER BY fullname
		`;

    query
      .runSuiteQLPaged({
        query: productCategoryQueryString,
        pageSize: 1000,
      })
      .iterator()
      .each((page) => {
        page.value.data.results.forEach((result) => {
          const productId = result.values[0];
          const productName = result.values[1];

          if (!productId || !productName) {
            return;
          }
          if (productId === wineProductId) {
            wineProductName = productName.toString();
          } else if (productId === spiritsProductId) {
            spiritsProductName = productName.toString();
          } else if (
            wineProductName &&
            productName.toString().startsWith(`${wineProductName} : `)
          ) {
            windProductIdsArr.push(Number(productId));
          } else if (
            spiritsProductName &&
            productName.toString().startsWith(`${spiritsProductName} : `)
          ) {
            spiritsProductIdsArr.push(Number(productId));
          }
        });
        return true;
      });

    return {
      wineCategories: `(${windProductIdsArr.join(",")})`,
      spiritCategories: `(${spiritsProductIdsArr.join(",")})`,
    };
  };

  /**
   * Retrieve a query that returns the transaction and transasction line data
   * Include items using bottles as unit based on conversionrate
   *   but do not count them towards the cases count
   * Use current date when a date parameter is not set in the MR deployment
   *
   * @param {string} date Formatted date string
   * @param {string} transactionType Transasction type
   * @returns {string} SuiteQL Query String
   */
  exports.getTransactionLineQueryString = function (date, transactionType) {
    const isVendorBill = transactionType === "VendBill";

    let selectClause = `
			SELECT
				transaction.id,
				transactionLine.linesequencenumber,
				transactionLine.quantity / unitstypeuom.conversionrate,
				transactionLine.subsidiary,
				unitstypeuom.conversionrate
		`;
    selectClause = isVendorBill
      ? selectClause + ", nexttransactionlink.previousdoc"
      : selectClause + ", previoustransactionlink.nextdoc";

    const fromClause = "FROM transaction";

    let joinClause = `
			JOIN transactionLine ON transactionLine.transaction = transaction.id
			LEFT JOIN unitstypeuom ON transactionLine.units = unitstypeuom.internalid
			JOIN item ON item.id = transactionLine.item
			JOIN classification ON classification.id = item.class
		`;
    joinClause = isVendorBill
      ? joinClause +
        "JOIN nexttransactionlink ON nexttransactionlink.nextdoc = transaction.id"
      : joinClause +
        "JOIN previoustransactionlink ON previoustransactionlink.previousdoc = transaction.id";

    let whereClause = `
			WHERE (transaction.custbody_brdg_not_eligible_disc IS NULL OR transaction.custbody_brdg_not_eligible_disc = 'F')
				AND ( transaction.entity = ${royalWineCompanyId} OR transaction.entity = ${royalWineCompanyCnRId} )
				AND type = '${transactionType}'
				AND (
						(${
              date
                ? `'${date}' = transaction.trandate`
                : `TO_CHAR (SYSDATE, 'MM/DD/YYYY') = TO_CHAR (transaction.trandate, 'MM/DD/YYYY')`
            }
							AND
							(transaction.custbody_brdg_is_bill_and_hold = 'F' or transaction.custbody_brdg_is_bill_and_hold IS NULL)
						)
					OR
						(${
              date
                ? `'${date}' = transaction.custbody_brdg_bill_and_hold_date`
                : `TO_CHAR (SYSDATE, 'MM/DD/YYYY') = TO_CHAR (transaction.custbody_brdg_bill_and_hold_date, 'MM/DD/YYYY')`
            }
								AND 
								transaction.custbody_brdg_is_bill_and_hold = 'T'
						)
				)
		`;
    whereClause = isVendorBill
      ? whereClause + "AND nexttransactionlink.linktype = 'OrdBill'"
      : whereClause + "AND previoustransactionlink.linktype = 'OrdBill'";

    return `
			${selectClause}
			${fromClause}
			${joinClause}
			${whereClause}
		`;
  };

  /**
	 * Create the object containing the line indices to apply discounts to per transaction
	 *
	 * Sample BridgeSubsidiariesObject:
	 * {
	 	"casesCount": 136,
		"transactionsObj": {
			"7126318": {
				"recordType": "vendorbill",
				"indices": [
					4,
					5,
					8,
				],
				"linkedTransaction": "7114938"
			},
			"7126628": {
				"recordType": "vendorbill",
				"indices": [
					15,
					16,
					20,
				],
				"linkedTransaction": "7126526"
			}
		  },		
		}
	 * 
	 * 
	 * @param {{ transactionQueryString: string, transactionType: string, categoryIds: string }} param Transaction line SuiteQL parameters
	 * @returns {import("../../Classes/brdg_transaction_object").BridgeSubsidiariesObject} Transaction object with line indices to update and number of cases
	 */
  exports.getTransactionsObj = ({
    transactionQueryString,
    transactionType,
    categoryIds,
  }) => {
    if (
      !transactionQueryString ||
      !transactionType ||
      !categoryIds ||
      categoryIds === "()"
    ) {
      return {};
    }

    const recordType =
      transactionType === "VendBill"
        ? record.Type.VENDOR_BILL
        : record.Type.PURCHASE_ORDER;
    const royalDiscount = new BridgeRoyalDiscount(recordType);
    const queryString =
      transactionQueryString + `AND classification.id IN ${categoryIds}`;

    query
      .runSuiteQLPaged({
        query: queryString,
        pageSize: 1000,
      })
      .iterator()
      .each((page) => {
        page.value.data.results.forEach((result) => {
          const queryResult = {
            transactionId: result.values[0]?.toString() || "",
            sublistLineIndex: Number(result.values[1]) - 1,
            additionalCases: Number(result.values[2]),
            subsidiaryId: result.values[3]?.toString() || "", //Leave in for reference only
            isCase: Number(result.values[4]) !== 1,
            linkedTransaction: result.values[5]?.toString() || "",
          };

          royalDiscount.setCurrentTransactionObject(queryResult);

          if (!queryResult.linkedTransaction) {
            log.error(
              `No transaction linked to ${royalDiscount.recordType}`,
              queryResult.transactionId
            );
            return;
          }

          royalDiscount.addSublistIndex(); //Add to create indices arr, initalize new one if needed.
          royalDiscount.incrementCasesCount(); //if transaction isCase, increment case
          royalDiscount.setLinkedTransaction();
        });

        return true;
      });

    return royalDiscount;
  };

  /**
   * Create a table row based on an array of cell values
   *
   * @param {string[]} row Cell values
   * @param {string} rowTag HTML tag for row
   * @param {string} cellTag HTML tag for cell
   * @returns {string} HTML table row string
   */
  exports.createTableRow = (row, rowTag, cellTag) => {
    const tableRowArr = [`<${rowTag}>`];

    row.forEach((cell) => {
      tableRowArr.push(`<${cellTag}>${cell}</${cellTag}>`);
    });
    tableRowArr.push(`</${rowTag}>`);

    return tableRowArr.join("");
  };

  /**
   * Generate tier group name from tier level names
   * E.g. 2 $20 Off Case(s), 4 $80 Off Case(s)
   *
   * @param {string[]} tierNames Array containing Tier Names
   * @returns {string} Tier group name
   */
  exports.getTierGroupName = (tierNames) => {
    const tierGroupNameArr = [];

    for (let i = 1; i <= maxNumberOfTiersPerGroup; i++) {
      if (tierNames[i]) {
        tierGroupNameArr.push(tierNames[i]);
      }
    }

    return tierGroupNameArr.join(", ");
  };

  /**
   * Generate WHERE clause filters for tier levels
   * Iterate through all available Tier Level IDs
   * E.g. WHERE tier1 = A AND tier2 = B
   *
   * @param {string[]} tierLevels Array of Tier Level record IDs
   * @param {{[key: number]: string}} tierFields Object containing Tier Group field IDs
   * @returns {string} Where clause filter
   */
  exports.getTierFilters = (tierLevels, tierFields) => {
    const tierFiltersArr = [];

    for (let i = 1; i <= maxNumberOfTiersPerGroup; i++) {
      const filter = tierLevels[i] ? `= ${tierLevels[i]}` : "IS NULL";
      tierFiltersArr.push(`${tierFields[i]} ${filter}`);
    }

    return tierFiltersArr.join(" AND ");
  };

  /**
   * Get an item's last purchase price for a specific subsidiary - by the stock unit
   *
   * @param {String} itemId Item ID
   * @param {String || Number} subsidiary
   * @returns {Array} Inventory Information
   */
  exports.getLastPurchasePriceByStockUnit = (subsidiary, itemId) => {
    const unitConversionRateSubSelect = `
		SELECT
   purchaseunit.conversionrate / stockunit.conversionrate 
FROM
   item 
   INNER JOIN
      unitstypeuom purchaseunit 
      ON purchaseunit.internalid = item.purchaseunit 
   INNER JOIN
      unitstypeuom stockunit 
      ON stockunit.internalid = item.stockunit 
WHERE
   id = ${itemId}`;

    const sqlQuery = `
		SELECT
   item.itemid,
   item.displayname,
   BUILTIN.DF(inventoryItemLocations.location) inventory_location,
   inventoryItemLocations.quantityavailable available,
   inventoryItemLocations.lastpurchasepricemli / (${unitConversionRateSubSelect}) stock_price_per_unit,
   inventoryItemLocations.lastpurchasepricemli,
   BUILTIN.DF(Location.subsidiary) subsidiary,
   --Field not available in criteria, only in results
FROM
   item 
   LEFT OUTER JOIN
      inventoryItemLocations 
      ON inventoryItemLocations.item = item.id 
   LEFT OUTER JOIN
      Location 
      ON inventoryItemLocations.location = Location.id 
   LEFT OUTER JOIN
      LocationSubsidiaryMap 		--Need this to filter for the subsidiary as Location.subsidiary is not available
      ON Location.id = LocationSubsidiaryMap.location 
WHERE
   LocationSubsidiaryMap.subsidiary = ? 
   AND item.id = ?`;

    const lastPurchasePriceByStockUnit = query
      .runSuiteQL({
        query: sqlQuery,
        params: [subsidiary, itemId],
      })
      .asMappedResults();

    return lastPurchasePriceByStockUnit;
  };
});
