/******************************************************************************************************
	Script Name - AVA_CLI_CertCaptureViewBatch.js
	Company -     Avalara Technologies Pvt Ltd.
******************************************************************************************************/

/**
 *@NApiVersion 2.0
 *@NScriptType ClientScript
*/

define(['N/currentRecord', 'N/url'],
	function(currentRecord, url){
		function fieldChanged(context){
			try{
				if(context.fieldId == 'custpage_batchnamelist'){
					var suiteURL = url.resolveScript({
						scriptId: 'customscript_avacertviewbatch_suitelet',
						deploymentId: 'customdeploy_avacertcaptureviewbatch'
					});
					var currentRecObj = context.currentRecord;
					var batchNameListValue = currentRecObj.getValue("custpage_batchnamelist");
					suiteURL += "&batchid=" + encodeURI(batchNameListValue);
					window.onbeforeunload = null;
					window.location.assign(suiteURL);
				}
			}
			catch(e){
				log.error('fieldChanged', 'ERROR NAME = ' + e.name + ', ERROR TYPE = ' + e.type + ', ERROR MESSAGE = ' + e.message);
			}
		}
		
		function saveRecord(context){
			try{
				var currentRec = currentRecord.get();
				var batchNameListValue = currentRec.getValue({
					fieldId: 'custpage_batchnamelist'
				});
				if(!batchNameListValue){
					alert('Select a batch to delete.');
					return false;
				}
				var batchStatus = currentRec.getValue({
					fieldId: 'ava_batchstatus'
				});
				if(batchStatus != 'Completed'){
					alert('You can delete a batch only when the status is complete.');
					return false;
				}
				return true;
			}
			catch(e){
				log.error('saveRecord', 'ERROR NAME = ' + e.name + ', ERROR TYPE = ' + e.type + ', ERROR MESSAGE = ' + e.message);
			}
		}
		
		function AVA_CertCaptureRefresh(){
			try{
				var currentRecObj = currentRecord.get();
				var batchId = currentRecObj.getValue({fieldId:'custpage_batchnamelist'});
				var criteriaSuiteURL = url.resolveScript({
					scriptId: 'customscript_avacertviewbatch_suitelet',
					deploymentId: 'customdeploy_avacertcaptureviewbatch'
				});
				criteriaSuiteURL += "&batchid=" + encodeURI(batchId);
				window.onbeforeunload = null;
				window.location.assign(criteriaSuiteURL);
			}
			catch(e){
				log.error('AVA_CertCaptureRefresh', 'ERROR NAME = ' + e.name + ', ERROR TYPE = ' + e.type + ', ERROR MESSAGE = ' + e.message);
			}
		}
		
		return{
			saveRecord: saveRecord,
			fieldChanged: fieldChanged,
			AVA_CertCaptureRefresh: AVA_CertCaptureRefresh
		};
	}
);