define([
	"require",
	"N/log",
	"N/search",
	"N/file",
	"GetEdiFileContents",
	"Moment",
	"PapaParse",
	"../../../EDI/Libraries/Customer_Integration_Libraries/Outgoing_832_Libs/spl_get_result_obj_for_832_lib",
], function (
	require,
	log,
	search,
	file,
	getEdiFileContentsLib,
	moment,
	papaParse
) {
	/**@type {import ("../../../EDI/Libraries/Customer_Integration_Libraries/Outgoing_832_Libs/spl_get_result_obj_for_832_lib")} */
	const getResultObjFor832Lib = require("../../../EDI/Libraries/Customer_Integration_Libraries/Outgoing_832_Libs/spl_get_result_obj_for_832_lib");
	function processCatalog(dataObj, savedSearchInternalId) {
		try {
			const searchObj = search.load({
				id: savedSearchInternalId,
			});

			const myPagedData = searchObj.runPaged();
			const itemRowResults = [];

			myPagedData.pageRanges.forEach(function (pageRange) {
				const myPage = myPagedData.fetch({ index: pageRange.index });

				myPage.data.forEach(function (row) {
					const rowObj = getResultObjFor832Lib.getForItemCatalog(row);

					itemRowResults.push(rowObj);
				});
			});

			const csvFile = papaParse.unparse(JSON.stringify(itemRowResults));
			const fileToUpload = getFileToUpload(csvFile);
			uploadFileToTheirServer(fileToUpload);
			uploadFileToSupplyLineServer(fileToUpload);
		} catch (e) {
			throw { name: `${e.name} ERROR_PROCESSING_CATALOG`, message: e.message };
		}

		//#region Catalog Helper Functions
		function getFileToUpload(csvFile) {
			return file.create({
				name: `SPLN_Item_Catalog_${moment().format("YYYY_MM_DD")}.csv`,
				fileType: file.Type.CSV,
				contents: csvFile,
			});
		}

		function uploadFileToTheirServer(fileToUpload) {
			const connection = getEdiFileContentsLib.createConnection(dataObj);

			connection.upload({
				file: fileToUpload,
				directory: "",
				replaceExisting: true,
			});
		}

		function uploadFileToSupplyLineServer(fileToUpload) {
			try {
				const referenceDirectory = `/EDI Reference Files/${dataObj.purchasingSoftware}/OUT/832`;

				const supplyLineConnection = getEdiFileContentsLib.createConnection(
					dataObj,
					"",
					referenceDirectory
				);

				supplyLineConnection.upload({
					file: fileToUpload,
					replaceExisting: true,
				});
			} catch (e) {
				throw `${dataObj.purchasingSoftware} ${dataObj.transactionType}:  Could not upload Item Catalog to SupplyLine Server. Error: ${e}`;
			}
		}
		//#endregion
	}

	return {
		processCatalog,
	};
});
