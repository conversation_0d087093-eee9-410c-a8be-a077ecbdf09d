//@ts-ignore
define(["N/log", "N/email", "EdiTransactionRecord"], function (
	log,
	emailc,
	EdiTransactionRecord
) {
	function getEdiTransactionObj(
		purchaseOrderName,
		salesOrderName,
		salesOrderInternalId,
		customerId,
		ediData,
		fileName,
		errorLog,
		processingLog
	) {
		/**
		 * Return the transaction json string as a Purchase Order Transaction Object
		 *
		 * @returns {import("../../../Classes/vlmd_edi_transaction").EdiTransactionRecord|null} Purchase Order Transaction Object
		 */

		/** @type {import("../../../Classes/vlmd_edi_transaction").EdiTransactionRecord} */
		const ediTransactionRecord = new EdiTransactionRecord();
		ediTransactionRecord.processingStatus = getProcessingStatus(
			errorLog,
			processingLog
		);
		ediTransactionRecord.emailSubject = getEmailSubject(
			ediTransactionRecord.processingStatus,
			salesOrderInternalId
		);
		ediTransactionRecord.emailMessage = getEmailMessage(
			errorLog,
			processingLog
		);

		ediTransactionRecord.controlNumber; //TODO: set
		ediTransactionRecord.customer = customerId ?? null;
		ediTransactionRecord.documentTypeId = ediData.documentTypeId;
		ediTransactionRecord.purchasingSoftwareId = ediData.purchasingSoftwareId;

		return ediTransactionRecord;

		function getProcessingStatus(errorLog, processingLog) {
			if (processingLog.length <= 0 && errorLog <= 0) {
				return 1; //Processed with no errors
			} else if (processingLog.length > 0 && errorLog <= 0) {
				return 2; //Processed with errors
			} else if (errorLog.length > 0) {
				return 4; //Document not created
			} else {
				return 3; //Programming error
			}
		}

		function getEmailSubject(processingStatus, salesOrderInternalId) {
			if (processingStatus == 4) {
				return `Failure: ${ediData.purchasingSoftware} ${fileName} Not Processed Successfully`;
			} else if (processingStatus == 1 && salesOrderInternalId) {
				return `Item Fulfillment Created Successfully for ${salesOrderName}`;
			} else if (processingStatus == 1 && !salesOrderInternalId) {
				return `Item Fulfillment Created Successfully for Warehouse Order ${purchaseOrderName}`;
			} else if (processingStatus == 2 && salesOrderInternalId) {
				return `DropShip Item Fulfillment Created With Errors for ${salesOrderName}`;
			} else if (processingStatus == 2 && !salesOrderInternalId) {
				return `Item Fulfillment Created With Errors for Warehouse Order ${purchaseOrderName}`;
			} else if (processingStatus == 4) {
				return `Tracking Not Processed`;
			}
		}

		function getEmailMessage(errorLog, processingLog) {
			let messageText = `Please review the tracking results below:
			
			`;

			if (processingLog.length > 0) {
				processingLog.forEach(
					(processingMessage) =>
						(messageText += `${processingMessage}

				`)
				);
			}

			if (errorLog.length <= 0) {
				if (salesOrderInternalId) {
					if (itemFulfillmentsCreated.length > 0) {
						messageText += "\n\nThe following item fulfillments were created:";
						itemFulfillmentsCreated.forEach(
							(itemFulfillment) => (messageText += "\n" + itemFulfillment)
						);
					} else {
						messageText += "\n\nNo item fulfillments created.";
					}
				} else {
					`Tracking number ${trackingNumber} added to ${purchaseOrderName}.`;
				}

				messageText += `\n\nEDI File Name: ${fileName}`;
			}
			return messageText;
		}
	}
	return {
		getEdiTransactionObj,
	};
});
