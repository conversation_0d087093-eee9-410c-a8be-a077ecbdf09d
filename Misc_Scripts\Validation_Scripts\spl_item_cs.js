/**
 * @description Sets and validates values:
 *
 * </br><b>Deployed On:</b> Inventory, Non-Inventory Items
 * </br><b>Exection Context:</b> ALL
 * </br><b>Event Type/Mode:</b> ALL
 * </br><b>Entry Points:</b> fieldChanged, saveRecord
 *
 * @NApiVersion 2.1
 * @NScriptType ClientScript
 * @NAmdConfig /SuiteScripts/config.json
 *
 * <AUTHOR>
 * @module spl_item_cs
 */

define([
	"require",
	"../../Classes/spl_item_record",
	"../../Classes/spl_ui_notification_object",
	"../../Classes/vlmd_custom_error_object",
	"N/log",
	"N/search",
	"N/ui/dialog",
], (/** @type {any} */ require) => {
	/** @type {import("../../Classes/spl_item_record").SplItemRecord} */
	const SplItemRecord = require("../../Classes/spl_item_record");

	/** @type {import("../../Classes/spl_ui_notification_object").UINotification} */
	const UINotification = require("../../Classes/spl_ui_notification_object");

	/** @type {import("../../Classes/vlmd_custom_error_object").CustomErrorObject} */
	const CustomErrorObject = require("../../Classes/vlmd_custom_error_object");

	const log = require("N/log");
	const search = require("N/search");
	const dialog = require("N/ui/dialog");

	const customErrorObject = new CustomErrorObject();
	let _isSplSubsidiary;
	let _originalCategory;
	let _subsidiary;

	const helperFunctions = (function () {
		return {
			/**
			 * Returns boolean whether subsidiary is Supplyline
			 *
			 * @returns {boolean}
			 */
			isSplSubsidiary(subsidiary) {
				//Subsidiaries can be a string or array, filtering for only Supplyline subsidiary
				return (
					(Array.isArray(subsidiary) && subsidiary.includes("1")) ||
					(typeof subsidiary == "string" && subsidiary == "1")
				);
			},

			dialogPopUp: (messageTitle, messageText, netSuiteItemRecord) => {
				function dialogSuccess(result) {
					if (result) {
						//User pressed "OK", setting the item name to "To be generated".
						//Keeping the field editable for now (as per Mr. Davidowitz)
						netSuiteItemRecord.setValue({
							fieldId: "itemid",
							value: "To Be Generated",
						});
					}
					if (!result) {
						//User pressed "Cancel", setting the category back to what it was
						netSuiteItemRecord.setValue({
							fieldId: "class",
							value: _originalCategory,
						});
					}
				}

				function dialogFailure(result) {
					log.error("Error with dialog!", result);
				}

				dialog
					.confirm({
						title: messageTitle,
						message: messageText,
					})
					.then(dialogSuccess)
					.catch(dialogFailure);
			},
		};
	})();

	return {
		//#region Commenting out until SPL team is ready for SKU change
		//pageInIt may not be called if the page was refreshed, so although we get the subsidiary here, we also get it on fieldChanged etc.

		pageInit: (context) => {
			const netSuiteItemRecord = context.currentRecord;
			_subsidiary = netSuiteItemRecord.getValue("subsidiary");
			_isSplSubsidiary = helperFunctions.isSplSubsidiary(_subsidiary);
			if (!_isSplSubsidiary) {
				return;
			}

			_originalCategory = netSuiteItemRecord.getValue("class");
			if (netSuiteItemRecord.isNew) {
				netSuiteItemRecord.setValue({
					fieldId: "itemid",
					value: "To Be Generated",
				});
			}
		},
		//#endregion

		fieldChanged: (context) => {
			try {
				switch (context.fieldId) {
					//#region case subsidiary
					//This case "subsidiary" may not be needed as each specific function gets the subsidiary anyway.
					//If we remove the get subsidiary from each function, will need to add this back in case the subsidiary changes.
					// case "subsidiary":
					// _subsidiary = context.currentRecord.getValue("subsidiary");
					// _isSplSubsidiary = helperFunctions.isSplSubsidiary(subsidiary);
					// break;
					//#endregion

					//#region case class
					case "class":
						try {
							let netSuiteItemRecord = context.currentRecord;

							//Get the subsidiary just in case it wasn't triggered on pageInIt or it changed since.
							_subsidiary = netSuiteItemRecord.getValue("subsidiary");
							_isSplSubsidiary = helperFunctions.isSplSubsidiary(_subsidiary);

							if (!_isSplSubsidiary) {
								return;
							}
							let splItemRecord = new SplItemRecord(netSuiteItemRecord);
							if (splItemRecord.dontAutomateItemName) {
								//The item's name should not be generated automatically.
								return;
							}
							const newCategory = netSuiteItemRecord.getValue("class");
							if (!newCategory) {
								/*Product category was removed.
						Netsuite will require this field on some forms when saving, but a query runs on fieldChanged that needs a category ID,
						so return here to prevent a SQL error from popping up.
						Also, don't want to reset the item SKU as there's nothing to pull from*/
								return;
							}
							const newCategoryParent =
								splItemRecord.getParentCategoryId(newCategory);
							const originalCategoryParent = _originalCategory
								? splItemRecord.getParentCategoryId(_originalCategory)
								: null;
							if (newCategoryParent == originalCategoryParent) {
								//Only the sub category was changed.
								return;
							}
							netSuiteItemRecord.isNew
								? netSuiteItemRecord.setValue({
										fieldId: "itemid",
										value: "To Be Generated",
								  })
								: helperFunctions.dialogPopUp(
										"Warning",
										"Changing the top level product category will cause the item SKU to be updated when saved. Please confirm to proceed with this change.",
										netSuiteItemRecord
								  );

							break;
						} catch (e) {
							customErrorObject.throwError({
								summaryText: `PRODUCT_CATEGORY_ERROR`,
								error: e,
							});
						}
					//#endregion

					case "vendorname":
						const uiNotification = new UINotification();
						netSuiteItemRecord = context.currentRecord;

						//Get the subsidiary just in case it wasn't triggered on pageInIt or it changed since.
						_subsidiary = netSuiteItemRecord.getValue("subsidiary");

						if (!_subsidiary) {
							//Sometimes, the subsidiary is not accesible via the regular getValue function, so use search lookup instead.
							//Ex: matrix sub-items

							_subsidiary = search.lookupFields({
								type: search.Type.ITEM,
								id: netSuiteItemRecord.id,
								columns: ["subsidiary"],
							}).subsidiary[0].value;
						}
						_isSplSubsidiary = helperFunctions.isSplSubsidiary(_subsidiary);

						if (!_isSplSubsidiary) {
							//Not Supplyline, return
							return;
						}

						let splItemRecord = new SplItemRecord(netSuiteItemRecord);
						if (!splItemRecord.vendorCode) {
							return;
						}

						try {
							splItemRecord.validateVendorCode();
						} catch (e) {
							uiNotification.addNotification(
								"warnings",
								`Vendor code warning! \n\n ${e.message}.`
							);
							uiNotification.displayNotifications();
						}
				}
			} catch (e) {
				customErrorObject.throwError({
					summaryText: `FIELD_CHANGED_ERROR`,
					error: e,
				});
			}
		},

		saveRecord: (context) => {
			const uiNotification = new UINotification();
			const itemRecord = new SplItemRecord(context.currentRecord);

			//Will need a separate try catch for each validation as the first one that throws an error will skip the rest
			try {
				itemRecord.validateDisplayComponents();
			} catch (e) {
				uiNotification.addNotification("errors", e.message);
			}

			uiNotification.displayNotifications();

			return uiNotification.allowSave(); //If only alerts and no errors, allow save. If errors, block save.
		},
	};
});
