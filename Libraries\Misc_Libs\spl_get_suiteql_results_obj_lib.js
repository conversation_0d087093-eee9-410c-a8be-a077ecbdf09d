//@ts-ignore
define(["N/log", "N/query"], function (log, query) {
	function getTransactionsEditedOrCreatedYesterday(
		transactionTypeArr,
		childSubsidiary,
		parentSubsidiary,
		subsidariesToExcludeArr,
		numberOfDaysBack,
		startDate,
		endDate,
		returnQueryString
	) {
		const selectClause = `SELECT DISTINCT
		trnsctn.id,
		trnsctn.type as recordtype,
		trnsctn.tranid`;

		const fromClause = `FROM
		   transaction AS trnsctn`;

		let joinClause = `LEFT OUTER JOIN
			  transactionLine as trnsctnln
			  ON trnsctnln.transaction = trnsctn.id
		   JOIN
			  subsidiary AS sbsdry
			  ON trnsctnln.subsidiary = sbsdry.id
			  `;

		if (parentSubsidiary) {
			joinClause += `
			JOIN
subsidiary AS prntsbsdry
ON sbsdry.parent = prntsbsdry.id
JOIN
subsidiary AS grndprntsbsdry 
ON prntsbsdry.parent = grndprntsbsdry.id `;
		}

		let whereClause = `WHERE
		  ${
				parentSubsidiary
					? `(prntsbsdry.id = '${parentSubsidiary}' OR grndprntsbsdry.id = '${parentSubsidiary}')`
					: "sbsdry.id = '" + childSubsidiary + "'"
			}`;

		subsidariesToExcludeArr.forEach((subsidiary) => {
			whereClause += `
			AND sbsdry.id NOT LIKE '${subsidiary}'`;
		});

		var transactionTypeString = ``;

		transactionTypeArr.forEach((transactionType, index) => {
			transactionTypeString +=
				(index > 0 ? `OR ` : ``) + `trnsctn.type = '${transactionType}'`;
		});

		whereClause += `
		AND (${transactionTypeString})`;

		whereClause +=
			startDate && endDate
				? `   
				AND 
				(
			 ( to_date(trnsctn.createddate, 'MM/DD/YYYY') BETWEEN to_date( '${startDate}', 'MM/DD/YYYY' ) AND to_date( '${endDate}', 'MM/DD/YYYY' ) ) 
				   OR 
				   (
					to_date( trnsctn.lastmodifieddate, 'MM/DD/YYYY') BETWEEN to_date( '${startDate}', 'MM/DD/YYYY' ) AND to_date( '${endDate}', 'MM/DD/YYYY' ) 
				   )
				)`
				: `
			AND
		   (
			  TO_CHAR (SYSDATE - ${
					numberOfDaysBack ? numberOfDaysBack : "1"
				} , 'MM/DD/YYYY') = TO_CHAR (trnsctn.createddate, 'MM/DD/YYYY')
			  OR TO_CHAR (SYSDATE - ${
					numberOfDaysBack ? numberOfDaysBack : "1"
				} , 'MM/DD/YYYY') = TO_CHAR (trnsctn.lastmodifieddate, 'MM/DD/YYYY')
		   )`;

		const sqlQuery = [selectClause, fromClause, joinClause, whereClause].join(
			"\n"
		);

		try {
			return returnQueryString
				? sqlQuery
				: (resultIterator =
						query
							.runSuiteQL({
								query: sqlQuery,
								params: [],
							})
							.asMappedResults() ?? []);
		} catch (e) {
			throw `${e}: ${sqlQuery}`;
		}
	}

	function getPurchasingSoftwareFeeForInvoice(invoiceInternalId) {
		const sqlQuery = `SELECT
		ps.custrecord_spl_prchsng_sftwr_cntrctd_rt,
 	FROM
		customrecord_vlmd_edi_integration AS integration 
	JOIN
	   map_customrecord_vlmd_edi_integration_custrecord_edi_intgrtn_prnt_fclty map 
	   ON map.mapone = integration.id 
	JOIN
	   customer AS integratedentity 
	   ON map.maptwo = 
	   (
		  CASE
			 WHEN
				integratedentity.parent IS NULL 
			 THEN
				integratedentity.id 
			 ELSE
				integratedentity.parent 
		  END
	   )
	JOIN
	   transaction AS invoice 
	   ON invoice.entity = integratedentity.id 
	INNER JOIN
	   previoustransactionlinelink AS ptll 
	   ON ptll.nextdoc = invoice.id 
	INNER JOIN
	   transaction AS so 
	   ON so.id = ptll.previousdoc 
	INNER JOIN
	   customrecord_vlmd_purchasing_software AS ps 
	   ON integration.custrecord_edi_intgrtn_prchsng_sftwr = ps.id 
 	WHERE
		invoice.id =  ?  
		AND 
		so.trandate >= integration.custrecord_edi_intgrtn_start_date
		AND
		so.trandate >= ps.custrecord_spl_ps_rate_start_date`;

		return _getSuiteQlResultsObj(sqlQuery, invoiceInternalId, false);
	}

	function getGPOReferralFeeResForInvoice(invoiceInternalId) {
		const sqlQuery = `SELECT
		gpo.custrecord_gpo_referral_fee,
	 FROM
		transaction AS inv 
		INNER JOIN
		   previoustransactionlinelink AS ptll 
		   ON ptll.nextdoc = inv.id 
		INNER JOIN
		   transaction AS so 
		   ON so.id = ptll.previousdoc 
		INNER JOIN
		   customer AS cus 
		   ON cus.id = so.entity 
		INNER JOIN
		   customer AS prnt 
		   ON prnt.id = cus.parent 
		INNER JOIN
		   customrecord_spl_gpo AS gpo 
		   ON prnt.custentity_mhi_supplyline_gpo = gpo.id 
	 WHERE
		inv.id =  ?  
		AND gpo.custrecord_spl_gpo_charge_referral_fee = 'T' 
		AND so.trandate >= gpo.custrecord_spl_commission_start_date`;

		return _getSuiteQlResultsObj(sqlQuery, invoiceInternalId, false);
	}

	function getCnsldtdShipRevenueForInvoice(invoiceInternalId) {
		const sqlQuery = `SELECT
		COUNT (invoiceshipping.doc) AS numberofinvoices,
		MAX(invoiceforsalesorder.id) AS lastinvoiceid,
		salesorder.status AS salesorderstatus,
		SUM (invoiceshipping.shippingrate) AS consolidatedrevenue,
	 FROM
		transaction AS currentinvoice 
		INNER JOIN
		   previoustransactionlinelink AS ptll 
		   ON ptll.nextdoc = currentinvoice.id 
		   AND ptll.previoustype = 'SalesOrd'
			AND ptll.nextline = 1
		INNER JOIN
		   transaction AS salesorder 
		   ON ptll.previousdoc = salesorder.id 
		INNER JOIN
		   nexttransactionlinelink AS ntll 
		   ON ntll.previousdoc = salesorder.id 
		   AND ntll.linktype = 'OrdBill' 
		   AND ntll.nextline = 1 
		INNER JOIN
		   transaction AS invoiceforsalesorder 
		   ON invoiceforsalesorder.id = ntll.nextdoc 
		INNER JOIN
		   transactionshipment AS invoiceshipping 
		   ON invoiceshipping.doc = invoiceforsalesorder.id 
	 WHERE
		currentinvoice.id = ? 
		AND currentinvoice.type = 'CustInvc'
	 GROUP BY
		salesorder.status`;

		return _getSuiteQlResultsObj(sqlQuery, invoiceInternalId, true);
	}

	function getRelatedBillForInvoice(invoiceInternalId) {
		const sqlQuery = `SELECT
		purchaseorder.status as postatus,
		SUM (DISTINCT billline.netamount)as billshipamount
	 FROM
		transaction AS currentinvoice 
		INNER JOIN
		   previoustransactionlinelink AS ptll 
		   ON ptll.nextdoc = currentinvoice.id 
		INNER JOIN
		   transaction AS salesorder 
		   ON ptll.previousdoc = salesorder.id 
		INNER JOIN
		   nexttransactionlinelink AS pontll 
		   ON pontll.previousdoc = salesorder.id 
		LEFT OUTER JOIN
		   transaction AS purchaseorder 
		   ON purchaseorder.id = pontll.nextdoc 
		LEFT OUTER JOIN
		   nexttransactionlinelink AS billntll 
		   ON billntll.previousdoc = purchaseorder.id 
		LEFT OUTER JOIN
		   transaction AS bill 
		   ON bill.id = billntll.nextdoc 
		   AND billntll.nexttype = 'VendBill' 
		LEFT OUTER JOIN
		   transactionline AS billline 
		   ON billline.transaction = bill.id  
		   AND billline.expenseaccount = 823
		/*51000 Cost of Goods Sold : Shipping*/
		   
	 WHERE
		currentinvoice.id =  ?  
		AND pontll.nexttype = 'PurchOrd' 
	 GROUP BY
		purchaseorder.status 
	 ORDER BY
		purchaseorder.status ASC`;

		return _getSuiteQlResultsObj(sqlQuery, invoiceInternalId, true);
	}

	function getCnsldtdLtlShipCogsForInvoice(invoiceInternalId) {
		let sqlQuery = `SELECT 
		SUM(billline.netamount)
	 FROM
		transaction AS currentinvoice 
		INNER JOIN
		   previoustransactionlinelink AS ptll 
		   ON ptll.nextdoc = currentinvoice.id 
		INNER JOIN
		   transaction AS salesorder 
		   ON ptll.previousdoc = salesorder.id 
		   AND ptll.previousline = 1
		INNER JOIN
		   transaction AS bill 
		   ON bill.custbody_spl_so_for_ltl_bill = salesorder.id 
		INNER JOIN
		   transactionline AS billline 
		   ON billline.transaction = bill.id 
	 WHERE
		currentinvoice.id =  ? 
		AND billline.expenseaccount in(472,823)`; //65510 Expenses : Delivery Services (1099) : Common Carrier
		//or 51000 Cost of Goods Sold : Shipping account
		return _getSuiteQlResultsObj(sqlQuery, invoiceInternalId, true);
	}

	function getInvoiceShipMethodIsDropShipWorkAround(invoiceInternalId) {
		const sqlQuery = `SELECT
		MAX (shipment.shippingmethod) 
	 FROM
		transaction AS currentinvoice 
		INNER JOIN
		   previoustransactionlinelink AS ptll 
		   ON ptll.nextdoc = currentinvoice.id 
		INNER JOIN
		   transaction AS salesorder 
		   ON ptll.previousdoc = salesorder.id 
		INNER JOIN
		   nexttransactionlinelink AS ntll 
		   ON ntll.previousdoc = salesorder.id 
		INNER JOIN
		   transactionshipment AS shipment 
		   ON shipment.doc = salesorder.id 
	 WHERE
		currentinvoice.id =  ?  
		AND ntll.nexttype = 'CustInvc' 
		AND 
		(
		   shipment.shippingmethod = 'Drop Ship' 
		   OR shipment.shippingmethod = 'Freight' 
		   OR shipment.shippingmethod = 'Customer Pick Up' 
		)`;

		return _getSuiteQlResultsObj(sqlQuery, invoiceInternalId, true);
	}

	function getCnsldtdLtlShipCogsForDropShipWorkAroundInvoice(
		invoiceInternalId
	) {
		const sqlQuery = `SELECT DISTINCT
            SUM(DISTINCT itemfulfillmentforsalesorder.custbody_spl_our_shipping_cost),
            SUM (DISTINCT itemfulfillmentshipinfo.shippingrate),
         FROM
            transaction AS currentinvoice 
            INNER JOIN
               previoustransactionlinelink AS ptll 
               ON ptll.nextdoc = currentinvoice.id 
            INNER JOIN
               transaction AS salesorder 
               ON ptll.previousdoc = salesorder.id 
            INNER JOIN
               nexttransactionlinelink AS ntll 
               ON ntll.previousdoc = salesorder.id 
            INNER JOIN
               transaction AS itemfulfillmentforsalesorder 
               ON itemfulfillmentforsalesorder .id = ntll.nextdoc 
            INNER JOIN
               transactionshipment AS itemfulfillmentshipinfo 
               ON itemfulfillmentforsalesorder.id = itemfulfillmentshipinfo.doc 
         WHERE
            currentinvoice.id =  ?  
            AND ntll.nexttype = 'ItemShip'`;
		return _getSuiteQlResultsObj(sqlQuery, invoiceInternalId, true);
	}

	function getCnsldtBillLinesForInvoice(invoiceInternalId) {
		//Get related ship account bill lines by associated item fulfillments
		const sqlQuery = `SELECT
		SUM(netamount) 
	 FROM
		transactionline 
	 WHERE
		custcol_related_item_fulfillment IN 
		(
		   SELECT
			  nextdoc 
		   FROM
			  nexttransactionlink 
		   WHERE
			  linktype = 'ShipRcpt' 
			  AND previousdoc IN 
			  (
				 SELECT
					previousdoc 
				 FROM
					previoustransactionlink 
				 WHERE
					linktype = 'OrdBill' 
					AND nextdoc = ?
			  )
		)`;
		return _getSuiteQlResultsObj(sqlQuery, invoiceInternalId, false);
	}

	function _getSuiteQlResultsObj(
		sqlQuery,
		invoiceInternalId,
		throwErrorIfNoResults
	) {
		try {
			const resultIterator = query.runSuiteQL({
				query: sqlQuery,
				params: [invoiceInternalId],
			});

			if (resultIterator.results.length <= 0 && throwErrorIfNoResults) {
				throw `No results returned for this query.`;
			}

			//Return 0 when no result line is queried
			return resultIterator.results[0] ?? 0;
		} catch (e) {
			throw `${e}: ${sqlQuery}`;
		}
	}

	return {
		getTransactionsEditedOrCreatedYesterday,
		getPurchasingSoftwareFeeForInvoice,
		getGPOReferralFeeResForInvoice,
		getCnsldtdShipRevenueForInvoice,
		getRelatedBillForInvoice,
		getCnsldtdLtlShipCogsForInvoice,
		getCnsldtdLtlShipCogsForDropShipWorkAroundInvoice,
		getInvoiceShipMethodIsDropShipWorkAround,
		getCnsldtBillLinesForInvoice,
	};
});
