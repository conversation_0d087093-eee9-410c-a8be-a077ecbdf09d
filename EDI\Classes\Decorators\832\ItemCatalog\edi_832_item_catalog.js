/**
 * @description Represents the Item Catalog object used within an EDI context
 *
 * @NApiVersion 2.1
 * <AUTHOR> <<EMAIL>>
 */

define([
    "exports",
    "require",
    "N/email",
    "N/file",
    "N/log",
    "./edi_832_item_catalog_processor",
    "../../../Models/File/edi_incoming",
    "../../../Models/File/edi_outgoing",
], (/** @type {any} */ exports, /** @type {any} */ require) => {
    const email = require("N/email");
    const file = require("N/file");
    const log = require("N/log");
    const {
        EDIItemCatalogProcessor,
    } = require("./edi_832_item_catalog_processor");
    const { EDIIncoming } = require("../../../Models/File/edi_incoming");
    const { EDIOutgoing } = require("../../../Models/File/edi_outgoing");

    /**
     * Item Catalog Class
     *
     * @class
     * @typedef {import("../../../Interfaces/Models/File/edi_file").EDIFileInterface} EDIFileInterface
     * @typedef {import("../../../Interfaces/Models/File/edi_outgoing").SuiteQLObjectReference} SuiteQLObjectReference
     * @typedef {import("../../../Interfaces/Decorators/832/edi_832_item_catalog").EDIItemCatalogInterface} EDIItemCatalogInterface
     * @typedef {import("../../../Interfaces/Decorators/832/edi_832_item_catalog").EDIItemCatalogQueryResult} EDIItemCatalogQueryResult
     * @typedef {import("../../../Interfaces/Models/Server/edi_server").EDIServerInterface} EDIServerInterface
     * @typedef {import("../../../../../Classes/vlmd_custom_error_object").CustomErrorObject} CustomErrorObject
     * @typedef {import("../../../Interfaces/Decorators/edi_decorator").DecoratorParams} DecoratorParams
     * @implements {EDIItemCatalogInterface}
     */
    class EDIItemCatalog {
        /** @param {DecoratorParams} params */
        constructor(params) {
            //EDI File
            /** @type {CustomErrorObject | null} */
            this.customError = null;
            /** @type {EDIItemCatalogProcessor} */
            this.processor = params.processor;
            /** @type {any} */
            this.parser = params.parser;
            /** @type {EDIServerInterface | null} */
            this.server = null;
            /** @type {string} */
            this.type = params.type;
            /** @type {number} */
            this.typeId = params.typeId;

            // EDI Outgoing
            /** @type {file.File | null} */
            this.file = null;
            /** @type {string} */
            this.fileName = "SPLN_MasterCatalog";
            /** @type {number} */
            this.fileId = 0;
            /** @type {number} */
            this.folderId = 6511263;
            /** @type {string} */
            this.emailSubject = "Requested EDI File";

            // EDI Incoming
            /** @type {any} */
            this.transaction = null;
        }

        /**
         * Decorate the EDI File with Item Catalog implementation
         *
         * @param {EDIFileInterface} ediFile Object to decorate
         */
        decorate(ediFile) {
            log.debug(
                "Item Catalog (decorate)",
                `Decorating the EDI File: ${ediFile.ediType}`,
            );
            ediFile.type = this.type;
            ediFile.typeId = this.typeId;
            if (ediFile instanceof EDIIncoming) {
                ediFile.parse = this.parse;
                ediFile.transform = this.transform;
                ediFile.archive = this.archive;
                ediFile.summarize = this.summarize;
            }
            else if (ediFile instanceof EDIOutgoing) {
                ediFile.processor = this.processor;
                ediFile.fileName = this.fileName;
                ediFile.folderId = this.folderId;
                ediFile.emailSubject = this.emailSubject;
                ediFile.load = this.load;
                ediFile.process = this.process;
                ediFile.create = this.create;
                ediFile.save = this.save;
                ediFile.email = this.email;
                ediFile.upload = this.upload;
                ediFile.complete = this.complete;
            }
        }

        /**
         * Parse the incoming EDI File
         *
         * @returns{void}
         */
        parse() {
            return;
        }

        /**
         * Transform an EDI File to a NetSuite record
         *
         * @returns {void}
         */
        transform() {
            return;
        }

        /**
         * Move the EDI File to the Reference directory
         *
         * @param {object} params Archive params
         * @param {string} [params.filename] File name
         * @param {string} [params.source] Source directory
         * @param {string} [params.target] Target directory
         * @param {string} [params.content] File content to upload
         * @returns {void}
         */
        archive(params) {
            return;
        }

        /**
         * Retrieve the email object after processing the transaction
         *
         * @returns {void}
         */
        summarize() {
            return;
        }

        /**
         * Return the query string to load the item records
         *
         * @param {{[key:string]: any}} params Parametrised object
         * @returns {SuiteQLObjectReference} Object containing the query string
         */
        load(params) {
            this.processor.setSoftwareSpecificValues();

            return {
                type: "suiteql",
                query: this.processor.getItemsQueryString(),
                params: [],
            };
        }

        /**
         * Parse the file and extract information to update various NetSuite records
         *
         * @param {{queryResultsRowObj: EDIItemCatalogQueryResult}} params Item Catalog query result
         * @returns {string}
         */
        process({ queryResultsRowObj }) {
            this.processor.validateQueryResultValues(queryResultsRowObj);
            this.processor.setCurrentItemDefaultValues();
            this.processor.setCurrentItemSpecificValues(queryResultsRowObj);
            this.processor.setDescription(queryResultsRowObj.description);
            this.processor.setUomValues(queryResultsRowObj);

            return this.processor.getCurrentItemAsString();
        }

        /**
         * Create the file object
         *
         * @param {{[key:string]: any}} params Parametrised object
         * @returns {void}
         */
        create(params) {
            try {
                this.processor.setSoftwareSpecificValues();
                this.processor.addCustomColumnHeaders();

                let [currentDate, currentTime] = new Date().toISOString().split("T");
                currentDate = currentDate.split("-").join("");
                currentTime = currentTime
                    .split(/[:\.]+/)
                    .slice(0, 3)
                    .join("");
                const fileName = `${this.fileName}_${currentDate}${currentTime}.csv`;

                log.debug(
                    "Item Catalog (create)",
                    `Creating file object ${fileName}..`
                );
                this.file = file.create({
                    name: fileName,
                    fileType: file.Type.CSV,
                    contents: this.processor.buildHeaderRow() + params.fileContent,
                });
            } catch (err) {
                throw this.customError?.updateError({
                    errorType: this.customError?.ErrorTypes.UNHANDLED_ERROR,
                    summary: "FILE_OBJ_NOT_CREATED",
                    details: `Error creating file object: ${err}`,
                });
            }
        }

        /**
         * Save file to a NetSuite folder
         *
         * @param {{[key:string]: any}} params Parametrised object
         * @returns {void}
         */
        save(params) {
            try {
                if (this.file) {
                    log.debug(
                        "Item Catalog (save)",
                        `Saving file in Folder ${this.folderId}..`
                    );
                    this.file.folder = this.folderId;
                    this.fileId = this.file.save();
                } else {
                    log.error("Error: Item Catalog (save)", "No file object to save.");
                }
            } catch (err) {
                throw this.customError?.updateError({
                    errorType: this.customError?.ErrorTypes.UNHANDLED_ERROR,
                    summary: "FILE_NOT_SAVED_TO_FOLDER",
                    details: `Error saving file to NetSuite folder: ${err}`,
                });
            }
        }

        /**
         * Email file to a recipient
         *
         * @param {{[key:string]: any}} params Parametrised object
         * @returns {void}
         */
        email(params) {
            try {
                if (this.file) {
                    log.debug(
                        "Item Catalog (email)",
                        `Sending email with file #${this.fileId}`
                    );
                    email.send({
                        author: 262579, // EDI
                        recipients: [3288, 140818], // TODO LP: Remove 140818
                        subject: this.emailSubject,
                        body: `<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
                            <html dir="ltr" xmlns="http://www.w3.org/1999/xhtml" xmlns:o="urn:schemas-microsoft-com:office:office">
                                <head>
                                    <meta charset="UTF-8">
                                    <meta content="width=device-width, initial-scale=1" name="viewport">
                                    <meta name="x-apple-disable-message-reformatting">
                                    <meta http-equiv="X-UA-Compatible" content="IE=edge">
                                    <meta content="telephone=no" name="format-detection">
                                    <title></title>
                                    <!--[if (mso 16)]>
                                    <style type="text/css">
                                    a {text-decoration: none;}
                                    </style>
                                    <![endif]-->
                                    <!--[if gte mso 9]><style>sup { font-size: 100% !important; }</style><![endif]-->
                                    <!--[if gte mso 9]>
                                    <xml>
                                        <o:OfficeDocumentSettings>
                                        <o:AllowPNG></o:AllowPNG>
                                        <o:PixelsPerInch>96</o:PixelsPerInch>
                                        </o:OfficeDocumentSettings>
                                    </xml>
                                    <![endif]-->
                                </head>
                                <body data-new-gr-c-s-loaded="14.1165.0">
                                    <div dir="ltr" class="es-wrapper-color">
                                        <!--[if gte mso 9]>
                                            <v:background xmlns:v="urn:schemas-microsoft-com:vml" fill="t">
                                                <v:fill type="tile" color="#fafafa"></v:fill>
                                            </v:background>
                                        <![endif]-->
                                        <table class="es-wrapper" width="100%" cellspacing="0" cellpadding="0">
                                            <tbody>
                                                <tr>
                                                    <td class="esd-email-paddings" valign="top">
                                                        <table cellpadding="0" cellspacing="0" class="es-content esd-header-popover" align="center">
                                                            <tbody>
                                                                <tr>
                                                                    <td class="esd-stripe" align="center">
                                                                        <table bgcolor="#ffffff" class="es-content-body" align="center" cellpadding="0" cellspacing="0" width="600">
                                                                            <tbody>
                                                                                <tr>
                                                                                    <td class="esd-structure es-p15t es-p20r es-p20l" align="left">
                                                                                        <table cellpadding="0" cellspacing="0" width="100%">
                                                                                            <tbody>
                                                                                                <tr>
                                                                                                    <td width="560" class="esd-container-frame" align="center" valign="top">
                                                                                                        <table cellpadding="0" cellspacing="0" width="100%">
                                                                                                            <tbody>
                                                                                                                <tr>
                                                                                                                    <td align="center" class="esd-block-text es-p10b es-m-txt-c">
                                                                                                                        <h1>Your file is ready</h1>
                                                                                                                    </td>
                                                                                                                </tr>
                                                                                                            </tbody>
                                                                                                        </table>
                                                                                                    </td>
                                                                                                </tr>
                                                                                            </tbody>
                                                                                        </table>
                                                                                    </td>
                                                                                </tr>
                                                                            </tbody>
                                                                        </table>
                                                                    </td>
                                                                </tr>
                                                            </tbody>
                                                        </table>
                                                        <table cellpadding="0" cellspacing="0" class="es-content esd-footer-popover" align="center">
                                                            <tbody>
                                                                <tr>
                                                                    <td class="esd-stripe" align="center">
                                                                        <table bgcolor="#ffffff" class="es-content-body" align="center" cellpadding="0" cellspacing="0" width="600">
                                                                            <tbody>
                                                                                <tr>
                                                                                    <td class="esd-structure es-p20t es-p10b es-p20r es-p20l" align="left">
                                                                                        <table cellpadding="0" cellspacing="0" width="100%">
                                                                                            <tbody>
                                                                                                <tr>
                                                                                                    <td width="560" class="esd-container-frame" align="center" valign="top">
                                                                                                        <table cellpadding="0" cellspacing="0" width="100%">
                                                                                                            <tbody>
                                                                                                                <tr>
                                                                                                                    <td align="center" class="esd-block-text es-p5t es-p5b es-p40r es-p40l es-m-p0r es-m-p0l">
                                                                                                                    <br/><br/>    
                                                                                                                    <p>Please find your requested file attached.</p>
                                                                                                                    <br/><br/>    
                                                                                                                    </td>
                                                                                                                </tr>
                                                                                                            </tbody>
                                                                                                        </table>
                                                                                                    </td>
                                                                                                </tr>
                                                                                            </tbody>
                                                                                        </table>
                                                                                    </td>
                                                                                </tr>
                                                                                <tr>
                                                                                    <td class="esd-structure es-p15t es-p10b es-p20r es-p20l" align="left">
                                                                                        <table cellpadding="0" cellspacing="0" width="100%">
                                                                                            <tbody>
                                                                                                <tr>
                                                                                                    <td width="560" align="left" class="esd-container-frame">
                                                                                                        <table cellpadding="0" cellspacing="0" width="100%">
                                                                                                            <tbody>
                                                                                                                <tr>
                                                                                                                    <td align="center" class="esd-block-text es-p10t es-p10b">
                                                                                                                        <p>Have a question? Reach out to <a href="mailto:<EMAIL>"><EMAIL></a></p>
                                                                                                                    </td>
                                                                                                                </tr>
                                                                                                            </tbody>
                                                                                                        </table>
                                                                                                    </td>
                                                                                                </tr>
                                                                                            </tbody>
                                                                                        </table>
                                                                                    </td>
                                                                                </tr>
                                                                            </tbody>
                                                                        </table>
                                                                    </td>
                                                                </tr>
                                                            </tbody>
                                                        </table>
                                                    </td>
                                                </tr>
                                            </tbody>
                                        </table>
                                    </div>
                                </body>
                            </html>`,
                        attachments: [this.file],
                    });
                } else {
                    log.error("Error: Item Catalog", "No file object to attach.");
                }
            } catch (err) {
                throw this.customError?.updateError({
                    errorType: this.customError?.ErrorTypes.UNHANDLED_ERROR,
                    summary: "ERROR_EMAILING_FILE",
                    details: `Error emailing file: ${err}`,
                });
            }
        }

        /**
         * Upload file to partner's server
         *
         * @param {{[key:string]: any}} params Parametrised object
         * @returns {void}
         */
        upload(params) {
            try {
                log.debug("Item Catalog (upload)", `Uploading file..`);
                if (this.file && this.server?.connection) {
                    this.server.connection.upload({
                        file: this.file,
                        replaceExisting: true,
                    });
                } else {
                    log.error(
                        "ERROR: Item Catalog (upload)",
                        "Cannot upload to partner's server. No connection or file created.",
                    );
                }
            } catch (err) {
                throw this.customError?.updateError({
                    errorType: this.customError.ErrorTypes.UNHANDLED_ERROR,
                    summary: "FILE_NOT_UPLOADED",
                    details: `Error uploading file: ${err}`,
                });
            }
        }

        /**
         * Mark the transaction as processed
         *
         * @param {{[key:string]: any}} [params] Parametrised object
         * @returns {number} Document Control Number record ID
         */
        complete(params) { return 0; }
    }

    exports.EDIItemCatalog = EDIItemCatalog;
});