/**
 * Interface and type definitions for Transaction Record class
 *
 * <AUTHOR>
 */

export interface ItemFulfillmentTransactionRecord {
	Field: {[key: string]: string};
	customerId: string;
	customerName: string;
	customerIsShipByTruck: boolean;
}

export interface TransactionRecord {
	Field: {[key: string]: string};
	customerId: string;
	customerName: string;
	transactionInternalId: number;
	transactionTotal: number;
	transactionNumber: string;
}

export interface CustomTransactionRecord extends TransactionRecord {
	Column: {[key: string]: string};
	customerLookup: object;
	customerParentObj?: CustomerParentObject;
	dontUseCustomerInNewOrders?: boolean;
}

export interface SalesOrderTransactionRecord extends CustomTransactionRecord {
	isCreditLimitExceeded: () => boolean;
	hasToBeCorrected: () => boolean;
	getDuplicateSalesOrder: () => string;
	createdFrom: string;
	creditLimitObj?: CreditLimitObject;
	correctionStatus: string;
	ediControlNumber: string;
	needFacilityInfo: boolean;
	orderStatus: string;
	poNumber: string;
}

export type CreditLimitObject {
	creditAndBalanceByParent: boolean;
	creditLimit: string;
	balance: string;
}

export type CustomerParentObject {
	id: string;
	name: string;
}