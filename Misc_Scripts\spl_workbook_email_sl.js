/**
 * @description Load the workbook and send the results via email
 *
 * </br><b>Event Type/Mode:</b> ALL
 * </br><b>Entry Points:</b> onRequest
 * </br><b>Execute as Role:</b> Current Role
 * 
 * This is only being used to test ldev-1722
 * Only deployed in SB1
 *
 * @NApiVersion 2.1
 * @NScriptType Suitelet
 * @module spl_workbook_email_sl
 * <AUTHOR>
 * @NAmdConfig /SuiteScripts/config.json
 */

define([
	"require",
	"N/log",
	"N/ui/serverWidget",
	"N/runtime",
	"N/workbook",
	"../Classes/spl_query_email"
], function (
	/** @type {any} */ require
) {

	const log = require("N/log");
	const serverWidget = require("N/ui/serverWidget");
	const runtime = require("N/runtime");
	const workbook = require("N/workbook");
	/** @type {import("../Classes/spl_query_email").QueryEmail} */
	const QueryEmail = require("../Classes/spl_query_email");
	
	return {

		/**
		 * 
		 * @param {import("N/types").EntryPoints.Suitelet.onRequestContext} context Suitelet On request context
		 */
		onRequest: function (context) {
			try {
				const form = serverWidget.createForm({
					title: 'Workbook'
				});

				// Add workbook list select field
				const workbookList = form.addField({
					id: 'custpage_workbook_list',
					type: serverWidget.FieldType.SELECT,
					label: 'Workbooks'
				});
				workbookList.isMandatory = true;
				workbookList.updateLayoutType({
					layoutType: serverWidget.FieldLayoutType.OUTSIDE
				});
				workbookList.updateBreakType({
					breakType: serverWidget.FieldBreakType.STARTROW
				});

				// Add submit button
				form.addSubmitButton({
					label: 'Submit'
				});

				let selectedWorkbook = "";
				if (context.request.method === "POST") {
					selectedWorkbook = context.request.parameters && context.request.parameters["custpage_workbook_list"];

					const workbookResultField = form.addField({
						type: serverWidget.FieldType.INLINEHTML,
						label: 'Workbook Result',
						id: 'custpage_workbook_result'
					});

					try {

						let queryEmail;

						/*
						// Test for SuiteQL
						queryEmail = new QueryEmail({
							columns: ["Name", "Day", "State"],
							queryString: `
								SELECT CUSTOMRECORD_SPL_ZIP_CODES.name, custrecord_spl_shpng_day_frm_shpng_zon, custrecord_spl_zip_code_state
								FROM  customrecord_spl_zip_codes
							`,
						});
						*/

						queryEmail = new QueryEmail({ queryId: selectedWorkbook, threshold: 71 });
						queryEmail.generateQueryResults();

						const tableHtml = queryEmail.buildResultHtml();

						workbookResultField.defaultValue = tableHtml;
						workbookResultField.updateLayoutType({
							layoutType: serverWidget.FieldLayoutType.OUTSIDE
						});
						workbookResultField.updateBreakType({
							breakType: serverWidget.FieldBreakType.STARTROW
						});

						queryEmail.sendResults({
							recipients: [runtime.getCurrentUser().id],
							subject: "Workbook Results",
							onlyIncludeHtml: true,
						})
					} catch ( /** @type {any} */ queryError) {
						log.error(queryError.name, queryError.message);
						workbookResultField.defaultValue = `<br><strong>Error loading ${selectedWorkbook}</strong><br>${queryError.name}: ${queryError.message}`;
					}
				}

				// Get all custom workbooks and add as select options
				// Default to the selectedWorkbook if it's available
				const workbookObjArr = workbook.list();

				// @ts-ignore workbookObjArr returns Object, but I want to specify the properties
				workbookObjArr.forEach(( /** @type {{name: string, id: string}} */ workbookObj) => {
					if (true) {
						workbookList.addSelectOption({
							text: workbookObj.name,
							value: workbookObj.id,
							isSelected: !!selectedWorkbook && selectedWorkbook === workbookObj.id
						});
					}
				});

				context.response.writePage(form);
			} catch ( /** @type {any} */ err) {
				log.error(err.name, err.message);
			}
		}
	};
});