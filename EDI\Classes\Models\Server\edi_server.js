/**
 * @description Represents connection to partner sftp servers
 *
 * @NApiVersion 2.1
 * <AUTHOR> <<EMAIL>>
 */

define([
    "exports",
    "require",
    "N/log",
    "N/sftp",
    "N/encode",
], (/** @type {any} */ exports, /** @type {any} */ require) => {
    const log = require("N/log");
    const sftp = require("N/sftp");
    const encode = require("N/encode");

    /**
     * EDI Server Class
     *
     * @class
     * @typedef {import("../../Interfaces/Models/Server/edi_server").EDIServerInterface} EDIServerInterface
     * @typedef {import("../../Interfaces/Models/Server/edi_server").EDIConnectionParameters} EDIConnectionParameters
     * @typedef {import("../../Interfaces/Models/Partner/edi_partner").EDIPartnerInterface} EDIPartnerInterface
     * @typedef {import("../../../../Classes/vlmd_custom_error_object").CustomErrorObject} CustomErrorObject
     * @implements {EDIServerInterface}
     */
    class EDIServer {
        /** @param {{[key:string]: any}} props */
        constructor(props) {
            /** @type {string} */
            this.username = "edi";
            /** @type {string} */
            this.url = "************";
            /** @type {EDIPartnerInterface} */
            this.partner = props.partner;
            /** @type {string} */
            this.prodGUID = props.prodGUID || "";
            /** @type {string} */
            this.sandboxGUID = props.sandboxGUID || "";
            /** @type {string} */
            this.hostKey = "AAAAB3NzaC1yc2EAAAABIwAAAIEA2YIvZPcsWA3jP6yq5J" +
                "5XYunNySNspUZUhtzxXoePDZEhxJFIF/am9o+15CqO4hlyEDni0m+VW4" +
                "LVSoiUnj31xJis91EcYrUeCewIcSwof4Xs05ghth/2SKn0cITsR5hOeST" +
                "l4jFsMf9oK0+b2gr4jdV4fria4E/eF2u0qQArWuc=";
            /** @type {sftp.Connection | null} */
            this.connection = null;
            /** @type {CustomErrorObject} */
            this.customError = props.customError;
        }

        /**
         * Create an N/sftp connection
         *
         * @param {EDIConnectionParameters} params Connection parameters
         * @returns {sftp.Connection | undefined} SFTP Server connection
         */
        connect(params) {
            let directory = '/';
            let passwordGuid = this.prodGUID;
            let username = this.username;
            
            if (params.target) {
                switch (params.target) {
                    case "PROD":
                        directory = this.partner.prodDirectory;
                        break;
                    case "SANDBOX":
                        directory = this.partner.testDirectory;
                        passwordGuid = this.sandboxGUID;
                        break;
                    case "REF":
                        directory = this.partner.referenceDirectory;
                        break;
                    default:
                        break;
                };
            } else if (params.directory !== undefined && params.username && params.passwordGuid) {
                directory = params.directory;
                username = params.username;
                passwordGuid = params.passwordGuid;
            } else {
                log.error("PARAMETERS_ERROR", `No target or overrides supplied: ${JSON.stringify({
                    target: params.target || "<none>",
                    directory: params.directory || "<none>",
                    username: params.username || "<none>",
                    passwordGuid: params.passwordGuid || "<none>",
                })}`);

                return;
            }

            const connectionParams = {
                username,
                passwordGuid,
                url: this.url,
                hostKey: this.hostKey,
                directory
            };

            log.audit({
                title: "EDI Server (connect)",
                details: `Connecting to FTP server: ${JSON.stringify(connectionParams)}`,
            });
            this.connection = this.username && passwordGuid && this.url && this.hostKey
                ? sftp.createConnection(connectionParams)
                : null;

            if (!this.connection) {
                log.error({
                    title: "NO_SERVER_CONNECTION",
                    details: "Connection to server was not created.",
                });
                throw this.customError.updateError({
                    errorType: this.customError.ErrorTypes.SFTP_CONNECTION_FAILURE,
                    summary: "NO_SERVER_CONNECTION",
                    details: "Connection to server was not created.",
                });
            } else {
                return this.connection;
            }
        }

        /**
         * Download the file from the SFTP server
         *
         * @param {string} filename File name in the server
         * @param {{[key:string]: boolean}} options Additional optional flags
         * @returns {string} File content
         */
        download(filename, options) {
            try {
                const downloadedFile = this.connection?.download({
                    directory: "",
                    filename: filename,
                });

                log.debug({
                    title: "EDI Server (download)",
                    details: JSON.stringify({downloadedFile}),
                });

                const fileContent = downloadedFile?.getContents();

                if (fileContent && options?.decode) {
                    return encode.convert({
                        string: fileContent,
                        inputEncoding: encode.Encoding.BASE_64,
                        outputEncoding: encode.Encoding.UTF_8,
                    });
                }

                return fileContent ?? "";
            } catch (/** @type {any} */ err) {
                log.error({
                    title: "EDI Server (download)",
                    details: `Failed to download ${filename}: ${err}`,
                });
                throw this.customError.updateError({
                    errorType: this.customError.ErrorTypes.FILE_NOT_LOADED,
                    summary: "DOWNLOAD_FAILED",
                    details: err,
                });
            }
        }
    }

    exports.EDIServer = EDIServer;
});