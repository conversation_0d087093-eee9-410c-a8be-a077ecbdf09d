/**
 * @description Iterates over all BRDG Allied POs that are fully billed within the past month that have more then one bill and runs RIPS on them
 *
 * </br><b>Schedule:</b> Runs at end of every month
 *
 * @NApiVersion 2.1
 * @NScriptType MapReduceScript
 * @NAmdConfig /SuiteScripts/config.json
 *
 * <AUTHOR>
 * @module brdg_rip_run_multiple_bills_po_mr
 */

define([
  "require",
  "N/log",
  "N/query",
  "N/email",
  "N/runtime",
  "N/record",
  "../../../Classes/vlmd_custom_error_object",
  "../Libraries/brdg_rip_multiple_bills_lib",
], (/** @type {any} */ require) => {
  const log = require("N/log");
  const query = require("N/query");
  const email = require("N/email");
  const runtime = require("N/runtime");
  const record = require("N/record");

  /**@type {import ("../../../Classes/vlmd_custom_error_object").CustomErrorObject}} */
  const CustomErrorObject = require("../../../Classes/vlmd_custom_error_object");
  const customErrorObject = new CustomErrorObject();

  /**@type {import ("../Libraries/brdg_rip_multiple_bills_lib")}} */
  const multipleBillsLib = require("../Libraries/brdg_rip_multiple_bills_lib");

  function getRelatedBillsFromPo(poId) {
    const relatedBillsQuery = `SELECT nextdoc
    FROM   nexttransactionlink
           JOIN TRANSACTION t
             ON t.id = nextdoc
           LEFT OUTER JOIN transactionline tl
                        ON tl.TRANSACTION = t.id
           JOIN subsidiary AS sbsdry
             ON tl.subsidiary = sbsdry.id
           JOIN subsidiary AS prntsbsdry
             ON sbsdry.parent = prntsbsdry.id
           JOIN subsidiary AS grndprntsbsdry
             ON prntsbsdry.parent = grndprntsbsdry.id
    WHERE  linktype = 'OrdBill'
           AND t.type = 'VendBill'
           AND ( sbsdry.id = '16'
                  OR prntsbsdry.id = '16'
                  OR grndprntsbsdry.id = '16' )
           AND previousdoc = ?
    GROUP  BY nextdoc `;

    const sqlResults = query
      .runSuiteQL({
        query: relatedBillsQuery,
        params: [poId],
      })
      .asMappedResults();

    const relatedBills = [];
    sqlResults.forEach((itemObj) => relatedBills.push(Number(itemObj.nextdoc)));

    return relatedBills;
  }

  function sendResultsEmail(resultsObj) {
    const { posUpdatedSucessfully, posWithErrors } = resultsObj;

    let emailText = `The regenerate rips mr that runs on multiple bills created from the same PO was just ran. See results below:<br /> `;

    if (posUpdatedSucessfully.length > 0) {
      let sucessTable = `<table style = "padding: 10px">
    <caption>${posUpdatedSucessfully.length} ${
        posUpdatedSucessfully.length > 1 ? " POs" : " PO"
      } updated successfully:</caption>
				<tr>
				<th  style = "border: 1px solid black; border-collapse: collapse">PURCHASE ORDER</th>
				<th  style = "border: 1px solid black; border-collapse: collapse">RELATED BILLS</th>
				</tr>`;

      for (i = 0; i < posUpdatedSucessfully.length; i++) {
        ("<tr>");
        sucessTable += `<td style = "border: 1px solid black; border-collapse: collapse">
          <a href='/app/accounting/transactions/purchord.nl?id=${posUpdatedSucessfully[i].poId}'> Purchase Order </a>
						</td>`;

        if (posUpdatedSucessfully[i].updatedBillsArr != null) {
          posUpdatedSucessfully[i].updatedBillsArr.forEach(
            (updatedBill) =>
              (sucessTable += `<td style = "border: 1px solid black; border-collapse: collapse">
          
              <a href='/app/accounting/transactions/vendbill.nl?id=${updatedBill}'> Updated Bill </a> 
          
						</td>`)
          );
        }
        sucessTable += `</tr>`;
      }
      sucessTable += "</table>";
      emailText += `<br/><br/>${sucessTable}`;
    }

    if (posWithErrors.length > 0) {
      let errorTable = `<table style = "padding: 15px">
      <caption>${posWithErrors.length} ${
        posWithErrors.length > 1 ? " POs" : " PO"
      } could not update:</caption>
				<tr>
				<th  style = "border: 1px solid black; border-collapse: collapse">BILL</th>
				<th  style = "border: 1px solid black; border-collapse: collapse">ERROR</th>
				</tr>`;
      for (i = 0; i < posWithErrors.length; i++) {
        ("<tr>");
        errorTable += `<td style = "border: 1px solid black; border-collapse: collapse">
          <a href='/app/accounting/transactions/purchord.nl?id=${
            posWithErrors[i].poId
          }'> Vendor Bill </a>
						</td>
            <td style = "border: 1px solid black; border-collapse: collapse">
          ${posWithErrors[i].errorMessage ?? "No specific error message found"}
						</td>
            </tr>`;
      }
      errorTable += "</table>";
      emailText += `<br/><br/>${errorTable}`;
    }

    email.send({
      author: 223244, //Requests
      recipients: "<EMAIL>",
      body: emailText,
      subject: `Results from multiple bills per PO`,
    });
  }

  function getInputData(context) {
    try {
      const currentScript = runtime.getCurrentScript();

      const poId = currentScript.getParameter({
        name: "custscript_specific_po_id",
      });

      if (poId) {
        return {
          value: {
            values: [poId],
          },
        };
      }
      const posWithDuplicateBillsQuery = `SELECT
      RecordID, 
    FROM 
      SystemNote 
    WHERE 
      RecordTypeID = -30 
      AND
      Field = 'TRANDOC.KSTATUS' 
      and oldvalue != 'Fully Billed' 
      and newvalue = 'Fully Billed' 
      and TO_DATE(SystemNote.date, 'MM/DD/YYYY') BETWEEN TO_DATE(
        BUILTIN.RELATIVE_RANGES('LMTD', 'END'), 
        'MM/DD/YYYY'
      ) 
      AND TO_DATE(SYSDATE, 'MM/DD/YYYY') 
      AND RECORDID IN (
        select 
          previousdoc
        from 
          nexttransactionlink 
          JOIN transaction t on t.id = previousdoc 
          LEFT OUTER JOIN transactionline tl on tl.transaction = t.id 
        where 
          --previousdoc = 7014153 and 
          linktype = 'OrdBill' 
          and t.type = 'PurchOrd' 
          and (
            BUILTIN.DF(tl.subsidiary) like '%ineyard%' 
            or BUILTIN.DF(tl.subsidiary) like '%ridge%'
          ) 
        group by 
          previousdoc, 
          BUILTIN.DF(previousdoc), 
          t.type, 
          BUILTIN.DF(tl.subsidiary) 
        having 
          count(distinct nextdoc) > 2
      ) 
    GROUP BY 
      recordid
    `;
      //Test query for SB bec. no system notes in SB
      const testQuery = `SELECT id
    FROM   transaction
    WHERE 
     id = '7035951'
`;

      return {
        type: "suiteql",
        query: posWithDuplicateBillsQuery,
        params: [],
      };
    } catch (e) {
      customErrorObject.throwError({
        summaryText: `GET_INPUT_DATA_ERROR`,
        error: e,
        errorWillBeGrouped: true,
      });
    }
  }
  function map(context) {
    const mapErrorObject = new CustomErrorObject();
    var parsedResult = JSON.parse(context.value).values;
    const [poId] = parsedResult;
    try {
      const relatedBills = getRelatedBillsFromPo(poId);

      context.write({
        key: "Successfully Got PO",
        value: { poId: poId, relatedBillsArr: relatedBills },
      });
    } catch (e) {
      context.write({
        key: "Error With PO",
        value: { poId: poId, errorMessage: e.message },
      });
      mapErrorObject.throwError({
        summaryText: `MAP_ERROR`,
        error: e,
        recordId: billId,
        errorWillBeGrouped: true,
      });
    }
  }

  function reduce(context) {
    const reduceErrorObject = new CustomErrorObject();
    const passedInObj = JSON.parse(context.values);

    try {
      if (context.key == "Successfully Got PO") {
        const relatedBillsArr = passedInObj.relatedBillsArr;
        const updateRecordsFromLib =
          multipleBillsLib.generateRipsOnMultipleBills(relatedBillsArr);
        context.write({
          key: "PO Bill's Updated Sucessfully",
          value: {
            poId: passedInObj.poId,
            updatedBillsArr: updateRecordsFromLib.updatedBillsArr,
          },
        });
      } else {
        context.write({
          key: "POs With Errors",
          value: {
            poId: passedInObj.poId,
            errorMessage: passedInObj.errorMessage,
          },
        });
      }
    } catch (e) {
      context.write({
        key: "POs With Errors",
        value: {
          poId: passedInObj.poId,
          errorMessage: e.message,
        },
      });
      reduceErrorObject.throwError({
        summaryText: `REDUCE_ERROR`,
        error: e,
        errorWillBeGrouped: true,
      });
    }
  }

  function summarize(context) {
    const posUpdatedSucessfully = [];
    const posWithErrors = [];
    context.output.iterator().each(function (key, value) {
      switch (key) {
        case "PO Bill's Updated Sucessfully":
          posUpdatedSucessfully.push(JSON.parse(value));
          break;

        case "POs With Errors":
          posWithErrors.push(JSON.parse(value));
          break;
      }
      return true; //To continue iterating the context.output
    });
    if (posUpdatedSucessfully.length > 0 || posWithErrors.length > 0) {
      log.debug("POs Updated Succesfully", posUpdatedSucessfully);
      log.debug("posWithErrors", posWithErrors);
      sendResultsEmail({ posUpdatedSucessfully, posWithErrors });
    }
  }
  return {
    getInputData,
    map,
    reduce,
    summarize,
  };
});
