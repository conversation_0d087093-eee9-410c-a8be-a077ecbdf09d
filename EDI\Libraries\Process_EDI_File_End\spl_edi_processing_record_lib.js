define([
	"require",
	"../../../Helper_Libraries/vlmd_record_module_helper_lib",
], function (require) {
	const log = require("N/log");
	const record = require("N/record");
	const recordHelperLib = require("../../../Helper_Libraries/vlmd_record_module_helper_lib");

	function getInitialDataObj(paramsObj, customErrorObject) {
		try {
			const {
				purchasingSoftware,
				transactionType,
				purchasingSoftwareId,
				documentTypeId,
			} = paramsObj;

			return {
				recipients: ["<EMAIL>"],
				cc: ["<EMAIL>"],
				processingStatus: 7, //In Progress,
				subject: `EDI ${purchasingSoftware} ${transactionType} In Progress`,
				purchasingSoftwareId,
				documentTypeId,
			};
		} catch (err) {
			throw customErrorObject.updateError({
				errorType: customErrorObject.ErrorTypes.NO_VALUE_GOTTEN,
				summary: "RESULTS_OBJ_NOT_GOTTEN",
				details: `Error getting edi transaction results obj: ${JSON.stringify(
					err
				)}`,
			});
		}
	}

	function createRecord(paramsObj, customErrorObject) {
		try {
			const {
				recipients,
				cc,
				processingStatus,
				subject,
				purchasingSoftwareId,
				documentTypeId,
			} = paramsObj;

			const formsDirectory = {
				6: 310, //Invoice : Customer Form
				null: -1707, //Standard Form
			};

			try {
				var ediTransactionRecord = record.create({
					type: "customrecord_spl_edi_transaction",
				});
			} catch (err) {
				throw customErrorObject.updateError({
					errorType: customErrorObject.ErrorTypes.RECORD_NOT_CREATED,
					summary: "GET_INPUT_DATA_EDI_PROCESSING_RECORD_NOT_CREATED",
					details: `EDI processing record not created: ${JSON.stringify(err)}`,
				});
			}

			const mappedValuesObj = {
				custrecord_spl_edi_document_type: documentTypeId,
				custrecord_spl_processing_status: processingStatus,
				custrecord_spl_edi_email_subject: subject,
				custrecord_spl_edi_email_recipient: recipients && recipients[0],
				custrecord_spl_edi_email_cc_recipient: cc && cc[0],
				custrecord_spl_edi_purchasing_software: purchasingSoftwareId,
				customform: formsDirectory[documentTypeId],
			};

			try {
				recordHelperLib.setBodyValues(mappedValuesObj, ediTransactionRecord);
			} catch (err) {
				throw customErrorObject.updateError({
					errorType: customErrorObject.ErrorTypes.VALUE_NOT_SET,
					summary: "RECORD_BODY_VALUES_NOT_SET",
					details: `Error setting edi transaction document main values: ${err}`,
				});
			}

			try {
				return (ediTransactionRecordId = ediTransactionRecord.save());
			} catch (err) {
				throw customErrorObject.updateError({
					errorType: customErrorObject.ErrorTypes.RECORD_NOT_SAVED,
					summary: "EDI_PROCESSING_RECORD_NOT_SAVED",
					details: `Error saving record for the first time: ${JSON.stringify(
						err
					)}`,
				});
			}
		} catch (err) {
			throw customErrorObject.updateError({
				errorType: customErrorObject.ErrorTypes.UNHANDLED_ERROR,
				summary: "ERROR_CREATING_EDI_TRANSACTION_PROCESSING_RECORD",
				details: `Error creating EDI transaction processing record: ${JSON.stringify(
					err
				)}`,
			});
		}
	}

	function getSummaryDataObj(paramsObj, customErrorObject) {
		try {
			const {
				recordsProcessedMessage,
				errorsMessage,
				noTransactionsToProcess,
				purchasingSoftware,
				transactionType,
				purchasingSoftwareId,
				documentTypeId,
				poRefNumber,
			} = paramsObj;

			let statusId;

			const statusObj = {
				1: "Success",
				5: "No Transactions To Process",
				3: "Failure",
			};

			if (!errorsMessage && recordsProcessedMessage) {
				statusId = 1; //Processed With No Errors
			} else if (
				!errorsMessage &&
				!recordsProcessedMessage &&
				noTransactionsToProcess
			) {
				statusId = 5; //No Transactions To Process
			} else {
				statusId = 3; //Programming Error;
			}

			const resultsText = `${
				recordsProcessedMessage
					? "Records Processed Successfully:\n" + recordsProcessedMessage + "\n"
					: ""
			}
${errorsMessage ? "Errors:\n" + errorsMessage + "\n" : ""}
${
	noTransactionsToProcess
		? "No Transactions To Process:\n" + noTransactionsToProcess
		: ""
}`;

			return {
				recipients: ["<EMAIL>"],
				cc: ["<EMAIL>"],
				documentControlNumbers: recordsProcessedMessage
					? recordsProcessedMessage.split(",")
					: [],
				processingStatus: statusId,
				status: statusObj[statusId],
				subject: `${statusObj[statusId]}: EDI ${purchasingSoftware} ${transactionType} Results`,
				resultsText,
				purchasingSoftwareId,
				poRefNumber,
				documentTypeId,
			};
		} catch (err) {
			throw customErrorObject.updateError({
				errorType: customErrorObject.ErrorTypes.NO_VALUE_GOTTEN,
				summary: "RESULTS_OBJ_NOT_GOTTEN",
				details: `Error getting edi transaction results obj: ${JSON.stringify(
					err
				)}`,
			});
		}
	}

	function updateRecord(paramsObj, customErrorObject) {
		try {
			let { processingStatus, subject, resultsText, ediTransactionRecordId } =
				paramsObj;

			let ediTransactionRecord;

			try {
				ediTransactionRecord = record.load({
					type: "customrecord_spl_edi_transaction",
					id: ediTransactionRecordId,
				});
			} catch (err) {
				throw customErrorObject.updateError({
					errorType: customErrorObject.ErrorTypes.RECORD_NOT_LOADED,
					summary: "SUMMARIZE_EDI_PROCESSING_RECORD_NOT_LOADED",
					details: `EDI processing record not created: ${JSON.stringify(err)}`,
				});
			}

			const mappedValuesObj = {
				custrecord_spl_processing_status: processingStatus,
				custrecord_spl_edi_email_subject: subject,
				custrecord_spl_edi_email_message: resultsText,
			};

			try {
				recordHelperLib.setBodyValues(mappedValuesObj, ediTransactionRecord);
			} catch (err) {
				throw customErrorObject.updateError({
					errorType: customErrorObject.ErrorTypes.VALUE_NOT_SET,
					summary: "RECORD_BODY_VALUES_NOT_SET",
					details: `Error setting edi transaction document main values: ${err}`,
				});
			}

			try {
				ediTransactionRecordId = ediTransactionRecord.save();
			} catch (err) {
				throw customErrorObject.updateError({
					errorType: customErrorObject.ErrorTypes.RECORD_NOT_SAVED,
					summary: "EDI_PROCESSING_RECORD_NOT_SAVED",
					details: `Error saving record for the second time: ${JSON.stringify(
						err
					)}`,
				});
			}
		} catch (err) {
			throw customErrorObject.updateError({
				errorType: customErrorObject.ErrorTypes.UNHANDLED_ERROR,
				summary: "ERROR_UPDATING_EDI_TRANSACTION_PROCESSING_RECORD",
				details: `Error updating EDI transaction processing record: ${JSON.stringify(
					err
				)}`,
			});
		}
	}

	return {
		getInitialDataObj,
		getSummaryDataObj,
		createRecord,
		updateRecord,
	};
});
