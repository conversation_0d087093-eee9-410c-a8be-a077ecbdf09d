/**
 * @description Class containing function template specific to parsing 850 Incoming file
 *
 * @NApiVersion 2.1
 * <AUTHOR> <<EMAIL>>
 */

define([
    "exports",
    "require",
    "N/log",
    "../edi_parser",
], (/** @type {any} */ exports, /** @type {any} */ require) => {
    const log = require("N/log");
    const { EDIParser } = require("../edi_parser");

    /**
     * 850 Parser Class
     *
     * @class
     * @typedef {import("../../Interfaces/Models/File/edi_file").EDIPostProcessEmail} EDIPostProcessEmail
     * @typedef {import("../../Interfaces/Decorators/850/edi_850_parser").EDI850ParserInterface} EDI850ParserInterface
     * @extends {EDIParser}
     * @implements {EDI850ParserInterface}
     */
    class EDI850Parser extends EDIParser {
        /** @param {{[key:string]: any}} params */
        constructor(params) {
            super(params);
        }

        parse() {
            log.error("EDI 850 Parser", "Error: Parent parse function needs to be implemented by child class.");
        }

        transform() {
            log.error("EDI 850 Parser", "Error: Parent transform function needs to be implemented by child class.");
        }

        summarize() {
            log.error("EDI 850 Parser", "Error: Parent summarize function needs to be implemented by child class.");
        }
    }

    exports.EDI850Parser = EDI850Parser;
});