SELECT
   CASE
      WHEN
         log.custrecord_in8_vend_tran_custom LIKE '%31f506b0-4549-4dd5-a7f4-8680bd6ec836%' 
      THEN
         261911 			-- <PERSON><PERSON><PERSON> - north
      WHEN
         log.custrecord_in8_vend_tran_custom LIKE '%80b1ac6f-5d7d-467f-8beb-06f7b1e3409d%' 
      THEN
         261911 			-- <PERSON><PERSON><PERSON> - westgate
      WHEN
         log.custrecord_in8_vend_tran_custom LIKE '%fc6668aa-717c-404c-bded-d8c526801a68%' 
      THEN
         261911 			-- <PERSON><PERSON><PERSON> - south
      WHEN
         log.custrecord_in8_vend_tran_custom LIKE '%59405c22-a5f9-469f-9ea1-41787c1fd3fd%' 
      THEN
         261911 			-- <PERSON><PERSON><PERSON> - express
      WHEN
         log.custrecord_in8_vend_tran_custom LIKE '%39cc04bc-ce4f-4384-b5cd-89606a9b5efc%' 
      THEN
         261911 			-- <PERSON><PERSON><PERSON> - <PERSON><PERSON>
      WHEN
         log.custrecord_in8_vend_tran_custom LIKE '%6492d76e-f5ac-481b-952d-0b8ef2e1ce68%' 
      THEN
         261911 			-- Yehuda Stefansky - lol
      WHEN
         log.custrecord_in8_vend_tran_custom LIKE '%bdcc430a-02c1-4e90-aeab-5bd505fe15b6%' 
      THEN
         17897 			-- Simcha Ingber- madison
      WHEN
         log.custrecord_in8_vend_tran_custom LIKE '%7bf319a4-a009-4afa-9b72-b377a94cc90c%' 
      THEN
         17897 			-- Simcha Ingber- westgate
      WHEN
         log.custrecord_in8_vend_tran_custom LIKE '%8ff27c43-add2-47e1-ad81-ffc93b109b73%' 
      THEN
         17897 			-- Simcha Ingber- south
      WHEN
         log.custrecord_in8_vend_tran_custom LIKE '%ba7cc74e-3470-4e56-b38e-11864bab7959%' 
      THEN
         17897 			-- Simcha Ingber- express
      WHEN
         log.custrecord_in8_vend_tran_custom LIKE '%f4a35eee-128f-4ea6-b870-5fc8591339c2%' 
      THEN
         17897 			-- Simcha Ingber- bergenfield
      WHEN
         log.custrecord_in8_vend_tran_custom LIKE '%685b474c-d093-4e58-96a5-ad8705c51586%' 
      THEN
         17897 			-- Simcha Ingber- lol
      WHEN
         log.custrecord_in8_vend_tran_custom LIKE '%8ac99481-1d0b-45bf-9f08-9c51e09a1470%' 
      THEN
         261912 			-- Sruly Iowiniski - madison
      WHEN
         log.custrecord_in8_vend_tran_custom LIKE '%7d153078-5770-40bd-bb75-9ce4e336e07f%' 
      THEN
         261913 			-- Shea Berkowitz - madison
      WHEN
         log.custrecord_in8_vend_tran_custom LIKE '%909c01ff-8c6f-4f76-860c-9fe952299782%' 
      THEN
         296541 -- Aryeh Kranz - madison
      WHEN
         log.custrecord_in8_vend_tran_custom LIKE '%62725517-6d98-4c27-aa64-ea99f4e6c9c0%' 
      THEN
         261914 			-- Naftal Bandman - westgate
      WHEN
         log.custrecord_in8_vend_tran_custom LIKE '%ec0737bb-9540-4a38-b47d-1d402213952a%' 
      THEN
         13482 			--Shuki Waldman - south
      WHEN
         log.custrecord_in8_vend_tran_custom LIKE '%7429141b-9785-486a-839e-bf721eac9e14%' 
      THEN
         261916  	-- Meir Walden- express
      WHEN
         log.custrecord_in8_vend_tran_custom LIKE '%e004d093-a5fc-4ff5-94e0-4f184a05ea80%' 
      THEN
         313965 			-- Dovi Hirsch - bergenfield
   END
   AS sales_rep, t.id transaction_id, t.tranid, t.custbody_brdg_sales_rep, log.recordid id, log.custrecord_in8_vend_raw_error error, log.custrecord_in8_vend_log_status status, log.custrecord_in8_vend_domain_prefix_ref store, 	--log.custrecord_in8_vend_log_payload payload 
FROM
   customrecord_in8_vend_log log 
   JOIN
      transaction t 
      ON log.custrecordin8_vend_log_tran_num = t.tranid 
WHERE
   (
( log.custrecord_in8_vend_tran_custom LIKE '%31f506b0-4549-4dd5-a7f4-8680bd6ec836%' 		-- "WhatsApp Order - Yehuda Stefansky" north
      OR log.custrecord_in8_vend_tran_custom LIKE '%80b1ac6f-5d7d-467f-8beb-06f7b1e3409d%' 		-- westgate
      OR log.custrecord_in8_vend_tran_custom LIKE '%fc6668aa-717c-404c-bded-d8c526801a68%' 		--south
      OR log.custrecord_in8_vend_tran_custom LIKE '%59405c22-a5f9-469f-9ea1-41787c1fd3fd%' 		--express
      OR log.custrecord_in8_vend_tran_custom LIKE '%39cc04bc-ce4f-4384-b5cd-89606a9b5efc%' 		--bergenfield
      OR log.custrecord_in8_vend_tran_custom LIKE '%6492d76e-f5ac-481b-952d-0b8ef2e1ce68%' 		--lol
) 
      AND t.custbody_brdg_sales_rep != 261911 		-- Stefansky, Yehuda 
   )
   OR 
   (
( log.custrecord_in8_vend_tran_custom LIKE '%bdcc430a-02c1-4e90-aeab-5bd505fe15b6%' 		-- "WhatsApp Order - Simcha Ingber" north
      OR log.custrecord_in8_vend_tran_custom LIKE '%7bf319a4-a009-4afa-9b72-b377a94cc90c%' 		--westgate
      OR log.custrecord_in8_vend_tran_custom LIKE '%8ff27c43-add2-47e1-ad81-ffc93b109b73%' 		--south
      OR log.custrecord_in8_vend_tran_custom LIKE '%ba7cc74e-3470-4e56-b38e-11864bab7959%' 		--express
      OR log.custrecord_in8_vend_tran_custom LIKE '%f4a35eee-128f-4ea6-b870-5fc8591339c2%' 		--bergenfield
      OR log.custrecord_in8_vend_tran_custom LIKE '%685b474c-d093-4e58-96a5-ad8705c51586%' 		--lol
) 
      AND t.custbody_brdg_sales_rep != 17897 		-- Ingber, Simcha
   )
   OR 
   (
      log.custrecord_in8_vend_tran_custom LIKE '%8ac99481-1d0b-45bf-9f08-9c51e09a1470%' 		-- "WhatsApp Order - Sruly Iowiniski"
      AND t.custbody_brdg_sales_rep != 261912 		-- "Sruly Iowiniski"
   )
   OR 
   (
      log.custrecord_in8_vend_tran_custom LIKE '%7d153078-5770-40bd-bb75-9ce4e336e07f%' 		-- 	"WhatsApp Order - Shea Berkowitz"
      AND t.custbody_brdg_sales_rep != 261913 		-- Shea Berkowitz
   )
   OR 
   (
      log.custrecord_in8_vend_tran_custom LIKE '%909c01ff-8c6f-4f76-860c-9fe952299782%' 		-- 	"WhatsApp Order - Aryeh Kranz",
      AND t.custbody_brdg_sales_rep != 296541--  Aryeh Kranz
   )
   OR 
   (
      log.custrecord_in8_vend_tran_custom LIKE '%62725517-6d98-4c27-aa64-ea99f4e6c9c0%' 		-- 		"WhatsApp Order - Naftali Bandman",
      AND t.custbody_brdg_sales_rep != 261914 		-- Naftali Bandman
   )
   OR 
   (
      log.custrecord_in8_vend_tran_custom LIKE '%ec0737bb-9540-4a38-b47d-1d402213952a%' 		-- 		"WhatsApp Order - Shuki Waldman"
      AND t.custbody_brdg_sales_rep != 13482 		--  Shuki Waldman
   )
   OR 
   (
      log.custrecord_in8_vend_tran_custom LIKE '%7429141b-9785-486a-839e-bf721eac9e14%' 		-- 		"WhatsApp Order - Meir Walden"
      AND t.custbody_brdg_sales_rep != 261916  	--  Meir Walden"
   )
   OR 
   (
      log.custrecord_in8_vend_tran_custom LIKE '%e004d093-a5fc-4ff5-94e0-4f184a05ea80%' 		-- 		"WhatsApp Order - Dovi Hirsch",
      AND t.custbody_brdg_sales_rep != 313965 		--  Dovi Hirsch
   )

ORDER BY
sales_rep