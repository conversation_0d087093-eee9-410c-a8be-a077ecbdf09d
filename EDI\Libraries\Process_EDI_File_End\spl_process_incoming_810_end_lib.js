/**
 * @NApiVersion 2.x
 */

//@ts-ignore
define(["N/log", "N/email"], function (log, email) {
	function processEnd(
		processingLog,
		invoiceObj,
		netSuiteBillId,
		netSuiteBillRefNumber,
		fileName,
		vendorName
	) {
		var resultsData = setResultsData();

		if (resultsData.processingStatus == 4) {
			if (
				processingLog.length == 1 &&
				processingLog[0].includes("have already been billed")
			) {
				//The only error for this transaction is that is has already been billed -> don't need an email of it.
			} else {
				//sendEmail();
			}
		}

		logResult();

		return resultsData;

		function setResultsData() {
			var data = {};
			data.documentControlNumbers = [];
			var errorText = "";

			processingLog.forEach(function (error) {
				errorText += "\n\n" + error + "\n\n";
			});

			if (netSuiteBillId) {
				if (processingLog.length <= 0) {
					processCreatedSuccessfully(netSuiteBillRefNumber);
				} else {
					processCreatedWithErrors(netSuiteBillRefNumber);
				}
			} else {
				_processFailedData();
			}

			return data;

			function processCreatedSuccessfully(netSuiteBillRefNumber) {
				data.subject = `Success: Please Approve - ${vendorName} Bill ${netSuiteBillRefNumber} Created for Purchase Order ${invoiceObj.poNumber}`;
				data.body = `EDI file processed successfully and created bill ${netSuiteBillRefNumber}.
                        Please review and approve the order.
                        EDI File Name: ${fileName}`;
				data.recipients = ["<EMAIL>"];
				data.logTitle = "Created Successfully";
				data.processingStatus = 1; //Processed With No Errors
			}

			function processCreatedWithErrors(netSuiteBillRefNumber) {
				data.subject = `Errors: Please Review and Correct - ${vendorName} Bill ${netSuiteBillRefNumber} Created for Purchase Order ${invoiceObj.poNumber}`;
				data.body = `EDI file processed successfully and created bill ${netSuiteBillRefNumber}.
                        Please review the errors below and correct.
                        
                        ${errorText}

                        EDI File Name: ${fileName}`;
				data.recipients = ["<EMAIL>"];
				data.logTitle = "Created with Errors";
				data.processingStatus = 2; //Processed With Errors
			}

			function _processFailedData() {
				data.subject =
					vendorName + " Bill Not Created for " + invoiceObj.poNumber;
				data.body = `Transaction ${invoiceObj.transactionControlNumber} failed.
                        EDI # ${fileName}
                        Please investigate the errors below.
                        
                        ${errorText}
                    
                        EDI File Name: ${fileName}`;
				data.recipients = ["<EMAIL>"];
				data.cc = ["<EMAIL>"];
				data.logTitle = "Bill Not Created";
				data.processingStatus = 4; //Document Not Created
			}
		}

		function sendEmail() {
			email.send({
				author: 262579, //EDI
				recipients: resultsData.recipients,
				cc: resultsData.cc,
				subject: resultsData.subject,
				body: resultsData.body,
				relatedRecords: {
					if(netSuiteBillId) {
						transactionId: netSuiteBillId;
					},
				},
			});
		}

		function logResult() {
			log.debug({
				title: resultsData.logTitle,
				details: resultsData.errorText,
			});
		}
	}

	return {
		processEnd: processEnd,
	};
});
