/**
 * @description Gets the quantity of all received items in a PO
 * 
 * </br><b>Called by:</b> vlmd_wms_select_item_on_load_cs
 *
 * @NApiVersion 2.1
 * @NScriptType RESTlet
 * 
 * <AUTHOR>
 * @module vlmd_wms_get_post_ir_itemlist_rl
 */

define([
    "require", 
    "N/query", 
    "../../../Classes/vlmd_custom_error_object"
], (require) => {
        const query = require("N/query");
        const CustomErrorObject = require("../../../Classes/vlmd_custom_error_object");
        const customErrorObject = new CustomErrorObject();
  
        const post = (requestBody) => {       
            log.audit('requestBody', requestBody);

            try {  
                let { warehouseLocationId, transactionInternalId } = requestBody.params;

                if(typeof warehouseLocationId == 'undefined') {
                    throw customErrorObject.updateError({
                        errorType: customErrorObject.ErrorTypes.MISSING_PARAM,
                        summary: "MISSING_PARAMETER",
                        details: `Parameter warehouseLocationId is missing.`,
                    });
                }
                if(typeof transactionInternalId == 'undefined') {
                    throw customErrorObject.updateError({
                        errorType: customErrorObject.ErrorTypes.MISSING_PARAM,
                        summary: "MISSING_PARAMETER",
                        details: `Parameter transactionInternalId is missing.`,
                    });
                }

                let sqlQuery = /*sql*/`
                    SELECT
                        SUM(open_task.custrecord_wmsse_act_qty) AS received_quantity
                    FROM
                        customrecord_wmsse_trn_opentask AS open_task
                    WHERE
                        open_task.custrecord_wmsse_order_no = ${transactionInternalId} AND
                        open_task.custrecord_wmsse_wms_location = ${warehouseLocationId} AND
                        open_task.custrecord_wmsse_nsconfirm_ref_no IS NULL
                `;
                
                let poReceivedQuantity = query.runSuiteQL({
                    query: sqlQuery
                }).asMappedResults()[0]?.received_quantity || 0;

                return {
                    "data": poReceivedQuantity
                };
            } catch (err) {
				customErrorObject.throwError({
					summaryText: "ERROR_GETTING_PO_RECEIVED_QUANTITY",
					error: err,
				});
            }
        }

        return {post}
    });
