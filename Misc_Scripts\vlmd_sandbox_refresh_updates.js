/**
 * @NApiVersion 2.1
 * @NScriptType scheduledscript
 */

//@ts-ignore
define([
	"require",
	"N/log",
	"N/runtime",
	"N/search",
	"N/record",
	"../Classes/vlmd_custom_error_object",
], function (require, log, runtime, search, record) {
	function execute() {
		/**@type {import ('../Classes/vlmd_custom_error_object').CustomErrorObject} */
		const CustomErrorObject = require("../Classes/vlmd_custom_error_object");
		const customErrorObject = new CustomErrorObject();

		try {
			var environmentType = runtime.envType;

			if (environmentType != "SANDBOX") {
				return;
			}

			setScheduleScriptDeploymentsToUndeployed();
			unscheduleScheduledSavedSearches();
		} catch (err) {
			customErrorObject.throwError({
				summaryText: "SB_ERROR",
				error: err,
			});
		}

		function setScheduleScriptDeploymentsToUndeployed() {
			try {
				var scriptdeploymentSearchObj = search.create({
					type: "scriptdeployment",
					filters: [
						[
							"status",
							"anyof",
							"SCHEDULED",
							"INPROGRESS",
							"INQUEUE",
							"COMPLETED",
						],
					],
					columns: ["title"],
				});

				scriptdeploymentSearchObj.run().each(function (result) {
					try {
						record.submitFields({
							type: "scriptdeployment",
							id: result.id,
							values: { isdeployed: false },
						});

						return true;
					} catch (err) {
						throw customErrorObject.updateError({
							errorType: err && err.name,
							summary: "ERROR_SUBMITTING_FIELD",
							details: `${result.id} not set undeployed successfully`,
						});
					}
				});
			} catch (err) {
				throw customErrorObject.updateError({
					errorType: err & err.name,
					summary: "ERROR_UNDEPLOYING_SCRIPTS",
					details: `Error updeploying scripts. ERROR: ${err.message}`,
				});
			}
		}

		function unscheduleScheduledSavedSearches() {
			var searchSavedSearch = search.create({
				type: search.Type.SAVED_SEARCH,
				filters: [
					["sendscheduledemails", "is", "T"],
					"AND",
					[
						"internalid",
						"noneof",
						"2031",
						"2443",
						"3706",
						"4721",
						"4711",
						"4632",
					], //These scripts fail when try to delete via script -> unschedule manually
				],
			});

			var searchResults = searchSavedSearch
				.run()
				.getRange({ start: 0, end: 1000 });

			searchResults.forEach((result) => {
				try {
					search.delete({
						id: result.id,
					});
				} catch (err) {
					customErrorObject.updateError({
						errorType: customErrorObject.ErrorTypes.RECORD_NOT_DELETED,
						summary: "ERROR_DELETING_SEARCH",
						details: `${result?.id} : ${err?.message ?? err}`,
					});
				}
			});

			if (customErrorObject.summary) {
				throw customErrorObject.updateError({
					errorType: customErrorObject.ErrorTypes.UNHANDLED_ERROR,
					summary: "ERROR_DELETING_SEARCH",
					details: `Error deleting these scheduled saved searches (internal ids):`,
				});
			}
		}
	}

	return {
		execute,
	};
});
