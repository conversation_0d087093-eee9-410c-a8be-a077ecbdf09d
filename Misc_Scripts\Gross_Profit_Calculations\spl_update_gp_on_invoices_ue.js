/**
 * @NApiVersion 2.1
 * @NScriptType UserEventScript
 * @NAmdConfig /SuiteScripts/config.json
 */

//@ts-ignore
define(["N/log", "N/search"], function (log, search) {
	function beforeLoad(context) {
		try {
			if (
				context.type === context.UserEventType.VIEW &&
				context.newRecord.getValue("subsidiary")
				//Subisidiary is SPL
			) {
				const invoiceId = context.newRecord.id;
				var currentForm = context.form;
				currentForm.clientScriptFileId = 5618190;

				const recordType = context.newRecord.type;
				let createdFromRecordType;

				if (recordType == "creditmemo") {
					const createdFrom = context.newRecord.getValue("createdfrom");

					const searchResultObj = search.lookupFields({
						type: search.Type.RETURN_AUTHORIZATION,
						id: createdFrom,
						columns: ["type"],
					})["type"];

					createdFromRecordType = searchResultObj && searchResultObj[0].value;
				}

				currentForm.addButton({
					id: "custpage_my_button",
					label: "Calculate Gross Profit",
					functionName:
						'calculateGrossProfitOnButtonClick("' +
						invoiceId +
						'","' +
						recordType +
						'","' +
						createdFromRecordType +
						'")',
				});
			}
		} catch (e) {
			log.debug("Error", e);
		}
	}

	return {
		beforeLoad,
	};
});
