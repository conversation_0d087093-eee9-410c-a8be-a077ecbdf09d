/**
 * @description Validate inventory sublist items' unit cost is not null or $0
 * 
 * </br><b>Deployed On:</b> Inventory Adjustments
 * </br><b>Execution Context:</b> ALL
 * </br><b>Event Type/Mode:</b> ALL
 * </br><b>Entry Points:</b> validateLine, saveRecord
 *
 * @NApiVersion 2.1
 * @NScriptType ClientScript
 * @NAmdConfig /SuiteScripts/config.json
 * 
 * <AUTHOR> <PERSON>
 * @module brdg_validate_inventory_unit_costs_cs
 */

define([
	"N/log",
	"N/ui/dialog",
	"BridgeHelperFunctionsLib"
], function (
	log,
	dialog,
	bridgeHelperFunctionsLib
) {

	// Create singleton to prevent multiple executions of query
	let bridgeSubsidiaries = [];

	/**
	 * Run sublist line-level validations
	 * 
	 * @param {import("@hitc/netsuite-types/N/types").EntryPoints.Client.validateLineContext} context Validate line script context
	 * @returns {boolean} Return true if line is valid
	 */
	function validateLine(context) {
		try {

			const {currentRecord, sublistId} = context;
			const subsidiary = currentRecord.getValue({
				fieldId: "subsidiary"
			});

			bridgeSubsidiaries = bridgeSubsidiaries.length <= 0
				? bridgeHelperFunctionsLib.getBridgeSubsidiaries()
				: bridgeSubsidiaries;

			if (sublistId !== "inventory" || !bridgeHelperFunctionsLib.isBridgeSubsidiary(subsidiary, bridgeSubsidiaries)) {
				return true;
			}		

			const itemName = currentRecord.getCurrentSublistText({
				sublistId,
				fieldId: "item"
			});

			if (!bridgeHelperFunctionsLib.hasItemEstimatedUnitCost(context)) {
				dialog.alert({
					title: "Error",
					message: `The unit cost for ${itemName} should not be blank or 0.`
				});
				return false;
			}
		} catch (error) {
			log.error("Error validating inventory unit costs", error);
		}

		return true;
	}

	/**
	 * Run sublist line-level validations
	 * 
	 * @param {import("@hitc/netsuite-types/N/types").EntryPoints.Client.saveRecordContext} context Save record script context
	 * @returns {boolean} Return true if record is valid
	 */
	function saveRecord(context) {
		try {

			const { currentRecord } = context;
			const subsidiary = currentRecord.getValue({
				fieldId: "subsidiary"
			});

			bridgeSubsidiaries = bridgeSubsidiaries.length <= 0
				? bridgeHelperFunctionsLib.getBridgeSubsidiaries()
				: bridgeSubsidiaries;

			if (!bridgeHelperFunctionsLib.isBridgeSubsidiary(subsidiary, bridgeSubsidiaries)) {
				return true;
			}

			const itemsWithInvalidUnitCosts = bridgeHelperFunctionsLib.getItemsWithInvalidUnitCosts(context);
			if (itemsWithInvalidUnitCosts.length > 0) {
				dialog.alert({
					title: "Error",
					message: `
						The unit cost for the following items should not be blank or 0:<br> -
						${itemsWithInvalidUnitCosts.join("<br> - ")}
					`
				});
				return false;
			}
		} catch (error) {
			log.error("Error validating the inventory adjustment record", error);
		}
		return true;
	}

	return {
		validateLine,
		saveRecord
	};
});
