SELECT
   delta.custrecord_item item_id,
   delta.custrecord_price_type price_type,
   p.custrecord_parent_customer customer_id,
   BUILTIN.DF(delta.custrecord_item) sku,
   delta.custrecord_price price,
   BUILTIN.DF (item.saleunit) unit,
   item.custitem_vlmd_shopify_variant_id shopify_variant_id,
   item.itemtype type 
FROM
   customrecord_dlta_prc_per_itm_per_cstmr delta 
   INNER JOIN
      customrecord_customer_pricing_files p 
      ON p.id = delta.custrecord_customer_pricing_files_record 
      AND p.custrecord_price_files_last_updated >= TRUNC(CURRENT_DATE) - 1 
   INNER JOIN
      item 
      ON item.id = delta.custrecord_item 
WHERE
   p.custrecord_parent_customer = {{clientInternalId}} 
   AND rownum <= {{max}} 
UNION
SELECT
   delta.custrecord_item item_id,
   delta.custrecord_price_type price_type,
   delta.custrecord_customer customer_id,
   BUILTIN.DF(delta.custrecord_item) sku,
   delta.custrecord_price price,
   BUILTIN.DF (item.saleunit) unit,
   item.custitem_vlmd_shopify_variant_id shopify_variant_id,
   item.itemtype type 
FROM
   customrecord_dlta_prc_per_itm_per_cstmr delta 
   INNER JOIN
      item 
      ON item.id = delta.custrecord_item 
WHERE
   delta.custrecord_customer = {{clientInternalId}} 
   AND rownum <= {{max}} 
   AND delta.created >= TRUNC(CURRENT_DATE) - 1