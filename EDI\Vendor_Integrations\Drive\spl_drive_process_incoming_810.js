/**
 * @NApiVersion 2.1
 * @NScriptType scheduledscript
 * @NAmdConfig /SuiteScripts/config.json
 */

define([
	"N/sftp",
	"N/encode",
	"GetEdiPartnerValuesLib",
	"ParseIncoming810Lib",
	"ProcessIncoming810Lib",
], function (
	sftp,
	encode,
	getEdiPartnerValuesLib,
	parseInvoiceLib,
	processInvLib
) {
	function execute(context) {
		var vendorData = {
			prodGuidBool: true,
			prodGUID: "384fa4d4fdd54ec88d3453af6a38a5a1",
			sandboxGUID: "********************************",
			vendorInternalId: 728,
			integrationStartDate: "1/21/2021 12:00 am",
			prodDirectoryBool: true,
			testDirectory: "/users/Drive/TEST/IN/810",
			prodDirectory: "/users/Drive/IN/810",
			referenceDirectory: "/EDI Reference Files/Drive/IN/810",
			vendorName: "Drive",
			documentTypeId: 3,
			pushEmailToDB: true,
		};

		var hostKey =
			"AAAAB3NzaC1yc2EAAAABIwAAAIEA2YIvZPcsWA3jP6yq5J" +
			"5XYunNySNspUZUhtzxXoePDZEhxJFIF/am9o+15CqO4hlyEDni0m+VW4" +
			"LVSoiUnj31xJis91EcYrUeCewIcSwof4Xs05ghth/2SKn0cITsR5hOeST" +
			"l4jFsMf9oK0+b2gr4jdV4fria4E/eF2u0qQArWuc=";

		var connection = createConnection();
		var directoryList = getDirectoryList();
		var fileNames = getFileNames();
		if (fileNames.length > 0) {
			var fileContents = getFileContents();
			var partnerValues = getEdiPartnerValuesLib.getDriveValues();
			processInvoice();
		} else {
			log.debug("Script ran, no results found");
		}

		/**********Process Invoices Helper Functions**********/
		function createConnection() {
			return sftp.createConnection({
				username: "FTPadmin",
				passwordGuid: vendorData.prodGUID, //production account
				//passwordGuid: vendorData.sandboxGUID, //sandbox account,
				url: "************",
				hostKey: hostKey,
				//directory: vendorData.testDirectory
				directory: vendorData.prodDirectory,
			});
		}

		function getDirectoryList() {
			return connection.list({
				path: "",
			});
		}

		function getFileNames() {
			try {
				var names = [];
				for (var x = 0; x < directoryList.length; x++) {
					if (!directoryList[x].directory) {
						names.push(directoryList[x]);
					}
				}
				return names;
			} catch (e) {
				throw e;
			}
		}

		function getFileContents() {
			try {
				var contents = [];

				fileNames.forEach(function (ediFile) {
					var downloadedFile = downLoadFile();
					getContent();

					function downLoadFile() {
						return connection.download({
							directory: "",
							filename: ediFile.name,
						});
					}

					function getContent() {
						var toDecode = downloadedFile.getContents();
						var content = encode.convert({
							string: toDecode,
							inputEncoding: encode.Encoding.BASE_64,
							outputEncoding: encode.Encoding.UTF_8,
						});
						contents.push({
							content: content,
							name: ediFile.name,
						});
					}
				});

				return contents;
			} catch (e) {
				throw e;
			}
		}

		function processInvoice() {
			fileContents.forEach((file) => {
				var parsedFileObj = parseInvoiceLib.parse810(
					file.content,
					partnerValues
				);
				if (parsedFileObj.errorLog.length > 0) {
					throw `Error parsing edi file ${file.name}: ${parsedFileObj.errorLog}`;
				}
				parsedFileObj.invoiceObj.invoiceNumber =
					parsedFileObj.invoiceObj.invoiceNumber.replace(/^(00)/, "");
				//The file they send via EDI has 00 before, the emails don't. Taking out so will be able to search
				try {
					processInvLib.processInvoice(
						parsedFileObj.invoiceObj,
						file.name,
						[],
						vendorData,
						connection,
						vendorData.prodDirectory
					);
				} catch (e) {
					throw e;
				}
			});
		}
	}

	return {
		execute: execute,
	};
});
