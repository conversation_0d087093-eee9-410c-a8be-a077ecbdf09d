/**
 * Interface and type definitions for Bridge item record
 *
 * <AUTHOR>
 */

import { EntryPoints } from "N/types";
import { Result } from "N/query";
import { FieldValue, Record, ClientCurrentRecord } from "N/record";

export interface BridgeItemRecord {
    /** Constructor */
    new(itemRecord: Record|ClientCurrentRecord): BridgeItemRecord;
    /** Item record id */
    id: number;
    /** NetSuite record for the item */
    record: Record|ClientCurrentRecord;
    /** Record fields */
    fields: Record.fields;
    /** Item type */
    type: Record.type;
    /** Subsidiaries assigned to the item record */
    subsidiaries: FieldValue;
    /** UPC Code assigned to the item */
    upcCode: FieldValue;
    /** Option to skip setting of UPC Code */
    isUpcCodeNotAvailableChecked: FieldValue;
    /** Item product category */
    category: FieldValue
    /** Item purchase price */
    purchasePrice: FieldValue;
    /** Override calculation of markup amount */
    ignoreMarkupCalculation: FieldValue;
    /** Markup Amount */
    markupAmount: Result.value
    /** Do not sync LightSpeed fields checkbox */
    doNotSyncToLs: FieldValue;
    /** Item Number */
    itemNumber: FieldValue;
    /** LightSpeed fields lookup object */
    lightSpeed: {Field: {[key:string]:string}};
    /** Retrieve markup amount and record ID */
    calculateMarkupPercentageObj: () => void;
    /** Set the Bridge price level value based on the markup amount */
    setMarkupValues: () => void;
    /** Set other price levels to null */
    setOtherPriceFieldsToNull: () => void;
    /** Validate the UPC Code used on the item */
    validateUpcCode: (itemCache?: ItemCache[]) => void;
    /** Sets the subsidiary to Bridge only */
    setSubsidiaryToBridgManagementeOnly: () => void;
    /** Validate item number */
    validateItemNumber: () => void;
    /** Validate Bridge subsidiary */
    validateBridgeSubsidiary: () => void;
    /** Set default item record values */
    setDefaultValues: (/** @type {EntryPoints.UserEvent.beforeSubmitContext|EntryPoints.UserEvent.beforeLoadContext} */ context) => void;
    /** Set Sync Light Speed fields */
    setSyncToLightSpeedFields: () => void;
    setMatchBillToReceipt: () => void;
    getParentProductCategoryInfo: () => void;
}

export interface BridgeItemClientRecord extends BridgeItemRecord{
    /** Constructor */
    new(itemRecord: ClientCurrentRecord): BridgeItemClientRecord;
    /** Set default item record values */
    setClientDefaultValues: () => void;
    /** Set Sync Light Speed fields */
    setSyncToLightSpeedClientFields: () => void;
    /** Set Match Bill to Receipt to true */
    setClientMatchBillToReceipt: () => void;
}

export type ItemCache {
    id: string;
    itemId: string;
    subsidiaries: string[];
    upcCode: string;
    displayName: string;
}