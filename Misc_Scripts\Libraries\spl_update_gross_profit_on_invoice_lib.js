//@ts-ignore
define([
	"N/log",
	"N/runtime",
	"RecordModuleHelperLib",
	"GetGrossProfitValuesLib",
], function (log, runtime, recordHelperLib, getGrossProfitValuesLib) {
	function updateGrossProfitValues(
		invoiceInternalId,
		recordType,
		createdFromRecordType
	) {
		let errorLog = [];

		const helperFunctions = (function () {
			function addUserInfoForErrorLog() {
				const user = runtime.getCurrentUser(); //Will work on UE, when MR or ScSc will display "System" as the user.

				errorLog.push(
					`User: ${user.name} , Role: ${user.role}, Subsidiary: ${user.subsidiary}`
				);
			}

			function compareOldAndNewValues(invoiceRecord, grossProfitValuesObj) {
				let existingGrossProfit = Number(
					invoiceRecord.getValue("custbody_spl_gp_invoice_gross_profit")
				).toFixed(3);

				if (!existingGrossProfit) {
					return false;
				}

				let newGrossProfit = Number(grossProfitValuesObj.grossProfit).toFixed(
					3
				);

				let newGrossProfitPercentage = Number(
					grossProfitValuesObj.grossProfitPercentage
				).toFixed(3);

				let existingGrossProfitPercentage = Number(
					invoiceRecord.getValue("custbody_spl_gp_invoice_gp_percentage")
				).toFixed(3);

				return (
					newGrossProfit == existingGrossProfit &&
					newGrossProfitPercentage == existingGrossProfitPercentage
				);
			}

			function mapValuesToKeyProp(grossProfitValuesObj) {
				const {
					itemsSubtotal,
					shippingRevenue,
					netRevenue,
					itemsCOGS,
					shippingCOGS,
					shipNotesArr,
					purchasingSoftwareFee,
					gpoReferralFee,
					netExpenses,
					grossProfit,
					grossProfitPercentage,
				} = grossProfitValuesObj;

				return {
					custbody_spl_gp_invoice_subtotal: itemsSubtotal,
					custbody_spl_gp_invoice_shipping: shippingRevenue,
					custbody_spl_gp_net_revenue: netRevenue,
					custbody_spl_gp_invoice_items_cogs_ttl: itemsCOGS,
					custbody_spl_gp_invoice_shipping_cogs: shippingCOGS,
					custbody_spl_gp_invoice_gpo_rfrl_fee: gpoReferralFee,
					custbody_vlmd_gp_notes: shipNotesArr,
					custbody_spl_gp_prchsng_sftwr_fee: purchasingSoftwareFee,
					custbody_spl_gp_net_expenses: netExpenses,
					custbody_spl_gp_invoice_gross_profit: grossProfit,
					custbody_spl_gp_invoice_gp_percentage: grossProfitPercentage,
				};
			}

			function setAllValues(mappedValuesObj, invoiceRecord) {
				const transactionRecord = recordHelperLib.setBodyValues(
					mappedValuesObj,
					invoiceRecord
				);
				return transactionRecord;
			}

			return {
				addUserInfoForErrorLog,
				compareOldAndNewValues,
				mapValuesToKeyProp,
				setAllValues,
			};
		})();

		const recordModuleMappingObj = {
			custinvc: "INVOICE",
			custcred: "CREDIT_MEMO",
			cashsale: "CASH_SALE",
			creditmemo: "CREDIT_MEMO",
			invoice: "INVOICE",
			CustInvc: "INVOICE",
			CustCred: "CREDIT_MEMO",
			RtnAuth: "RETURN_AUTHORIZATION",
			INVOICE: "INVOICE",
			CREDIT_MEMO: "CREDIT_MEMO",
			RETURN_AUTHORIZATION: "RETURN_AUTHORIZATION",
		};

		recordType = recordModuleMappingObj[recordType];
		createdFromRecordType = recordModuleMappingObj[createdFromRecordType];

		try {
			invoiceRecord = recordHelperLib.loadRecord(invoiceInternalId, recordType);

			const { grossProfitValuesObj, errorLog: getValuesErrorLog } =
				getGrossProfitValuesLib.getGrossProfitValues(
					invoiceRecord,
					invoiceInternalId,
					recordType,
					createdFromRecordType
				);

			if (getValuesErrorLog.length > 0) {
				errorLog = [...errorLog, ...getValuesErrorLog];
				helperFunctions.addUserInfoForErrorLog();

				return { errorLog };
			}

			let valuesAreTheSame = helperFunctions.compareOldAndNewValues(
				invoiceRecord,
				grossProfitValuesObj
			);

			if (!valuesAreTheSame) {
				//Get the obj formatted as {NS field id: value to set}
				const mappedValuesObj =
					helperFunctions.mapValuesToKeyProp(grossProfitValuesObj);

				const transactionRecord = helperFunctions.setAllValues(
					mappedValuesObj,
					invoiceRecord
				);

				recordHelperLib.saveRecord(transactionRecord);
			}
		} catch (e) {
			errorLog.push(e.message);
		}

		if (errorLog.length > 0) {
			helperFunctions.addUserInfoForErrorLog();
		}

		return { errorLog };
	}

	return {
		updateGrossProfitValues,
	};
});
