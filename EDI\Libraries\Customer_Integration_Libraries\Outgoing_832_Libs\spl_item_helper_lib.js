/**
 * @NApiVersion 2.1
 * @param {string} uomToParse
 */

define(["N/log", "N/query"], function (log, query) {
	function _getUomData(saleUnitId) {
		try {
			let sqlQuery = /*sql */ `
				SELECT
					abbreviation,
					unitstype,
				FROM
					unitsTypeUom
				WHERE	
					internalid = ?`;

			let results = query
				.runSuiteQL({
					query: sqlQuery,
					params: [saleUnitId],
				})
				.asMappedResults()[0];

			const abbreviationString = results?.abbreviation;
			const baseUnitIsEach = results?.unitstype == 1; //Each;

			if (!abbreviationString) {
				throw {
					name: "NO_QUERY_RESULTS_RETURNED",
					message: `No results returned for query to get the UOM data. Unit name param: ${saleUnitId}`,
				};
			}

			const saleUnitAbbreviationArr = abbreviationString.split("/");
			return { saleUnitAbbreviationArr, baseUnitIsEach };
		} catch (err) {
			throw {
				name: "ERROR_GETTING_UOM_DATA",
				message: `No UOM gotten for ${saleUnitId}: ${err}`,
			};
		}
	}

	function getUomAbbreviationForEdi(itemRow) {
		try {
			let { unit: saleUnitId, type, abbreviation } = itemRow;
			let uomAbbreviation;

			switch (type) {
				case "kit":
				case "Kit":
					uomAbbreviation = abbreviation ?? "EA";
					break;
				default:
					if (!saleUnitId) {
						throw {
							name: "NO_UNIT_NAME",
							message: `No unit name passed in`,
						};
					}

					let { saleUnitAbbreviationArr, baseUnitIsEach } =
						_getUomData(saleUnitId);

					uomAbbreviation = baseUnitIsEach
						? saleUnitAbbreviationArr.pop()
						: saleUnitAbbreviationArr.shift();
			}

			if (!uomAbbreviation) {
				throw {
					name: "ERROR_GETTING_UOM",
					message: `No UOM gotten for ${saleUnitId}`,
				};
			}

			uomAbbreviation = uomAbbreviation.trim().toUpperCase();

			if (uomAbbreviation.length > 2) {
				throw {
					name: "INVALID_UOM",
					message: `Incorrect abbreviation, ${uomAbbreviation}, gotten for ${saleUnitId}`,
				};
			}

			uomAbbreviation = uomAbbreviation.replace("CS", "CA"); //DSSI requires 'CASE' as CA instead

			return uomAbbreviation;
		} catch (err) {
			throw {
				name: "ERROR_GETTING_UOM_ABBREVIATION",
				message: `ERROR: ${JSON.stringify(err)}, ${JSON.stringify(itemRow)}`,
			};
		}
	}

	function getUomObj(unitName, itemType) {
		try {
			if ((unitName && unitName.toUpperCase() == "EACH") || itemType == "Kit") {
				return {
					abbreviation: "EA",
					contentUnitQuantity: "",
					contentUnitSize: "",
				};
			}
			let { uomArr, baseUnitIsEach } = _getUomData(unitName);

			const uomObj = baseUnitIsEach
				? {
						//Sample format: 6 EA / CS
						abbreviation: getUomAbbreviationForEdi(unitName),
						contentUnitQuantity: uomArr[0].split(" ")[0],
						contentUnitSize: uomArr[0].split(" ")[1],
				  }
				: {
						//Sample format: CS/6EA
						abbreviation: getUomAbbreviationForEdi(unitName),
						contentUnitQuantity: uomArr[1].replace(/\D/g, ""),
						contentUnitSize: uomArr[1].replace(/[0-9]/g, ""),
				  };

			//DSSI requires 'CASE' as CA instead
			uomObj.contentUnitQuantity = uomObj.contentUnitQuantity.replace(
				"CS",
				"CA"
			);

			return uomObj;
		} catch (e) {
			throw {
				name: `${e.name} ERROR_GETTING_UOM_OBJ`,
				message: `UNIT NAME: ${unitName}, MESSAGE: ${e.message}`,
			};
		}
	}

	return {
		getUomAbbreviationForEdi,
		getUomObj,
	};
});
