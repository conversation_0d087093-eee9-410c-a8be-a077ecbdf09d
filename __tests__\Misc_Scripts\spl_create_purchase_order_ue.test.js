import search from "N/search";
// @ts-ignore spl_create_purchase_order_ue is not a module
import spl_create_purchase_order_ue from "../../Misc_Scripts/spl_create_purchase_order_ue";

beforeEach(() =>  {
    jest.resetModules();
});
jest.mock("CreatePurchaseOrderLib", () => ({
    createPoWithDropShipItems: () => ({
        processingErrorsArr: []
    })
}), { virtual: true });
// @ts-ignore Cannot find module 'CreatePurchaseOrderLib' or its corresponding type declarations.ts(2307)
import createPurchaseOrderLib from "CreatePurchaseOrderLib";

describe("spl_create_purchase_order_ue", () => {
    describe("afterSubmit", () => {
        beforeEach(() => {
            jest.spyOn(search, "lookupFields").mockReturnValue({
                parent: [{text: "Venza", value: "17554"}]
            });
            jest.spyOn(createPurchaseOrderLib, "createPoWithDropShipItems");
        });
        it("creates a purchase order", () => {
            const getValue = (/** @type {any} */ param) => {
                switch(param.fieldId){
                    case "entity":
                        return "dummyEntity";
                    case "state":
                        return "CO";
                    default:
                        return param.fieldId;
                }
            };
            const getSublistValue = (/** @type {any} */ param) => {
                switch(param.fieldId){
                    case "item":
                        return "54939";
                    default:
                        return param.fieldId;
                }
            };
            const context = {
                newRecord: {
                    id: "dummyVendorBillId",
                    getValue,
                    getSubrecord: () => ({
                        getValue
                    }),
                    getLineCount: () => 1,
                    getSublistValue,
                    getSublistText: () => "dummyItemName"
                },
                oldRecord: {
                    getValue,
                    getSubrecord: () => ({
                        getValue
                    })
                },
                type: "approve",
                UserEventType: {
                    EDIT: "edit",
                    APPROVE: "approve"
                }
            }
            spl_create_purchase_order_ue.afterSubmit(context);
            expect(createPurchaseOrderLib.createPoWithDropShipItems).toHaveBeenCalledWith(
                {
                    "customerInternalId": "dummyEntity", 
                    "items": [
                        {
                            "internalId": "54939",
                            "itemName": "dummyItemName",
                            "line": 0,
                            "quantity": "quantity",
                            "uomId": "units",
                            "vendorCost": "porate"
                        }
                    ]
                },
                "dummyVendorBillId"
            );
        });
    });
});