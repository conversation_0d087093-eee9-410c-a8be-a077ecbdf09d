/**
 * @description Emails BRDG when quantity on vendor bill is updated, to let them know to update rip records
 *
 * </br><b>Deployed On:</b> Vendor Bills
 * </br><b>Excecution Context:</b> USEREVENT
 * </br><b>Event Type/Mode:</b> !CREATE
 * </br><b>Entry Points:</b> beforeSubmit
 *
 * @NApiVersion 2.1
 * @NScriptType UserEventScript
 * @NAmdConfig /SuiteScripts/config.json
 *
 * <AUTHOR>
 * @module brdg_rip_update_rip_records_ue
 */

define(["require", "BridgeHelperFunctionsLib", "LoDash", "N/log", "N/email"], (
	require,
	bridgeHelperFunctionsLib,
	_
) => {
	const log = require("N/log");
	const email = require("N/email");

	function getRipItemsObjsArr(oldBill, newBill) {
		let ripItemsObjsArr = [];

		const lineCount = oldBill.getLineCount({ sublistId: "item" });

		for (var i = 0; i < lineCount; i++) {
			//Looping through every item on the transaction and checking to see if quantity was updated
			const itemQuantityOld = oldBill.getSublistValue({
				sublistId: "item",
				fieldId: "quantity",
				line: i,
			});
			const itemQuantityCurrent = newBill.getSublistValue({
				sublistId: "item",
				fieldId: "quantity",
				line: i,
			});

			const ripAccrualRecordLink = newBill.getSublistValue({
				sublistId: "item",
				fieldId: "custcol_rip_accrual_record_link",
				line: i,
			});

			if (itemQuantityOld !== itemQuantityCurrent && ripAccrualRecordLink) {
				const itemId = newBill.getSublistValue({
					sublistId: "item",
					fieldId: "item",
					line: i,
				});

				const itemUnits = newBill.getSublistValue({
					sublistId: "item",
					fieldId: "units_display",
					line: i,
				});

				const unitsType = itemUnits.includes("CS") ? 2 : 1;

				ripItemsObjsArr.push({
					itemId,
					unitsType,
					itemQuantityToSubtract: itemQuantityOld,
					itemQuantity: itemQuantityCurrent,
					ripAccrualRecordLink,
					// relatedVendorCredit,
				});
			}
		}
		return ripItemsObjsArr;
	}

	function groupRipsByAccrualRecord(ripItemsObjsArr) {
		ripCodeTotalQtyArr = _.chain(ripItemsObjsArr)
			.groupBy("ripAccrualRecordLink")
			.map(function (objects, ripAccrualRecordLink) {
				return {
					ripAccrualRecordInternalId: ripAccrualRecordLink,
					updatedTotalQuantityReceived: _.sumBy(objects, "itemQuantity"),
					oldTotalQuantity: _.sumBy(objects, "itemQuantityToSubtract"),
					unitsType: objects[0].unitsType,
					itemsIncluded: objects.map((a) => a.itemId),
				};
			})
			.value();

		return ripCodeTotalQtyArr;
	}

	function beforeSubmit(context) {
		if (context.type == "create") {
			return;
		}

		try {
			const oldBill = context.oldRecord;
			const newBill = context.newRecord;
			const subsidiariesArr = newBill.getValue({
				fieldId: "subsidiary"
			});
			const bridgeSubsidiaries = bridgeHelperFunctionsLib.getBridgeSubsidiaries();
			const isBridgeSubsidiary = bridgeHelperFunctionsLib.isBridgeSubsidiary(subsidiariesArr, bridgeSubsidiaries);

			if (!isBridgeSubsidiary) {
				return;
			}
			const relatedVendorCredit = newBill.getValue(
				"custbody_associated_bill_credit"
			);
			const approvalStatus = newBill.getValue("approvalstatus");

			if (relatedVendorCredit && approvalStatus == 2) {
				const ripItemsObjsArr = getRipItemsObjsArr(oldBill, newBill);
				const groupedAccrualInfoArr =
					ripItemsObjsArr.length < 0
						? ripItemsObjsArr
						: groupRipsByAccrualRecord(ripItemsObjsArr);

				let accrualTable = "<table>";
				for (i = 0; i < groupedAccrualInfoArr.length; i++) {
					("<tr>");
					accrualTable +=
						'<td  style = "border: 1px solid black; border-collapse: collapse">' +
						"RIP ACCRUAL RECORD" +
						"</>" +
						'<td style = "border: 1px solid black">' +
						'<a href="/app/common/custom/custrecordentry.nl?rectype=2309&id=' +
						groupedAccrualInfoArr[i].ripAccrualRecordInternalId +
						'">' +
						"Rip accrual link" +
						"</a></td>";
					accrualTable += "</tr><tr>";
					accrualTable +=
						'<td style = "border: 1px solid black; border-collapse: collapse">' +
						"UPDATED TOTAL QUANTITY" +
						"</>" +
						'<td style = "border: 1px solid black">' +
						groupedAccrualInfoArr[i].updatedTotalQuantityReceived +
						"</td>";
					accrualTable += "</tr><tr>";
					accrualTable +=
						'<td style = "border: 1px solid black; border-collapse: collapse">' +
						"OLD TOTAL QUANTITY" +
						"</>" +
						'<td style = "border: 1px solid black">' +
						groupedAccrualInfoArr[i].oldTotalQuantity +
						"</td>";
					accrualTable += "</tr><tr>";
					accrualTable +=
						'<td style = "border: 1px solid black; border-collapse: collapse">' +
						"UNITS TYPE" +
						"</>" +
						'<td style = "border: 1px solid black">' +
						groupedAccrualInfoArr[i].unitsType +
						"</td>";
					accrualTable += "</tr><tr>";
					accrualTable +=
						'<td style = "border: 1px solid black; border-collapse: collapse">' +
						"ITEMS INCLUDED" +
						"</>" +
						'<td style = "border: 1px solid black">' +
						groupedAccrualInfoArr[i].itemsIncluded +
						"</td>";
				}
				accrualTable += "</table>";

				var recipientEmail = "<EMAIL>";
				email.send({
					author: 15131,
					recipients: recipientEmail,
					subject:
						"A vendor bill was updated. Please update related RIP records.",
					body: `The quantity on <a href="/app/accounting/transactions/vendbill.nl?id=${newBill.id}">
					a vendor bill</a> was just updated.<br />
					<a href="/app/accounting/transactions/transaction.nl?id=${relatedVendorCredit}">
					Update this vendor credit accordingly.</a><br />
					<br /> Update related rip records: <br / ><br / >${accrualTable}`,
				});
			} else {
				return;
			}
		} catch (e) {
			let err = {
				name: "UNABLE_TO_EMAIL_UPDATE_INSTRUCTIONS",
				message:
					"Vendor bill was updated but the system was unable to email update instructions.",
			};

			log.error(`${err.name} - ${err.message}`);
		}
	}

	return {
		beforeSubmit,
	};
});
