/**
 * @description Pull in any item with a price change and create a delta record for each customer with their price for that item
 *
 * </br><b>Schedule:</b> Called from summarize stage of the script that handles price changes on a customer level
 * 
 * @NApiVersion 2.1
 * @NScriptType MapReduceScript
 *
 * @NAmdConfig /SuiteScripts/config.json
 * <AUTHOR>
 */

define([
  "require",
  "N/log",
  "N/runtime",
  "./Classes/price_changes_item_level",
  "../../Classes/vlmd_custom_error_object",
  "../../Classes/vlmd_mr_summary_handling",
], (/** @type {any} */ require) => {
  const log = require("N/log");
  const runtime = require("N/runtime");

  /** @type {import("./Classes/price_changes_item_level")} */
  const PriceChangesFactory =
    require("./Classes/price_changes_item_level").ItemPriceChangesFactory;

  /** @type {import("../../Classes/vlmd_custom_error_object").CustomErrorObject} */
  const CustomErrorObject = require("../../Classes/vlmd_custom_error_object");

  /**
   * Get all items that have a price change within the given time period.
   *
   * @returns {SuiteQLObjectReference|undefined} Object containing SuiteQL string
   */
  function getInputData() {
    const customError = new CustomErrorObject();
    try {
      const currentScript = runtime.getCurrentScript();
      const numberOfDaysBack = currentScript.getParameter({
        name: "custscript_number_of_days_back_itms",
      });

      log.audit(
        "Get Input Data: Number of Days Back",
        JSON.stringify({
          numberOfDaysBack,
        })
      );

      let priceChangesFactory = new PriceChangesFactory();

      priceChangesFactory.getItemsWithPriceChangesQueryString(numberOfDaysBack);

      log.audit(
        "Query String | Items With Price Changes",
        priceChangesFactory.queryString
      );
     
      return {
        type: "suiteql",
        query: priceChangesFactory.queryString,
        params: [],
      };
    } catch (err) {
      customError.throwError({
        summaryText: "GET_INPUT_DATA",
        error: err,
      });
    }
  }

  /**
   * Create delta records for each customer with their price for the item
   *
   * @param {import("N/types").EntryPoints.MapReduce.mapContext} context Reduce context
   * @returns {void}
   */
  function reduce(context) {
    let priceChangesFactory = new PriceChangesFactory();

    try {
      const parsedItemRow = JSON.parse(context.values).values;

      priceChangesFactory.setItemValuesForIteration(parsedItemRow);
      priceChangesFactory.setNewPrices();
      priceChangesFactory.createDeltaRecords();

      context.write(priceChangesFactory.itemObj.name, context.value);
    } catch (err) {
      priceChangesFactory.customErrorObject.throwError({
        summaryText: "REDUCE_ERROR",
        error: err,
        recordType: "PRICE",
        errorWillBeGrouped: true,
      });
    }
  }

  /**
   * Log summary results
   *
   * @param {import("N/types").EntryPoints.MapReduce.summarizeContext} context Summarize context
   * @returns {void}
   */
  function summarize(context) {
    /**@typedef {import("../../Classes/vlmd_mr_summary_handling")}*/
    const StageHandling = require("../../Classes/vlmd_mr_summary_handling");
    const stageHandling = new StageHandling(context);

    stageHandling.printErrors({
      groupErrors: true,
    });

    stageHandling.printScriptProcessingSummary();
  }

  return {
    getInputData,
    reduce,
    summarize,
  };
});
