/**
 * @description SL that gets triggered as the third step in the Shipday WF. If a new address was entered, set this to be the delivery address custom field.
 *
 * </br><b>Entry Points:</b> onRequest
 *
 * @NApiVersion 2.1
 * @NScriptType Suitelet
 *
 * <AUTHOR>
 * @module ltspd_shipday
 */

define(["require", "N/log"], function (/** @type {any} */ require) {
  const log = require("N/log");
  /**
   * Return the object for LightSpeed containing the action message of requiring new delivery custom fields
   *
   * @param {import("N/types").EntryPoints.Suitelet.onRequestContext} context Suitelet onRequest context
   */
  function onRequest(context) {
    if (context.request.method === "POST") {
      try {
        const data = context.request.body;
        const jsonObj = JSON.parse(data);
        const username = jsonObj.user["username"];

        if (!jsonObj.customer?.id) return;
        // if (username != "<PERSON><PERSON> Drillick" && username != "<PERSON>")
        //   return;

        const customFields = jsonObj.sale["custom_fields"];

        if (!customFields || customFields.length < 1) return;
        const deliveryAddressField = customFields.find(
          (field) => field.name === "delivery_address"
        );

        if (!deliveryAddressField || !deliveryAddressField.string_value) return;

        let deliveryAddress;
        try {
          deliveryAddress = JSON.parse(deliveryAddressField.string_value);
        } catch (parseError) {
          log.error("Error parsing delivery address JSON", {
            error: parseError,
            value: deliveryAddressField.string_value,
          });
          return;
        }

        if (!deliveryAddress || deliveryAddress.address1 !== "Other") return;

        const [a1, c, s, z] = ["address1", "city", "state", "zip"].map(
          (name) =>
            customFields.find((f) => f.name === name)?.string_value || ""
        );

        // Only set formattedAddress if we have at least address1, city, state, and zip
        let formattedAddress = "";
        if (a1 && c && s && z) {
          formattedAddress = `${a1}\n${c}, ${s} ${z}`;
        }

        log.audit("Formatted Address", formattedAddress);

        if (formattedAddress) {
          const actionToReturn = {
            actions: [
              {
                type: "confirm",
                title: "Confirm new address:",
                message: `Please confirm the new delivery address: \n ${formattedAddress}`,
                confirm_label: "Confirm",
                dismiss_label: "Cancel",
              },
              {
                type: "set_custom_field",
                entity: "sale",
                custom_field_name: "delivery_address",
                custom_field_value: JSON.stringify({
                  address1: a1,
                  city: c,
                  state: s,
                  zip: z,
                }),
              },
            ],
          };

          context.response.write(JSON.stringify(actionToReturn));
        }
      } catch (/** @type {any} */ err) {
        log.error(err.name, err.message);
      }
    }
  }

  return {
    onRequest: onRequest,
  };
});
