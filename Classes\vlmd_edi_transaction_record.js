/**
 * EDI Data class
 * represents the information of an edi transaction
 *
 * @NApiVersion 2.1
 */

//@ts-ignore
define([], () => {
	/**
	 * EDI Transaction Record class
	 *
	 * @class
	 * @type {import("./vlmd_edi_transaction").EdiTransactionRecord}
	 */

	class EdiTransactionRecord {
		constructor(json) {
			// this.prodGuidBool = json["prodGuidBool"];
			this.Field = {
				ccRecipients: "custrecord_spl_edi_email_cc_recipient",
				controlNumber: "custrecord_spl_edi_trnsctn_cntrl_nmbr",
				customer: "custrecord_edi_customer",
				documentTypeId: "custrecord_spl_edi_document_type",
				emailMessage: "custrecord_spl_edi_email_message",
				emailRecipient: "custrecord_spl_edi_email_recipient",
				emailSubject: "custrecord_spl_edi_email_subject",
				errorTypeId: "custrecord_edi_transaction_error_type",
				integrationEntity: "custrecord_edi_integration_entity",
				poRefNumber: "custrecord_spl_edi_po_ref_nmbr",
				processingStatus: "custrecord_spl_processing_status",
				purchasingSoftwareId: "custrecord_spl_edi_purchasing_software",
				vendor: "custrecord_spl_edi_vendor",
			};
		}
	}

	return EdiTransactionRecord;
});
