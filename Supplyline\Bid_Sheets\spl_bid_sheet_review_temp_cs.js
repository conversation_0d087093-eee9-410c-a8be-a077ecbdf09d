/**
 * @NApiVersion 2.1
 * @NScriptType ClientScript
 *
 * @description Client script for bid sheet review suitelet
 */
define([
'N/runtime',
'./Classes/Text_Matching/spl_bid_sheet_text_matching_class',
'./Classes/Text_Matching/spl_bid_sheet_word_frequency_class',
'./Classes/Text_Matching/spl_bid_sheet_item_repository_class',
], (
runtime,
TextMatching,
WordFrequencyAnalyzer,
ItemRepository
) => {

    /**
     * Function to be executed after page is initialized and ready
     *
     * @param {Object} context
     */
    function pageInit(context) {
        try {

            // const cachedItems = localStorage.getItem('bidSheetItemsCache');
            // if (cachedItems) {
            //     const parsedCache = JSON.parse(cachedItems);
            //     const textMatcher = new TextMatching();
            //     window.textMatcher = textMatcher;
            //     console.log('Cached items: ' + parsedCache.length);
            // } else {
            //     const textMatcher = new TextMatching();
            //     window.textMatcher = textMatcher;
            // }

            window.textMatcherClass = TextMatching;
            window.wordFrequencyAnalyzerClass = WordFrequencyAnalyzer;
            window.itemRepositoryClass = ItemRepository;

        } catch (error) {
            console.error('Error initializing TextMatching class:', error);
        }
    }

    return {
        pageInit: pageInit
    };
});



