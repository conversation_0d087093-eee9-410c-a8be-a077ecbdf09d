/**
 * Transaction Record class
 * containing custom and searched data
 * that represents a NetSuite Sales Order
 *
 * @NApiVersion 2.1
 * <AUTHOR>
 */

define(["N/search", "CustomTransactionRecord"], (search, CustomTransactionRecord) => {

	/**
	 * Sales Order Transaction Record class
	 *
	 * @class
	 * @type {import("./spl_transaction_record").SalesOrderTransactionRecord}
	 */
	class SalesOrderTransactionRecord extends CustomTransactionRecord {
		constructor(transaction) {
			super(transaction);

			/**
			 * Create the credit limit object
			 * If the customer parent is New Goldland, credit limit goes according to the parent,
			 * for all other facilities, credit limit goes according to the individual facilities.
			 *
			 * @param {import("./spl_transaction_record").CustomerParentObject|undefined} parentObject Contains parent customer ID
			 * @param {object} fallbackLookup Search lookup object to be used as fallback
			 * @returns {import("./spl_transaction_record").CreditLimitObject|undefined} Credit limit object
			 */
			const getCreditLimitObject = (parentObject, fallbackLookup) => {
				const newGoldlandCustomerId = 584;
				const isParentCustomerNewGoldland = Boolean(parentObject && parseInt(parentObject.id) === newGoldlandCustomerId);
				const customerCreditLookup = isParentCustomerNewGoldland
					? search.lookupFields({
						type: search.Type.CUSTOMER,
						id: newGoldlandCustomerId,
						columns: [
							this.Column.balance,
							this.Column.consolidatedBalance,
							this.Column.creditLimit,
						],
					})
					: fallbackLookup;
					
				if (!customerCreditLookup) {
					return;
				}
				
				return {
					creditAndBalanceByParent: isParentCustomerNewGoldland,
					creditLimit: customerCreditLookup[this.Column.creditLimit],
					balance: isParentCustomerNewGoldland
						? customerCreditLookup[this.Column.consolidatedBalance] // The parent's consolidated balance of all of its subs
						: customerCreditLookup[this.Column.balance] // The balance of the individual facility
				};
			};

			this.createdFrom = transaction.getValue({
				fieldId: this.Field.createdFrom
			});
			this.needFacilityInfo = transaction.getValue({
				fieldId: this.Field.needFacilityInfo
			});
			this.orderStatus = transaction.getValue({
				fieldId: this.Field.orderStatus
			});
			this.poNumber = transaction.getValue({
				fieldId: this.Field.otherReferenceNumber
			});
			this.ediControlNumber = transaction.getValue({
				fieldId: this.Field.ediControlNumber
			});
			this.correctionStatus = transaction.getValue({
				fieldId: this.Field.correctionStatus
			});
			this.creditLimitObj = getCreditLimitObject(this.customerParentObj, this.customerLookup);
		}

		/**
		 * Determine if the balance and transaction total's sum exceed the credit limit
		 *
		 * @returns {boolean} True if credit limit has been exceeded
		 */
		isCreditLimitExceeded() {
			if(this.creditLimitObj) {
				const balance = parseFloat(this.creditLimitObj.balance);
				let creditLimit = parseFloat(this.creditLimitObj.creditLimit);
				const salesOrderTotal = parseFloat(this.transactionTotal);
				if (creditLimit === null || creditLimit === undefined) {
					alert(
						`This customer${this.creditLimitObj.creditAndBalanceByParent ? "'s parent" : ""}`
						+ ` is missing a credit limit amount. Please add.`
					);
					creditLimit = 0; //Setting the credit limit to zero when its is undefined so it calculates and displays the alert when applicable.
				}
				return salesOrderTotal + balance > creditLimit;
			}
			return false;
		}

		/**
		 * Determine if Sales Order needs to be corrected
		 *
		 * @returns {boolean} True if Sales Order is marked for correction
		 */
		hasToBeCorrected() {
			const correctionsNeededId = "2";
			const approvedOrderStatusId = "B";

			return this.correctionStatus === correctionsNeededId && this.orderStatus === approvedOrderStatusId;
		}

		/**
		 * Determine if the current transaction has a duplicate
		 * Editing a pre-existing SO so it's a duplicate
		 *   if the search returns a result of a SO that isn't the current one
		 * It's only NOT a duplicate if the search returns a SO as a result
		 *   that's the same as the SO being edited
		 *   -> the result is the PO# of the current SO, just being edited and saved
		 *
		 * @returns {string} Internal ID of the existing Sales Order
		 */
		getDuplicateSalesOrder() {
			if(this.poNumber && this.customerParentObj && this.customerParentObj.id) {
				const duplicateSalesOrderSearch = search.create({
					type: search.Type.SALES_ORDER,
					filters: [
						["type", "anyof", "SalesOrd"],
						"AND",
						["mainline", "is", "T"],
						"AND",
						["otherrefnum", "equalto", this.poNumber],
						"AND",
						["customersubof", "anyof", this.customerParentObj.id],
					],
					columns: [
						search.createColumn({ name: "tranid", label: "Document Number" }),
					],
				});

				const salesOrderArr = duplicateSalesOrderSearch && duplicateSalesOrderSearch.run().getRange({
					start: 0,
					end: 1000
				});

				if (Array.isArray(salesOrderArr) && salesOrderArr.length > 0) {
					const existingSalesOrder = salesOrderArr[0].id;
					if(!this.transactionInternalId || existingSalesOrder != this.transactionInternalId) {
						return existingSalesOrder;
					}
				}
			}
			return "";
		}
	}

	return SalesOrderTransactionRecord;
});