/******************************************************************************************************
    Script Name - AVA_SUT_AvalaraDashBoard.js 
    Company -     Avalara Technologies Pvt Ltd.
******************************************************************************************************/

/**
 *@NApiVersion 2.0
 *@NScriptType Suitelet
*/

define(['N/ui/serverWidget', 'N/file', 'N/url', 'N/search'],
	function(ui, file, url, search){
		function onRequest(context){
			if(context.request.method === 'GET'){
				var setupImage, viewTransImage, reconcileTransImage, validateAddressImage, recalculateTaxImage, manageExemptImage, syncItemsImage, deleteRecordsImage;
				var bundleId = 'Bundle 296604';

				var form = ui.createForm({
					title: ' '
				});

				// getting Image URL's
				var folderId;

				var searchFolder = search.create({
					type: search.Type.FOLDER,
					filters: ['name', 'is', bundleId]
				});
				searchFolder = searchFolder.run();
				var searchFolderResult = searchFolder.getRange({
					start: 0,
					end: 5
				});

				if(searchFolderResult != null && searchFolderResult.length > 0){
					folderId = searchFolderResult[0].id;
				}

				if(folderId){
					var fileSearchObj = search.create({
						type: "file",
						filters: ["folder", "anyof", folderId],
						columns:
						[
							"name",
							"url"
						]
					});
					var searchFileResult = fileSearchObj.run();
					searchFileResult = searchFileResult.getRange({
						start: 0,
						end: 1000
					});
					
					for(var i = 0; i < searchFileResult.length; i++){
						var fileName = searchFileResult[i].getValue('name');
						var fileUrl = searchFileResult[i].getValue('url');

						if(fileName == 'Setup.svg'){
							setupImage = fileUrl;
						}
						else if(fileName == 'View Transactions.svg'){
							viewTransImage = fileUrl;
						}
						else if(fileName == 'Reconcile Transactions.svg'){
							reconcileTransImage = fileUrl;
						}
						else if(fileName == 'Validate Addresses.svg'){
							validateAddressImage = fileUrl;
						}
						else if(fileName == 'Recalculate Taxes.svg'){
							recalculateTaxImage = fileUrl;
						}
						else if(fileName == 'Manage Exemptions.svg'){
							manageExemptImage = fileUrl;
						}
						else if(fileName == 'Sync Items.svg'){
							syncItemsImage = fileUrl;
						}
						else if(fileName == 'Delete Records.svg'){
							deleteRecordsImage = fileUrl;
						}
					}
				}
				// End getting Image URL's

				//getting links for Tiles
				var configUrl = url.resolveScript({
					scriptId: 'customscript_avaconfig_wizard',
					deploymentId: 'customdeploy_ava_configurewizard'
				});
				var avalaraTknUrl = url.resolveScript({
					scriptId: 'customscript_avaregtkn_suitelet',
					deploymentId: 'customdeploy_avaregtkn'
				});
				var entityUseCodeUrl = url.resolveScript({
					scriptId: 'customscript_avaentityuselist_suitlet',
					deploymentId: 'customdeploy_entityuselist'
				});
				var shippingCodeUrl = url.resolveScript({
					scriptId: 'customscript_avashippinglist_suitlet',
					deploymentId: 'customdeploy_shippingcodelist'
				});
				var transactionsUrl = url.resolveScript({
					scriptId: 'customscript_avatransactionlist_suitelet',
					deploymentId: 'customdeploy_avatransactionlist'
				});
				var voidedTransactionsUrl = url.resolveScript({
					scriptId: 'customscript_avavoidedlist_suitlet',
					deploymentId: 'customdeploy_avavoidedlist'
				});
				var committedListUrl = url.resolveScript({
					scriptId: 'customscript_avacommittedlist_suitlet',
					deploymentId: 'customdeploy_avacommittedlist'
				});
				var createReconcileBatchUrl = url.resolveScript({
					scriptId: 'customscript_avareconcilelist_suitelet',
					deploymentId: 'customdeploy_avareconcilelist'
				});
				var viewReconcileBatchUrl = url.resolveScript({
					scriptId: 'customscript_ava_reconciliation_suitelet',
					deploymentId: 'customdeploy_ava_reconcileresult'
				});
				var quickValidateUrl = url.resolveScript({
					scriptId: 'customscript_avaquickvalidation_suitlet',
					deploymentId: 'customdeploy_avaquickaddressvalidation'
				});
				var createAddrValidateUrl = url.resolveScript({
					scriptId: 'customscript_avaddressvalidation_suitlet',
					deploymentId: 'customdeploy_avaaddressvalidation'
				});
				var viewAddrValidateUrl = url.resolveScript({
					scriptId: 'customscript_avaaddvalidresults_suitelet',
					deploymentId: 'customdeploy_avaaddressvalidationresults'
				});
				var createRecalcUrl = url.resolveScript({
					scriptId: 'customscript_ava_recalcutility',
					deploymentId: 'customdeploy_recalcform'
				});
				var viewRecalcUrl = url.resolveScript({
					scriptId: 'customscript_ava_recalcbatches',
					deploymentId: 'customdeploy_ava_recalcbatches'
				});
				var getCertificatesUrl = url.resolveScript({
					scriptId: 'customscript_avacertificates_suitelet',
					deploymentId: 'customdeploy_avacertificates_suitelet'
				});
				var createBatchToAddCertUrl = url.resolveScript({
					scriptId: 'customscript_avaaddcuststocert_suitelet',
					deploymentId: 'customdeploy_avaaddcuststocertcapture'
				});
				var viewBatchToAddCertUrl = url.resolveScript({
					scriptId: 'customscript_avacertviewbatch_suitelet',
					deploymentId: 'customdeploy_avacertcaptureviewbatch'
				});
				var createBatchFromNSToAV = url.resolveScript({
					scriptId: 'customscript_ava_imscreatebatch_suitelet',
					deploymentId: 'customdeploy_ava_imscreatebatch'
				});
				var viewBatchFromNSToAV = url.resolveScript({
					scriptId: 'customscript_ava_imsviewbatch_suitelet',
					deploymentId: 'customdeploy_ava_imsviewbatch'
				});
				var createBatchFromAVToNS = url.resolveScript({
					scriptId: 'customscript_ava_2wayimscreatebatch_suit',
					deploymentId: 'customdeploy_ava_2wayimscreatebatch_suit'
				});
				var viewBatchFromAVToNS = url.resolveScript({
					scriptId: 'customscript_ava_twowayimsviewbatch_suit',
					deploymentId: 'customdeploy_ava_twowayimsviewbatch_suit'
				});
				var deleteRecordsCreateBatch= url.resolveScript({
					scriptId: 'customscript_ava_deleteoldrecordcb_suit',
					deploymentId: 'customdeploy_ava_deleteoldrecordcb_suit'
				});
				var deleteRecordsViewBatch = url.resolveScript({
					scriptId: 'customscript_ava_deleteoldrecordvb_suit',
					deploymentId: 'customdeploy_ava_deleteoldrecordvb_suit'
				});
				//End getting links for Tiles

				var htmlCode = '<html> <html lang="en">';
				htmlCode += '<body>';

				htmlCode += '<div class="container-fluid ml-2 mr-2">';
				htmlCode += '<h1 class="h1Class">Manage Avalara</h1>';
				htmlCode += '<script>window.document.title = "Manage Avalara"</script>';

				htmlCode += '<div class="row">';
				htmlCode += '<div class="col-md-9 mb-2 mt-1 describeclass">';
				htmlCode += 'Manage Avalara page gives you quick access to settings and options, all in one place. ';
				htmlCode += '<a href="https://knowledge.avalara.com/bundle/chz1662726137134_chz1662726137134" target="_blank">Learn more</a>';
				htmlCode += '</div>';
				htmlCode += '</div>';

				//htmlCode += '<input id="myInput" type="text" placeholder="Search" class ="searchClass form-control form-rounded"><br><br><div>';
				//htmlCode += '<div id="searchDiv" class="ml-2">';

				// 1st row Start
				htmlCode += '<div class="row">';
				//Setup Box started
				htmlCode += '<div class="card m-2 dashclass">';
				htmlCode += '<div class="card-body p-3">';
				htmlCode += '<span>';
				htmlCode += '<img src=' + setupImage + 'class ="imgBackgroud"/>';
				htmlCode += '<h3 class="card-title tileHeadClass">Setup</h3>';
				htmlCode += '</span>';
				htmlCode += '<h5 class="tileChildClass"><a href="' + configUrl + '"target="_blank">Avalara</a></h5>';
				htmlCode += '<h5 class="tileChildClass"><a href="' + avalaraTknUrl + '"target="_blank">Regenerate Avalara Token</a></h5>';
				htmlCode += '<h5 class="tileChildClass"><a href="' + entityUseCodeUrl + '"target="_blank">Entity/Use codes</a></h5>';
				htmlCode += '<h5 class="tileChildClass"><a href="' + shippingCodeUrl + '"target="_blank">Shipping codes</a></h5>';
				htmlCode += '</div>';
				htmlCode += '</div>';

				//View Transactions Box started
				htmlCode += '<div class="card m-2 dashclass">';
				htmlCode += '<div class="card-body p-3">';
				htmlCode += '<span>';
				htmlCode += '<img src=' + viewTransImage + 'class ="imgBackgroud"/>';
				htmlCode += '<h3 class="card-title tileHeadClass">View Transactions</h3>';
				htmlCode += '</span>';
				htmlCode += '<h5 class="tileChildClass"><a href="' + transactionsUrl + '"target="_blank">All</a></h5>';
				htmlCode += '<h5 class="tileChildClass"><a href="' + voidedTransactionsUrl + '"target="_blank">Voided</a></h5>';
				htmlCode += '<h5 class="tileChildClass"><a href="' + committedListUrl + '"target="_blank">Commited</a></h5>';
				htmlCode += '</div>';
				htmlCode += '</div>';

				//Reconcile Transactions Box started
				htmlCode += '<div class="card m-2 dashclass">';
				htmlCode += '<div class="card-body p-3">';
				htmlCode += '<span>';
				htmlCode += '<img src=' + reconcileTransImage + 'class ="imgBackgroud"/>';
				htmlCode += '<h3 class="card-title tileHeadClass">Reconcile Transactions</h3>';
				htmlCode += '</span>';
				htmlCode += '<h5 class="tileChildClass"><a href="' + createReconcileBatchUrl + '"target="_blank">Create batch</a></h5>';
				htmlCode += '<h5 class="tileChildClass"><a href="' + viewReconcileBatchUrl + '"target="_blank">View batch</a></h5>';
				htmlCode += '</div>';
				htmlCode += '</div>';

				//Validate Addresses Box started
				htmlCode += '<div class="card m-2 dashclass">';
				htmlCode += '<div class="card-body p-3">';
				htmlCode += '<span>';
				htmlCode += '<img src=' + validateAddressImage + 'class ="imgBackgroud"/>';
				htmlCode += '<h3 class="card-title tileHeadClass">Validate Addresses</h3>';
				htmlCode += '</span>';
				htmlCode += '<h5 class="tileChildClass"><a href="' + quickValidateUrl + '"target="_blank">Quick validation</a></h5>';
				htmlCode += '<h5 class="tileChildClass"><a href="' + createAddrValidateUrl + '"target="_blank">Create batch</a></h5>';
				htmlCode += '<h5 class="tileChildClass"><a href="' + viewAddrValidateUrl + '"target="_blank">View batch</a></h5>';
				htmlCode += '</div>';
				htmlCode += '</div>';

				//Recalculate Taxes Box started
				htmlCode += '<div class="card m-2 dashclass">';
				htmlCode += '<div class="card-body p-3">';
				htmlCode += '<span>';
				htmlCode += '<img src=' + recalculateTaxImage + 'class ="imgBackgroud"/>';
				htmlCode += '<h3 class="card-title tileHeadClass">Recalculate Taxes</h3>';
				htmlCode += '</span>';
				htmlCode += '<h5 class="tileChildClass"><a href="' + createRecalcUrl + '"target="_blank">Create batch</a></h5>';
				htmlCode += '<h5 class="tileChildClass"><a href="' + viewRecalcUrl + '"target="_blank">View batch</a></h5>';
				htmlCode += '</div>';
				htmlCode += '</div>';

				//Manage Exemptions Box
				htmlCode += '<div class="card m-2 dashclass">';
				htmlCode += '<div class="card-body p-3">';
				htmlCode += '<span>';
				htmlCode += '<img src=' + manageExemptImage + 'class ="imgBackgroud"/>';
				htmlCode += '<h3 class="card-title tileHeadClass">Manage Exemptions</h3>';
				htmlCode += '</span>';
				htmlCode += '<h5 class="accordionHeadClass"><a href="' + getCertificatesUrl + '"target="_blank">Get certificate/Get status</a></h5>';

				htmlCode += '<div id="accordioncert">';
				htmlCode += '<div class="accordionHeadClass">';
				htmlCode += '<a class="card-link" data-toggle="collapse" href="#collapseThree"><span class="spanClass">&#x2BC6;</span>Add multiple customers to ECM</a>';
				htmlCode += '<div id="collapseThree" class="collapse accordionClass" data-parent="#accordioncert">';
				htmlCode += '<a href="' + createBatchToAddCertUrl + '"target="_blank">Create batch</a></br>';
				htmlCode += '<a href="' + viewBatchToAddCertUrl + '"target="_blank">View batch</a>';
				htmlCode += '</div>';
				htmlCode += '</div>';
				htmlCode += '</div>';//accordion

				htmlCode += '</div>';
				htmlCode += '</div>';

				//Sync Items Box
				htmlCode += '<div class="card m-2 dashclass">';
				htmlCode += '<div class="card-body p-3">';
				htmlCode += '<span>';
				htmlCode += '<img src=' + syncItemsImage + 'class ="imgBackgroud"/>';
				htmlCode += '<h3 class="card-title tileHeadClass">Sync Items</h3>';
				htmlCode += '</span>';

				htmlCode += '<div id="accordion">';
				htmlCode += '<div class="accordionHeadClass">';
				htmlCode += '<a class="card-link" data-toggle="collapse" href="#collapseOne"><span class="spanClass">&#x2BC6;</span>From NetSuite to Avalara</a>';
				htmlCode += '<div id="collapseOne" class="collapse accordionClass" data-parent="#accordion">';
				htmlCode += '<a href="' + createBatchFromNSToAV + '"target="_blank">Create batch</a></br>';
				htmlCode += '<a href="' + viewBatchFromNSToAV + '"target="_blank">View batch</a>';
				htmlCode += '</div>';
				htmlCode += '</div>';

				htmlCode += '<div class="accordionHeadClass">';
				htmlCode += '<a class="card-link" data-toggle="collapse" href="#collapseTwo"><span class="spanClass">&#x2BC6;</span>From Avalara to NetSuite</a>';
				htmlCode += '<div id="collapseTwo" class="collapse accordionClass" data-parent="#accordion">';
				htmlCode += '<a href="' + createBatchFromAVToNS + '"target="_blank">Create batch</a></br>';
				htmlCode += '<a href="' + viewBatchFromAVToNS + '"target="_blank">View batch</a>';
				htmlCode += '</div>';
				htmlCode += '</div>';
				htmlCode += '</div>';//accordion

				htmlCode += '</div>';
				htmlCode += '</div>';
				// Sync Items Box column end

				//Delete Custom Record Types Box started
				htmlCode += '<div class="card m-2 dashclass">';
				htmlCode += '<div class="card-body p-3">';
				htmlCode += '<span>';
				htmlCode += '<img src=' + deleteRecordsImage + 'class ="imgBackgroud"/>';
				htmlCode += '<h3 class="card-title tileHeadClass">Delete Avalara Transaction Logs</h3>';
				htmlCode += '</span>';
				htmlCode += '<h5 class="tileChildClass"><a href="' + deleteRecordsCreateBatch + '"target="_blank">Create batch</a></h5>';
				htmlCode += '<h5 class="tileChildClass"><a href="' + deleteRecordsViewBatch + '"target="_blank">View batch</a></h5>';
				htmlCode += '</div>';
				htmlCode += '</div>';

				htmlCode += '</div>';// row End

				//htmlCode += '</div>';
				//htmlCode += '</div>';//Search div ended

				//bottom row started
				htmlCode += '<footer style= "padding-top: 50px">';
				htmlCode += '<hr class = "hrclass">'
				htmlCode += '<div class="row align-items-end supportclass">';
				htmlCode += 'Still need help?';
				htmlCode += '<div class="col supportclass">';
				htmlCode += '<a class="float-left" href="https://account.avalara.com/#/support-cases/support-request" target="_blank"><span class="spanClass">&#x1F5E8;</span>Contact Support</a>';
				htmlCode += '</div>';
				htmlCode += '</div>';
				htmlCode += '</footer>';
				// bottom row End

				htmlCode += '</div>';
				htmlCode += '</body>';

				htmlCode += '</html>';

				var cssFile = file.load({
					id: './utility/Avalara Dashboard/bootstrap.min.css'
				});
				var bs1File = file.load({
					id: './utility/Avalara Dashboard/bootstrap.bundle.min.js'
				});
				var bs2File = file.load({
					id: './utility/Avalara Dashboard/jquery.slim.min.js'
				});
				var bs3File = file.load({
					id: './utility/Avalara Dashboard/popper.min.js'
				});
				var customJQueryfile = file.load({
					id: './utility/Avalara Dashboard/AVA_SearchJQueryFile.js'
				});
				var cssFile1 = file.load({
					id: './utility/Avalara Dashboard/AVA_DashBoardTiles.css'
				});

				htmlCode += '<script src="' + customJQueryfile.url + '"></script>';
				htmlCode += '<link rel="stylesheet" href=' + cssFile.url + '>';
				//htmlCode += '<link rel="stylesheet" href= https://use.fontawesome.com/releases/v5.7.0/css/all.css "integrity" = sha384-lZN37f5QGtY3VHgisS14W3ExzMWZxybE1SJSEsQp9S+oqd12jhcu+A56Ebc1zFSJ "crossorigin" = "anonymous">';
				htmlCode += '<script src="' + bs1File.url + '"></script>';
				htmlCode += '<script src="' + bs2File.url + '"></script>';
				htmlCode += '<script src="' + bs3File.url + '"></script>';
				htmlCode += '<link rel="stylesheet" href=' + cssFile1.url + '>';

				var htmlDetails = form.addField({
					id: 'htmldetails',
					label: ' ',
					type: ui.FieldType.INLINEHTML
				});
				htmlDetails.defaultValue = htmlCode;

				context.response.writePage({
					pageObject: form
				});
			}
		}
		
		return{
			onRequest: onRequest
		};
	}
);