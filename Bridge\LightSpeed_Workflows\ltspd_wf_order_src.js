/**
 * @description Sets the order source custom field value and removes the order source line on the order coming from LS.
 * @NApiVersion 2.1
 * @NScriptType Suitelet
 * <AUTHOR>
 * @module ltspd_wf_order_src
 */

define([
	"require",
	"N/log",
	"../../Classes/vlmd_custom_error_object",
], function (require, log) {
	/** @type {import("../../Classes/vlmd_custom_error_object").CustomErrorObject} */

	const CustomErrorObject = require("../../Classes/vlmd_custom_error_object");
	const customErrorObject = new CustomErrorObject();

	function onRequest(context) {
		if (context.request.method === "POST") {
			var orderSourceObj = {
				//Vineyard North
				"0ac54c19-8c55-11ed-f913-3920e12a6941": {
					"004f80c4-0438-4aa7-a2de-5cca899302e8": "WhatsApp Order",
					"0c1ac9c1-f4bd-4a81-9cd6-797d99be55aa": "Retail Order",
					"9140155a-76c2-40b1-9447-97bad91528ee": "Phone Order",
					"788947e4-3e2f-4848-afe4-3233d23cc17b": "Email Order",
					"5cf1594e-2354-4ba2-ac9b-740d9c178fd2": "DoorDash Order",
					"31f506b0-4549-4dd5-a7f4-8680bd6ec836":
						"WhatsApp Order - Yehuda Stefansky",
					"bdcc430a-02c1-4e90-aeab-5bd505fe15b6":
						"WhatsApp Order - Simcha Ingber",
					"8ac99481-1d0b-45bf-9f08-9c51e09a1470":
						"WhatsApp Order - Sruly Iowiniski",
					"7d153078-5770-40bd-bb75-9ce4e336e07f":
						"WhatsApp Order - Shea Berkowitz",
					"909c01ff-8c6f-4f76-860c-9fe952299782":
						"WhatsApp Order - Aryeh Kranz",
				},
				//Vineyard Westgate
				"0ac54c19-8c55-11ed-fd4a-8d314c8bb4e4": {
					"11e7ec50-6c5a-4dd8-bc3f-d76db9da927f": "WhatsApp Order",
					"d2e49788-b9da-4c4d-b005-e3321f6f369c": "Retail Order",
					"caa0fe15-fcf7-451a-8bcd-1fed09b0554c": "Phone Order",
					"058b8fea-dd73-4bec-8fe3-9278ad767bff": "Email Order",
					"c1bb73ea-1d20-4eab-9e84-69898744b052": "DoorDash Order",
					"80b1ac6f-5d7d-467f-8beb-06f7b1e3409d":
						"WhatsApp Order - Yehuda Stefansky",
					"7bf319a4-a009-4afa-9b72-b377a94cc90c":
						"WhatsApp Order - Simcha Ingber",
					"62725517-6d98-4c27-aa64-ea99f4e6c9c0":
						"WhatsApp Order - Naftali Bandman",
				},
				//Vineyard South
				"0ac54c19-8c55-11ed-fd4a-75a7fae850fa": {
					"79fb502e-c56f-456e-be2b-f4c6903e6bf0": "WhatsApp Order",
					"eaf3794e-e539-419e-9fa4-0cf5c79d4868": "Retail Order",
					"7f400917-00b0-432b-881c-e0f08f7ac150": "Phone Order",
					"41cd9d93-837a-4f1a-9a89-278781fe432c": "Email Order",
					"9c6ca88e-1086-4c6d-a7c9-93ae78153fb5": "DoorDash Order",
					"fc6668aa-717c-404c-bded-d8c526801a68":
						"WhatsApp Order - Yehuda Stefansky",
					"8ff27c43-add2-47e1-ad81-ffc93b109b73":
						"WhatsApp Order - Simcha Ingber",
					"ec0737bb-9540-4a38-b47d-1d402213952a":
						"WhatsApp Order - Shuki Waldman",
				},
				//Vineyard Express
				"0604d568-81ed-11ed-f947-b2006653c61d": {
					"5b7bc88d-966a-4aa6-95e0-6285e6edcebb": "WhatsApp Order",
					"40cac8da-5eef-4b69-a828-3677c600a117": "Retail Order",
					"9bbaf4ed-8a7b-4a59-9185-201e8213d21b": "Phone Order",
					"93ed6774-38b6-4c1e-9de7-e1d4d1d5b76a": "Email Order",
					"26bab28d-d289-435b-b437-72b87e334d95": "DoorDash Order",
					"59405c22-a5f9-469f-9ea1-41787c1fd3fd":
						"WhatsApp Order - Yehuda Stefansky",
					"ba7cc74e-3470-4e56-b38e-11864bab7959":
						"WhatsApp Order - Simcha Ingber",
					"7429141b-9785-486a-839e-bf721eac9e14":
						"WhatsApp Order - Meir Walden",
				},
				//Vineyard Bergenfield
				"0ac54c19-8c55-11ed-fd4a-8d31f3232984": {
					"d8a0a5a1-c087-4c99-a36a-41ecb1059935": "WhatsApp Order",
					"522f8b57-8abe-45ad-bd64-da75f935d9d4": "Retail Order",
					"f134f2f5-c6ec-47e4-b03a-5b27ea21f35a": "Phone Order",
					"f6a2070f-4584-43b0-a90e-538ea44bb62a": "Email Order",
					"bb2c5458-2c1a-4f08-8d51-c6496fbef756": "DoorDash Order",
					"39cc04bc-ce4f-4384-b5cd-89606a9b5efc":
						"WhatsApp Order - Yehuda Stefansky",
					"f4a35eee-128f-4ea6-b870-5fc8591339c2":
						"WhatsApp Order - Simcha Ingber",
					"e004d093-a5fc-4ff5-94e0-4f184a05ea80":
						"WhatsApp Order - Dovi Hirsch",
				},
				//Lots of Liquor
				"0ac54c19-8c55-11ed-fd4a-75a7dd482da4": {
					"5cdc12ee-dd76-44e5-91c0-156017e46158": "WhatsApp Order",
					"1e0a762a-196f-4435-9653-5a4dc9c5e0d9": "Retail Order",
					"01b1744f-4d0b-4a2d-8493-f06e77dbb61d": "Phone Order",
					"923b0323-a444-413f-9ca7-3c1260961198": "Email Order",
					"1acbaeaa-db10-4b13-836d-280ef0ef6555": "DoorDash Order",
					"6492d76e-f5ac-481b-952d-0b8ef2e1ce68":
						"WhatsApp Order - Yehuda Stefansky",
					"685b474c-d093-4e58-96a5-ad8705c51586":
						"WhatsApp Order - Simcha Ingber",
				},
			};

			const parsedJson = JSON.parse(context.request.body),
				username = parsedJson["user"]["username"],
				storeName = parsedJson["retailer"]["domain_prefix"],
				retailStoreId = parsedJson["retailer_id"],
				lineItemsArr = parsedJson["sale"]["line_items"],
				saleId = parsedJson["sale"]["id"];

			if (parsedJson["event_type"] == "sale.ready_for_payment") {
				try {
					let actionObj = {
						actions: [],
					};

					//Get info for if the custom field was already set
					let orderSourceObjInCustomFields = parsedJson["sale"][
						"custom_fields"
					].find((obj) => obj.name == "order_source");

					let orderSourceIdInCustomField =
						orderSourceObjInCustomFields &&
						orderSourceObjInCustomFields["string_value"];

					//Get info for the current line items in the payload
					let orderSourceLinesInPayloadArr = lineItemsArr.filter((itemObj) => {
						const lineItemId = itemObj["product_id"];

						let itemIdFoundInOrderSourceObj =
							orderSourceObj[retailStoreId][lineItemId];

						return (
							itemIdFoundInOrderSourceObj == true ||
							itemIdFoundInOrderSourceObj != null
						);
					});

					let orderSourceIdFromLine =
						orderSourceLinesInPayloadArr.length > 0
							? orderSourceLinesInPayloadArr[0]["product_id"]
							: false;

					//Handle setting order source
					if (
						!orderSourceObjInCustomFields ||
						(orderSourceIdFromLine &&
							orderSourceIdFromLine != orderSourceIdInCustomField)
					) {
						//Validate only one order source was chosen
						if (orderSourceLinesInPayloadArr.length > 1) {
							log.audit(
								"MULTIPLE_ORDER_SOURCES_CHOSEN",
								`Store: ${storeName} User: ${username}, Order Source IDs Chosen: ${orderSourceLinesInPayloadArr
									.map((obj) => obj?.id)
									.join(",")}`
							);

							actionObj.actions.push({
								type: "stop",
								title: "Multiple Order Sources Chosen",
								message: `Please choose only one order source.`,
								dismiss_label: "OK",
							});
						}

						//Set to default order source if no order source was chosen (set to retail order for store)
						if (!orderSourceIdFromLine) {
							log.audit(
								"NO_ORDER_SOURCE_CHOSEN",
								`Store: ${storeName}, User: ${username}, LightSpeed Sale Id: ${saleId}`
							);

							//Set to the default order source id for the store
							orderSourceIdFromLine = Object.keys(
								orderSourceObj[retailStoreId]
							).find(
								(key) => orderSourceObj[retailStoreId][key] === "Retail Order"
							);

							//Throw error if couldn't find default retail order id for this store
							if (!orderSourceIdFromLine) {
								throw customErrorObject.updateError({
									errorType: customErrorObject.ErrorTypes.NO_VALUE_RETURNED,
									summary: "RETAIL_ORDER_VALUE_NOT_FOUND",
									details: `Default retail order value for store not found. Store: ${storeName}, User: ${username}, LightSpeed Sale Id: ${saleId}`,
								});
							}
						}

						//Add set order source custom field to actions array
						actionObj.actions.push({
							type: "set_custom_field",
							entity: "sale",
							custom_field_name: "order_source",
							custom_field_value: orderSourceIdFromLine,
						});

						//Set sales rep if is WhatsApp order
						let orderSourceName =
							orderSourceObj[retailStoreId][orderSourceIdFromLine];

						orderSourceName.includes("WhatsApp Order - ") &&
							actionObj.actions.push({
								type: "set_custom_field",
								entity: "sale",
								custom_field_name: "sales_rep",
								custom_field_value: orderSourceName.replace(
									"WhatsApp Order - ",
									""
								),
							});
					}

					/*Handle removing order source from line items -
						need to use the "id" field, not the "product_id" field for this*/
					if (
						orderSourceIdFromLine &&
						orderSourceLinesInPayloadArr[0] &&
						orderSourceLinesInPayloadArr[0]["id"]
					) {
						actionObj.actions.push({
							type: "remove_line_item",
							line_item_id: orderSourceLinesInPayloadArr[0]["id"],
						});
					}

					//If any actions were set - write the actions JSON object to context
					if (actionObj.actions.length > 0) {
						log.debug(`Sale ID: ${saleId}`, actionObj.actions);

						const jsonActionObj = JSON.stringify(actionObj);
						context.response.write(jsonActionObj);
					}
				} catch (err) {
					customErrorObject.throwError({
						summaryText: `ERROR_PAYING: ${retailStoreId}`,
						error: err,
					});
				}
			}
		}
	}

	return {
		onRequest,
	};
});
