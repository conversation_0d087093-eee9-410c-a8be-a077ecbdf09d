/**
 * @description Sets values on Purchase Orders - sets the custom PO email address field
 *
 * </br><b>Deployed On:</b> PurchaseOrder
 * </br><b>Execution Context:</b> USEREVENT
 * </br><b>Event Type/Mode:</b> ALL
 * </br><b>Entry Points:</b> beforeSubmit
 *
 * @NApiVersion 2.1
 * @NScriptType UserEventScript
 * @NAmdConfig /SuiteScripts/config.json
 *
 * <AUTHOR>
 * @module spl_set_purchase_order_email_ue
 */

define([
  "require",
  "./../Libraries/Misc_Libs/spl_get_order_email_address_lib",
  "./../Classes/vlmd_custom_error_object",
], (/** @type {any} */ require) => {
  const getOrderEmailAddress = require("./../Libraries/Misc_Libs/spl_get_order_email_address_lib");
  /** @type {import("./../Classes/vlmd_custom_error_object").CustomErrorObject} */
  const CustomErrorObject = require("./../Classes/vlmd_custom_error_object");
  const customErrorObject = new CustomErrorObject();

  return {
    beforeSubmit: (context) => {
      try {
        const purchaseOrder = context.newRecord;

        const vendorId = purchaseOrder.getValue("entity");

        if (!vendorId) return;

        let poEmailResults =
          getOrderEmailAddress.getPurchaseOrderEmailAddress(vendorId);

        if (poEmailResults && poEmailResults.length > 0) {
          poEmailResults = poEmailResults.join(";");
          purchaseOrder.setValue("email", poEmailResults);
        }
      } catch (error) {
        customErrorObject.throwError({
          summaryText: `ERROR_UPDATING_PURCHASE_ORDER`,
          error: error,
          recordId: context.newRecord ? context.newRecord.id : 'UNKNOWN_ID',
          recordType: `PURCHASE_ORDER`,
          errorWillBeGrouped: true,
        });
      }
    },
  };
});
