/**
 * @NApiVersion 2.1
 */
//@ts-ignore
define(["N/query", "N/log"], function (query, log) {
		function getDeliveryFields(shippingAddressId) {
			const sqlQuery = `select custrecord_adr_delivery_contact, custrecord_adr_delivery_instructions, custrecord_adr_liftgate_required
			 from entityaddress where recordowner = ${shippingAddressId}`

			var sqlResults = query.runSuiteQL({
				query: sqlQuery,
			}).asMappedResults()[0];

				return sqlResults;
				
	}

		return{
			 getDeliveryFields: getDeliveryFields
		};
	
})