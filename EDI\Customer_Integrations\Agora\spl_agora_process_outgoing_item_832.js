/**
 * @NApiVersion 2.1
 * @NScriptType scheduledscript
 * @NAmdConfig /SuiteScripts/config.json
 */

define([
	"require",
	"N/log",
	"../../../EDI/Libraries/Process_EDI_File/spl_process_outgoing_832_item_lib",
], function (require, log) {
	function execute(context) {
		/**@type {import ('../../../EDI/Libraries/Process_EDI_File/spl_process_outgoing_832_item_lib')} */
		const processCatalogLib = require("../../../EDI/Libraries/Process_EDI_File/spl_process_outgoing_832_item_lib");

		try {
			var dataObj = {
				prodGuidBool: true, //Toggle for Sandbox or Prod
				prodDirectoryBool: true, //Toggle for Sandbox or Prod
				prodGUID: "********************************",
				sandboxGUID: "",
				prodDirectory: "/users/Agora/OUT/832",
				testDirectory: "/users/Agora/Test",
				transactionType: "Item Catalog",
				purchasingSoftware: "Agora",
			};

			processCatalogLib.processCatalog(dataObj, 4241); //AGORA Item Catalog
		} catch (e) {
			throw e;
		}
	}

	return {
		execute: execute,
	};
});
