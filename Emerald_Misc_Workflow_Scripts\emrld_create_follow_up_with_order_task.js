/**
 *@NApiVersion 2.1
 *@NScriptType UserEventScript
 * @NAmdConfig /SuiteScripts/config.json
 */

define(["N/record"], function (record) {
	function afterSubmit(context) {
		if (context.type == "create" || context.type == "copy") {
			var invoice = context.newRecord;
			var subsidiary = invoice.getValue("subsidiary");
			if (subsidiary == 7) {
				//Adilo
				var invoiceName = invoice.getValue("tranid");
				var customer = invoice.getValue("entity");
				var assignTaskTo = 6046; //Mr. Zelcer

				var startDate = new Date();
				startDate.setDate(startDate.getDate() + 14);

				var task = record.create({
					type: record.Type.TASK,
				});

				task.setValue({
					fieldId: "customform",
					value: 111, //Adilo Task Form
				});

				task.setValue({
					fieldId: "title",
					value: `Follow up with <PERSON><PERSON><PERSON> on ${invoiceName}`,
				});

				task.setValue({
					fieldId: "assigned",
					value: assignTaskTo,
				});

				task.setValue({
					fieldId: "company",
					value: customer,
				});

				task.setValue({
					fieldId: "transaction",
					value: invoice.id,
				});

				task.setValue({
					fieldId: "duedate",
					value: startDate,
				});

				task.save();
			}
		}
	}

	return {
		afterSubmit: afterSubmit,
	};
});
