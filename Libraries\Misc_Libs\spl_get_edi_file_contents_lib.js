/**
 * @NApiVersion 2.1
 */

define([
	"require",
	"N/log",
	"N/sftp",
	"N/encode",
	"N/error",
	"N/runtime",
], function (require) {
	const log = require("N/log");
	const sftp = require("N/sftp");
	const encode = require("N/encode");
	const error = require("N/error");
	const runtime = require("N/runtime");

	var errorLog = [];

	var hostKey =
		"AAAAB3NzaC1yc2EAAAABIwAAAIEA2YIvZPcsWA3jP6yq5J" +
		"5XYunNySNspUZUhtzxXoePDZEhxJFIF/am9o+15CqO4hlyEDni0m+VW4" +
		"LVSoiUnj31xJis91EcYrUeCewIcSwof4Xs05ghth/2SKn0cITsR5hOeST" +
		"l4jFsMf9oK0+b2gr4jdV4fria4E/eF2u0qQArWuc=";

	function createConnection(
		data,
		appendToDirectory,
		referenceDirectory,
		createConnectionToRoot
	) {
		try {
			var directory;
			if (referenceDirectory) {
				directory = referenceDirectory;
			} else if (createConnectionToRoot) {
				return sftp.createConnection({
					username: "FTPadmin",
					passwordGuid: data.prodGuidBool ? data.prodGUID : data.sandboxGUID,
					url: "************",
					hostKey: hostKey,
					directory: "/",
				});
			} else {
				directory = data.prodDirectoryBool
					? data.prodDirectory
					: data.testDirectory;
			}

			if (appendToDirectory) {
				directory += appendToDirectory;
			}

			return sftp.createConnection({
				username: "FTPadmin",
				passwordGuid: data.prodGuidBool ? data.prodGUID : data.sandboxGUID,
				url: "************",
				hostKey: hostKey,
				directory: directory,
			});
		} catch (err) {
			var errorText = `Connection not created for ${directory}.
			`;

			if (err.name == "UNEXPECTED_ERROR") {
				errorText += "Please check the directory path.";
			} else {
				errorText += ` Error: ${err.message}`;
			}

			throw error.create({
				name: err.name,
				message: errorText,
			});
		}
	}

	function getFileNamesForCurrentIntegration(data, connection) {
		var names = [];

		try {
			var directoryList = _getDirectoryList();

			for (var x = 0; x < directoryList.length; x++) {
				if (!directoryList[x].directory) {
					names.push(directoryList[x]);
				}
			}
		} catch (e) {
			var text = `File names not gotten for ${data.vendorName} ${data.documentType}. Error: ${e}`;
			errorLog.push(text);

			log.error({
				title: "Get File Names Error",
				details: text,
			});
		}

		return names;

		function _getDirectoryList() {
			try {
				return connection.list({
					path: "",
				});
			} catch (e) {
				var text = `Directory list not gotten for ${data.vendorName} ${data.documentType}. Error: ${e}`;
				errorLog.push(text);

				log.error({
					title: "Get Directory List Error",
					details: text,
				});

				return false;
			}
		}
	}

	function getContentForFile(connection, fileName, data) {
		var downloadedFile = downLoadFile();
		var fileContent = getContent();

		return fileContent;

		function downLoadFile() {
			try {
				var download = connection.download({
					directory: "",
					filename: fileName,
				});

				return download;
			} catch (e) {
				log.error({ title: "Download File Error", details: e });
				errorLog.push(e);

				throw `Download File Error Caused Script to Fail`;
			}
		}

		function getContent() {
			var content = downloadedFile.getContents();

			if (data.decodeContent) {
				content = encode.convert({
					string: content,
					inputEncoding: encode.Encoding.BASE_64,
					outputEncoding: encode.Encoding.UTF_8,
				});
			}

			return {
				fileName: fileName,
				content: content,
			};
		}
	}

	function getEdiFileContents(data, integrationName) {
		try {
			function getFileContentsForGivenFileNames(connection, integrationName) {
				try {
					const fileContents = [];

					/*Can't use a forEach loop because need ability to break,
						Can't use for...of... loop because the arr is type obj, not str*/
					for (let x = 0; x < fileNames.length; x++) {
						let ediFile = fileNames[x];

						const remainingUsage = runtime
							.getCurrentScript()
							.getRemainingUsage();

						if (remainingUsage < 100) {
							log.error(
								`Script Max Reached for ${integrationName}`,
								`Execution stopped at file ${ediFile.name}.`
							);

							return { continueProcessing: false, fileContents: [] }; //Stop execution and return empty arr for this integration
						}

						fileContents.push(
							getContentForFile(connection, ediFile.name, data)
						);
					}

					return { continueProcessing: true, fileContents };
				} catch (e) {
					var text = `File contents not gotten for ${data.vendorName} ${data.documentType} - ${e}`;
					errorLog.push(text);

					log.error({
						title: "Get File Content Error",
						details: text,
					});

					return { continueProcessing: false, fileContents: [] };
				}
			}

			const connection = createConnection(data);
			let fileNames = getFileNamesForCurrentIntegration(data, connection);
			fileNames = fileNames.slice(0, 30);

			if (fileNames.length <= 0) {
				return { continueProcessing: true, fileContents: [] };
			}

			let getContentsObj = getFileContentsForGivenFileNames(
				connection,
				integrationName
			);

			return getContentsObj;
		} catch (e) {
			throw error.create({
				name: "Custom System Error",
				message: `${e} - Refer to script deployment log for details:

				${errorLog}`,
			});
		}
	}

	function getDownloadConnection(data) {
		try {
			return sftp.createConnection({
				username: "FTPadmin",
				passwordGuid: data.prodGuidBool ? data.prodGUID : data.sandboxGUID,
				url: "************",
				hostKey: hostKey,
			});
		} catch (e) {
			var text = `Download connection not created for${data.vendorName} ${data.documentType}. Error: ${e}`;
			throw error.create({
				name: "Get Download Connection Error",
				message: text,
			});
		}
	}

	function getCustomerFolders(data) {
		try {
			var customerFoldersArr = [];

			var connection = createConnection(data);
			var folderList = connection.list({
				path: "",
			});

			folderList = folderList
				.filter(
					(object) =>
						//@ts-ignore
						object.directory && (object.name != ".") & (object.name != "..")
				)
				.map((object) => object.name); //Filters to only return folders vs. documents or empty folders

			var testIndex = folderList.indexOf("Test");

			if (data.prodDirectoryBool) {
				folderList.splice(testIndex, 1);
				customerFoldersArr = folderList;
			} else {
				customerFoldersArr = ["iCareTest_3391"]; //Using iCare to test.
			}
			customerFoldersArr = customerFoldersArr.map((folder) => {
				var customerObj = folder.split("_");
				return {
					folderName: folder,
					customerId: customerObj[0],
					customerName: customerObj[1],
				};
			});

			return customerFoldersArr;
		} catch (e) {
			throw error.create({
				name: "Error Getting Customer Folders",
				message: e,
			});
		}
	}

	return {
		createConnection,
		getFileNamesForCurrentIntegration,
		getContentForFile,
		getEdiFileContents,
		getDownloadConnection,
		getCustomerFolders,
	};
});
