/**
 * @description Map reduce script that emails results of a sql query
 * </br><b>Schedule:</b> Each deployment has a specific schedule
 *
 * @NApiVersion 2.1
 * @NScriptType MapReduceScript
 * @NAmdConfig /SuiteScripts/config.json
 *
 * <AUTHOR>
 * @module vlmd_email_query_results_mr
 */

define([
	"require",
	"N/log",
	"N/runtime",
	"N/file",
	"../Classes/vlmd_custom_error_object",
	"../Classes/vlmd_mr_summary_handling",
	"../Classes/spl_query_email",
], (/** @type {any} */ require) => {
	const log = require("N/log");
	const runtime = require("N/runtime");
	const file = require("N/file");

	/** @type {import("../Classes/vlmd_custom_error_object").CustomErrorObject} */

	const CustomErrorObject = require("../Classes/vlmd_custom_error_object");
	const customErrorObject = new CustomErrorObject();

	/**
	 * Get input stage of the Map/Reduce script
	 * List all customers that have item pricing below 15% markup
	 *
	 * @param {import("N/types").EntryPoints.MapReduce.getInputDataContext} context Get input data context
	 * @returns {object} SQL Query Object
	 */
	function getInputData(context) {
		try {
			const currentScript = runtime.getCurrentScript();

			const fileId = currentScript.getParameter({
				name: "custscript_sql_file_id",
			});

			if (!fileId) {
				throw customErrorObject.updateError({
					errorType: customErrorObject.ErrorTypes.MISSING_PARAMETER,
					summary: "MISSING_FILE_ID",
					details: `No file id passed in from MR parameter`,
				});
			}

			const fileObj = file.load({ id: fileId });
			const sqlQuery = fileObj.getContents();

			if (!sqlQuery) {
				throw customErrorObject.updateError({
					errorType: customErrorObject.ErrorTypes.MISSING_PARAMETER,
					summary: "MISSING_SQL_QUERY",
					details: `No query contents from selected file`,
				});
			}

			// Use pagination approach similar to vlmd_sql_query_tool_sl.js
			const query = require('N/query');
			let moreRecords = true;
			let paginatedRowBegin = 1;
			let paginatedRowEnd = 5000;
			let records = [];

			do {
				const paginatedSQL = 'SELECT * FROM ( SELECT ROWNUM AS ROWNUMBER, * FROM (' + 
					sqlQuery + ' ) ) WHERE ( ROWNUMBER BETWEEN ' + paginatedRowBegin + ' AND ' + paginatedRowEnd + ')';
				
				const queryResults = query.runSuiteQL({ 
					query: paginatedSQL 
				}).asMappedResults();
				
				queryResults.forEach(result => {
					// Delete the ROWNUMBER property from each result object
					delete result.rownumber;
					records.push(result);
				});

				if (queryResults.length < 5000) {
					moreRecords = false;
				}
				
				paginatedRowBegin = paginatedRowBegin + 5000;
				paginatedRowEnd = paginatedRowEnd + 5000;
				
			} while (moreRecords);

			// Return all records for processing in the map stage
			return records;
		} catch (/** @type {any} */ err) {
			customErrorObject.throwError({
				summaryText: "GET_INPUT_DATA_ERROR",
				error: err,
			});
		}
	}

	/**
	 * Map stage of the Map/Reduce script
	 * Process each record from the query results
	 *
	 * @param {import("N/types").EntryPoints.MapReduce.mapContext} context Map context
	 * @returns {void}
	 */
	function map(context) {
		try {
			// Each record from the query results is passed to the map function
			const record = JSON.parse(context.value);
			
			// Write the record to the reduce stage with a common key
			context.write({
				key: "Results",
				value: JSON.stringify(record)
			});
		} catch (err) {
			log.error({
				title: 'MAP_ERROR',
				details: err
			});
			throw err;
		}
	}

	/**
	 * Reduce stage of the Map/Reduce script
	 * Groups together the results of the sql query
	 *
	 * @param {import("N/types").EntryPoints.MapReduce.reduceContext} context Reduce context
	 * @returns {void}
	 */
	function reduce(context) {
		const mapErrorObject = new CustomErrorObject();
		try {
			const currentScript = runtime.getCurrentScript();

			const stringColumnNames = currentScript.getParameter({
				name: "custscript_string_titles",
			});
			
			// Process each record passed from the map stage
			context.values.forEach(valueString => {
				const record = JSON.parse(valueString);
				
				// Convert record to array if needed for column mapping
				const valuesArr = Object.values(record);
				
				// If there was a string of column names being passed in, then use that as the column headers
				// Otherwise use the original record
				const objectForSummarize = stringColumnNames
					? Object.fromEntries(
							stringColumnNames
								.split(",")
								.map((key, index) => [key, valuesArr[index]])
					  )
					: record;

				context.write({ key: "Results", value: objectForSummarize });
			});
		} catch (err) {
			mapErrorObject.throwError({
				summaryText: `REDUCE_ERROR`,
				error: err,
			});
		}
	}

	/**
	 * The summarize stage of the Map/Reduce script
	 *    Emails out the results as a CSV file
	 *
	 * @param {import("N/types").EntryPoints.MapReduce.summarizeContext} summary Summarize context
	 * @returns {void}
	 */
	function summarize(context) {
		const currentScript = runtime.getCurrentScript();

		const fileName = currentScript.getParameter({
			name: "custscript_file_name",
		});

		const emailResults = currentScript.getParameter({
			name: "custscript_results_email_address",
		});

		const subject = currentScript.getParameter({
			name: "custscript_results_subject",
		});

		/** @type {import("../Classes/spl_query_email").QueryEmail} */
		const QueryEmail = require("../Classes/spl_query_email");
		const queryEmail = new QueryEmail({});
		try {
			// Modify the formatMrResultsToCSV method to properly handle commas
			// This will be implemented in the QueryEmail class
			queryEmail.formatMrResultsToCSV(
				context,
				fileName
					? fileName.toString()
					: `MRResults  ${new Date().toLocaleDateString()}`
			);
			queryEmail.sendResults({
				recipients: emailResults.toString(),
				subject: subject ?? `Please see MR Query Results: ${new Date().toLocaleDateString()}`,
				onlyIncludeHtml: false,
			});

			/** @type {import("../Classes/vlmd_mr_summary_handling").MapReduceSummaryStageHandling} */
			const StageHandling = require("../Classes/vlmd_mr_summary_handling");

			const stageHandling = new StageHandling(context);
			stageHandling.printErrors();
		}
		catch(e){
			log.debug("ERROR",e);
		}
	}

	return {
		getInputData,
		map,
		reduce,
		summarize,
	};
});
