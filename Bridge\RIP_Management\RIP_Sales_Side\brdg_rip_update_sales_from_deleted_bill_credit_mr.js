/**
 * @description Takes in array of sales transactions that have to be updated because a bill credit was deleted
 *  
 * </br><b>Schedule:</b> Gets called by the brdg_rip_deleted_bill_credit_ue
 *  
 * @NApiVersion 2.1
 * @NScriptType MapReduceScript
 * 
 * <AUTHOR>
 * @module brdg_rip_deleted_bill_credit_mr
 */
define([
  "require",
  "N/log",
  "N/record",
  "N/runtime",
  "../../../Classes/vlmd_custom_error_object",
], (/** @type {any} */ require) => {
  const record = require("N/record");
  const runtime = require("N/runtime");

  /** @type {import("../../../Classes/vlmd_custom_error_object").CustomErrorObject} */

  const CustomErrorObject = require("../../../Classes/vlmd_custom_error_object");
  const customErrorObject = new CustomErrorObject();

  function getInputData(context) {
    try {
      const transactionsToUpdate = JSON.parse(
        runtime.getCurrentScript().getParameter({
          name: "custscript_transactions_to_update",
        })
      );

      if (!transactionsToUpdate || transactionsToUpdate.length < 0) {
        customErrorObject.updateError({
          summaryText: "GET_INPUT_DATA_ERROR",
          error: "No transactions to update found!",
        });
      }

      return transactionsToUpdate;
    } catch (e) {
      customErrorObject.throwError({
        summaryText: "GET_INPUT_DATA_ERROR",
        error: e,
      });
    }
  }

  function map(context) {
    const mapErrorObject = new CustomErrorObject();
    const transactionData = JSON.parse(context.value);

    try {
      if (!transactionData || !transactionData.transactionid || !transactionData.recordtype) {
        mapErrorObject.throwError({
          summaryText: "MAP_ERROR",
          error: "No transaction data found!",
        });
      }

      const recordTypes = {
        SalesOrd: record.Type.SALES_ORDER,
        CashSale: record.Type.CASH_SALE,
      };

      const transactionRecord = record.load({
        type: recordTypes[transactionData.recordtype],
        id: transactionData.transactionid,
        isDynamic: false,
      });

      const lineNumber = transactionRecord.findSublistLineWithValue({
        sublistId: "item",
        fieldId: "item",
        value: transactionData.itemid,
      });

      const fieldsToUpdate = [
        "custcol_item_rip_bill_credit_applied",
        "custcol_item_rip_rate",
        "custcol_item_rip_quantity_used",
        "custcol_total_cog_rip",
      ];

      fieldsToUpdate.forEach((fieldId) => {
        transactionRecord.setSublistValue({
          sublistId: "item",
          fieldId: fieldId,
          line: lineNumber,
          value: "",
        });
      });

      transactionRecord.setSublistValue({
        sublistId: "item",
        fieldId: "custcol_item_rip_applied_sales",
        line: lineNumber,
        value: false,
      });

      try {
        transactionRecord.save({
          enableSourcing: true,
          ignoreMandatoryFields: true,
        });
      } catch (e) {
        log.audit("Error saving record!", e);
      }
      context.write({
        key: "Success",
        value: {
          transactionId: transactionData.transactionid,
          transactionName: transactionData.transactionname,
          recordType: transactionData.recordtype,
        },
      });

    } catch (e) {
      context.write({
        key: "Error",
        value: {
          transactionId: transactionData.transactionId,
          transactionName: transactionData.transactionName,
          error: e.message,
        },
      });
      mapErrorObject.throwError({
        summaryText: `Map Error!`,
        error: e,
        errorWillBeGrouped: true,
      });
    }
  }

  function summarize(context) {
    let successCount = 0;
    let errorCount = 0;
    const successfulTransactions = [];
    const failedTransactions = [];

    context.output.iterator().each(function (key, value) {
      const valueObj = JSON.parse(value);

      if (key === "Success") {
        successCount++;
        successfulTransactions.push({
          name: valueObj.transactionName,
        });
      } else {
        errorCount++;
        failedTransactions.push({
          id: valueObj.transactionId,
          name: valueObj.transactionName,
          error: valueObj.error,
        });
      }
      return true;
    });

    log.audit("Bill Credit Link Removal Summary", {
      totalProcessed: successCount + errorCount,
      successCount: successCount,
      errorCount: errorCount,
      successfulTransactions: successfulTransactions,
      failedTransactions: failedTransactions,
    });

  }

  return {
    getInputData,
    map,
    summarize,
  };
});
