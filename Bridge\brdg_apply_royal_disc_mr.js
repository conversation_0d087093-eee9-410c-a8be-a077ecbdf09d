/**
 * @description Iterates over all BRDG Royal vendor bills within the time frame and applies the 5% discount
 * 	to the rate on the line items if the threshold is met
 *
 * </br><b>Schedule:</b> Runs every morning @ 1:00 AM
 *
 * @NApiVersion 2.1
 * @NScriptType MapReduceScript
 * @NAmdConfig /SuiteScripts/config.json
 *
 * <AUTHOR>
 * @module brdg_apply_royal_disc_mr
 */

define([
  "require",
  "./Libraries/brdg_helper_functions_lib",
  "N/log",
  "N/record",
  "N/format",
  "N/runtime",
], (/** @type {any} */ require) => {
  const bridgeHelperFunctionsLib = require("./Libraries/brdg_helper_functions_lib");
  const log = require("N/log");
  const record = require("N/record");
  const format = require("N/format");
  const runtime = require("N/runtime");

  /**
   * Retrieve the transaction object with the additional indices from the spirits transaction line items
   * Since we run the transaction line query separately for wine and spirit items,
   *   we need to consolidate the indices from the wine and spirit query results
   *
   * @param {import("../Classes/brdg_transaction_object").BridgeTransactionsObject} wineTransactionsObj Object containing transaction line indices for wine
   * @param {import("../Classes/brdg_transaction_object").BridgeTransactionsObject} spiritsTransactionsObj Object containing transaction line indices for spirits
   * @returns {import("../Classes/brdg_transaction_object").BridgeTransactionsObject} Object containing additional indices from spirits
   */
  function getConsolidatedWineAndSpiritTransactionObj(
    wineTransactionsObj,
    spiritsTransactionsObj
  ) {
    const transactionIdArr = [
      ...new Set([
        ...Object.keys(wineTransactionsObj),
        ...Object.keys(spiritsTransactionsObj),
      ]),
    ];

    /*Combine the eligible wine and spirit indices for each transaction 
		be merging the spirits obj into the wine obj (since they get the same 5% discount)*/
    transactionIdArr.forEach((transactionId) => {
      if (
        wineTransactionsObj[transactionId] &&
        spiritsTransactionsObj[transactionId]
      ) {
        wineTransactionsObj[transactionId].indices = [
          ...wineTransactionsObj[transactionId].indices,
          ...spiritsTransactionsObj[transactionId].indices,
        ];
      } else if (
        !wineTransactionsObj[transactionId] &&
        spiritsTransactionsObj[transactionId]
      ) {
        wineTransactionsObj[transactionId] =
          spiritsTransactionsObj[transactionId];
      }
    });

    return wineTransactionsObj;
  }

  /**
   * Get a list all transaction dates of bills created on a given date
   * 	(if no date is provided, pulls from yesterday)
   *
   * @param {import("N/types").EntryPoints.MapReduce.getInputDataContext} context Get input data context
   * @returns {string[] | undefined} Formatted dates array
   */
  function getInputData(context) {
    try {
      const currentScript = runtime.getCurrentScript();

      const createdDateOverride = currentScript.getParameter({
        name: "custscript_brdg_royal_created_override",
      });

      const runOneTransactionDate = currentScript.getParameter({
        name: "custscript_run_specific_transaction_date",
      });

      //If running this from the SL, run it by the date of the transactions, not the created date
      if (runOneTransactionDate) {
        log.audit("Triggered via SL", `Date Chosen: ${runOneTransactionDate}`);

        return [
          format.format({
            value: new Date(runOneTransactionDate),
            type: format.Type["DATE"],
          }),
        ];
      }

      let formattedCreatedDateOverride = createdDateOverride
        ? format.format({
            value: createdDateOverride,
            type: format.Type.DATE,
          })
        : null;

      //Returns an array of date strings ['01/01/2024', '02/12/2024']
      const datesArr =
        bridgeHelperFunctionsLib.getTransactionDatesFromDateCreated(
          formattedCreatedDateOverride
        );

      log.audit("Dates Processed", datesArr);
	  
      return datesArr;
    } catch (/** @type {any} */ err) {
      log.error(err.title, err.message);
    }
  }

  /**
   * For each date, query for all bill lines/items that are eligible for the 5% discount
   *
   * @param {import("N/types").EntryPoints.MapReduce.mapContext} context Map context
   * @returns {void}
   */
  function map(context) {
    try {
      const date = context.value;

      const minimumWineCasesCount = 50;
      const minimumSpiritCasesCount = 15;

      /*Gets an obj with category string props 
				{wineCategories: `(1, 2, 3)`, spiritCategories: `(4,5,6)`}	*/
      const { wineCategories, spiritCategories } =
        bridgeHelperFunctionsLib.getWineAndSpiritCategories();

      //Build a query string to pull all transaction lines and relevant info for transactions for this date
      const vendorBillQueryString =
        bridgeHelperFunctionsLib.getTransactionLineQueryString(
          date,
          "VendBill"
        );

      /* Get an obj for wine and spirits each, composed of vendor bill transaction line indices grouped by transaction
			Sample Data Returned: 
				{
					"casesCount": 136,
					"transactionsObj": {
					"7126318": {
						"recordType": "vendorbill",
						"indices": [
						5,
						8,
						],
						"linkedTransaction": "7114938"
					},
					"7126628": {
						"recordType": "vendorbill",
						"indices": [
						16,
						20,
						],
						"linkedTransaction": "7126526"
					}
				}
			}
			*/
      const wineVendorBillsObj = bridgeHelperFunctionsLib.getTransactionsObj({
        transactionQueryString: vendorBillQueryString,
        transactionType: "VendBill",
        categoryIds: wineCategories,
      });

      const spiritVendorBillsObj = bridgeHelperFunctionsLib.getTransactionsObj({
        transactionQueryString: vendorBillQueryString,
        transactionType: "VendBill",
        categoryIds: spiritCategories,
      });

      //Consolidate into one obj of transasction objs with indices for all wine and spirit line items that meet the threshold.
      const transactionObj = getConsolidatedWineAndSpiritTransactionObj(
        wineVendorBillsObj.casesCount >= minimumWineCasesCount
          ? wineVendorBillsObj.transactionsObj
          : {},
        spiritVendorBillsObj.casesCount >= minimumSpiritCasesCount
          ? spiritVendorBillsObj.transactionsObj
          : {}
      );

      log.audit(`Transction Object`, transactionObj);

      Object.keys(transactionObj).forEach((bill) => {
        context.write({
          key: bill,
          value: transactionObj[bill],
        });
      });
    } catch (/** @type {any} */ err) {
      log.error(err.title, err.message);
    }
  }

  /**
   * Reduce stage of the Map/Reduce script
   * Load transaction to update line item rates with 5% discount
   * Save the original rates to the custom column Rate Before Discount
   *
   * @param {import("N/types").EntryPoints.MapReduce.reduceContext} context Reduce context
   * @returns {void}
   */
  function reduce(context) {
    try {
      const transactionId = context.key;
      const itemSublist = JSON.parse(context.values[0]);
      const { recordType, indices } = itemSublist;

      const transaction = record.load({
        type: recordType,
        id: transactionId,
      });
      const itemSublistLineCount = transaction.getLineCount({
        sublistId: "item",
      });

      let totalDiscount = 0;

      for (let line = 0; line < itemSublistLineCount; line++) {
        if (!indices.includes(line)) {
          continue;
        }

        const quantity = transaction.getSublistValue({
          sublistId: "item",
          fieldId: "quantity",
          line,
        });

        const itemRate = transaction.getSublistValue({
          sublistId: "item",
          fieldId: "rate",
          line,
        });

        const rateBeforeDiscount =
          transaction.getSublistValue({
            sublistId: "item",
            fieldId: "custcol_brdg_rate_before_discount",
            line,
          }) || itemRate;

        transaction.setSublistValue({
          sublistId: "item",
          fieldId: "custcol_brdg_rate_before_discount",
          line,
          value: rateBeforeDiscount,
        });

        transaction.setSublistValue({
          sublistId: "item",
          fieldId: "rate",
          line,
          value: Number(rateBeforeDiscount) * 0.95,
        });

        totalDiscount += Number(quantity) * Number(rateBeforeDiscount) * 0.05;
      }

      if (totalDiscount && totalDiscount > 0) {
        transaction.setValue({
          fieldId: "custbody_brdg_total_discount",
          value: totalDiscount,
        });
      }

      transaction.save();
    } catch (/** @type {any} */ err) {
      log.error(err.title, err.message);
    }
  }

  /**
   * The summarize stage of the Map/Reduce script.
   *
   * @param {import("N/types").EntryPoints.MapReduce.summarizeContext} summary Summarize context
   * @returns {void}
   */
  function summarize(summary) {
    const /** @type {string[]} */ mapKeysArr = [];
    const /** @type {string[]} */ reduceKeysArr = [];

    summary.mapSummary.keys.iterator().each(function (key, value) {
      mapKeysArr.push(key);
      return true;
    });

    summary.reduceSummary.keys.iterator().each(function (key) {
      reduceKeysArr.push(key);
      return true;
    });

    log.audit(`Finished processing ${mapKeysArr.length} dates`, mapKeysArr);
    log.audit(
      `Finished processing ${reduceKeysArr.length} transactions`,
      reduceKeysArr
    );
  }

  return {
    getInputData,
    map,
    reduce,
    summarize,
  };
});
