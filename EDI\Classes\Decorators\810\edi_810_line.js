/**
 * @description Class containing functions to extract 810 column data through headers
 *
 * @NApiVersion 2.1
 * <AUTHOR> <<EMAIL>>
 */

define([
    "exports",
    "require",
    "../../../../Libraries/Misc_Libs/vlmd_add_item_internal_ids_lib",
    "N/log",
], function (
    /** @type {any} */ exports,
    /** @type {any} */ require,
    /** @type {any} */ addInternalIdsLib
) {
    const log = require("N/log");

    class EDI810Line {
        /** @param {{[key:string]: any}} params */
        constructor (params) {
            /** @type {string} */
            this.content = params.content;
            /** @type {string} */
            this.segmentDelimiter = params.segmentDelimiter;
            /** @type {string} */
            this.fieldDelimiter = params.fieldDelimiter;
        }
        
        /**
         * Get segment information based on the header title provided
         *
         * @param {string} segmentTitle Segment title
         * @returns {string} Segment information
         */
        getSegment(segmentTitle) {
            const regExp = new RegExp(
                this.segmentDelimiter + segmentTitle + ".*?" + this.segmentDelimiter
            );
            const segmentRegExpMatch = this.content.match(regExp);

            return segmentRegExpMatch
                ? segmentRegExpMatch[0].split(this.segmentDelimiter).join("")
                : "";
        }

        /**
         * Retrieve transaction control number from the line being parsed
         *
         * @returns {string} Transacstion control number
         */
        getTransactionControlNumber() {
            const regExp = new RegExp("ISA" + ".*?" + this.segmentDelimiter);
            let isaSegmentRegExpMatch = this.content.match(regExp);

            return isaSegmentRegExpMatch
                ? isaSegmentRegExpMatch[0]
                    .replace(this.segmentDelimiter, "")
                    .split(this.fieldDelimiter)[13]
                : "";
        }

        /**
         * Retrieve invoice number from the line being parsed
         *
         * @returns {string} Invoice Number
         */
        getInvoiceNumber() {
            return this.getSegment("BIG").split(this.fieldDelimiter)[2];
        }

        /**
         * Retrieve purchase order number from the line being parsed
         *
         * @returns {string} Purchase Order Number
         */
        getPONumber() {
            return this.getSegment("BIG").split(this.fieldDelimiter)[4];
        }

        /**
         * Retrieve total amount from the line being parsed
         *
         * @returns {number} Total amount
         */
        getTotal() {
            return Number(this.getSegment("TDS").split(this.fieldDelimiter)[1]) / 100;
        }

        /**
         * Retrieve shipping amount from the line being parsed
         *
         * @returns {number} Total amount
         */
        getShippingAmount() {
            return Number(this.getSegment("SAC").split(this.fieldDelimiter)[5]) / 100;
        }

        /**
         * Retrieve tax amount from the line being parsed
         *
         * @returns {number} Tax amount
         */
        getTaxAmount() {
            return Number(this.getSegment("TXI").split(this.fieldDelimiter)[2]);
        }

        /**
         * Retrieve date from the line being parsed
         *
         * @returns {string} Date string
         */
        getDate() {
            return this.getSegment("BIG").split(this.fieldDelimiter)[1];
        }

        /**
         * Retrieve item internal IDs
         *
         * @returns {number[]} Internal IDs
         */
        getItems() {
            const regExp = new RegExp("IT1" + ".*?" + this.segmentDelimiter, "g");
            const itemSegments = this.content.match(regExp);

            /** @type {{[key:string]: string}[]} */
            const itemsArr = [];

            const fieldDelimiter = this.fieldDelimiter;
            const segmentDelimiter = this.segmentDelimiter;
            itemSegments?.forEach(function (itemSegment) {
                const itemFields = itemSegment.split(fieldDelimiter);
                const item = {
                    quantity: itemFields[2],
                    rate: itemFields[4],
                    itemName: ""
                };
                const length = itemFields.length;
                if (length >= 9) {
                    item.itemName = itemFields[9].replace(segmentDelimiter, "");
                } else {
                    item.itemName = itemFields[7].replace(segmentDelimiter, "");
                }

                itemsArr.push(item);
            });

            log.debug("EDI 810 Line (getItems)", JSON.stringify(itemsArr));

            const itemsWithInternalIds = addInternalIdsLib.addItemInternalIds(
                itemsArr,
                [1, 2] //Supplyline subsidiary and Valmar;
            );
            return itemsWithInternalIds;
        }
    }

    exports.EDI810Line = EDI810Line;
});