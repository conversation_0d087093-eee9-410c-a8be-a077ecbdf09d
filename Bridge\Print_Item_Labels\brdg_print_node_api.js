/**
 * @NApiVersion 2.1
 * @NModuleScope Public
 */


define(['N/https','N/encode', 'N/file','N/url', '/.bundle/132118/PRI_AS_Engine'],(https, encode, file, url, asEngine)=>{
    const AS = {
        app:'PrintNode',
        apiKey: 'fCiB-JEmEud8kI-akICxI0HoAuKnLJl-aAIobbtQ_ZM',
    },
    printNodeUrl = 'https://api.printnode.com',
    convertToBase64 = string => encode.convert({
        string, 
        inputEncoding: encode.Encoding.UTF_8,
        outputEncoding: encode.Encoding.BASE_64
    }),
    authHeader = ()=>({Authorization: 'Baslsic '+convertToBase64(asEngine.readAppSettings(AS.app, AS.apiKey))}),
    handleResponse = response => {
        const body = JSON.parse(response.body);
        if(response.code<200 || response.code > 299){
            throw error.create({
                name: body.code,
                message: response.code
            })
        }
        else{
            return body
        }
    },
    get = resource => handleResponse(https.get({
        ur: printNodeUrl +'/'+resource,
        headers: authHeader(),
    })),
    post = (resource, body) => handleResponse(https.post({
        urL: printNodeUrl+'/'+resource,
        headers: authHeader(),
        body: JSON.stringify(body),
    }));
    return{
        createPrintJob: options => {
            const pdf = file.load(options.fileId);
            return{
                jobId: post('printjobs',{
                    printerId: options.printer,
                    contentType: 'pdf_url',
                    content: pdf.url.includes('https')?pdf.url: 'https://'+url.resolveDomain({hostType: url.hostType.APPLICATION})+pdf.url,
                    title: pdf.name, 
                    source: options.source,
                    options: {
                        copies: options.copies
                    }
                })
            };
        },
        printerJobs: options => {
            let results = [];
            if(options.printers?.length)
                results = get('printers/'+options.printers.join(',')+'/printjobs');
            return {results}
        },
        computers: () => ({results: get('computers')}),
        printers: options => ({results: get('printers'+(options.printers?.length?'/'+options.printers.join(','):''))}),
        printJobs: options => ({results: get('printjobs'+(options.jobs?.length?'/'+options.jobs.join(','):''))}),
        whoami: ()=> ({whoami: get('whoami')})
    };
})