/**
 * @description Partner class for TrueCommerce
 *
 * @NApiVersion 2.1
 * <AUTHOR> <<EMAIL>>
 */

define(["exports", "require", "./edi_vendor"], (
  /** @type {any} */ exports,
  /** @type {any} */ require
) => {
  const { EDIVendor } = require("./edi_vendor");

  /**
   * TrueCommerce Vendor Class
   *
   * @class
   * @typedef {import("../../../Interfaces/Models/Partner/edi_partner").EDIVendorInterface} EDIVendorInterface
   * @typedef {import("../../../Interfaces/Models/Partner/edi_partner").EDIPartnerConstructorParams} EDIPartnerConstructorParams
   * @typedef {import("../../../Interfaces/Models/Partner/edi_partner").EDIParsingInformation} EDIParsingInformation
   * @typedef {import("../../../Interfaces/Models/Partner/edi_partner").EDIParsingParams} EDIParsingParams
   * @extends {EDIVendor}
   * @implements {EDIVendorInterface}
   */
  class EDITrueCommerce extends EDIVendor {
    /** @param {EDIPartnerConstructorParams} params Constructor Params*/
    constructor(params) {
      super();
      /** @type {string} */
      this.id = "13331";
      /** @type {string} */
      this.purchasingSoftwareId = "12";
      /** @type {string} */
      this.name = "TrueCommerce";
      /** @type {string} */
      this.prodDirectory = "/edi/prod/vendor/true_commerce/in/810";
      /** @type {string} */
      this.testDirectory = `/edi/test/vendor/true_commerce/${params.direction}/${params.transactionType}`;
      /** @type {string} */
      this.referenceDirectory = `/edi/reference/vendor/true_commerce/${params.direction}/${params.transactionType}`;
      /** @type {EDIParsingParams} */
      this.delimiters = {
        fieldDelimiter: "*",
        segmentDelimiter: "~",
        fileDelimiter: "|",
        ediVersion: "00401",
        receiverQualifier: "12",
        receiverId: "7188525330",
      };
    }
  }

  exports.EDITrueCommerce = EDITrueCommerce;
});
