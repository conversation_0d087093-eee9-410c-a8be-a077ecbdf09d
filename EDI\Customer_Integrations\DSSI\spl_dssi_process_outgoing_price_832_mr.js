/**
 * @NApiVersion 2.1
 * @NScriptType MapReduceScript
 * @NAmdConfig /SuiteScripts/config.json
 */
//@ts-ignore
define(["N/log", "N/task", "GetEdiIntegrationsLib"], function (
	log,
	task,
	getEdiIntegrationsLib
) {
	var dataObj = {
		prodGuidBool: true,
		prodDirectoryBool: true,
		decodeContent: true,
		prodGUID: "70baa2379f804e92a62164fb67168fe1",
		sandboxGUID: "",
		testDirectory: "/users/DSSI/Test/OUT/832",
		transactionType: "Price Catalog",
		purchasingSoftware: "DSSI",
		purchasingSoftwareId: 2,
	};

	function getInputData(context) {
		var customerFoldersArr = getEdiIntegrationsLib.getIntegrationFolders(
			dataObj.purchasingSoftwareId
		);
		return customerFoldersArr.map((customer) => ({
			...customer,
			dataObj,
		})); //loop through the arrary of customers and add dataObj as a property to each customer object.
	}

	function map(context) {
		try {
			var parsedResult = JSON.parse(context.value);
			var { accountNumber, customerName, savedSearchInternalId, dataObj } =
				parsedResult;

			dataObj.prodDirectory = `/users/DSSI/${accountNumber}/OUT/832`;
			dataObj.customerName = customerName;

			var scriptTask = task.create({
				taskType: task.TaskType.MAP_REDUCE,
			});

			scriptTask.scriptId = "customscript_spl_prcs_otgng_832_mr_lib";
			scriptTask.deploymentId = "customdeploy_spl_prcs_otgng_832_mr_lib";

			scriptTask.params = {
				custscript_spl_data_obj: dataObj,
				custscript_spl_saved_search_internal_id: savedSearchInternalId,
				custscript_spl_cstmr_accnt_nmbr: accountNumber,
			};

			var scriptTaskId = scriptTask.submit();
			var taskStatus = task.checkStatus(scriptTaskId);
			if (taskStatus.status === "FAILED") {
				log.error("MR Task Failed");
			}

			context.write(customerName, accountNumber);
		} catch (e) {
			log.error(e);
		}
	}

	function reduce(context) {
		context.write({
			key: context.key,
			value: context.values,
		});
	}

	function summarize(context) {
		var customersCatalogsProcessed = "";
		context.output.iterator().each(function (key, value) {
			customersCatalogsProcessed += `${key} ${value}, `;
			return true;
		});
	}

	return {
		getInputData: getInputData,
		map: map,
		reduce: reduce,
		summarize: summarize,
	};
});
