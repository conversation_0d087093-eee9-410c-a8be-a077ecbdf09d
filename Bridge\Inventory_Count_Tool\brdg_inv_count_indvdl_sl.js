/**
 * @description A webpage for users to enter the UPC code and quantity in store
 *
 * @NApiVersion 2.1
 * @NScriptType Suitelet
 * @NAmdConfig /SuiteScripts/config.json
 *
 * @module brdg_inv_count_indvdl_sl
 */
define(["require", "N/ui/serverWidget", "N/log"], function (require) {
	const log = require("N/log");
	const serverWidget = require("N/ui/serverWidget");

	return {
		onRequest: function (context) {
			if (context.request.method === "GET") {
				const form = serverWidget.createForm({
					title: "Inventory Count Tool",
				});

				let formHtmlObj = form.addField({
					id: "custpage_form_objects",
					label: "HTML - do be Hidden",
					type: serverWidget.FieldType.INLINEHTML,
				});

				var tableHtml = `
              <link rel="stylesheet" href="https://stackpath.bootstrapcdn.com/bootstrap/4.5.2/css/bootstrap.min.css">
              <script src="https://stackpath.bootstrapcdn.com/bootstrap/4.5.2/js/bootstrap.min.js"></script>
              
			  <div id='itemFormDiv' class='.col-xs-6'></div>
              <div id='enterItemDiv' class='.col-xs-6'></div>
              <div id='tableViewDiv' class='.col-xs-6'></div>
              
			  <div id='loadDataDiv' class='.col-xs-6'>
              		<h1><label for="inventoryCountData">Count:</label></h1>
                    <select class="form-control" id="inventoryCountData" style="width: 100%;">
                    </select><br /> 
              		<h1><label for="fldLoadData">Sessions:</label></h1>
                    <select class="form-control" id="fldLoadData" style="width: 100%;">
                    </select><br /> <br />
                   
					<button type="button" class="btn btn-success" id="btnStartNewCount">New Count</button>
                    <button type="button" class="btn btn-primary" id="btnLoadData">Load Count</button>
              </div>`;

				formHtmlObj.defaultValue = tableHtml;

				formHtmlObj.updateDisplayType({
					displayType: serverWidget.FieldDisplayType.INLINE,
				});

				form.clientScriptFileId = "9386209";

				context.response.writePage(form);
			}
		},
	};
});
