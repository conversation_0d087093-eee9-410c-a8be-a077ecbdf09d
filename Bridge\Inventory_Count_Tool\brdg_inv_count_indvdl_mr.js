/**
 * @description Creates a Custom Record Entry for each Items being scanned in the Inventory Count Tool
 *
 * </br><b>Schedule:</b> No
 *
 * @NApiVersion 2.1
 * @NScriptType MapReduceScript
 * @NAmdConfig /SuiteScripts/config.json
 *
 * <AUTHOR>
 * @module brdg_inv_count_indvdl_mr
 */

define([
    'require',
    'N/email',
	'N/file',
    'N/log',
    'N/record',
    'N/runtime'
], function(require) {
    const email = require("N/email");
	const file = require("N/file");
    const log = require("N/log");
    const record = require("N/record");
    const runtime = require("N/runtime");
    function getInputData(context) {
     
            let getArrObj = JSON.parse(runtime.getCurrentScript().getParameter({name:'custscript_brdg_inv_count_indvdl_data'}));
            let newArr = JSON.parse(getArrObj);
            return newArr;
    }

    function reduce(context) {
        
        try { 
        let itemObj = JSON.parse(context.values)
        log.debug('Reduce',JSON.stringify(itemObj))
        let custRecChild = record.create({
            type: 'customrecord_brdg_inv_count_line',
            isDynamic: true
        });
        let newDate = new Date();
        custRecChild.setValue({fieldId:'name',value:itemObj["itemDesc"]});
         custRecChild.setValue({fieldId:'custrecord_inv_count_child_par',value:itemObj["parentRecord"]});
         custRecChild.setValue({fieldId:'custrecord_inv_count_child_date',value:newDate});
         custRecChild.setValue({fieldId:'custrecord_inv_count_child_sub',value:itemObj["subsidiaryId"]});
         custRecChild.setValue({fieldId:'custrecord_inv_count_child_upc',value:itemObj["upcCode"]});
         custRecChild.setValue({fieldId:'custrecord_inv_count_child_desc',value:itemObj["itemDesc"]});
         custRecChild.setValue({fieldId:'custrecord_inv_count_child_item_intern',value:itemObj["itemId"]});
         custRecChild.setValue({fieldId:'custrecord_inv_count_child_qty_store',value:itemObj["quantityEntered"]});
         custRecChild.setValue({fieldId:'custrecord_inv_count_child_uom',value:itemObj["uomId"]});
         custRecChild.setValue({fieldId:'custrecord_inv_count_child_qty_ns',value:itemObj["stockAvailable"]});
         custRecChild.setValue({fieldId:'custrecord_inv_count_child_lpp',value:itemObj["stockPurchPrice"]});
         custRecChild.setValue({fieldId:'custrecord_inv_count_child_item_id',value:itemObj["itemNumber"]});
         custRecChild.setValue({fieldId:'custrecord_inv_count_child_discr',value:itemObj["discrepancy"]});
         custRecChild.setValue({fieldId:'custrecord_inv_count_child_rev_disc',value:itemObj["reverseDiscrepacy"]});       
         
        if(itemObj["itemType"] == 'Kit'){
            custRecChild.setValue({fieldId:'custrecord_inv_count_child_comp',value:itemObj["compOf"]});
        }
       
            let getRecId = custRecChild.save({enableSourcing: false});
        } catch (error) {
            log.debug('REDUCE',`error = ${error}`);
        }
        
       
        
    }
    function formatDate(date) {
        var month = date.getMonth() + 1;
        var day = date.getDate();
        var year = date.getFullYear();
        var hours = date.getHours();
        var minutes = date.getMinutes();
    
        // Add leading zeros if necessary
        month = (month < 10 ? "0" : "") + month;
        day = (day < 10 ? "0" : "") + day;
        hours = (hours < 10 ? "0" : "") + hours;
        minutes = (minutes < 10 ? "0" : "") + minutes;
    
        // Return the formatted date string
        return month + "/" + day + "/" + year + " " + hours + ":" + minutes;
    }
	
	return {
		getInputData: getInputData,
		reduce: reduce
	};

});