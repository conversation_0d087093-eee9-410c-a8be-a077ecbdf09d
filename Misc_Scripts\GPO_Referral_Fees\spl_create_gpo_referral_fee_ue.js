/**
 * @NApiVersion 2.1
 * @NScriptType UserEventScript
 * @NAmdConfig /SuiteScripts/config.json
 */

//@ts-ignore
define(["N/log", "N/runtime", "CreateReferralFeeLib"], function (
	log,
	runtime,
	createReferralFeeLib
) {
	function afterSubmit(context) {
		if (
			context.type == "create" ||
			context.type == "copy"
			//||(context.type == "edit" && runtime.getCurrentUser().id == 3288)
			/*And <PERSON><PERSON><PERSON><PERSON> is the user, uncomment this line when need to add 
			commission for previous invoices.*/
		) {
			const invoice = context.newRecord;
			const invoiceName = invoice.getValue("tranid");

			try {
				var subsidiary = invoice.getValue("subsidiary");

				if (subsidiary != 1) {
					//Supplyline
					return;
				}

				const invoiceObj = {
					invoiceInternalId: invoice.id,
					invoiceName,
					subsidiary,
					taxTotal: invoice.getValue("taxtotal"),
					shippingAmount: invoice.getValue("shippingcost"),
					subtotal: invoice.getValue("subtotal"),
					total: invoice.getValue("total"),
					customer: invoice.getValue("entity"),
					invoiceLevelCustomer: invoice.getValue("entity"),
					invoiceDate: invoice.getValue("trandate"),
				};

				var createReferralObj =
					createReferralFeeLib.createGpoReferral(invoiceObj);

				if (createReferralObj.errorLog.length > 0) {
					log.error(
						"Error Creating GPO Referral Fee",
						createReferralObj.errorLog
					);
				}
			} catch (e) {
				throw `${invoiceName}, internal ID ${invoice.id} - Error: ${e} ${e.stack}`;
			}
		}
	}

	return {
		afterSubmit: afterSubmit,
	};
});
