/**
 * @NApiVersion 2.1
 * @NScriptType MapReduceScript
 * @NAmdConfig /SuiteScripts/config.json
 */

//@ts-ignore
define([
	"N/log",
	"N/error",
	"GetEdiFileContents",
	"GetEdiPartnerValuesLib",
	"ParseIncoming810Lib",
	"ProcessIncoming810Lib",
], function (
	log,
	error,
	getEdiFileContentsLib,
	getEdiPartnerValuesLib,
	parseInvoiceLib,
	processInvLib
) {
	var vendorData = {
		prodGuidBool: true,
		prodDirectoryBool: true,
		prodGUID: "4b3ea1380d654c5e81f21a16242f810c",
		sandboxGUID: "********************************",
		decodeContent: true,
		vendorInternalId: 742,
		integrationStartDate: "1/21/2021 12:00 am",
		prodDirectory: "/users/medlineprod/IN/810",
		testDirectory: "/users/medlinetest/IN/Invoices",
		referenceDirectory: "/EDI Reference Files/Medline/IN/810",
		vendorName: "Medline",
		documentType: "Bill",
		documentTypeId: 3,
		pushEmailToDB: true,
	};

	var partnerValues = getEdiPartnerValuesLib.getMedlineValues();

	function getInputData(context) {
		try {
			var connection = getEdiFileContentsLib.createConnection(vendorData);

			var fileNames = getEdiFileContentsLib.getFileNamesForCurrentIntegration(
				vendorData,
				connection
			);

			if (fileNames.length <= 0) {
				log.debug("No new bills to process.");
			}

			return fileNames;
		} catch (e) {
			log.error("Error Getting File Contents", e);
			throw error.create("Error Getting File Contents", e);
		}
	}

	function map(context) {
		var parsedResult = JSON.parse(context.value);
		var { name } = parsedResult;

		try {
			var connection = getEdiFileContentsLib.createConnection(vendorData);

			var fileContentObj = getEdiFileContentsLib.getContentForFile(
				connection,
				name,
				vendorData
			);
		} catch (e) {
			log.error("Error Getting Contents for File", e);
			context.write(name, e);
		}

		
		try {
			var downloadConnection =
			getEdiFileContentsLib.getDownloadConnection(vendorData);

			var parsedFileObj = parseInvoiceLib.parse810(
				fileContentObj.content,
				partnerValues
			);

			if (parsedFileObj.errorLog.length > 0) {
				throw `Error parsing edi file ${fileContentObj.fileName}: ${parsedFileObj.errorLog}`;
			}

			processInvLib.processInvoice(
				parsedFileObj.invoiceObj,
				fileContentObj.fileName,
				[],
				vendorData,
				downloadConnection,
				vendorData.prodDirectory
			);
		} catch (e) {
			log.error("Error Processing Invoice", e);
			context.write(name, e);
		}
	}

	function reduce(context) {
		context.write({
			key: context.key,
			value: context.values,
		});
	}

	function summarize(context) {
		var errorsProcessingBills = "";

		context.output.iterator().each(function (key, value) {
			errorsProcessingBills += `${key} ${value}, `;
			return true;
		});

		if (errorsProcessingBills) {
			throw error.create({
				name: "Errors Processing Medline Bills",
				message: errorsProcessingBills,
			});
		}
	}

	return {
		getInputData: getInputData,
		map: map,
		reduce: reduce,
		summarize: summarize,
	};
});
