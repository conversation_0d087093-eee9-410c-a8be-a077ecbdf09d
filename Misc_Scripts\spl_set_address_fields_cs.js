/**
 * @NApiVersion 2.1
 * @NScriptType ClientScript
 * @NAmdConfig /SuiteScripts/config.json
 */

/*Deployed On: Sales Orders and Item Fulfillments
Function: checks for customer's particular address info and sets relevant fields */

//@ts-ignore
define(["N/runtime",  "GetAddressObjLib", "N/log"], function (
	runtime,
	getAddressObjLib,
	log
) {
	function setAddressInformation(transaction, shippingAddressId) {
		try {
			if(!shippingAddressId){
				return
			}
		
			let addressObj = getAddressObjLib.getDeliveryFields(shippingAddressId);
      
			if (addressObj) {
				let liftgateRequired =
					addressObj.custrecord_adr_liftgate_required == 1 ? true : false;
				//1 is the value of the "yes" (that liftgate is required) in the custom list
				let deliveryInstructions =
					addressObj.custrecord_adr_delivery_instructions ?? "";
				let deliveryContact = addressObj.custrecord_adr_delivery_contact ?? "";
				transaction.setValue(
					"custbody_spl_address_liftgate_required",
					liftgateRequired
				);
				transaction.setValue(
					"custbody_spl_delivery_instructions",
					deliveryInstructions
				);
				transaction.setValue(
					"custbody_spl_address_delivery_contact",
					deliveryContact
				);
			}
		} catch (e) {
			log.error("Error updating address information!", getErrorMessageText(e));
		}
	}

	function getErrorMessageText(e) {
		return `User: ${runtime.getCurrentUser().name}
Role: ${runtime.getCurrentUser().role}
Execution Context: ${runtime.executionContext}
Error: ${e}`;
	}

	function pageInit(context) {
		try {
			let transaction = context.currentRecord;
			let pageMode = context.mode;

			if (pageMode == "edit") {
				//We can't get the ID on create, bec. ship address is not set yet
				let shippingAddressId = transaction.getValue("shippingaddress_key");
				if (shippingAddressId) {
					setAddressInformation(transaction, shippingAddressId);
				}
			}
		} catch (e) {
			log.error("Error updating address information", e);
		}
	}

	function fieldChanged(context) {
		//This will get triggered when the customer field is edited or when address is changed/updated
		if (context.fieldId == "shipaddress") {
			const transaction = context.currentRecord;
			const transactionType = transaction.type;

			if (transactionType == "itemfulfillment") {
				return;
			}

			let shippingAddressId = transaction.getValue("shippingaddress_key");
			if (shippingAddressId) {
				setAddressInformation(transaction, shippingAddressId);
			}
		}
	}

	function saveRecord(context) {
		let transaction = context.currentRecord;
		let shippingAddressId = transaction.getValue("shippingaddress_key");
		setAddressInformation(transaction, shippingAddressId);

		return true;
	}

	return {
		pageInit: pageInit,
		fieldChanged: fieldChanged,
		saveRecord: saveRecord,
	};
});
