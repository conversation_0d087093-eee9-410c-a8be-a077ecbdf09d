/**
 * @NApiVersion 2.1
 * @NScriptType scheduledscript
 * @NAmdConfig /SuiteScripts/config.json
 */

//@ts-ignore
define([
	"N/log",
	"GetEdiFileContents",
	"GetEdiPartnerValuesLib",
	"ParseIncoming855Lib",
	"ProcessIncoming855Lib",
], function (
	log,
	getEdiFileContentsLib,
	getEdiPartnerValuesLib,
	parsePoAcknowledgmentLib,
	processPOAcknowledgmentLib
) {
	function execute(context) {
		var vendorData = {
			prodGuidBool: true,
			prodDirectoryBool: true,
			decodeContent: true,
			prodGUID: "6d477fb6fc1442c0b154eca838248091",
			sandboxGUID: "",
			vendorInternalId: 742,
			testDirectory: "/users/medlinetest/IN/Purchase_Order_Acknowledgments",
			prodDirectory: "/users/medlineprod/IN/855",
			referenceDirectory: "/EDI Reference Files/Medline/IN/855",
			vendorName: "Medline",
			documentType: "Purchase Order Acknowledgment",
			documentTypeId: 2,
			pushEmailToDB: true,
		};

		var fileContents =
			getEdiFileContentsLib.getEdiFileContents(vendorData).fileContents;
		if (fileContents.length > 0) {
			var partnerValues = getEdiPartnerValuesLib.getMedlineValues();
			fileContents.forEach((ediFile) => {
				var parsedPoAckObj = parsePoAcknowledgmentLib.parse855(
					ediFile.content,
					partnerValues
				);

				if (parsedPoAckObj.errorLog.length <= 0) {
					processPOAcknowledgmentLib.processPoAck(
						vendorData,
						parsedPoAckObj.poAckObj,
						ediFile.fileName
					);
				}
			});
		} else {
			log.debug("Script ran, no results found");
		}
	}

	return {
		execute: execute,
	};
});
