/**
 * @NApiVersion 2.1
 * @NScriptType UserEventScript
 * @NAmdConfig /SuiteScripts/config.json
 */
//@ts-ignore
define(["N/search","N/ui/message", "N/log"], function (search, message, log) {
    function showAlert(messageText) {
        var options = {
            title: 'Alert',
            message: messageText,
            type: 'warning'
        };
        var alert = message.create(options);
        alert.show();
    }

    function showMessage(messageText) {
        const resultsMessage = message.create({
            title:'Alert',
            type: 'warning',
            message: messageText,
        });

        resultsMessage.show({ duration: 3000 });
    }
	function afterSubmit(context) {
		try {
			log.debug("afterSubmit");
			var currentRecord = context.newRecord;
			var billId = currentRecord.id;
			var total = currentRecord.getValue("total");
			log.debug("afterSubmit total", total);
			var fileResultsArr = search.lookupFields({
				type: search.Type.TRANSACTION,
				id: billId,
				columns: "file.internalid",
			})["file.internalid"];
			var fileId;
			log.debug("File results arr", fileResultsArr);
			if (fileResultsArr.length > 0) {
				fileId = fileResultsArr[0].value;
			}

			if (total >= 500 && !fileId) {
                showMessage("You must attach a document to save this record.");
				// alert("You must attach a document to save this record.");
				return false;
			} else {
				log.debug("IN THE ELSE");
				try{showMessage("TESTING")}catch(e){log.debug("error ",e)};
			}
		} catch (e) {
			log.error("Error with Valmar attachments!", e);
		}
		return true;
	}
	return {
		afterSubmit: afterSubmit,
	};
});
