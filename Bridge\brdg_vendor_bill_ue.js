/**
 * @description Validate that the sales price for the items are above the purchase cost
 *
 * </br><b>Deployed On:</b> BRDG Vendor Bills
 * </br><b>Execution Context:</b> ALL
 * </br><b>Event Type/Mode:</b> ALL
 * </br><b>Entry Points:</b> beforeLoad
 *
 * @NApiVersion 2.1
 * @NScriptType UserEventScript
 * @NAmdConfig /SuiteScripts/config.json
 *
 * <AUTHOR>
 * @module brdg_vendor_bill_ue
 */

define(["require", "BridgeHelperFunctionsLib", "N/error", "N/log"], (
  /** @type {any} */ require,
  /** @type {import("./Libraries/brdg_helper_functions_lib")} */ bridgeHelperFunctionsLib
) => {
  const error = require("N/error");
  const log = require("N/log");

  return {
    /**
     * Ensure that sales price is greater than the purchase cost
     *
     * @param {import("N/types").EntryPoints.UserEvent.beforeSubmitContext} context Before submit script context
     * @returns {void}
     */
    beforeSubmit: (context) => {
      try {
        const { newRecord } = context;
        const subsidiariesArr = newRecord.getValue({
          fieldId: "subsidiary",
        });
        const bridgeSubsidiaries =
          bridgeHelperFunctionsLib.getBridgeSubsidiaries();
        const isBridgeSubsidiary = bridgeHelperFunctionsLib.isBridgeSubsidiary(
          subsidiariesArr,
          bridgeSubsidiaries
        );

        if (!isBridgeSubsidiary) {
          return;
        }

        const transactionNumber = newRecord.getValue({
          fieldId: "transactionnumber",
        });

        const transactionLocation = newRecord.getValue({
          fieldId: "location",
        });

        if (!transactionLocation) {
          throw error.create({
            name: "MISSING_LOCATION",
            message: `No location for this transaction. (newRecord: ${JSON.stringify(
              newRecord
            )})`,
          });
        }

        const itemsLineCount = newRecord.getLineCount({
          sublistId: "item",
        });

        const lineErrorsArr = [];

        for (let line = 0; line < itemsLineCount; line++) {
          const itemId = newRecord.getSublistValue({
            sublistId: "item",
            fieldId: "item",
            line,
          });

          const itemDisplay = newRecord.getSublistValue({
            sublistId: "item",
            fieldId: "item_display",
            line,
          });
          
          const rate = newRecord.getSublistValue({
            sublistId: "item",
            fieldId: "rate",
            line,
          });

          if (!rate) {
            lineErrorsArr.push(
              `No cost for item ${itemDisplay} in line ${line + 1}`
            );
            continue;
          }

          const location =
            newRecord.getSublistValue({
              sublistId: "item",
              fieldId: "location",
              line,
            }) || transactionLocation;

          if (!location) {
            lineErrorsArr.push(
              `No location for item ${itemDisplay} in line ${
                line + 1
              }. (Bill: ${transactionNumber})`
            );
            continue;
          }

          const priceLevelId = bridgeHelperFunctionsLib.getValueByRelatedValue(
            "location",
            location,
            "priceLevel"
          );

          if (!priceLevelId) {
            lineErrorsArr.push(
              `No price level returned for item ${itemDisplay} in line ${
                line + 1
              }. (Bill: ${transactionNumber})`
            );
            continue;
          }

          const billPurchaseUnitRate = newRecord.getSublistValue({
            sublistId: "item",
            fieldId: "unitconversionrate",
            line,
          });

          const salesPrice = bridgeHelperFunctionsLib.getSalesPriceForLocation(
            itemId,
            priceLevelId,
            billPurchaseUnitRate
          );

          if (!salesPrice) {
            lineErrorsArr.push(
              `No sales price returned for item ${itemDisplay} in line ${
                line + 1
              }. (Bill: ${transactionNumber})`
            );
            continue;
          }

          if (Number(salesPrice) <= Number(rate)) {
            lineErrorsArr.push(
              `The sales price of $${salesPrice} for item ${itemDisplay} in line ${
                line + 1
              } is below the cost. (Bill: ${transactionNumber})`
            );
            continue;
          }
        }

        if (lineErrorsArr.length > 0) {
          throw error.create({
            name: "ITEM_ERROR",
            message: lineErrorsArr.join(", "),
          });
        }
      } catch (/** @type {any} */ err) {
        log.error(err.name, err.message);
      }
    },
  };
});
