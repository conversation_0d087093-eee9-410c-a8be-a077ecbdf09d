/**
 * @description Summary Class for Decorators
 *
 * @NApiVersion 2.1
 * <AUTHOR> <<EMAIL>>
 */

define([
    "exports",
    "require",
    "N/log",
    "N/record",
    "../../../Classes/vlmd_custom_error_object",
], function (/** @type {any} */ exports, /** @type {any} */ require,) {
    const log = require("N/log");
    const record = require("N/record");
    /** @type {import("../../../Classes/vlmd_custom_error_object").CustomErrorObject} */
    const CustomErrorObject = require("../../../Classes/vlmd_custom_error_object");

    /**
     * Summary Class
     * 
     * @typedef {import("../Interfaces/Models/File/edi_file").EDIPostProcessEmail} EDIPostProcessEmail
     * @typedef {import("../Interfaces/Models/Partner/edi_partner").EDIPartnerInterface} EDIPartnerInterface
     * @class
     */
    class EDISummary {
        /** @param {{[key:string]: any}} params */
        constructor (params) {
            /** @type {EDIPostProcessEmail} */
            this.data = {
                subject: "",
                body: "",
                recipients: [],
                cc: [],
                logTitle: "",
                processingStatus: -1,
                errorText: "",
                documentControlNumbers: [],
            };
            /** @type {string[]} */
            this.errors = params.errors;
            /** @type {string} */
            this.documentType = params.documentType;
            /** @type {string} */
            this.filename = params.filename;
            /** @type {string} */
            this.transactionType = params.transactionType;
            /** @type {number} */
            this.transactionId = params.transactionId;
            /** @type {string} */
            this.transactionNumber = params.transactionNumber;
            /** @type {string} */
            this.controlNumber = params.controlNumber;
            /** @type {string} */
            this.referenceNumber = params.referenceNumber;
            /** @type {EDIPartnerInterface} */
            this.partner = params.partner;
        }

        /**
         * Create logs for successfully processed files
         *
         * @returns {void}
         */
        processCreatedSuccessfully() {
            this.data.subject = `Success: Please Approve - ${this.partner.name} ${this.transactionType} ${this.referenceNumber} Created for ${this.transactionNumber}`;
            this.data.body = `EDI file processed successfully and created ${this.transactionType} ${this.referenceNumber}.
                Please review and approve the order.
                EDI File Name: ${this.filename}`;
            this.data.logTitle = "Created Successfully";
            this.data.processingStatus = 1; //Processed With No Errors
            log.audit({
                title: "EDI Summary (processCreatedSuccessfully)",
                details: JSON.stringify(this.data)
            });
        }

        /**
         * Create logs for partially processed files
         *
         * @returns {void}
         */
        processCreatedWithErrors() {
            this.data.subject = `Errors: Please Review and Correct - ${this.partner.name} ${this.transactionType} ${this.referenceNumber} Created for ${this.transactionNumber}`;
            this.data.body = `EDI file processed successfully and created ${this.transactionType} ${this.referenceNumber}.
                Please review the errors below and correct.
                
                ${this.data.errorText}

                EDI File Name: ${this.filename}`;
            this.data.logTitle = "Created with Errors";
            this.data.processingStatus = 2; //Processed With Errors
            log.audit({
                title: "EDI Summary (processCreatedWithErrors)",
                details: JSON.stringify(this.data)
            });
        }

        /**
         * Create logs for failed processing
         *
         * @returns {void}
         */
        processFailedData() {
            this.data.subject = `${this.partner.name} ${this.transactionType} Not Created for ${this.transactionNumber}`;
            this.data.body = `Transaction ${this.controlNumber} failed.
                EDI # ${this.filename}
                Please investigate the errors below.
                
                ${this.data.errorText}
            
                EDI File Name: ${this.filename}`;
            this.data.recipients = ["<EMAIL>"];
            this.data.cc = ["<EMAIL>"];
            this.data.logTitle = `${this.transactionType} Not Created`;
            this.data.processingStatus = 4; //Document Not Created
            log.audit({
                title: "EDI Summary (processFailedData)",
                details: JSON.stringify(this.data)
            });
        }

        /**
         * Create the object to be saved in the EDI Transaction record
         *
         * @returns {void}
         */
        createPostProcessEmail() {
            log.debug({
                title: "EDI Summary (createPostProcessEmail)",
                details: `Creating post-processing email object for ${this.transactionType} #${this.transactionId}`
            });
            this.data.documentControlNumbers = [];
            this.data.errorText = this.errors.reduce((/** @type {string} */ summary, /** @type {string} */ error) => {
                summary += "\n" + error + "\n";

                return summary;
            }, "");
    
            if (this.transactionId) {
                if (this.errors.length <= 0) {
                    this.processCreatedSuccessfully();
                } else {
                    this.processCreatedWithErrors();
                }
            } else {
                this.processFailedData();
            }

            log.debug({
                title: this.data.logTitle,
                details: this.data.errorText ?? "No errors.",
            });
        }

        /**
         * Summarize the processed transactions and errors produced in the map function
         *
         * @param {import("N/types").EntryPoints.MapReduce.summarizeContext} context Summarize context
         * @returns {EDISummary} This instance
         */
        summarizeProcess(context) {
            const /** @type {string[]} */ processedTransactions = [];
            context.output.iterator().each((tranid, controlNumberObj) => {
                processedTransactions.push(tranid.toString());
                this.data.documentControlNumbers.push(JSON.parse(controlNumberObj).id);
                return true;
            });

            const /** @type {string[]} */ errorMessages = [];
            context.mapSummary.errors.iterator().each((key, error) => {
                errorMessages.push(JSON.parse(error).message);
                return true;
            });

            this.data.subject = (() => {
                if (errorMessages.length > 0 && processedTransactions.length > 0) {
                    return `EDI ${this.transactionType}: Processed with Errors`;
                } else if (errorMessages.length > 0) {
                    return `Failure: EDI ${this.transactionType} Notice Results`;
                } else if (processedTransactions.length === 0) {
                    return `EDI ${this.transactionType}: No transactions Processed`;
                } else {
                    return `Success: EDI ${this.transactionType} Notice Results`;
                }
            })();
            this.data.body = `Processed Transactions: ${processedTransactions.length > 0 ? `\n${processedTransactions.join("\n")}` : "None"}
                ${errorMessages.length > 0
                    ? `Errors:\n${errorMessages.join("\n")}\n`
                    : ""
                }
                Usage Consumed: ${context.usage}
                Concurrency Number: ${context.concurrency}
                Number of Yields: ${context.yields}`;
            this.data.processingStatus = (() => {
                if (errorMessages.length > 0 && processedTransactions.length > 0) {
                    return 2; // Processed With Errors
                } else if (errorMessages.length > 0) {
                    return 4; // Document Not Created
                } else if (processedTransactions.length === 0) {
                    return 5; // No Transaction to Process
                } else {
                    return 1; // Processed With No Errors
                }
            })();
            this.data.recipients = ["<EMAIL>"];
            this.data.cc = ["<EMAIL>"];
            log.audit({
                title: "EDI Summary (summarizeProcess)",
                details: JSON.stringify(this.data)
            });

            return this;
        }

        /**
         * Save email body to EDI Transaction record
         *
         * @returns {number | undefined} EDI Transaction record internal ID
         */
        pushEdiEmailInfoToDB() {
            try {
                const customErrorObject = new CustomErrorObject();

                const ediTransactionRecord = record.create({
                    type: "customrecord_spl_edi_transaction",
                });

                ediTransactionRecord.setValue({
                    fieldId: "custrecord_spl_edi_document_type",
                    value: this.documentType,
                });

                ediTransactionRecord.setValue({
                    fieldId: "custrecord_spl_processing_status",
                    value: this.data.processingStatus,
                });

                ediTransactionRecord.setValue({
                    fieldId: "custrecord_spl_edi_email_subject",
                    value: this.data.subject,
                });

                ediTransactionRecord.setValue({
                    fieldId: "custrecord_spl_edi_email_message",
                    value: this.data.body,
                });

                ediTransactionRecord.setValue({
                    fieldId: "custrecord_spl_edi_email_recipient",
                    value: this.data.recipients[0],
                });

                if (this.data.cc.length > 0) {
                    ediTransactionRecord.setValue({
                        fieldId: "custrecord_spl_edi_email_cc_recipient",
                        value: this.data.cc[0],
                    });
                }

                this.controlNumber && ediTransactionRecord.setValue({
                    fieldId: "custrecord_spl_edi_trnsctn_cntrl_nmbr",
                    value: this.controlNumber,
                });

                this.transactionNumber && ediTransactionRecord.setValue({
                    fieldId: "custrecord_spl_edi_po_ref_nmbr",
                    value: this.transactionNumber,
                });

                ediTransactionRecord.setValue({
                    fieldId: "custrecord_spl_edi_purchasing_software",
                    value: this.partner.purchasingSoftwareId,
                });

                ediTransactionRecord.setValue({
                    fieldId: "custrecord_spl_edi_vendor",
                    value: this.partner.id,
                });

                const ediTransactionRecordId = ediTransactionRecord.save();

                if (!ediTransactionRecordId) {
                    throw customErrorObject.updateError({
                        errorType: customErrorObject.ErrorTypes.RECORD_NOT_CREATED,
                        summary: "EDI_TRANSACTION_ERROR",
                        details: "EDI transaction not saved to database.",
                    });
                }

                //Set EDI transaction record id on the document control number record
                if (this.data.documentControlNumbers && this.data.documentControlNumbers.length > 0) {
                    this.data.documentControlNumbers.forEach((documentControlNumberId) => {
                        if (documentControlNumberId && parseInt(documentControlNumberId)) {
                            //Load customrecord_edi_dcn_doc_ctrl_num record and set value of ediTransaction record to link them.
                            const documentCtrlNumberRecObj = record.load({
                                type: "customrecord_edi_dcn_doc_ctrl_num",
                                id: documentControlNumberId,
                            });

                            if (!documentCtrlNumberRecObj) {
                                throw customErrorObject.updateError({
                                    errorType: customErrorObject.ErrorTypes.RECORD_NOT_LOADED,
                                    summary: "DOC_CONTROL_NUMBER_ERROR",
                                    details: `documentCtrlNumberRecObj not gotten for ${documentControlNumberId}`,
                                });
                            }

                            documentCtrlNumberRecObj.setValue({
                                fieldId: "custrecord_edi_dcn_edi_tran_rec",
                                value: ediTransactionRecordId,
                            });

                            documentCtrlNumberRecObj.save();
                        }
                    });
                }
                log.debug({
                    title: "EDI Summary (pushEdiEmailInfoToDB)",
                    details: JSON.stringify({ediTransactionRecordId})
                });

                return ediTransactionRecordId;
            } catch (e) {
                log.error({
                    title: "Error Pushing EDI Transaction to DB",
                    details: `Transaction Title: ${this.data.subject ?? ""} Error: ${e}`
                });
            }
        }
    }

    exports.EDISummary = EDISummary;
});

