/**
 * @description Triggers the clear RIPs/apply RIPs scripts
 * Called by the brdg_rip_redirect_to_mr script.
 *
 * @NApiVersion 2.1
 * @NScriptType Suitelet
 *
 * <AUTHOR>
 * @module brdg_rip_regenerate_sl
 */

define([
  "require",
  "N/log",
  "N/task",
  "N/ui/serverWidget",
  "../../../../Classes/vlmd_custom_error_object",
], function (require) {
  const log = require("N/log");
  const task = require("N/task");
  const serverWidget = require("N/ui/serverWidget");

  /**@type {import ("../../../../Classes/vlmd_custom_error_object").CustomErrorObject}} */
  const CustomErrorObject = require("../../../../Classes/vlmd_custom_error_object");

  function redirectToSuitelet(billCreditId, context) {
    const form = serverWidget.createForm({
      title: " ",
    });

    form.addField({
      id: "custpage_message",
      type: serverWidget.FieldType.INLINEHTML,
      label: "Message",
    }).defaultValue = `
        <script>
            alert('Map/Reduce script has been initiated. The process will run in the background.');
            window.location.href = '/app/accounting/transactions/vendcred.nl?id=${billCreditId}';
        </script>
    `;

    context.response.writePage(form);
  }

  var exports = {};

  function onRequest(context) {
    const customErrorObject = new CustomErrorObject();
    try {
      if (context.request.method === "GET") {
        const billCreditId = context.request.parameters["billcreditId"];
        const functionName = context.request.parameters["functionName"];

        if (functionName == "updateRips") {
          let mrTask = task.create({
            taskType: task.TaskType.MAP_REDUCE,
            scriptId: "customscript_brdg_rip_clear_applied_sale",
            deploymentId: "customdeploy_brdg_rip_clear_spc_sales_mr",
            params: {
              custscript_specific_bill_credit: billCreditId,
              custscript_triggered_from_btn: true,
            },
          });

          const taskId = mrTask.submit();

          redirectToSuitelet(billCreditId, context);
        }

        if (functionName == "applyRips") {
          let mrTask = task.create({
            taskType: task.TaskType.MAP_REDUCE,
            scriptId: "customscript_brdg_rip_apply_sales",
            deploymentId: "customdeploy_brdg_rip_apply_sales",
            params: {
              custscript_apply_specific_bill_credit: billCreditId,
            },
          });

          const taskId = mrTask.submit();

        }
        redirectToSuitelet(billCreditId, context);
      }
    } catch (e) {
      customErrorObject.throwError({
        summaryText: "ERROR_CALLING_SUITELET",
        error: e,
      });
    }
  }
  exports.onRequest = onRequest;
  return exports;
});
