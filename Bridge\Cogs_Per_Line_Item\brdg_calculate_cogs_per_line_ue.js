/**
 * @description Displays a button to calculate the COGS per line item and sets the value on the custom sublist field value
 *
 * </br><b>Deployed On:</b> BRDG Cash Sales and Invoices - Testing Status
 * </br><b>Excecution Context:</b> ALL
 * </br><b>Event Type/Mode:</b> VIEW
 * </br><b>Entry Points:</b> beforeLoad
 *
 * @NApiVersion 2.1
 * @NScriptType UserEventScript
 * @NAmdConfig /SuiteScripts/config.json
 *
 * <AUTHOR>
 * @module brdg_calculate_cogs_per_line_ue
 */

//@ts-ignore
define(["require", "BridgeHelperFunctionsLib", "N/log"], (
	require,
	bridgeHelperFunctionsLib
) => {
	return {
		/**
		 *
		 * @param {import ("N/types").EntryPoints.UserEvent.beforeLoad} context
		 * @returns {void}
		 */
		beforeLoad: (context) => {
			const log = require("N/log");

			try {
				if (context.type === context.UserEventType.VIEW) {
					const subsidiariesArr = context.newRecord.getValue({
						fieldId: "subsidiary"
					});
					const bridgeSubsidiaries = bridgeHelperFunctionsLib.getBridgeSubsidiaries();
					const isBridgeSubsidiary = bridgeHelperFunctionsLib.isBridgeSubsidiary(subsidiariesArr, bridgeSubsidiaries);
		
					if (!isBridgeSubsidiary) {
						return;
					}

					const recordId = context.newRecord.id;
					const recordType = context.newRecord.type;

					var currentForm = context.form;
					currentForm.clientScriptFileId = 5618190;

					currentForm.addButton({
						id: "custpage_my_button",
						label: "Calculate COGS Per Item",
						functionName:
							'calculateCogsPerItemOnButtonClick("' +
							recordId +
							'","' +
							recordType +
							'")',
					});
				}
			} catch (e) {
				log.error("Error Creating 'Calculate COGS Per Item' Button", e);
			}
		},
	};
});
