/**
 * @description Takes the bill credit obj and finds and then applies the bill credit to the sales
 *
 * </br><b>Schedule:</b> Gets called by the brdg_rip_apply_sales mr
 *
 * @NApiVersion 2.1
 * @NScriptType MapReduceScript
 * @NAmdConfig /SuiteScripts/config.json
 *
 * <AUTHOR>
 * @module brdg_rip_apply_sales_2_mr
 */

define([
  "require",
  "N/record",
  "N/runtime",
  "N/email",
  "N/query",
  "../../../Classes/vlmd_mr_summary_handling",
  "../../../Classes/vlmd_custom_error_object",
], (/** @type {any} */ require) => {
  const record = require("N/record");
  const runtime = require("N/runtime");
  const email = require("N/email");
  const query = require("N/query");

  /** @type {import("../../../Classes/vlmd_custom_error_object").CustomErrorObject} */

  const CustomErrorObject = require("../../../Classes/vlmd_custom_error_object");
  const customErrorObject = new CustomErrorObject();

  function getInputData(context) {
    try {
      const billCreditObj = JSON.parse(
        runtime.getCurrentScript().getParameter({
          name: "custscript_bill_credit_data",
        })
      );

        if (!billCreditObj) {
        customErrorObject.updateError({
          errorType: customErrorObject.ErrorTypes.MISSING_PARAMETER,
          summary: "MISSING_INFO",
          details: `Missing bill credit info.`,
        });
      }

      const sqlQuery = /*sql */ `
      SELECT
      (ABS(quantity) * saleUnit.conversionrate) quantity,
         item,
         t.type,
         t.id transactionid 
      FROM
         transactionline tl 
         JOIN
            transaction t 
            ON tl.transaction = t.id 
            AND accountinglinetype = 'INCOME' 
         JOIN
            unitstypeuom saleUnit 
            ON tl.units = saleUnit.internalid 
      WHERE
         item = ${billCreditObj.itemId} 
         AND t.type IN 
         (
            'SalesOrd',
            'CashSale' 
         )
         --AND ((custcol_item_rip_quantity_used <>    (ABS(quantity) * saleUnit.conversionrate)) OR custcol_item_rip_quantity_used IS NULL )
         AND 
         (
            custcol_item_rip_bill_credit_applied is null
         )
         AND trandate > '${billCreditObj.transactionDate}' 
         AND tl.subsidiary = ${billCreditObj.subsidiary} 
      ORDER BY
         trandate`;

      const salesLinesObjsArr = query
        .runSuiteQL({
          query: sqlQuery,
          params: [],
        })
        .asMappedResults();

      if (salesLinesObjsArr.length <= 0) {
        log.audit(
          `There are no transactions found for this item: ${billCreditObj.itemId} from this bill credit: ${billCreditObj.transactionId}`
        );
      }

      let counter = 0;
      const salesLinesObjsArrToBeApplied = [];

      while (
        counter < billCreditObj.quantityRemaining &&
        salesLinesObjsArr.length > 0
      ) {
        const currentSalesLineObj = salesLinesObjsArr.shift();

        const quantityAvailable = Math.min(
          currentSalesLineObj.quantity,
          billCreditObj.quantityRemaining - counter
        );

        currentSalesLineObj.quantityToBeApplied = quantityAvailable;

        counter += quantityAvailable;
        salesLinesObjsArrToBeApplied.push(currentSalesLineObj);
      }

      const saleLinesObjsArr = {
        transactionLinesArr: salesLinesObjsArrToBeApplied,
        totalQuantityFound: counter,
      };

      billCreditObj.totalQuantityFound = counter;
      return saleLinesObjsArr.transactionLinesArr.map((line) => ({
        billCreditLineObj: billCreditObj,
        salesLine: line,
      }));
    } catch (e) {
      customErrorObject.throwError({
        summaryText: `GET_INPUT_DATA_ERROR`,
        error: e,
      });
    }
  }

//   Sample Data:
//[{
//     "billCreditLineObj": {
//       "itemId": 19883,
//       "quantityRemaining": 144,
//       "rate": 0.****************,
//       "transactionDate": "2/7/2024",
//       "transactionId": 8136556,
//       "originalQuantity": 144,
//       "subsidiary": 24,
//       "itemType": "InvtPart",
//       "totalQuantityFound": 2
//     },
//     "salesLine": {
//       "quantity": 1,
//       "item": 19883,
//       "type": "CashSale",
//       "transactionid": 9139634,
//       "quantityToBeApplied": 1
//     }
//   }
// ]

  function map(context) {
    const mapErrorObject = new CustomErrorObject();
    const { billCreditLineObj, salesLine } = JSON.parse(context.value);

    try {
      const recordTypes = {
        SalesOrd: record.Type.SALES_ORDER,
        CashSale: record.Type.CASH_SALE,
      };

      const salesRecord = record.load({
        type: recordTypes[salesLine.type],
        id: salesLine.transactionid,
        isDynamic: false,
      });

      let lineNumber = -1;
      const totalLines = salesRecord.getLineCount({
        sublistId: "item",
      });

      for (let i = 0; i < totalLines; i++) {
        const currentItem = salesRecord.getSublistValue({
          sublistId: "item",
          fieldId: "item",
          line: i,
        });

        if (Number(currentItem) === salesLine.item) {
          // Check if this line already has the RIP bill credit field filled
          const existingCredit = salesRecord.getSublistValue({
            sublistId: "item",
            fieldId: "custcol_item_rip_bill_credit_applied",
            line: i,
          });

          if (!existingCredit) {
            lineNumber = i;
            break;
          }
        }
      }
      if (lineNumber === -1) {
        // Handle the case where no eligible line was found - shouldn't really happen, it's just in case
        throw new Error(
          "No eligible line found for item " +
            salesLine.item +
            " on this sales order " +
            salesLine.transactionid
        );
      }

      const cogOnOrder = salesRecord.getSublistValue({
        sublistId: "item",
        fieldId: "custcol_actual_item_cogs",
        line: lineNumber,
      });

      if (!cogOnOrder) {
        context.write({
          key: "COGError",
          value: JSON.stringify({
            salesId: salesLine.transactionid,
            item: salesLine.item,
            billCreditId: billCreditLineObj.transactionId,
          }),
        });
        return;
      }

      const fieldsToUpdate = {
        custcol_item_rip_bill_credit_applied: billCreditLineObj.transactionId,
        custcol_item_rip_applied_sales: true,
        custcol_item_rip_rate: billCreditLineObj.rate,
        custcol_item_rip_quantity_used: salesLine.quantityToBeApplied,
        custcol_total_cog_rip:
        cogOnOrder - (salesLine.quantityToBeApplied *  billCreditLineObj.rate),
      };
      Object.entries(fieldsToUpdate).forEach(([fieldId, value]) => {
        salesRecord.setSublistValue({
          sublistId: "item",
          fieldId: fieldId,
          line: lineNumber,
          value: value,
        });
      });

      salesRecord.save({ ignoreMandatoryFields: true, enableSourcing: false });


      context.write({
        key: billCreditLineObj.transactionId,
        value: {
          success: true,
          salesId: salesLine.transactionid,
          quantityUsed: salesLine.quantityToBeApplied,
          billCreditLineObj: billCreditLineObj,
        },
      });
    } catch (e) {
      context.write({
        key: billCreditLineObj.transactionId,
        value: {
          success: false,
          salesId: salesLine.transactionid,
          error: e.message,
          quantityUsed: salesLine.quantityToBeApplied,
          billCreditLineObj: billCreditLineObj,
        },
      });
      mapErrorObject.throwError({
        summaryText: `MAP_ERROR`,
        error: e.message,
      });
    }
  }

  function reduce(context) {
    const reduceErrorObject = new CustomErrorObject();
    try {
      const billCreditId = context.key;
      let totalQuantityUsed = 0;
      const successfulSales = [];
      const failedSales = [];

      if (context.key == "COGError") {
        const cogErrorObj = JSON.parse(context.values);
        context.write({
          key: "COGError",
          value: JSON.stringify({
            billCreditId: cogErrorObj.billCreditId,
            transactionId: cogErrorObj.salesId,
            item: cogErrorObj.item,
          }),
        });
        return;
      }

      const firstValueWithBillCredit = context.values.find((value) => {
        const parsed = JSON.parse(value);
        return parsed.billCreditLineObj;
      });

      const billCreditLineObj = firstValueWithBillCredit
        ? JSON.parse(firstValueWithBillCredit).billCreditLineObj
        : null;

      context.values.forEach((value) => {
        const valueObj = JSON.parse(value);
        if (valueObj.success) {
          totalQuantityUsed += valueObj.quantityUsed;
          successfulSales.push(valueObj.salesId);
        } else {
          failedSales.push({
            salesId: valueObj.salesId,
            error: valueObj.error,
          });
        }
      });

      if (failedSales.length > 0) {
        failedSales.forEach((failedSale) => {
          context.write({
            key: billCreditId,
            value: {
              billCreditId: billCreditId,
              salesId: failedSale.salesId,
              error: failedSale.error,
              itemId: billCreditLineObj.itemId,
            },
          });
        });
        return;
      }

      try {
        const billCredit = record.load({
          type: record.Type.VENDOR_CREDIT,
          id: billCreditId,
          isDynamic: false,
        });

        const billCreditLineNumber = billCredit.findSublistLineWithValue({
          sublistId: "item",
          fieldId: "custcol_rip_item_link",
          value: billCreditLineObj.itemId,
        });

        const currentQuantityRemaining = billCredit.getSublistValue({
          sublistId: "item",
          fieldId: "custcol_rip_quantity_remaining",
          line: billCreditLineNumber,
        });

        const newQuantityRemaining =
          currentQuantityRemaining === null || currentQuantityRemaining === ""
            ? billCreditLineObj.quantityRemaining - totalQuantityUsed
            : currentQuantityRemaining - totalQuantityUsed;

        billCredit.setSublistValue({
          sublistId: "item",
          fieldId: "custcol_rip_quantity_remaining",
          line: billCreditLineNumber,
          value: newQuantityRemaining,
        });

        billCredit.setSublistValue({
          sublistId: "item",
          fieldId: "custcol_rip_original_quantity_bill_cr",
          line: billCreditLineNumber,
          value: billCreditLineObj.originalQuantity,
        });

        if (newQuantityRemaining === 0) {
          billCredit.setSublistValue({
            sublistId: "item",
            fieldId: "custbody_rip_application_complete",
            line: billCreditLineNumber,
            value: true,
          });
        }

        billCredit.save({ ignoreMandatoryFields: true });

        context.write({
          key: "Success",
          value: {
            billCreditId: billCreditId,
            quantityUsed: totalQuantityUsed,
            successfulSales: successfulSales,
            remainingQuantity: newQuantityRemaining,
            item: billCreditLineObj.itemId,
          },
        });
      } catch (e) {
        context.write({
          key: "Error",
          value: {
            billCreditId: billCreditId,
            error: e.message,
            successfulSales: successfulSales,
            failedSales: failedSales,
          },
        });
      }
    } catch (e) {
      reduceErrorObject.throwError({
        summaryText: `REDUCE_ERROR`,
        error: e,
      });
    }
  }

  function summarize(context) {
    let successfulBillCredits = [];
    let billCreditsWithErrors = [];
    const cogErrors = {
      transactions: [],
    };

    context.output.iterator().each(function (key, value) {
      try {
        let valueObj;
        try {
          valueObj = JSON.parse(value);
        } catch (parseError) {
          log.error("Failed to parse value", {
            key: key,
            value: value,
            error: parseError.message,
          });
          return true;
        }

        if (key === "COGError") {
          cogErrors.transactions.push({
            billCreditId: valueObj.billCreditId,
            transactionId: valueObj.transactionId,
            item: valueObj.item,
          });
        } else if (key === "Success") {
          successfulBillCredits.push({
            billCreditId: valueObj.billCreditId,
            quantityUsed: valueObj.quantityUsed,
            remainingQuantity: valueObj.remainingQuantity,
            salesOrders: valueObj.successfulSales,
          });
          log.audit(
            `Successfully applied Bill Credit ${valueObj.billCreditId}, item ${valueObj.item}`,
            `${valueObj.quantityUsed} was applied to sales.`
          );
        } else {
          billCreditsWithErrors.push({
            billCreditId: valueObj.billCreditId,
            error: valueObj.error,
            salesId: valueObj?.salesId,
            itemId: valueObj?.itemId,
          });
        }
      } catch (e) {
        log.error("Error processing result in summarize", {
          key: key,
          value: value,
          error: e.message,
        });
      }
      return true;
    });

    log.audit("Script Summary", {
      successfulBillCredits: successfulBillCredits.map((bc) => ({
        billCreditId: bc.billCreditId,
        quantityUsed: bc.quantityUsed,
        remainingQuantity: bc.remainingQuantity,
        salesOrders: bc.salesOrders,
      })),
      billCreditsWithErrors: billCreditsWithErrors.map((bc) => ({
        billCreditId: bc.billCreditId,
        error: bc.error,
        salesId: bc?.salesId,
        itemId: bc?.itemId,
      })),
      cogErrors: cogErrors.transactions.length > 0 ? cogErrors : null,
    });

    if (cogErrors.transactions.length > 0) {
      try {
        const emailBody = `
RIP Sales Application Results Summary:

Successfully Applied Bill Credits:
${successfulBillCredits
  .map(
    (bc) => `
• Bill Credit: ${bc.billCreditId}
  - Quantity Used: ${bc.quantityUsed}
  - Remaining Quantity: ${bc.remainingQuantity}
  - Applied to Sales Orders: ${bc.salesOrders.join(", ")}
`
  )
  .join("")}

Bill Credits with Errors:
${billCreditsWithErrors
  .map(
    (bc) => `
• Bill Credit: ${bc.billCreditId}
  - Error: ${bc.error}
  - Sales ID: ${bc.salesId || "N/A"}
  - Item ID: ${bc.itemId || "N/A"}
`
  )
  .join("")}

${
  cogErrors.transactions.length > 0
    ? `
COG Update Errors:
${cogErrors.transactions
  .map(
    (error) => `
• Sales Transaction ID: ${error.transactionId}
  - Item : ${error.item}
  - Bill Credit ID: ${error.billCreditId}
`
  )
  .join("")}`
    : ""
}
`;

        email.send({
          author: 15131,
          recipients: "<EMAIL>",
          subject: "Applying RIPS Script Summary",
          body: emailBody,
        });
      } catch (e) {
        log.error("Error sending email", e.message);
      }
    }
  }

  return {
    getInputData,
    map,
    reduce,
    summarize,
  };
});
