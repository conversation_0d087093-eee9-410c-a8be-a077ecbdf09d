/**
 * @NApiVersion 2.1
 * @NScriptType Suitelet
 * @NAmdConfig /SuiteScripts/config.json
 * @param {import ("N/types")}
 * <AUTHOR>
 * @description SL to be able to choose a product category, price level and tier and update all customers assigned to that tier
 * @module spl_update_pricing_tier_sl
 */

define([
  "require",
  "N/log",
  "N/ui/serverWidget",
  "N/ui/message",
  "N/url",
  "N/task",
  "N/redirect",
  "N/error",
], function (require) {
  const log = require("N/log");
  const serverWidget = require("N/ui/serverWidget");
  const message = require("N/ui/message");
  const url = require("N/url");
  const task = require("N/task");
  const redirect = require("N/redirect");
  const error = require("N/error");

  var helperFunctions = (function () {
    /**
     * @param {object} params
     * @returns submitted task to create MR
     */
    function createMapReduceTask(params) {
      const { tierLevelId, productCategoryId, priceLevelId } = params;
      var mapReduceTask = task.create({ taskType: task.TaskType.MAP_REDUCE });
      mapReduceTask.scriptId = "customscript_spl_update_pricing_tier_mr";
      mapReduceTask.deploymentId = "customdeploy_spl_update_pricing_by_tier";

      mapReduceTask.params = {
        custscript_tier_level_id: tierLevelId,
        custscript_product_category_id: productCategoryId,
        custscript_price_level_id: priceLevelId,
      };

      if (
        Object.values(mapReduceTask.params).some(
          (param) => param === undefined || param === null || param === ""
        )
      ) {
        //If any of the params are missing or undefined

        let err = {
          name: "UNABLE TO UPDATE",
          message: "Please make sure all your parameters are valid.",
        };

        throw `${err.name} - ${err.message}`;
      }

      return mapReduceTask.submit();
    }

    /**
     *
     * @param {integer} taskId
     */
    function redirectToSuitelet(taskId) {
      var suiteletURL = url.resolveScript({
        scriptId: "customscript_spl_update_pricing_by_tier",
        deploymentId: "customdeploy_spl_update_price_by_tier",
        params: {
          redirected_from_sl: true,
          task_id: taskId,
          goStraightToMRTask: false,
        },
      });

      redirect.redirect({ url: suiteletURL });
    }

    return {
      createMapReduceTask,
      redirectToSuitelet,
    };
  })();

  return {
    onRequest: function (context) {
      const goStraightToMRTask =
        context.request.parameters["goStraightToMRTask"] == "true"
          ? true
          : false;
      if (context.request.method === "GET" && goStraightToMRTask == false) {
        //If this is not coming from the SPL pricing tier page and we want a form to fill out what we're updating

        const form = serverWidget.createForm({
          title: "Update Pricing by Tier",
        });

        const redirectedFromSl =
          context.request.parameters["redirected_from_sl"];
        if (redirectedFromSl) {
          const mapReduceTaskId = context.request.parameters["task_id"];

          let taskStatus = task.checkStatus({
            taskId: mapReduceTaskId,
          });

          form.addPageInitMessage({
            type: message.Type.INFORMATION,
            title: "Updating Status",
            message: `Task Status: ${
              taskStatus.status
            } and ${taskStatus.getTotalReduceCount()} customer(s) were updated`,
          });
        } else {
          form.clientScriptFileId = "6782565"; //SPL confirm update pricing file
          //#Region Setting Field UI
          var setPricingValues = form.addFieldGroup({
            id: "setPricingValuesGroup",
            label: "Choose pricing information:",
          });
          setPricingValues.isCollapsible = false;

          const tierLevelField = form.addField({
            id: "custpage_tier_level",
            label: "Tier Level",
            type: serverWidget.FieldType.SELECT,
            source: "customlist466",
            container: "setPricingValues",
          });
          tierLevelField.setHelpText({
            help: "By choosing a price tier, you are choosing to update all customers that are assigned to that tier level.",
          });

          form.addField({
            id: "custpage_product_category",
            label: "Pricing Group",
            type: serverWidget.FieldType.SELECT,
            source: -187,
            container: "setPricingValues",
          });

          form.addField({
            id: "custpage_price_level",
            label: "Price Level",
            type: serverWidget.FieldType.SELECT,
            source: -186,
            container: "setPricingValues",
          });

          form.addSubmitButton({
            label: "Update Pricing",
          });

          form.addResetButton({});
        }

        context.response.writePage(form);
      } else {
        //POST Request
        const tierLevelId = context.request.parameters.custpage_tier_level;
        const productCategoryId =
          context.request.parameters.custpage_product_category;
        const priceLevelId = context.request.parameters.custpage_price_level;
        const taskId = helperFunctions.createMapReduceTask({
          tierLevelId,
          productCategoryId,
          priceLevelId,
        });
        helperFunctions.redirectToSuitelet(taskId);

        return;
      }
    },
  };
});
