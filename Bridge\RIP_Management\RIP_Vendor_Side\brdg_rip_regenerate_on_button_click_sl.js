/**
 * @description Runs the regenerate RIPS MR
 * Called by: brdg_rip_redirect_to_mr_on_button_click_cs script
 * Calls: brdg_rip_regenerate_rips_mr or brdg_rip_multiple_bills_po_mr
 *
 * @NApiVersion 2.1
 * @NScriptType Suitelet
 *
 * <AUTHOR>
 * @module brdg_rip_regenerate_sl
 */

define([
  "require",
  "N/log",
  "N/url",
  "N/task",
  "N/redirect",
  "N/runtime",
  "N/record",
  "N/search",
  "N/email",
  "../../../Classes/vlmd_custom_error_object",
], function (require) {
  const log = require("N/log");
  const url = require("N/url");
  const task = require("N/task");
  const redirect = require("N/redirect");
  const runtime = require("N/runtime");
  const record = require("N/record");
  const search = require("N/search");
  const email = require("N/email");

  /**@type {import ("../../../Classes/vlmd_custom_error_object").CustomErrorObject}} */
  const CustomErrorObject = require("../../../Classes/vlmd_custom_error_object");

  function redirectToSuitelet(taskId, billId) {
    var suiteletURL = url.resolveScript({
      scriptId: "customscript_brdg_rips_regenerate_sl",
      deploymentId: "customdeploy_brdg_rip_regenerate_sl",
      params: {
        task_id: taskId,
        bill_id: billId,
        redirected_from_sl: true,
      },
    });

    redirect.redirect({ url: suiteletURL });
  }

  var exports = {};

  function onRequest(context) {
    const customErrorObject = new CustomErrorObject();
    try {
      if (context.request.method === "GET") {
        const redirectedFromSl =
          context.request.parameters["redirected_from_sl"];
        if (redirectedFromSl) {
          const mapReduceTaskId = context.request.parameters["task_id"];
          const billId = context.request.parameters["bill_id"];
          const poId = context.request.parameters["po_id"];
          var taskStatus = task.checkStatus({
            taskId: mapReduceTaskId,
          });
          do {
            taskStatus = task.checkStatus({
              taskId: mapReduceTaskId,
            });
            if (taskStatus.status == "FAILED") {
              throw customErrorObject.updateError({
                errorType: customErrorObject.ErrorTypes.REFERENCE_ERROR,
                summary: "FAILED_MAP_REDUCE",
                details: `Map Reduce Creating New Rip Info Failed!`,
              });
            }
          } while (
            taskStatus.status != "COMPLETE" &&
            taskStatus.status != "FAILED"
          );

          redirect.toRecord({
            type: record.Type.VENDOR_BILL,
            id: billId,
          });

          email.send({
            author: 223244, //Requests
            recipients: runtime.getCurrentUser().id,
            body: `Rips were regenerated on this <a href='/app/accounting/transactions/vendbill.nl?id=${billId}'> Vendor Bill </a>. 
          For more information, go to the bill record.`,
            subject: `RIPS Regenerated`,
          });
        } else {
          const billId = context.request.parameters.custpage_bill_id;
          const poId = context.request.parameters.custpage_po_id;

          let mrTask;

          if (billId) {
            const vendorCreditId = search.lookupFields({
              type: search.Type.VENDOR_BILL,
              id: billId,
              columns: ["custbody_associated_bill_credit"],
            })["custbody_associated_bill_credit"][0].value;

            mrTask = task.create({
              taskType: task.TaskType.MAP_REDUCE,
              scriptId: "customscript_brdg_rips_regenerate_record",
              deploymentId: "customdeploy_brdg_rips_regenerate_mr",
              params: {
                custscript_specific_bill_id: billId,
                custscript_specific_vendor_credit_id: vendorCreditId,
              },
            });
          }
          if (poId) {
            mrTask = task.create({
              taskType: task.TaskType.MAP_REDUCE,
              scriptId: "customscript_brdg_rip_multiple_bills_mr",
              deploymentId: "customdeploy_brdg_rip_multiple_bills",
              params: {
                custscript_specific_po_id: poId,
              },
            });
          }
          const taskId = mrTask.submit();
          redirectToSuitelet(taskId, billId);
        }
      }
    } catch (e) {
      customErrorObject.throwError({
        summaryText: "ERROR_CALLING_SUITELET",
        error: e,
      });
    }
  }
  exports.onRequest = onRequest;
  return exports;
});
