/**
 * @description Script called by the Regenerate Rips button/Reapply Rips button on a bill to call the regenerate rips/apply rips map reduce scripts
 *   -> affects both RIP records and sales records
 *
 * </br><b>Called By:</b> brdg_rip_add_button_to_bill_ue, brdg_rip_reapply_button_ue
 * </br><b>Execution Context:</b> ALL
 * </br><b>Event Type/Mode:</b> ALL
 * </br><b>Entry Points:</b> pageLoad
 *
 * @NApiVersion 2.1
 * @NScriptType ClientScript
 * @NAmdConfig /SuiteScripts/config.json
 *
 * <AUTHOR>
 * @module brdg_rip_redirect_to_mr_cs
 */
define([
  "require",
  "N/log",
  "N/url",
  "N/query",
  "N/ui/dialog",
  "../../Classes/vlmd_custom_error_object",
], function (require) {
  const log = require("N/log");
  const url = require("N/url");
  const query = require("N/query");
  const dialog = require("N/ui/dialog");

  /**@type {import ("../../Classes/vlmd_custom_error_object").CustomErrorObject}} */
  const CustomErrorObject = require("../../Classes/vlmd_custom_error_object");

  function confirmAndRegenerateRips(billId) {
    const customErrorObject = new CustomErrorObject();
    try {
      var options = {
        title: `Are you sure you would like to regenerate RIPs?`,
        message: `This will delete all existing linked records and recreate them. Make sure there is a RIP invoice date.`,
      };
      function success(result) {
        if (result == true) {
          //call suitelet
          var suiteletURL = url.resolveScript({
            scriptId: "customscript_brdg_rips_regenerate_sl",
            deploymentId: "customdeploy_brdg_rip_regenerate_sl",
            params: {
              custpage_bill_id: billId,
            },
          });

          location.href = suiteletURL;
        }
        return result;
      }

      dialog.confirm(options).then(success);
    } catch (e) {
      customErrorObject.throwError({
        summaryText: "ERROR_CALLING_SUITELET",
        error: e,
        recordType: "vendorBill",
        recordId: billId,
      });
    }
  }

  function confirmAndRegenerateRipsFromPo(poId) {
    const customErrorObject = new CustomErrorObject();
    const relatedBillsQuery = /*sql*/
    `SELECT
    nextdoc 
 FROM
    nexttransactionlink 
    JOIN
       TRANSACTION t 
       ON t.id = nextdoc 
    LEFT OUTER JOIN
       transactionline tl 
       ON tl.TRANSACTION = t.id 
    JOIN
       subsidiary AS sbsdry 
       ON tl.subsidiary = sbsdry.id 
    JOIN
       subsidiary AS prntsbsdry 
       ON sbsdry.parent = prntsbsdry.id 
    JOIN
       subsidiary AS grndprntsbsdry 
       ON prntsbsdry.parent = grndprntsbsdry.id 
 WHERE
    linktype = 'OrdBill' 
    AND t.type = 'VendBill' 
    AND 
    (
       sbsdry.id = '16' 
       OR prntsbsdry.id = '16' 
       OR grndprntsbsdry.id = '16' 
    )
    AND previousdoc = ? 
 GROUP BY
    nextdoc`;

    const sqlResults = query
      .runSuiteQL({
        query: relatedBillsQuery,
        params: [poId],
      })
      .asMappedResults();

    if (sqlResults.length < 2) {
      dialog.alert({
        title: "Error",
        message: `There are not multiple bills related to this PO`,
      });
      return;
    }
    try {
      var options = {
        title: `Are you sure you would like to regenerate rips?`,
        message: `This will delete all existing linked records and recreate them. Make sure there is a RIP invoice date on all related bills`,
      };
      function success(result) {
        if (result == true) {
          //call suitelet
          var suiteletURL = url.resolveScript({
            scriptId: "customscript_brdg_rips_regenerate_sl",
            deploymentId: "customdeploy_brdg_rip_regenerate_sl",
            params: {
              custpage_po_id: poId,
            },
          });

          location.href = suiteletURL;
        }
        return result;
      }

      dialog.confirm(options).then(success);
    } catch (e) {
      customErrorObject.throwError({
        summaryText: "ERROR_CALLING_SUITELET",
        error: e,
        recordType: "purchaseOrder",
        recordId: poId,
      });
    }
  }

  function getAppliedSalesTransactions(billCreditId) {
    const linkedTransactionsQuery = /*sql*/ `
    SELECT DISTINCT
    saleline.transaction AS transactionId,
    BUILTIN.DF(saleline.transaction) AS transactionName,
    sale.type AS recordType,
    saleline.item AS itemId,
    saleline.id AS lineId 
 FROM
    transactionline saleline 
    JOIN
       transaction sale 
       ON sale.id = saleline.transaction 
 WHERE
    saleline.custcol_item_rip_bill_credit_applied = ?
    `;

    var results = query
      .runSuiteQL({
        query: linkedTransactionsQuery,
        params: [billCreditId],
      })
      .asMappedResults();

    return results;
  }

  function checkIfCanBeApplied(billCreditId) {
    const availBillCreditLines =
      query
        .runSuiteQL({
          query: `select billcreditline.transaction from transactionline billcreditline where transaction = ? and (custcol_rip_quantity_remaining > 0 or custcol_rip_quantity_remaining is not null)`,
          params: [billCreditId],
        })
        .asMappedResults().length > 0;

    const availSalesToBeAppliedQuery = /*sql*/`
    SELECT
    tl.transaction 
 FROM
    transactionline tl 
    JOIN
       transaction t 
       ON t.id = tl.transaction 
    CROSS JOIN
       (
          SELECT
             tl.subsidiary,
             t.trandate,
             tl.custcol_rip_item_link 
          FROM
             transaction t 
             JOIN
                transactionline tl 
                ON tl.transaction = t.id 
          WHERE
             t.id = ? 
       )
       st 
 WHERE
    tl.subsidiary = st.subsidiary 
    AND tl.item in 
    (
       st.custcol_rip_item_link
    )
    AND t.trandate > st.trandate 
    AND t.type in 
    (
       'CashSale',
       'Invoice'
    )
    AND 
    (
       tl.custcol_item_rip_applied_sales = 'F' 
       or tl.custcol_item_rip_applied_sales is null
    )
    AND tl.accountinglinetype = 'INCOME'`;

    const availSalesToBeApplied =
      query
        .runSuiteQL({
          query: availSalesToBeAppliedQuery,
          params: [billCreditId],
        })
        .asMappedResults().length > 0;
    return Boolean(availBillCreditLines) && Boolean(availSalesToBeApplied);
  }
  function confirmAndUpdateRips(billCreditId) {
    const appliedSales = getAppliedSalesTransactions(billCreditId);

    if (appliedSales.length <= 0) {
      dialog.alert({
        title: "Error",
        message: `There are no sales referencing this bill credit. Please choose the apply button instead!`,
      });
    } else {
      const options = {
        title: `RIPS are Already Applied to Sales!`,
        message: `The following sales (${appliedSales.length}) reference this bill credit:<br> ${appliedSales.map(
          (transaction) => ` ${transaction.transactionname}`
        )}. <br> Are you sure you would like to reapply RIPs?`,
      };

      dialog.confirm(options).then(success);

      function success(result) {
        if (result == true) {
          var suiteletURL = url.resolveScript({
            scriptId: "customscript_brdg_rip_apply_btn_sl",
            deploymentId: "customdeploy_brdg_rip_apply_btn_sl",
            params: {
              billcreditId: billCreditId,
              functionName: "updateRips",
            },
          });

          location.href = suiteletURL;
        }
        return result;
      }
    }
  }

  function confirmAndApplyRips(billCreditId) {
    const eligibleSalesForApplication = checkIfCanBeApplied(billCreditId);

    if (!eligibleSalesForApplication) {
      dialog.alert({
        title: "Error",
        message: `There are no available sales/no available bill credit lines to be applied!`,
      });
      return;
    }

    const options = {
      title: `Apply Rips to Sales!`,
      message: `Are you sure you would like to apply these RIPs to sales?`,
    };

    dialog.confirm(options).then(success);

    function success(result) {
      if (result == true) {
        var suiteletURL = url.resolveScript({
          scriptId: "customscript_brdg_rip_apply_btn_sl",
          deploymentId: "customdeploy_brdg_rip_apply_btn_sl",
          params: {
            billcreditId: billCreditId,
            functionName: "applyRips",
          },
        });

        location.href = suiteletURL;
      }
      return result;
    }
  }

  function pageInit() {
    //client script needs one entry point
  }
  return {
    confirmAndRegenerateRips,
    confirmAndRegenerateRipsFromPo,
    confirmAndUpdateRips,
    confirmAndApplyRips,
    pageInit,
  };
});
