/**
 * Transaction Record class
 * that represents the NetSuite Transaction
 *
 * @NApiVersion 2.1
 * <AUTHOR>
 */

define([], () => {

	/**
	 * Transaction Record class
	 *
	 * @class
	 * @type {import("./spl_transaction_record").TransactionRecord}
	 */
	class TransactionRecord {
		constructor(transaction) {
			this.Field = {
				correctionStatus: "custbody_spl_so_correction_status",
				createdFrom: "createdfrom",
				ediControlNumber: "custbody_spl_edi_trans_cntrl_num",
				entity: "entity",
				needFacilityInfo: "custbody_spl_need_facility_info",
				orderStatus: "orderstatus",
				otherReferenceNumber: "otherrefnum",
				total: "total",
				tranid: "tranid"
			};

			this.transactionInternalId = transaction.id;
			this.customerId = transaction.getValue({
				fieldId: this.Field.entity
			});
			this.customerName = transaction.getText({
				fieldId: this.Field.entity
			});
			this.transactionTotal = transaction.getValue({
				fieldId: this.Field.total
			});
			this.transactionNumber = transaction.getValue({
				fieldId: this.Field.tranid
			});
		}
	}

	return TransactionRecord;
});