/**
 * Interface and type definitions for UI Notification Object
 *
 * <AUTHOR>
 */

export interface UINotification {
	/** Constructor */
	new(): UINotification;
	/** Warning messages */
	warnings: string[];
	/** Error messages */
	errors: string[];
	/** Use NetSuite log module to log the specific type of messages */
	logNotifications: (level: string, messageType: string) => void;
	/** Add a notification to the specific message type */
	addNotification: (messageType: string, notification: string) => void;
	/** Use an alert dialog to display all warnings and errors */
	displayNotifications: () => void;
}
