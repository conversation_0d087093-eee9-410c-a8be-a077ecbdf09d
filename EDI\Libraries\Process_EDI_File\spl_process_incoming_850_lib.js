/**
 * @NApiVersion 2.1
 * @description A scheduled MR to process all incoming 850 files from DSSI
 */

//@ts-ignore
define([
	"N/log",
	"N/error",
	"N/search",
	"ValidateCreateSupplylineSalesOrderLib",
	"SetSalesOrderItemPricingLib",
	"CreateSalesOrderLib",
	"ValidateItemRatesLib",
	"SetSalesOrderCorrections",
	"MoveFileLib",
	"SendIncoming850EmailLib",
	"PushEdiEmailInfoToDBLib",
	"LoDash",
], function (
	log,
	error,
	search,
	validateDataLib,
	setItemPricingLib,
	createSalesOrderLib,
	validateItemRatesLib,
	setSalesOrderCorrectionsLib,
	moveFileLib,
	sendEmailLib,
	pushEdiEmailInfoToDBLib,
	_
) {
	function processIncoming850(
		customerData,
		purchaseOrderObj,
		ediFileName,
		integrationAccountNumber
	) {
		function _processFail(logMessage, programmingError) {
			generalProcessingLog.push({ logMessage, programmingError });
		}

		var helperFunctions = (function () {
			function validateDataAndUpdatePoObjWithMissingInfo() {
				//Although validating and getting info is 2 different kinds of functionality -
				//in this case they are interdependent -> they are combined into 1 function

				var validatedDataObj = validateDataLib.validateDataAndGetMissingInfo(
					purchaseOrderObj,
					customerData.purchasingSoftware,
					integrationAccountNumber
				);

				if (validatedDataObj.errorLog.length > 0) {
					generalProcessingLog = generalProcessingLog.concat(
						validatedDataObj.errorLog
					);
				}

				//Reset PO obj to be the updated PO obj return from lib
				purchaseOrderObj = validatedDataObj.purchaseOrderObj;

				if (!integrationAccountNumber) {
					integrationAccountNumber = validatedDataObj.integrationAccountNumber;
				}

				return validatedDataObj.createSalesOrder;
			}

			function setItemPricing() {
				var setItemPricingObj = setItemPricingLib.setItemPricing(
					purchaseOrderObj.items,
					purchaseOrderObj.parentObj.internalId
				);

				if (setItemPricingObj.errorLog.length > 0) {
					generalProcessingLog = generalProcessingLog.concat(
						setItemPricingObj.errorLog
					);
				}
				purchaseOrderObj.items = setItemPricingObj.items;
			}

			function createSalesOrder() {
				var salesOrderObj =
					createSalesOrderLib.createSalesOrder(purchaseOrderObj);

				if (salesOrderObj.processingErrors.length > 0) {
					generalProcessingLog = generalProcessingLog.concat(
						salesOrderObj.processingErrors
					);
				}

				return salesOrderObj.salesOrderId;
			}

			function validateItemRates() {
				var rateErrors = validateItemRatesLib.validateItemsRates(
					salesOrderId,
					purchaseOrderObj.items,
					purchaseOrderObj.customerInternalId
				);

				if (rateErrors.length > 0) {
					generalProcessingLog = generalProcessingLog.concat(rateErrors);
				}
			}

			function setCorrectionStatusOnSalesOrder() {
				var errorSettingCorrectionLog =
					setSalesOrderCorrectionsLib.setSalesOrderCorrectionStatus(
						salesOrderId,
						generalProcessingLog
					);

				if (errorSettingCorrectionLog.length > 0) {
					generalProcessingLog = generalProcessingLog.concat(
						errorSettingCorrectionLog
					);
				}
			}

			function createNoteRecordOnSalesOrder() {
				var errorCreatingNoteLog =
					setSalesOrderCorrectionsLib.createEdiNoteOnSalesOrder(
						salesOrderId,
						generalProcessingLog
					);

				if (errorCreatingNoteLog.length > 0) {
					generalProcessingLog =
						generalProcessingLog.concat(errorCreatingNoteLog);
				}
			}

			return {
				validateDataAndUpdatePoObjWithMissingInfo,
				setItemPricing,
				createSalesOrder,
				validateItemRates,
				setCorrectionStatusOnSalesOrder,
				createNoteRecordOnSalesOrder,
			};
		})();

		var endHelperFunctions = (function () {
			function moveFile() {
				try {
					if (customerData && purchaseOrderObj.parentObj) {
						customerData.referenceDirectory = `/EDI Reference Files/${customerData.purchasingSoftware}/${integrationAccountNumber}/IN/850`;
					}

					try {
						var moveErrorLog = moveFileLib.moveFile(
							customerData,
							ediFileName,
							generalProcessingLog.length <= 0
						);
					} catch (e) {
						throw e;
					}

					if (moveErrorLog.length > 0) {
						/*Try to move the file again, this time to the general errors folder to remove it 
                        from the folder that it will be processed from and prevent multiple move errors.*/

						customerData.referenceDirectory = `/EDI Reference Files/${customerData.purchasingSoftware}/Incoming_850_Processed_With_Errors/IN/850`;

						try {
							var moveErrorLog = moveFileLib.moveFile(
								customerData,
								ediFileName,
								false //Not processed successfully
							);

							//If the file still was not moved successfully, process the failure.
							if (moveErrorLog.length > 0) {
								_processFail(
									`${ediFileName} not moved. Error: ${moveErrorLog}`
								);

								return;
							}

							_processFail(
								`This file has been moved to the general EDI processing folder. 
                                Please investigate the cause for this error.`,
								true
							);
						} catch (e) {
							throw e;
						}
					}
				} catch (e) {
					throw error.create({
						name: "Error Moving File",
						message: e,
					});
				}
			}

			function _getSalesOrderName() {
				if (salesOrderId) {
					return search.lookupFields({
						type: search.Type.SALES_ORDER,
						id: salesOrderId,
						columns: "tranid",
					}).tranid;
				} else {
					return false;
				}
			}

			function populateErrorArrs() {
				const containsNull = generalProcessingLog.some(
					(errorLog) => errorLog == null
				);

				if (containsNull) {
					programmingErrors.push({ logMessage: 'Error log contains "null"' });
				} else {
					csrErrors = generalProcessingLog.filter((logError) => {
						return !logError.programmingError;
					});

					programmingErrors = generalProcessingLog.filter((logError) => {
						return logError.programmingError;
					});
				}
			}

			function sendEmail() {
				try {
					var ccEmailAddressesArr = ["<EMAIL>"];

					var sendEmailObj = sendEmailLib.send850Email(
						csrErrors,
						programmingErrors,
						_getSalesOrderName(),
						purchaseOrderObj.parentObj.name,
						customerData,
						purchaseOrderObj,
						salesOrderId,
						true,
						["<EMAIL>"],
						ccEmailAddressesArr,
						ediFileName
					);

					return sendEmailObj;
				} catch (e) {
					errorsSendingEmailAndCreatingEdiTransaction.push(
						error.create({
							name: "Error Processing Email",
							message: `Error Sending Email and Pushing to DB: ${e} ${
								csrErrors.length > 0
									? " CSR Errors: " + JSON.stringify(csrErrors)
									: ""
							}${
								programmingErrors.length > 0
									? " Programming Errors: " + JSON.stringify(programmingErrors)
									: ""
							}`,
						})
					);
				}
			}

			function createEdiTransactionRecord() {
				try {
					if (customerData.pushEmailToDB) {
						pushEdiEmailInfoToDBLib.pushEdiEmailInfoToDB(
							customerData,
							sentEmailObjs.csrResultsData,
							purchaseOrderObj.transactionControlNumber,
							purchaseOrderObj.poNumber
						);

						if (!_.isEmpty(sentEmailObjs.programmingResultsData)) {
							pushEdiEmailInfoToDBLib.pushEdiEmailInfoToDB(
								customerData,
								sentEmailObjs.programmingResultsData,
								purchaseOrderObj.transactionControlNumber,
								purchaseOrderObj.poNumber
							);
						}
					}
				} catch (e) {
					errorsSendingEmailAndCreatingEdiTransaction.push(e);
				}
			}

			return {
				moveFile,
				populateErrorArrs,
				sendEmail,
				createEdiTransactionRecord,
			};
		})();

		let generalProcessingLog = [];

		try {
			let createSalesOrder =
				helperFunctions.validateDataAndUpdatePoObjWithMissingInfo();

			if (createSalesOrder) {
				helperFunctions.setItemPricing();

				var salesOrderId = helperFunctions.createSalesOrder();

				if (salesOrderId) {
					helperFunctions.validateItemRates();
					helperFunctions.setCorrectionStatusOnSalesOrder();
					helperFunctions.createNoteRecordOnSalesOrder();
				}
			}
		} catch (e) {
			//To catch any unhandled errors and errors found in the validate lib
			generalProcessingLog.push({
				logMessage: e,
				programmingError: true,
			});
		}

		try {
			endHelperFunctions.moveFile();
		} catch (e) {
			generalProcessingLog.push({
				logMessage: `File was not moved to processed folder: ${e}`,
				programmingError: true,
			});
		}

		let csrErrors = [];
		let programmingErrors = [];

		//Split out different kinds of errors and who they should be emailed to
		endHelperFunctions.populateErrorArrs();

		const errorsSendingEmailAndCreatingEdiTransaction = [];

		const sentEmailObjs = endHelperFunctions.sendEmail();
		endHelperFunctions.createEdiTransactionRecord();

		return errorsSendingEmailAndCreatingEdiTransaction;
	}

	return {
		processIncoming850,
	};
});
