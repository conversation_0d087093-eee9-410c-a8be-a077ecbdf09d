/**
 * @description MR Script that matches products from CSV to SPL items.
 * 
 * Schedule: on-demand
 * 
 * @NApiVersion 2.1
 * @NScriptType MapReduceScript
 * 
 * <AUTHOR>
 * @module spl_bid_sheet_processing_mr
 */
define(["require",
    "N/record",
    "N/file",
    "N/runtime",
    "N/query",
    "N/search",
    "N/email",
    "N/url",
    "./spl_bid_sheet_text_matching_lib",
    "../../Classes/vlmd_mr_summary_handling",
    "../../Classes/vlmd_custom_error_object",
], (require) => {

    const record = require('N/record');
    const file = require('N/file');
    const runtime = require('N/runtime');
    const query = require('N/query');
    const search = require('N/search');
    const email = require('N/email');
    const url = require('N/url');

    const textMatching = require('./spl_bid_sheet_text_matching_lib');

    const CustomErrorObject = require("../../Classes/vlmd_custom_error_object");
    const customErrorObject = new CustomErrorObject();
    
    function getInputData(context) {  
        const bidSheetFileId = runtime.getCurrentScript().getParameter({
            name: 'custscript_spl_bid_sheet_file_id'
        });

        const customerId = runtime.getCurrentScript().getParameter({
            name: 'custscript_spl_bid_sheets_customer'
        });

        let binSheetRecord = record.create({
            type: 'customrecord_spl_bid_sheets'
        });

        binSheetRecord.setValue({
            fieldId: 'custrecord_spl_bid_sheets_customer',
            value: customerId
        });

        binSheetRecord.setValue({
            fieldId: 'custrecord_spl_bid_sheets_file',
            value: bidSheetFileId
        });

        const bidSheetRecordId = binSheetRecord.save();

        const bidSheetFile = file.load({
            id: bidSheetFileId
        });

        const rowItems = bidSheetFile.lines.iterator();        
        // Skip header row
        rowItems.each(() => false);
        
        const rows = [];
        let rowNumber = 0;
        rowItems.each((line) => {
            rowNumber++;
            rows.push({
                rowNumber: rowNumber,
                bidSheetId: bidSheetRecordId,
                customerItemDescription: line.value,
            });
            return true;
        });

        return rows;
    }

    function map(context) {
        try {
            const rowData = JSON.parse(context.value);
            let exactMatchFound = false;
            let bidSheetItemRecordId;
            let vendorCodes = [];
            let bestMatches = [];

            // Get all vendor codes BUT exclude 4 digit numbers
            let vendorCodeQuery = /*sql*/`
            SELECT DISTINCT
                item.id,
                item.itemid,
                item.displayname,
                item.vendorname as vendor_code,
                BUILTIN.HIERARCHY(item.class, 'DISPLAY_JOINED') as item_category
            FROM 
                item
            WHERE 
                item.isinactive = 'F'
                AND BUILTIN.DF(item.subsidiary) LIKE '%Supplyline%'
                AND item.vendorname IS NOT NULL
                AND NOT (LENGTH(item.vendorname) <= 4 AND REGEXP_LIKE(item.vendorname, '^[0-9]+$'))
            `;
            let vendorCodePagedResults = query.runSuiteQLPaged({
                query: vendorCodeQuery,
                pageSize: 1000
            });

            let tokens = textMatching.normalizeToken(rowData.customerItemDescription);

            for (let i = 0; i < vendorCodePagedResults.pageRanges.length; i++) {
                let currentPage = vendorCodePagedResults.fetch(i);
                let currentPagedData = currentPage.data.asMappedResults();
                currentPagedData.forEach((currentRow) => {
                    vendorCodes.push(currentRow);
                });

                for(const item of currentPagedData) {
                    let vendorCode = item.vendor_code?.toLowerCase();
                    let itemId = item.itemid?.toLowerCase();
                    if (tokens.includes(vendorCode) || tokens.includes(itemId)) {
                        bestMatches.push({
                            itemInternalId: item.id,
                            customerItemDescription: rowData.customerItemDescription,
                            displayName: item.displayname,
                            itemCategory: item.item_category,
                            vendorCode: item.vendor_code,
                            itemId: item.itemid,
                            combinedDescription: '',
                            score: 100
                        });
                        exactMatchFound = true;
                        if (bestMatches.length >= 2) {
                            break;
                        }
                    } 
                }
                if (exactMatchFound && bestMatches.length >= 2) {
                    break;
                }
            }
            
            if (exactMatchFound) {
                bidSheetItemRecordId = saveRowRecord(rowData.customerItemDescription, bestMatches, rowData.bidSheetId);
            }

            if(exactMatchFound) {
                context.write({
                    key: rowData.bidSheetId,
                    value: {
                        matches: bestMatches,
                        rowData: rowData
                    }
                });

                return;
            }

            let categoryQuery = /*sql*/`
                SELECT DISTINCT
                    BUILTIN.HIERARCHY(item.class, 'DISPLAY_JOINED') as item_category
                FROM 
                    item
                WHERE 
                    item.isinactive = 'F'
                    AND BUILTIN.DF(subsidiary) LIKE '%Supplyline%'
                    AND BUILTIN.HIERARCHY(item.class, 'DISPLAY_JOINED') IS NOT NULL
            `;
            let categoryPagedResults = query.runSuiteQLPaged({
                query: categoryQuery,
                pageSize: 1000
            });
            
            let bestCategoryMatches = [];
            for (let i = 0; i < categoryPagedResults.pageRanges.length; i++) {
                let currentPage = categoryPagedResults.fetch(i);
                let currentPagedData = currentPage.data.asMappedResults();
                for(const category of currentPagedData) {
                    const score = textMatching.getTokenDistance(
                        rowData.customerItemDescription, 
                        category.item_category.trim()       
                    );
                    bestCategoryMatches.push({
                        itemCategory: category.item_category,
                        score: score
                    });
                }
            }
            // Sort by score and keep only top 3 matches
            bestCategoryMatches = bestCategoryMatches
                .sort((a, b) => b.score - a.score)
                .slice(0, 3);

            let sqlQuery = /*sql*/`
                SELECT 
                    item.id,
                    item.itemid,
                    item.displayname,
                    item.vendorname,
                    BUILTIN.HIERARCHY(item.class, 'DISPLAY_JOINED') AS item_category,
                    item.itemid || ' ' || 
                    COALESCE(item.displayname, '') || ' ' || 
                    COALESCE(item.vendorname, '') || ' ' || 
                    COALESCE(BUILTIN.HIERARCHY(item.class, 'DISPLAY_JOINED'), '') AS combined_description
                FROM 
                    item
                WHERE 
                    item.isinactive = 'F'
                    AND BUILTIN.DF(subsidiary) LIKE '%Supplyline%'
                    AND BUILTIN.HIERARCHY(item.class, 'DISPLAY_JOINED') IN ('${bestCategoryMatches.map(match => match.itemCategory).join("','")}')
            `;

            let itemPagedResults = query.runSuiteQLPaged({
                query: sqlQuery,
                pageSize: 1000
            });

            for (let i = 0; i < itemPagedResults.pageRanges.length; i++) {
                let currentPage = itemPagedResults.fetch(i);
                let currentPagedData = currentPage.data.asMappedResults();
                for (const item of currentPagedData) {
                    const score = textMatching.getEnhancedTokenDistance(
                        rowData.customerItemDescription, 
                        item.combined_description.trim()
                    );    
                    bestMatches.push({
                        customerItemDescription: rowData.customerItemDescription,
                        itemInternalId: item.id,
                        displayName: item.displayname || '',
                        itemCategory: item.item_category || '',
                        vendorCode: item.vendorname || '',
                        itemId: item.itemid,
                        combinedDescription: item.combined_description,
                        score: score
                    });
                }
            }
            
            // Sort by score and keep only top 3 matches
            bestMatches = bestMatches
                .sort((a, b) => b.score - a.score)
                .slice(0, 3);

            bidSheetItemRecordId = saveRowRecord(rowData.customerItemDescription, bestMatches, rowData.bidSheetId);
            context.write({
                key: rowData.bidSheetId,
                value: {
                    matches: bestMatches,
                    rowData: rowData
                }
            });

            return;
        } catch (err) {
            customErrorObject.throwError({summaryText: 'ERROR_PROCESSING_BID_SHEET', error: err});
        }
    }

    function reduce(context) {
        try {
            const bidSheetRecordId = context.key;
            const bidSheetRecord = record.load({
                type: 'customrecord_spl_bid_sheets',
                id: bidSheetRecordId,
                isDynamic: false
            });

            const processedBidSheetFolderId = runtime.getCurrentScript().getParameter({name: 'custscript_spl_bid_sheets_prcssd_fldr_id'});
            const customerId = bidSheetRecord.getValue({ fieldId: 'custrecord_spl_bid_sheets_customer' });
            const customerName = bidSheetRecord.getText({ fieldId: 'custrecord_spl_bid_sheets_customer' });
            const employeeId = bidSheetRecord.getValue({ fieldId: 'lastmodifiedby' });
            const employeeEmail = search.lookupFields({
                type: search.Type.EMPLOYEE,
                id: employeeId,
                columns: ['email']
            }).email;
            const originalFileId = bidSheetRecord.getValue('custrecord_spl_bid_sheets_file');
            const originalFile = file.load({ id: originalFileId });
            const headers = originalFile.lines.iterator().next().value;
            const matchHeaders = [
                'Match 1 Item ID',
                'Match 1 Vendor Code',
                'Match 1 Item Name',
                'Match 1 Item Price',
                'Match 1 Matching Score',
                'Match 2 Item ID',
                'Match 2 Vendor Code',
                'Match 2 Item Name',
                'Match 2 Item Price',
                'Match 2 Matching Score',
                'Match 3 Item ID',
                'Match 3 Vendor Code',
                'Match 3 Item Name',
                'Match 3 Item Price',
                'Match 3 Matching Score'
            ].join(',');

            const processedBidSheetFile = file.create({
                name: `Processed_${originalFile.name}`,
                fileType: file.Type.CSV,
                contents: `${headers},${matchHeaders}\n`
            });
            processedBidSheetFile.folder = processedBidSheetFolderId;

            let itemIds = context.values
                .flatMap(value => JSON.parse(value).matches)
                .map(match => match.itemInternalId);

            let customerItemPrices = getCustomerItemPrice(customerId, itemIds);

            let customerItemPriceMap = new Map(
                customerItemPrices
                    .map(price => [price.item_internal_id, price])                
            );

            // Sort the values by rowNumber
            const sortedValues = context.values
                .map(value => JSON.parse(value))
                .sort((a, b) => a.rowData.rowNumber - b.rowData.rowNumber);

            for (let parsedValue of sortedValues) {
                const rowData = parsedValue.rowData;
                const matches = parsedValue.matches;
                let newRow = [rowData.customerItemDescription];

                for (const match of matches) {
                    newRow.push(sanitizeForCSV(match?.itemId));
                    newRow.push(sanitizeForCSV(match?.vendorCode));
                    newRow.push(sanitizeForCSV(match?.displayName));
                    newRow.push(sanitizeForCSV(customerItemPriceMap.get(match?.itemInternalId)?.rate));
                    newRow.push(sanitizeForCSV(`${match?.score}%`));
                }
                processedBidSheetFile.appendLine({ value: newRow.join(',') });
            }

            const processedBidSheetFileId = processedBidSheetFile.save();

            bidSheetRecord.setValue({
                fieldId: 'custrecord_spl_bid_sheets_finished_file',
                value: processedBidSheetFileId
            });

            bidSheetRecord.save();

            let reviewSuiteletPath = url.resolveScript({
                scriptId: 'customscript_spl_bid_sheet_review_sl',
                deploymentId: 'customdeploy_spl_bid_sheet_review_sl'
            });

            let emailBody = `
                    <p>Thank you for using the bid sheet tool! Your bid sheet has been processed and is ready for review.</p>
                    <p>Please click the link below to access the review interface:</p>
                    
                    <p><a href="${reviewSuiteletPath}&bidsheetid=${bidSheetRecordId}" style="padding: 10px 15px; background-color: #0066cc; color: white; text-decoration: none; border-radius: 4px; display: inline-block; margin: 10px 0;">Review Bid Sheet</a></p>
                    
                    <p>In the review interface, you can:</p>
                    <ul style="margin-left: 20px;">
                        <li>View the original bid sheet items</li>
                        <li>
                            See our top 3 product matches for each item, with:
                            <ul style="margin-left: 20px;">
                                <li>Item ID</li>
                                <li>Vendor Code</li>
                                <li>Item Name</li>
                                <li>Price</li>
                                <li>Match Score (1-100%; higher is better)</li>
                            </ul>
                        </li>
                        <li>Verify or override matches</li>
                        <li>Download the final CSV when you're done</li>
                    </ul>
                    <p><strong>IMPORTANT: </strong> This feature is still under development. Please review the matches carefully.</p>
                `;

            email.send({
                author: employeeId,
                recipients: employeeEmail, 
                body: emailBody,
                subject: `${customerName} Bid Sheet Ready for Review`,
            });

            context.write({
                key: bidSheetRecordId,
                value: processedBidSheetFileId
            });
        } catch (err) {
            customErrorObject.throwError({summaryText: 'ERROR_PROCESSING_BID_SHEET', error: err});
        }
    }

    function summarize(context) {        
        const StageHandling = require("../../Classes/vlmd_mr_summary_handling");
		const stageHandling = new StageHandling(context);
  
		stageHandling.printScriptProcessingSummary();
  
		stageHandling.printRecordsProcessed({
			includeLineBreak: true,
			includeKey: true
		});
  
		stageHandling.printErrors({
			groupErrors: true,
		});
    }

    function saveRowRecord(customerItemDescription, bestMatches, bidSheetRecordId) {

        let bidSheetItemRecord = record.create({
            type: 'customrecord_spl_bid_sheet_items',
            isDynamic: false
        });
        
        bidSheetItemRecord.setValue({
            fieldId: 'custrecord_spl_bsi_bid_sheet',
            value: bidSheetRecordId
        });

        bidSheetItemRecord.setValue({
            fieldId: 'custrecord_spl_bsi_row_data',
            value: customerItemDescription
        });

        bidSheetItemRecord.setValue({
            fieldId: 'custrecord_spl_bsi_potential_matches',
            value: JSON.stringify(bestMatches)
        });        

        return bidSheetItemRecord.save();
    }

    function getCustomerItemPrice(customerId, itemIds) {

        let purchaserQuery = /*SQL */ `
            SELECT
                integration.custrecord_spl_prchsng_fclty purchaser_id,
                parent.id parent_id,
                customer.id customer_id,
            FROM
                customer
                LEFT JOIN customer parent ON customer.parent = parent.id
                LEFT JOIN customrecord_vlmd_edi_integration integration ON (
                    parent.custentity_spl_edi_integration_record = integration.id
                    OR customer.custentity_spl_edi_integration_record = integration.id
                )
            WHERE
                customer.id = ${customerId}`;

        let purchaserResult = query.runSuiteQL({
            query: purchaserQuery,
        }).asMappedResults()?.[0];

        let purchaserId = 
            purchaserResult["purchaser_id"] ?? 
            purchaserResult["parent_id"] ?? 
            purchaserResult["customer_id"];

        if(!purchaserResult) customErrorObject.throwError({summaryText: 'ERROR_PROCESSING_BID_SHEET', error: 'No purchaser id found.'});

        let itemPricingQuery = /*SQL*/ `
                SELECT
                    ip.item as item_internal_id,
                    ip.price rate,
                    IP.level price_level_id,
                    BUILTIN.DF(IP.level) price_level_name,
                    i.pricinggroup price_group_id,
                    BUILTin.DF(pricinggroup) price_group_name,
                FROM
                    customerItemPricing AS IP 
                    JOIN
                    item i 
                    ON i.id = ip.item 
                WHERE
                    IP.customer = ${purchaserId}
                    AND IP.item IN (\'${itemIds.join(`','`)}\')
                UNION
                SELECT
                    i.id as item_internal_id,
                    p.unitPrice rate,
                    GP.level price_level_id,
                    BUILTIN.DF( GP.level) price_level_name,
                    GP.GROUP price_group_id,
                    BUILTIN.DF( GP.GROUP) price_group_name,
                FROM
                    CustomerGroupPricing AS gp 
                    INNER JOIN
                    item AS i 
                    ON i.pricinggroup = gp.GROUP 
                    LEFT JOIN
                    pricing p 
                    ON p.pricelevel = gp.level 
                    AND p.item = i.id 
                WHERE
                    gp.customer = ${purchaserId}
                    AND i.id IN (\'${itemIds.join(`','`)}\')
				ORDER BY 
					price_level_id `;

        let customerItemRates = query.runSuiteQL({
          query: itemPricingQuery,
        }).asMappedResults();

        if(!customerItemRates.length) customErrorObject.throwError({summaryText: 'ERROR_PROCESSING_BID_SHEET', error: 'No customer item rates found.'});

        return customerItemRates;
    }

    function sanitizeForCSV(value) {
        // Convert to string, handle null/undefined
        const str = (value ?? '').toString();
        // Escape quotes by doubling them and wrap in quotes
        return `"${str.replace(/"/g, '""')}"`;
    }

    return {
        getInputData: getInputData,
        map: map,
        reduce: reduce,
        summarize: summarize
    };
});




