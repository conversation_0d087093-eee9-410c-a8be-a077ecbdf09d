/**
 * @description EDI Server Interface
 *
 * <AUTHOR> <<EMAIL>>
 */

import { Connection } from "N/sftp";
import { EDIMFTServer } from "../../../Models/Server/edi_mft_server";
import { EDIPartnerInterface } from "../Partner/edi_partner";

export type EDIConnectionParameters = {
    /** Constant for Target Environment */
    target?: "PROD" | "SANDBOX" | "REF";
    /** Username override */
    username?: string;
    /** Directory override */
    directory?: string;
    /** Password override */
    passwordGuid?: string;
}

export interface EDIServerInterface {
    /** User Name to use for the server connection */
    username: string;
    /** URL String */
    url: string;
    /** EDI Partner Instance */
    partner: EDIPartnerInterface | null;
    /** SFTP Host Key */
    hostKey: string;
    /** Production GUID */
    prodGUID: string;
    /** Sandbox GUID */
    sandboxGUID: string;
    /** NetSuite SFTP Connection Object */
    connection: Connection | null;
    /** Create the Connection object by connecting to the SFTP server */
    connect(params: EDIConnectionParameters | undefined): Connection | undefined;
    /** Download the file from the SFTP server */
    download(filename, options): string;
}

export interface EDIMFTServerInterface extends EDIServerInterface {
    /** Password */
    password: string;
    /** Authorization URL */
    authorizeUrl: string;
    /** Get Messages URL */
    getMessagesUrl: string;
    /** Mark message as read URL */
    markAsRead: string;
    /** Mark message as unread URL */
    markAsUnread: string;
    /** Get attachments URL */
    getAttachmentsUrl: string;
    /** Messages URLs */
    messageUrls: string[];
    /** Authorization Token */
    token: string;
    /** Authorize the user by granting a token */
    authorize(): EDIMFTServer;
    /** List the files available from the inbox */
    list(): {url:string, name:string}[];
    /** Get contents of file from the SFTP server */
    download(url: string, options: {[key:string]: boolean}): string;
}