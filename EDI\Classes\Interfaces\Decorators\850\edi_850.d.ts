/**
 * EDI 850 Types and Interfaces
 *
 * <AUTHOR> <<EMAIL>>
 */

import { CustomErrorObject } from "../../../../../Classes/vlmd_custom_error_object";
import { EDI850ParsedTransaction } from "../../../Decorators/850/edi_850_parsed_transaction";
import { EDICustomer } from "../../../Models/Partner/Customer/edi_customer";
import { EDIFileInterface } from "../../Models/File/edi_file";
import { EDIIncomingInterface } from "../../Models/File/edi_incoming";
import { EDIOutgoingInterface } from "../../Models/File/edi_outgoing";

export interface EDI850Interface extends EDIFileInterface, EDIIncomingInterface, EDIOutgoingInterface {}

export type EDI850Transaction = {
    type: string;
    number: string;
    date: string;
}

export type EDI850Address = {
    street: string;
    city: string;
    state: string;
    zip: string;
}

export type EDI850Entity = {
    name: string;
    identifier: string;
    address: EDI850Address;
}

export type EDI850Item = {
    quantity: string;
    units: string;
    rate: string;
    name: string;
}

export type EDI850SegmentData = {
    controlNumber: string;
    purchaseOrder: EDI850Transaction;
    mustArriveBy: string;
    department: string;
    customer: EDI850Entity;
    supplier: EDI850Entity;
    shipPoint: string;
    items: EDI850Item[];
}

export type EDI850SalesOrderParams = {
    customError: CustomErrorObject
    parsedTransaction: EDI850ParsedTransaction,
    customer: EDICustomer,
    customerQueryString: string
}

export type EDI850CustomerParams = {
    customer: EDI850Entity,
    customError: CustomErrorObject,
    partner: EDICustomer,
}