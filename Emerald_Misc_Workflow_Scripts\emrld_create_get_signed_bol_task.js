/**
 * @NApiVersion 2.1
 * @NScriptType UserEventScript
 * @NAmdConfig /SuiteScripts/config.json
 */

//@ts-ignore
define(["N/log", "N/record"], function (log, record) {
	function afterSubmit(context) {
		if (context.type == "create" || context.type == "copy") {
			const invoice = context.newRecord;
			const subsidiary = invoice.getValue("subsidiary");
			if (subsidiary == 7) {
				//Emerald
				const invoiceName = invoice.getValue("tranid");
				const customer = invoice.getValue("entity");
				const assignTaskTo = 141221; //<PERSON>
				let startDate = new Date();
				startDate.setDate(startDate.getDate() + 2);

				let task = record.create({
					type: record.Type.TASK,
				});

				task.setValue({
					fieldId: "customform",
					value: 111, //Emerald Task Form
				});

				task.setValue({
					fieldId: "custevent_spl_task_type",
					value: 10, //BOL Needed
				});

				task.setValue({
					fieldId: "title",
					value: "Remember to Get Signed BOL for " + invoiceName,
				});

				task.setValue({
					fieldId: "assigned",
					value: assignTaskTo,
				});

				task.setValue({
					fieldId: "company",
					value: customer,
				});

				task.setValue({
					fieldId: "transaction",
					value: invoice.id,
				});

				task.setValue({
					fieldId: "duedate",
					value: startDate,
				});

				task.save();
			}
		}
	}

	return {
		afterSubmit: afterSubmit,
	};
});
