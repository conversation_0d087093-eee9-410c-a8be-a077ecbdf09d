/**
 * @NApiVersion 2.1
 * @NScriptType MapReduceScript
 * @NAmdConfig /SuiteScripts/config.json
 */
//@ts-ignore
define([
	"N/log",
	"N/error",
	"GetInvoicesToUpdateLib",
	"UpdateGrossProfitValuesOnInvoiceLib",
], function (
	log,
	error,
	getInvoicesToUpdateLib,
	updateGrossProfitValuesOnInvoicesLib
) {
	function getInputData(context) {
		const invoicesToUpdate = getInvoicesToUpdateLib.getInvoicesToUpdate();

		if (invoicesToUpdate.length == 0) {
			log.debug({
				title: "No invoices to update",
				details:
					"No invoices to update from bills or credit memos created/edited today",
			});
		}

		return invoicesToUpdate;
	}

	function map(context) {
		const parsedObj = JSON.parse(context.value);
		const { invoiceInternalId, invoiceName } = parsedObj;

		const updatedGrossProfitValuesObj =
			updateGrossProfitValuesOnInvoicesLib.updateGrossProfitValues(
				"MR",
				invoiceInternalId
			);

		if (updatedGrossProfitValuesObj.errorLog.length <= 0) {
			//Write successfully processed invoices to context
			context.write(invoiceName, invoiceInternalId);
		} else {
			throw error.create({
				name: invoiceName,
				message: `${invoiceName} - ${updatedGrossProfitValuesObj.errorLog}`,
			});
		}
	}

	function summarize(context) {
		var documentsProcessedSuccesfullyText =
			getDocumentsProcessedSuccessfully(context);
		var errorMessagesText = getErrorMessages(context);
		logResults();

		function getDocumentsProcessedSuccessfully(summary) {
			let summaryText = ``;

			summary.output.iterator().each(function (key, value) {
				summaryText += `${key}, `;

				return true;
			});

			return summaryText;
		}

		function getErrorMessages(summary) {
			let errorText = ``;

			summary.mapSummary.errors.iterator().each(function (key, value) {
				var errorMessage = JSON.parse(value).message;

				errorText += `${errorMessage}, 
				`;
				log.debug("Error Updating Gross Profit Values", errorMessage);

				return true;
			});

			return errorText;
		}

		function logResults() {
			if (documentsProcessedSuccesfullyText) {
				log.debug({
					title: `Invoices Processed Successfully`,
					details: documentsProcessedSuccesfullyText,
				});
			}

			if (errorMessagesText) {
				log.error({
					title: "Error Log",
					details: errorMessagesText,
				});
			}
		}
	}

	return {
		getInputData: getInputData,
		map: map,
		summarize: summarize,
	};
});
