/**
 * @NApiVersion 2.1
 * @NScriptType Suitelet
 * @NAmdConfig /SuiteScripts/config.json
 */

//@ts-ignore

define(["N/log", "N/ui/serverWidget", "Anime"], function (log, serverWidget, anime) {
	return {
		onRequest: function (context) {
			var request = context.request;
			var response = context.response;

			if (request.method === "GET") {
				var form = serverWidget.createForm({
					title: "Valmed Cyber Security",
				});

				var messageField = form.addField({
					id: "custpage_textfield",
					type: serverWidget.FieldType.INLINEHTML,
					label: "Text",
				});

				messageField.defaultValue =
					"<h3>Please fill out the requested information.<h3/><br/>";

				messageField.updateBreakType({
					breakType: serverWidget.FieldBreakType.STARTCOL,
				});

				//#region Name
				var firstName = form.addField({
					id: "custpage_first_name",
					label: "First Name",
					type: serverWidget.FieldType.TEXT,
				});

				firstName.padding = 1;

				var lastName = form.addField({
					id: "custpage_last_name",
					label: "Last Name",
					type: serverWidget.FieldType.TEXT,
				});

				var lineBreak = form.addField({
					id: "custpage_break1",
					type: serverWidget.FieldType.INLINEHTML,
					label: "Label",
				});

				lineBreak.defaultValue =
					"<br/>\
					<br/>";
				//#endregion

				//#region Use Netsuite
				var useLabel = form.addField({
					id: "use_label",
					label:
						"Do you currently use Netsuite? If not, skip questions 1 and 2",
					type: serverWidget.FieldType.LABEL,
				});

				useLabel.padding = 1;

				var useYes = form.addField({
					id: "custpage_use",
					label: "Yes",
					type: serverWidget.FieldType.RADIO,
					source: "1",
				});

				var useNo = form.addField({
					id: "custpage_use",
					label: "No",
					type: serverWidget.FieldType.RADIO,
					source: "2",
				});
				//#endregion

				//#region 2FA
				var twoFactorLabel = form.addField({
					id: "two_factor_label",
					label: "1 - Do you currently use 2FA?",
					type: serverWidget.FieldType.LABEL,
				});

				twoFactorLabel.padding = 1;

				var twoFactorYes = form.addField({
					id: "custpage_two_factor",
					label: "Yes",
					type: serverWidget.FieldType.RADIO,
					source: "1",
				});

				var twoFactorNo = form.addField({
					id: "custpage_two_factor",
					label: "No",
					type: serverWidget.FieldType.RADIO,
					source: "2",
				});
				//#endregion

				//Unique Password
				var passwordLabel = form.addField({
					id: "password_label",
					label:
						"2 - Is your NetSuite password unique to your account (Not used in other places as well)?",
					type: serverWidget.FieldType.LABEL,
				});
				// .updateLayoutType({
				// 	layoutType: serverWidget.FieldLayoutType.STARTROW,
				// });

				passwordLabel.padding = 1;

				form.addField({
					id: "custpage_password",
					label: "Yes",
					type: serverWidget.FieldType.RADIO,
					source: "1",
				});

				form.addField({
					id: "custpage_password",
					label: "No",
					type: serverWidget.FieldType.RADIO,
					source: "2",
				});
				//#endregion

				//#region Lock computer
				var lockDev = form
					.addField({
						id: "custpage_lock",
						label:
							"3 - Do you always lock your device when leaving the office?",
						type: serverWidget.FieldType.LABEL,
					})
					.updateLayoutType({
						layoutType: serverWidget.FieldLayoutType.STARTROW,
					});

				lockDev.padding = 1;

				form.addField({
					id: "custpage_lock_radio",
					label: "Yes",
					type: serverWidget.FieldType.RADIO,
					source: "1",
				});

				var twoFactorNo = form.addField({
					id: "custpage_lock_radio",
					label: "No",
					type: serverWidget.FieldType.RADIO,
					source: "2",
				});
				//#endregion

				var htmlImage = form.addField({
					id: "custpage_htmlfield",
					type: serverWidget.FieldType.INLINEHTML,
					label: "Button Here",
				});

				htmlImage.padding = 2;

				htmlImage.defaultValue =
					'<br/><br/><button\
				                    id="runaway-btn" \
				                    style="position: absolute; top: 70%;  left: 5%; transform: translate(-50%, -50%); height: 2.5rem	;width: 7rem;   font-size: 1.5rem;  border-radius: 5px;border: none; background-color: #3366CC; color: white;">\
				                    Submit\
				                    </button>';

				form.clientScriptModulePath = "./vlmd_runaway_button_cs";
				response.writePage(form);
			} else {
			}
		},
	};
});
