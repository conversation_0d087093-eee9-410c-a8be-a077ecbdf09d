/******************************************************************************************************
	Script Name - AVA_SUT_CustomersListForCertCapture.js
	Company -     Avalara Technologies Pvt Ltd.
******************************************************************************************************/

/**
 *@NApiVersion 2.0
 *@NScriptType Suitelet
 */

define(['N/ui/serverWidget', 'N/search', 'N/task', 'N/redirect', 'N/record', 'N/runtime', './utility/AVA_Library'],
	function(ui, search, task, redirect, record, runtime, ava_library){
		function onRequest(context){
			try{
				var checkServiceSecurity = ava_library.mainFunction('AVA_CheckService', 'AvaCert');
				if(checkServiceSecurity == 0){
					checkServiceSecurity = ava_library.mainFunction('AVA_CheckSecurity', 30);
				}

				if(checkServiceSecurity == 0){
					if(context.request.method === 'GET'){
						var avaConfigObjRecvd = ava_library.mainFunction('AVA_LoadValuesToGlobals', '');
						var avaCertCaptureForm = ui.createForm({
							title: 'Add Multiple Customers to CertCapture'
						});
						avaCertCaptureForm.clientScriptModulePath = './AVA_CLI_CustomersListForCertCapture.js';
						addFormFields(avaCertCaptureForm, context);
						addFormSublist(avaCertCaptureForm);
						setSublistFieldValues(avaCertCaptureForm, context, avaConfigObjRecvd.AVA_CustomerCode);
						avaCertCaptureForm.addSubmitButton({
							label: 'Submit'
						});
						avaCertCaptureForm.addButton({
							id: 'custpage_ava_certcapture_back',
							label: 'Back',
							functionName: 'AVA_CertCaptureBackToCriteria()'
						});
						context.response.writePage({
							pageObject: avaCertCaptureForm
						});
					}
					else{
						var batchName = context.request.parameters.ava_batchname;
						var certCaptureBatchId = createCertCaptureBatch(context);
						passCertCaptureDataToMapReduce(context, batchName, certCaptureBatchId);
						redirect.toSuitelet({
							scriptId: 'customscript_avacertviewbatch_suitelet',
							deploymentId: 'customdeploy_avacertcaptureviewbatch',
							parameters: {
								'batchid': certCaptureBatchId
							}
						});
					}
				}
				else{
					context.response.write({
						output: checkServiceSecurity
					});
				}
			}
			catch(e){
				log.error('ERROR', 'ERROR NAME = ' + e.name + ', ERROR TYPE = ' + e.type + ', ERROR MESSAGE = ' + e.message);
			}
		}

		function createCertCaptureBatch(context){
			try{
				var certCaptureBatch = record.create({
					type: 'customrecord_avacertcapturebatch'
				});
				certCaptureBatch.setValue({
					fieldId: 'name',
					value: context.request.parameters.ava_batchname
				});
				certCaptureBatch.setValue({
					fieldId: 'custrecord_ava_certcapturebatchname',
					value: context.request.parameters.ava_batchname
				});
				certCaptureBatch.setValue({
					fieldId: 'custrecord_ava_certcapturesubsidiary',
					value: context.request.parameters.ava_subsidiary
				});
				certCaptureBatch.setValue({
					fieldId: 'custrecord_ava_certcapturecustnamestarts',
					value: context.request.parameters.ava_customernamestartswith
				});
				certCaptureBatch.setValue({
					fieldId: 'custrecord_ava_certcapturecustnamecontai',
					value: context.request.parameters.ava_customernamecontains
				});
				certCaptureBatch.setValue({
					fieldId: 'custrecord_ava_certcapturecustomertype',
					value: context.request.parameters.ava_companytype
				});
				certCaptureBatch.setValue({
					fieldId: 'custrecord_ava_certcapturestartdate',
					value: ava_library.AVA_FormatDate(context.request.parameters.ava_startddate)
				});
				certCaptureBatch.setValue({
					fieldId: 'custrecord_ava_certcaptureenddate',
					value: ava_library.AVA_FormatDate(context.request.parameters.ava_enddate)
				});
				certCaptureBatch.setValue({
					fieldId: 'custrecord_ava_certcapturebatchstatus',
					value: 'In Queue'
				});
				var certCaptureBatchId = certCaptureBatch.save({
					enableSourcing: true,
					ignoreMandatoryFields: true
				});
				log.debug('createCertCaptureBatch', 'NEW RECORD - certCaptureBatchId = ' + certCaptureBatchId);
				return certCaptureBatchId;
			}
			catch(e){
				log.error('createCertCaptureBatch', 'ERROR NAME = ' + e.name + ', ERROR TYPE = ' + e.type + ', ERROR MESSAGE = ' + e.message);
			}
		}

		function passCertCaptureDataToMapReduce(context, batchName, certCaptureBatchId){
			try {
				var subsidiaryId = context.request.parameters.ava_subsidiary;
				var sublistCount = context.request.getLineCount({
					group: "ava_bulkcustomersublist"
				});
				var bulkCustomerDetails = new Array();
				for(var i = 0; i < sublistCount; i++){
					var selectMarkCheck = context.request.getSublistValue({
						group: 'ava_bulkcustomersublist',
						name: 'selectmarkcheck',
						line: i
					});
					if(selectMarkCheck == 'T'){
						var subListLineData = {
							parentbatchid: certCaptureBatchId,
							batchname: batchName,
							subsidiary: subsidiaryId,
							customerinternalid: context.request.getSublistValue({
								group: 'ava_bulkcustomersublist',
								name: 'customerinternalid',
								line: i
							}),
							customercode: context.request.getSublistValue({
								group: 'ava_bulkcustomersublist',
								name: 'customercode',
								line: i
							}),
							customername: context.request.getSublistValue({
								group: 'ava_bulkcustomersublist',
								name: 'customername',
								line: i
							}),
							attention: context.request.getSublistValue({
								group: 'ava_bulkcustomersublist',
								name: 'attention',
								line: i
							}),
							address1: context.request.getSublistValue({
								group: 'ava_bulkcustomersublist',
								name: 'addressline1',
								line: i
							}),
							address2: context.request.getSublistValue({
								group: 'ava_bulkcustomersublist',
								name: 'addressline2',
								line: i
							}),
							city: context.request.getSublistValue({
								group: 'ava_bulkcustomersublist',
								name: 'city',
								line: i
							}),
							zipcode: context.request.getSublistValue({
								group: 'ava_bulkcustomersublist',
								name: 'postalcode',
								line: i
							}),
							addressphone: context.request.getSublistValue({
								group: 'ava_bulkcustomersublist',
								name: 'phone',
								line: i
							}),
							fax: context.request.getSublistValue({
								group: 'ava_bulkcustomersublist',
								name: 'fax',
								line: i
							}),
							email: context.request.getSublistValue({
								group: 'ava_bulkcustomersublist',
								name: 'email',
								line: i
							}),
							state: context.request.getSublistValue({
								group: 'ava_bulkcustomersublist',
								name: 'state',
								line: i
							}),
							country: context.request.getSublistValue({
								group: 'ava_bulkcustomersublist',
								name: 'country',
								line: i
							}),
							contactname: context.request.getSublistValue({
								group: 'ava_bulkcustomersublist',
								name: 'contactname',
								line: i
							})
						};
						bulkCustomerDetails.push(subListLineData);
					}
				}

				var callMapReduce = task.create({
					taskType: task.TaskType.MAP_REDUCE
				});
				callMapReduce.scriptId = 'customscript_avaaddcusttocertcapture_map';
				callMapReduce.params = {
					custscript_certcapturecustomerdetails: bulkCustomerDetails
				};
				callMapReduce.submit();
			}
			catch(e){
				log.error('passCertCaptureDataToMapReduce', 'ERROR NAME = ' + e.name + ', ERROR TYPE = ' + e.type + ', ERROR MESSAGE = ' + e.message);
			}
		}

		function setSublistFieldValues(avaCertCaptureForm, context, customerCodeId){
			try {
				var nameStartWith = context.request.parameters.namestartswith;
				var nameContains = context.request.parameters.namecontains;
				var customerType = context.request.parameters.customertype;
				var startDate = context.request.parameters.startdate;
				var endDate = context.request.parameters.enddate;
				startDate += ' 12:00 am';
				endDate += ' 11:59 pm';
				var bulkCustomerSublist = avaCertCaptureForm.getSublist({
					id: 'ava_bulkcustomersublist'
				});
				var i_line = 0;
				var customerinternalidArray = [];
				var previousCustomerId = '';

				var filtersArray = [];
				filtersArray.push(["addresslabel", "isnotempty", ""]);
				filtersArray.push("AND");
				filtersArray.push(["isinactive", "is", "F"]);
				filtersArray.push("AND");
				filtersArray.push(["datecreated", "within", startDate, endDate]);
				var subsidiaryId = context.request.parameters.subsidiary;
				filtersArray.push("AND");
				filtersArray.push(["subsidiary", "anyof", subsidiaryId]);
				if(nameStartWith){
					filtersArray.push("AND");
					filtersArray.push(["entityid", "startswith", nameStartWith]);
				}
				if(nameContains){
					filtersArray.push("AND");
					filtersArray.push(["entityid", "contains", nameContains]);
				}
				if(customerType){
					filtersArray.push("AND");
					filtersArray.push(["isperson", "is", customerType]);
				}
				var columnsArray = [];
				columnsArray.push(search.createColumn({
					name: "internalid",
					sort: search.Sort.ASC
				}));
				columnsArray.push(search.createColumn({
					name: "isdefaultshipping",
					sort: search.Sort.DESC
				}));
				columnsArray.push(search.createColumn({
					name: "entityid"
				}));
				columnsArray.push(search.createColumn({
					name: "firstname"
				}));
				columnsArray.push(search.createColumn({
					name: "middlename"
				}));
				columnsArray.push(search.createColumn({
					name: "lastname"
				}));
				columnsArray.push(search.createColumn({
					name: "companyname"
				}));
				columnsArray.push(search.createColumn({
					name: "isperson"
				}));
				columnsArray.push(search.createColumn({
					name: "attention"
				}));
				columnsArray.push(search.createColumn({
					name: "address1"
				}));
				columnsArray.push(search.createColumn({
					name: "address2"
				}));
				columnsArray.push(search.createColumn({
					name: "city"
				}));
				columnsArray.push(search.createColumn({
					name: "zipcode"
				}));
				columnsArray.push(search.createColumn({
					name: "addressphone"
				}));
				columnsArray.push(search.createColumn({
					name: "phone"
				}));
				columnsArray.push(search.createColumn({
					name: "fax"
				}));
				columnsArray.push(search.createColumn({
					name: "email"
				}));
				columnsArray.push(search.createColumn({
					name: "state"
				}));
				columnsArray.push(search.createColumn({
					name: "country"
				}));
				columnsArray.push(search.createColumn({
					name: "addresslabel"
				}));
				columnsArray.push(search.createColumn({
					name: "address"
				}));
				if(runtime.isFeatureInEffect({feature: 'MULTIPARTNER'}) == true){
					columnsArray.push(search.createColumn({
						name: "partner"
					}));
					columnsArray.push(search.createColumn({
						name: "isperson",
						join: "partner"
					}));
					columnsArray.push(search.createColumn({
						name: "companyname",
						join: "partner"
					}));
					columnsArray.push(search.createColumn({
						name: "firstname",
						join: "partner"
					}));
					columnsArray.push(search.createColumn({
						name: "middlename",
						join: "partner"
					}));
					columnsArray.push(search.createColumn({
						name: "lastname",
						join: "partner"
					}));
				}
				var customerSearchObject = getAllSearchResults({
					type: 'customer',
					filters: filtersArray,
					columns: columnsArray
				});
				var addArray = [];
				if(nullValidation(customerSearchObject)){
					log.debug('setSublistFieldValues', 'customerSearchObject Length = ' + customerSearchObject.length);
					for(var i = 0; i < customerSearchObject.length; i++){
						var customerObject = customerSearchObject[i];
						var customerValues = JSON.stringify(customerObject);
						customerValues = JSON.parse(customerValues);
						customerValues = customerValues.values;
						var customerPhone = customerValues.phone;
						var customerAddressPhone = customerValues.addressphone;
						var customerFax = customerValues.fax;
						var customerState = '';
						if(customerValues.state.length > 0){
							customerState = customerValues.state[0].value;
						}
						var customerCountry = '';
						if(customerValues.country.length > 0){
							customerCountry = customerValues.country[0].value;
						}
						var customerAddressLabel = customerValues.addresslabel;
						var customerAddress = customerValues.address;
						var customerPhone = (customerAddressPhone != null && customerAddressPhone.length > 0) ? customerAddressPhone : customerPhone;
						if(customerPhone){
							customerPhone = customerPhone.replace(/\(|\)/gi, '');
						}
						if(customerFax){
							customerFax = customerFax.replace(/\(|\)/gi, '');
						}

						if(customerinternalidArray.indexOf(customerObject.id) < 0){
							previousCustomerId = customerObject.id;
							customerAddressLabel = '"'+customerAddressLabel+'"';
							var addObj = {"cstlb": customerAddressLabel,"cstid": customerObject.id};
							addArray.push(addObj);
							bulkCustomerSublist.setSublistValue({
								id: 'customerinternalid',
								line: i_line,
								value: customerObject.id
							});
							bulkCustomerSublist.setSublistValue({
								id: 'customername',
								line: i_line,
								value: customerValues.entityid
							});
							if(customerValues.attention){
								bulkCustomerSublist.setSublistValue({
									id: 'attention',
									line: i_line,
									value: customerValues.attention
								});
							}
							if(customerValues.address1){
								bulkCustomerSublist.setSublistValue({
									id: 'addressline1',
									line: i_line,
									value: customerValues.address1
								});
							}
							if(customerValues.address2){
								bulkCustomerSublist.setSublistValue({
									id: 'addressline2',
									line: i_line,
									value: customerValues.address2
								});
							}
							if(customerValues.city){
								bulkCustomerSublist.setSublistValue({
									id: 'city',
									line: i_line,
									value: customerValues.city
								});
							}
							if(customerValues.zipcode){
								bulkCustomerSublist.setSublistValue({
									id: 'postalcode',
									line: i_line,
									value: customerValues.zipcode
								});
							}
							if(customerPhone){
								bulkCustomerSublist.setSublistValue({
									id: 'phone',
									line: i_line,
									value: customerPhone
								});
							}
							if(customerFax){
								bulkCustomerSublist.setSublistValue({
									id: 'fax',
									line: i_line,
									value: customerFax
								});
							}
							if(customerValues.email){
								bulkCustomerSublist.setSublistValue({
									id: 'email',
									line: i_line,
									value: customerValues.email
								});
							}
							if(customerState){
								bulkCustomerSublist.setSublistValue({
									id: 'state',
									line: i_line,
									value: customerState
								});
							}
							if(customerCountry){
								bulkCustomerSublist.setSublistValue({
									id: 'country',
									line: i_line,
									value: customerCountry
								});
							}
							if(customerAddress){
								bulkCustomerSublist.setSublistValue({
									id: 'address',
									line: i_line,
									value: customerAddress
								});
							}
							
							switch(customerCodeId){
								case '0':
									bulkCustomerSublist.setSublistValue({
										id: 'customercode',
										line: i_line,
										value: customerValues.entityid
									});
									var customerName = (customerValues.isperson == true) ? (customerValues.firstname + ((customerValues.middlename != null && customerValues.middlename.length > 0) ? (' ' + customerValues.middlename + ' ') : ' ') + customerValues.lastname) : (customerValues.companyname);
									if(customerName){
										bulkCustomerSublist.setSublistValue({
											id: 'contactname',
											line: i_line,
											value: customerName
										});
									}
									break;
								case '1':
									var customerName = (customerValues.isperson == true) ? (customerValues.firstname + ((customerValues.middlename != null && customerValues.middlename.length > 0) ? (' ' + customerValues.middlename + ' ') : ' ') + customerValues.lastname) : (customerValues.companyname);
									if(customerName){
										bulkCustomerSublist.setSublistValue({
											id: 'customercode',
											line: i_line,
											value: customerName
										});
										bulkCustomerSublist.setSublistValue({
											id: 'contactname',
											line: i_line,
											value: customerName
										});
									}
									break;
								case '2':
									bulkCustomerSublist.setSublistValue({
										id: 'customercode',
										line: i_line,
										value: customerObject.id
									});
									var customerName = (customerValues.isperson == true) ? (customerValues.firstname + ((customerValues.middlename != null && customerValues.middlename.length > 0) ? (' ' + customerValues.middlename + ' ') : ' ') + customerValues.lastname) : (customerValues.companyname);
									if(customerName){
										bulkCustomerSublist.setSublistValue({
											id: 'contactname',
											line: i_line,
											value: customerName
										});
									}
									break;
								case '3':
									if(runtime.isFeatureInEffect({feature: 'MULTIPARTNER'}) == true && customerValues.partner[0].value != null && customerValues.partner[0].value.length > 0){
										if(customerValues.partner[0].text){
											bulkCustomerSublist.setSublistValue({
												id: 'customercode',
												line: i_line,
												value: customerValues.partner[0].text
											});
										}
										var partnerName = (customerValues["partner.isperson"] == true) ? (customerValues["partner.firstname"] + ((customerValues["partner.middlename"] != null && customerValues["partner.middlename"].length > 0) ? ('' + customerValues["partner.middlename"] + '') : '') + customerValues["partner.lastname"]) : (customerValues["partner.companyname"]);
										if(partnerName){
											bulkCustomerSublist.setSublistValue({
												id: 'contactname',
												line: i_line,
												value: partnerName
											});
										}
									}
									else{
										bulkCustomerSublist.setSublistValue({
											id: 'customercode',
											line: i_line,
											value: customerValues.entityid
										});
										var customerName = (customerValues.isperson == true) ? (customerValues.firstname + ((customerValues.middlename != null && customerValues.middlename.length > 0) ? (' ' + customerValues.middlename + ' ') : ' ') + customerValues.lastname) : (customerValues.companyname);
										if(customerName){
											bulkCustomerSublist.setSublistValue({
												id: 'contactname',
												line: i_line,
												value: customerName
											});
										}
									}
									break;
								case '4':
									if(runtime.isFeatureInEffect({feature: 'MULTIPARTNER'}) == true && customerValues.partner[0].value != null && customerValues.partner[0].value.length > 0){
										var partnerName = (customerValues["partner.isperson"] == true) ? (customerValues["partner.firstname"] + ((customerValues["partner.middlename"] != null && customerValues["partner.middlename"].length > 0) ? ('' + customerValues["partner.middlename"] + '') : '') + customerValues["partner.lastname"]) : (customerValues["partner.companyname"]);
										if(partnerName){
											bulkCustomerSublist.setSublistValue({
												id: 'customercode',
												line: i_line,
												value: partnerName
											});
											bulkCustomerSublist.setSublistValue({
												id: 'contactname',
												line: i_line,
												value: partnerName
											});
										}
									}
									else{
										var customerName = (customerValues.isperson == true) ? (customerValues.firstname + ((customerValues.middlename != null && customerValues.middlename.length > 0) ? (' ' + customerValues.middlename + ' ') : ' ') + customerValues.lastname) : (customerValues.companyname);
										if(customerName){
											bulkCustomerSublist.setSublistValue({
												id: 'customercode',
												line: i_line,
												value: customerName
											});
											bulkCustomerSublist.setSublistValue({
												id: 'contactname',
												line: i_line,
												value: customerName
											});
										}
									}
									break;
								case '5':
									if(runtime.isFeatureInEffect({feature: 'MULTIPARTNER'}) == true && crecord.getValue('partner') != null && crecord.getValue('partner').length > 0){
										if(customerValues.partner[0].value){
											bulkCustomerSublist.setSublistValue({
												id: 'customercode',
												line: i_line,
												value: customerValues.partner[0].value
											});
										}
										var partnerName = (customerValues["partner.isperson"] == true) ? (customerValues["partner.firstname"] + ((customerValues["partner.middlename"] != null && customerValues["partner.middlename"].length > 0) ? ('' + customerValues["partner.middlename"] + '') : '') + customerValues["partner.lastname"]) : (customerValues["partner.companyname"]);
										if(partnerName){
											bulkCustomerSublist.setSublistValue({
												id: 'contactname',
												line: i_line,
												value: partnerName
											});
										}
									}
									else{
										bulkCustomerSublist.setSublistValue({
											id: 'customercode',
											line: i_line,
											value: customerObject.id
										});
										var customerName = (customerValues.isperson == true) ? (customerValues.firstname + ((customerValues.middlename != null && customerValues.middlename.length > 0) ? (' ' + customerValues.middlename + ' ') : ' ') + customerValues.lastname) : (customerValues.companyname);
										if(customerName){
											bulkCustomerSublist.setSublistValue({
												id: 'contactname',
												line: i_line,
												value: customerName
											});
										}
									}
									break;
								case '6':
									bulkCustomerSublist.setSublistValue({
										id: 'customercode',
										line: i_line,
										value: customerValues.entityid
									});
									var customerName = (customerValues.isperson == true) ? (customerValues.firstname + ((customerValues.middlename != null && customerValues.middlename.length > 0) ? (' ' + customerValues.middlename + ' ') : ' ') + customerValues.lastname) : (customerValues.companyname);
									if(customerName){
										bulkCustomerSublist.setSublistValue({
											id: 'contactname',
											line: i_line,
											value: customerName
										});
									}
									break;
								case '7':
									if(runtime.isFeatureInEffect({feature: 'MULTIPARTNER'}) == true && customerValues.partner[0].value != null && customerValues.partner[0].value.length > 0){
										if(customerValues.partner[0].text){
											bulkCustomerSublist.setSublistValue({
												id: 'customercode',
												line: i_line,
												value: customerValues.partner[0].text
											});
										}
										var partnerName = (customerValues["partner.isperson"] == true) ? (customerValues["partner.firstname"] + ((customerValues["partner.middlename"] != null && customerValues["partner.middlename"].length > 0) ? ('' + customerValues["partner.middlename"] + '') : '') + customerValues["partner.lastname"]) : (customerValues["partner.companyname"]);
										if(partnerName){
											bulkCustomerSublist.setSublistValue({
												id: 'contactname',
												line: i_line,
												value: partnerName
											});
										}
									}
									else{
										bulkCustomerSublist.setSublistValue({
											id: 'customercode',
											line: i_line,
											value: customerValues.entityid
										});
										var customerName = (customerValues.isperson == true) ? (customerValues.firstname + ((customerValues.middlename != null && customerValues.middlename.length > 0) ? (' ' + customerValues.middlename + ' ') : ' ') + customerValues.lastname) : (customerValues.companyname);
										if(customerName){
											bulkCustomerSublist.setSublistValue({
												id: 'contactname',
												line: i_line,
												value: customerName
											});
										}
									}
									break;
								default:
									bulkCustomerSublist.setSublistValue({
										id: 'customercode',
										line: i_line,
										value: 0
									});
									break;
							}
							i_line++;
							customerinternalidArray.push(customerObject.id);
						}
						else{
							if(parseInt(previousCustomerId) == parseInt(customerObject.id)){
								var arrIndex = addArray.map(function(d){return d["cstid"] }).indexOf(customerObject.id);
								var labelData = addArray[arrIndex].cstlb;
								labelData = labelData.substring(1,labelData.length - 1);
								labelData = labelData+'~*'+customerAddressLabel;
								labelData = '"'+labelData+'"';
								addArray[arrIndex].cstlb = labelData;
							}
						}
					}
				}
				avaCertCaptureForm.updateDefaultValues({
					custpage_ava_addressstore : addArray
				})
				
			}
			catch(e){
				log.error('setSublistFieldValues', 'ERROR NAME = ' + e.name + ', ERROR TYPE = ' + e.type + ', ERROR MESSAGE = ' + e.message);
			}
		}

		function addFormSublist(avaCertCaptureForm){
			try{
				var bulkCustomerSublist = avaCertCaptureForm.addSublist({
					id: 'ava_bulkcustomersublist',
					label: 'Customers',
					type: 'list',
					tab: 'custpage_certcapturecustomers'
				});
				bulkCustomerSublist.addField({
					id: 'selectmarkcheck',
					type: ui.FieldType.CHECKBOX,
					label: 'Select'
				});
				bulkCustomerSublist.addMarkAllButtons();
				var sublistCustInternalId = bulkCustomerSublist.addField({
					id: 'customerinternalid',
					label: 'Customer Internal Id',
					type: 'select',
					source: 'customer'
				});
				sublistCustInternalId.updateDisplayType({
					displayType: ui.FieldDisplayType.HIDDEN
				});
				bulkCustomerSublist.addField({
					id: 'customercode',
					label: 'Customer Code',
					type: 'text'
				});
				var contachName = bulkCustomerSublist.addField({
					id: 'contactname',
					label: 'Contact Name',
					type: 'text'
				});
				contachName.updateDisplayType({
					displayType: ui.FieldDisplayType.HIDDEN
				});
				bulkCustomerSublist.addField({
					id: 'customername',
					label: 'Customer Name',
					type: 'text'
				});
				var sublistAttention = bulkCustomerSublist.addField({
					id: 'attention',
					label: 'Attention',
					type: 'text'
				});
				sublistAttention.updateDisplayType({
					displayType: ui.FieldDisplayType.ENTRY
				});
				sublistAttention.updateDisplayType({
					displayType: ui.FieldDisplayType.HIDDEN
				});
				var sublistAddress1 = bulkCustomerSublist.addField({
					id: 'addressline1',
					label: 'Add Line1',
					type: 'text'
				});
				sublistAddress1.updateDisplayType({
					displayType: ui.FieldDisplayType.ENTRY
				});
				sublistAddress1.updateDisplayType({
					displayType: ui.FieldDisplayType.HIDDEN
				});
				var sublistAddress2 = bulkCustomerSublist.addField({
					id: 'addressline2',
					label: 'Add Line2',
					type: 'text'
				});
				sublistAddress2.updateDisplayType({
					displayType: ui.FieldDisplayType.ENTRY
				});
				sublistAddress2.updateDisplayType({
					displayType: ui.FieldDisplayType.HIDDEN
				});
				var sublistCity = bulkCustomerSublist.addField({
					id: 'city',
					label: 'City',
					type: 'text'
				});
				sublistCity.updateDisplayType({
					displayType: ui.FieldDisplayType.ENTRY
				});
				sublistCity.updateDisplayType({
					displayType: ui.FieldDisplayType.HIDDEN
				});
				var sublistPostalCode = bulkCustomerSublist.addField({
					id: 'postalcode',
					label: 'Postal Code',
					type: 'text'
				});
				sublistPostalCode.updateDisplayType({
					displayType: ui.FieldDisplayType.ENTRY
				});
				sublistPostalCode.updateDisplayType({
					displayType: ui.FieldDisplayType.HIDDEN
				});
				bulkCustomerSublist.addField({
					id: 'email',
					label: 'Email',
					type: 'text'
				});
				bulkCustomerSublist.addField({
					id: 'fax',
					label: 'Fax',
					type: 'text'
				});
				var sublistPhone = bulkCustomerSublist.addField({
					id: 'phone',
					label: 'Phone',
					type: 'text'
				});
				sublistPhone.updateDisplayType({
					displayType: ui.FieldDisplayType.ENTRY
				});
				sublistPhone.updateDisplayType({
					displayType: ui.FieldDisplayType.DISABLED
				});
				var sublistState = bulkCustomerSublist.addField({
					id: 'state',
					label: 'State',
					type: 'text'
				});
				sublistState.updateDisplayType({
					displayType: ui.FieldDisplayType.ENTRY
				});
				sublistState.updateDisplayType({
					displayType: ui.FieldDisplayType.HIDDEN
				});
				var sublistAddressCountry = bulkCustomerSublist.addField({
					id: 'country',
					label: 'Country',
					type: 'text'
				});
				sublistAddressCountry.updateDisplayType({
					displayType: ui.FieldDisplayType.ENTRY
				});
				sublistAddressCountry.updateDisplayType({
					displayType: ui.FieldDisplayType.HIDDEN
				});
				bulkCustomerSublist.addField({
					id: 'custpage_selectaddresslabel',
					label: 'Address Label',
					type: 'select'
				});
				var sublistAddress = bulkCustomerSublist.addField({
					id: 'address',
					label: 'Address',
					type: 'TEXTAREA'
				});
				sublistAddress.updateDisplayType({
					displayType: ui.FieldDisplayType.ENTRY
				});
				sublistAddress.updateDisplayType({
					displayType: ui.FieldDisplayType.DISABLED
				});
			}
			catch(e){
				log.error('addFormSublist', 'ERROR NAME = ' + e.name + ', ERROR TYPE = ' + e.type + ', ERROR MESSAGE = ' + e.message);
			}
		}

		function addFormFields(avaCertCaptureForm, context){
			try{
				avaCertCaptureForm.addTab({
					id : 'custpage_certcapturecustomers',
					label : 'Customers'
				});
				avaCertCaptureForm.addTab({
					id : 'custpage_notes',
					label : ' '
				});
				var avaBatchName = avaCertCaptureForm.addField({
					id: 'ava_batchname',
					label: 'Batch Name',
					type: ui.FieldType.TEXT
				});
				avaBatchName.defaultValue = context.request.parameters.batchname;
				avaBatchName.updateDisplayType({
					displayType: ui.FieldDisplayType.DISABLED
				});
				avaBatchName.updateDisplaySize({
					width: 40,
					height: 0
				});
				 var avaSubsidiary = avaCertCaptureForm.addField({
					id: 'ava_subsidiary',
					label: 'Subsidiary',
					type: ui.FieldType.SELECT,
					source: 'subsidiary'
				});
				avaSubsidiary.defaultValue = context.request.parameters.subsidiary;
				avaSubsidiary.updateDisplayType({
					displayType: ui.FieldDisplayType.DISABLED
				});
				var customerNameStartsWith = avaCertCaptureForm.addField({
					id: 'ava_customernamestartswith',
					label: 'Customer Name Starts With',
					type: ui.FieldType.TEXT
				});
				customerNameStartsWith.defaultValue = context.request.parameters.namestartswith;
				customerNameStartsWith.updateDisplayType({
					displayType: ui.FieldDisplayType.HIDDEN
				});
				var customerNameContains = avaCertCaptureForm.addField({
					id: 'ava_customernamecontains',
					label: 'Customer Name Contains',
					type: ui.FieldType.TEXT
				});
				customerNameContains.defaultValue = context.request.parameters.namecontains;
				customerNameContains.updateDisplayType({
					displayType: ui.FieldDisplayType.HIDDEN
				});
				var customerType = avaCertCaptureForm.addField({
					id: 'ava_companytype',
					label: 'Type',
					type: ui.FieldType.TEXT
				});
				customerType.defaultValue = context.request.parameters.customertype;
				customerType.updateDisplayType({
					displayType: ui.FieldDisplayType.HIDDEN
				});
				var avaStartDate = avaCertCaptureForm.addField({
					id: 'ava_startddate',
					label: 'Start Date',
					type: ui.FieldType.DATE
				});
				avaStartDate.defaultValue = context.request.parameters.startdate;
				avaStartDate.updateDisplayType({
					displayType: ui.FieldDisplayType.HIDDEN
				});
				var avaEndDate = avaCertCaptureForm.addField({
					id: 'ava_enddate',
					label: 'End Date',
					type: ui.FieldType.DATE
				});
				avaEndDate.defaultValue = context.request.parameters.enddate;
				avaEndDate.updateDisplayType({
					displayType: ui.FieldDisplayType.HIDDEN
				});
				avaCertCaptureForm.addField({
					id: 'custpage_ava_addressstore',
					label: 'Address Store',
					type: ui.FieldType.INLINEHTML,
					container:'custpage_notes'
				});
			}
			catch(e){
				log.error('addFormFields', 'ERROR NAME = ' + e.name + ', ERROR TYPE = ' + e.type + ', ERROR MESSAGE = ' + e.message);
			}
		}

		function getAllSearchResults(options){
			try{
				var stRecordType = options.type;
				var stSavedSearch = options.searchId;
				var arrFilters = options.filters;
				var arrColumns = options.columns;
				var arrResults = [];
				var count = 1000;
				var start = 0;
				var end = 1000;
				var searchObj = search.create({
					type: stRecordType,
					filters: arrFilters,
					columns: arrColumns
				});
				var rs = searchObj.run();
				while(count == 1000) {
					var results = rs.getRange(start, end);
					arrResults = arrResults.concat(results);
					start = end;
					end += 1000;
					count = results.length;
				}
				return arrResults;
			}
			catch(e){
				log.error('getAllSearchResults', 'ERROR NAME = ' + e.name + ', ERROR TYPE = ' + e.type + ', ERROR MESSAGE = ' + e.message);
			}
		}
		
		function nullValidation(value){
			if(value == 'null' || value == null || value == '' || value == ' ' || value == undefined || value == 'undefined' || value == 'NaN' || value == NaN){
				return false;
			}
			else{
				return value;
			}
		}
		
		return{
			onRequest: onRequest
		};
	}
);