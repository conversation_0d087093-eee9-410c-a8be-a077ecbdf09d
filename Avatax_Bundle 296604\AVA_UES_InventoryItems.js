/******************************************************************************************************
	Script Name - AVA_UES_InventoryItems.js
	Company -     Avalara Technologies Pvt Ltd.
******************************************************************************************************/

/**
 *@NApiVersion 2.0
 *@NScriptType UserEventScript
*/

define(['N/ui/serverWidget', 'N/runtime', 'N/search', 'N/record', 'N/https', './utility/AVA_Library', './utility/AVA_CommonServerFunctions', './AVA_Signature2_0'],
	function(ui, runtime, search, record, https, ava_library, ava_commonFunction, ava_signature){
		function AVA_InventoryTabBeforeLoad(context){
			var cForm = context.form;
			var nRecord = context.newRecord;
			var avaConfigObjRec = ava_library.mainFunction('AVA_ReadConfig', '0');
			
			var serviceTypes = cForm.addField({
				id: 'custpage_ava_servicetypes',
				label: 'Service Types',
				type: ui.FieldType.TEXT
			});
			serviceTypes.updateDisplayType({
				displayType: ui.FieldDisplayType.HIDDEN
			});
			serviceTypes.defaultValue = avaConfigObjRec['AVA_ServiceTypes'];
			
			// Flag identification to decide on which Inventory Item screen to add the tab.
			// Default Value 'F' - No adding of tab and 'T' for adding tab.
			var AVA_DesignTab = 'F';
			var subtype = nRecord.getValue({
				fieldId: 'subtype'
			});
			
			if(subtype != 'Purchase' && runtime.executionContext == runtime.ContextType.USER_INTERFACE){
				var udf1 = cForm.getField({
					id: 'custitem_ava_udf1'
				});
				udf1.updateDisplayType({
					displayType: ui.FieldDisplayType.HIDDEN
				});
				var udf2 = cForm.getField({
					id: 'custitem_ava_udf2'
				});
				udf2.updateDisplayType({
					displayType: ui.FieldDisplayType.HIDDEN
				});
				var taxcode = cForm.getField({
					id: 'custitem_ava_taxcode'
				});
				taxcode.updateDisplayType({
					displayType: ui.FieldDisplayType.HIDDEN
				});
			}
			
			if(avaConfigObjRec['AVA_ServiceTypes'] != null && avaConfigObjRec['AVA_ServiceTypes'].search('TaxSvc') != -1){
				AVA_DesignTab = AVA_RecordTypeValidation(nRecord.type, subtype);
				
				if(AVA_DesignTab == 'T'){
					cForm.addTab({
						id: 'custpage_avatab',
						label: 'AvaTax'
					});
					var udf1 = cForm.addField({
						id: 'custpage_ava_udf1',
						label: 'User Defined 1',
						type: ui.FieldType.TEXT,
						container: 'custpage_avatab'
					});
					udf1.maxLength = 250;
					var udf2 = cForm.addField({
						id: 'custpage_ava_udf2',
						label: 'User Defined 2',
						type: ui.FieldType.TEXT,
						container: 'custpage_avatab'
					});
					udf2.maxLength = 250;
					var taxcodeMapping = cForm.addField({
						id: 'custpage_ava_taxcodemapping',
						label: 'AvaTax Tax Code',
						type: ui.FieldType.TEXT,
						container: 'custpage_avatab'
					});
					taxcodeMapping.maxLength = 25;
					var itemMapId = cForm.addField({
						id: 'custpage_ava_itemmapid',
						label: 'AVA Item Mapping ID',
						type: ui.FieldType.TEXT,
						container: 'custpage_avatab'
					});
					itemMapId.updateDisplayType({
						displayType: ui.FieldDisplayType.HIDDEN
					});
					
					if(context.type == context.UserEventType.EDIT || context.type == context.UserEventType.VIEW || context.type == context.UserEventType.XEDIT){
						var searchRecord = search.create({
							type: 'customrecord_avaitemmapping',
							filters: ['custrecord_ava_itemid', 'anyof', nRecord.id],
							columns: ['custrecord_ava_itemudf1', 'custrecord_ava_itemudf2', 'custrecord_ava_itemtaxcodemapping']
						});
						var searchresult = searchRecord.run();
						searchresult = searchresult.getRange({
							start: 0,
							end: 5
						});
						
						if(searchresult != null && searchresult.length > 0){
							for(var i = 0; i < searchresult.length; i++){
								itemMapId.defaultValue = searchresult[i].id;
								udf1.defaultValue = searchresult[i].getValue('custrecord_ava_itemudf1');
								udf2.defaultValue = searchresult[i].getValue('custrecord_ava_itemudf2');
								taxcodeMapping.defaultValue = searchresult[i].getValue('custrecord_ava_itemtaxcodemapping');
							}
						}
						else{
							var avaUdf1 = nRecord.getValue({
								fieldId: 'custitem_ava_udf1'
							});
							udf1.defaultValue = (avaUdf1 != null && avaUdf1.length > 0) ? avaUdf1 : '';
							var avaUdf2 = nRecord.getValue({
								fieldId: 'custitem_ava_udf2'
							});
							udf2.defaultValue = (avaUdf2 != null && avaUdf2.length > 0) ? avaUdf2 : '';
							var taxcode = nRecord.getValue({
								fieldId: 'custitem_ava_taxcode'
							});
							taxcodeMapping.defaultValue = (taxcode != null && taxcode.length > 0) ? taxcode : '';
						}
					}
					else if(context.type == context.UserEventType.COPY){
						var avaUdf1 = nRecord.getValue({
							fieldId: 'custitem_ava_udf1'
						});
						udf1.defaultValue = (avaUdf1 != null && avaUdf1.length > 0) ? avaUdf1 : '';
						var avaUdf2 = nRecord.getValue({
							fieldId: 'custitem_ava_udf2'
						});
						udf2.defaultValue = (avaUdf2 != null && avaUdf2.length > 0) ? avaUdf2 : '';
						var taxcode = nRecord.getValue({
							fieldId: 'custitem_ava_taxcode'
						});
						taxcodeMapping.defaultValue = (taxcode != null && taxcode.length > 0) ? taxcode : '';
					}
					
					if((context.type == context.UserEventType.EDIT || context.type == context.UserEventType.VIEW) && (runtime.executionContext == runtime.ContextType.USER_INTERFACE || runtime.executionContext == runtime.ContextType.CLIENT)){
						AVA_AddLogsSubList(cForm, ui, nRecord);
					}
				}
			}
		}
		
		function AVA_InventoryTabBeforeSubmit(context){
			var nRecord = context.newRecord;
			var serviceTypes = nRecord.getValue({
				fieldId: 'custpage_ava_servicetypes'
			});
			
			if(serviceTypes != null && serviceTypes.search('TaxSvc') != -1){
				if(runtime.executionContext != runtime.ContextType.CSV_IMPORT && runtime.executionContext != runtime.ContextType.WEBSERVICES){
					nRecord.setValue({
						fieldId: 'custitem_ava_udf1',
						value: nRecord.getValue({
							fieldId: 'custpage_ava_udf1'
						})
					});
					nRecord.setValue({
						fieldId: 'custitem_ava_udf2',
						value: nRecord.getValue({
							fieldId: 'custpage_ava_udf2'
						})
					});
					nRecord.setValue({
						fieldId: 'custitem_ava_taxcode',
						value: nRecord.getValue({
							fieldId: 'custpage_ava_taxcodemapping'
						})
					});
				}
			}
		}
		
		function AVA_InventoryTabAfterSubmit(context){
			var nRecord = context.newRecord;
			var serviceTypes = nRecord.getValue({
				fieldId: 'custpage_ava_servicetypes'
			});
			
			if(serviceTypes != null && serviceTypes.search('TaxSvc') != -1){
				var itemMapId = nRecord.getValue({
					fieldId: 'custpage_ava_itemmapid'
				});
				
				if(itemMapId != null && itemMapId.length > 0){
					record.delete({
						type: 'customrecord_avaitemmapping',
						id: itemMapId
					});
				}
				
				var subtype = nRecord.getValue({
					fieldId: 'subtype'
				});
				
				var avaConfigObjRec = ava_library.mainFunction('AVA_LoadValuesToGlobals', '');
				
				if((subtype != 'Purchase') && (AVA_RecordTypeValidation(nRecord.type, subtype) == 'T') && (avaConfigObjRec['AVA_DisableItemSync'] == false)){
					if(context.type == context.UserEventType.CREATE || context.type == context.UserEventType.EDIT || context.type == context.UserEventType.COPY){
						try{
							if(nRecord.getValue({fieldId: 'isinactive'}) == false){
								var parametersValueChange = false;
								var imsRecDetails = AVA_SearchIMSRecord(nRecord.id);
								
								if(runtime.executionContext == runtime.ContextType.MAP_REDUCE && imsRecDetails['contexttype'] == 'MapReduce'){
									return;
								}
								else{
									if(context.type == context.UserEventType.EDIT){
										if(imsRecDetails['imsrecid'] != 'null'){
											var oRecord = context.oldRecord;
											
											if(nRecord.getValue('itemid') != oRecord.getValue('itemid')){
												parametersValueChange = true;
											}
											
											var itemDescription = '';
											var itemOldDescription = '';
											if(nRecord.type == 'assemblyitem' || nRecord.type == 'kititem' || nRecord.type == 'lotnumberedassemblyitem' || nRecord.type == 'serializedassemblyitem'){
												itemDescription = nRecord.getValue('description');
												itemOldDescription = oRecord.getValue('description');
											}
											else{
												itemDescription = nRecord.getValue('salesdescription');
												itemOldDescription = oRecord.getValue('salesdescription');
											}
											
											if(itemDescription != itemOldDescription){
												parametersValueChange = true;
											}
											
											var taxCodeValue = nRecord.getValue('custpage_ava_taxcodemapping');
											if(!taxCodeValue){
												taxCodeValue = nRecord.getValue('custitem_ava_taxcode');
											}
											
											var taxCodeValueOld = oRecord.getValue('custpage_ava_taxcodemapping');
											if(!taxCodeValueOld){
												taxCodeValueOld = oRecord.getValue('custitem_ava_taxcode');
											}
											
											if(taxCodeValueOld != taxCodeValue){
												parametersValueChange = true;
											}
											
											if(runtime.isFeatureInEffect('BARCODES') == true){
												if(nRecord.getValue('upccode') != oRecord.getValue('upccode')){
													parametersValueChange = true;
												}
											}
										}
									}
									
									if((context.type == context.UserEventType.CREATE || context.type == context.UserEventType.COPY || imsRecDetails['imsrecid'] == 'null') || (parametersValueChange)){
										var responseItemId = '0';
										var subsidiaryDetails = nRecord.getValue('subsidiary');
										subsidiaryDetails = subsidiaryDetails.toString();
										subsidiaryDetails = subsidiaryDetails.split(',');
										var defCompanyId = ava_library.mainFunction('AVA_GetDefaultCompanyCode', subsidiaryDetails[0]);
										
										if(defCompanyId[1]){
											var details;
											
											if(avaConfigObjRec.AVA_AdditionalInfo3 != null && avaConfigObjRec.AVA_AdditionalInfo3.length > 0){
												details = avaConfigObjRec.AVA_AdditionalInfo3;
											}
											else if(avaConfigObjRec.AVA_AdditionalInfo != null && avaConfigObjRec.AVA_AdditionalInfo.length > 0){
												details = ava_commonFunction.mainFunction('AVA_General', (avaConfigObjRec.AVA_AccountValue + '+' + avaConfigObjRec.AVA_AdditionalInfo + '+' + avaConfigObjRec.AVA_AdditionalInfo1 + '+' + avaConfigObjRec.AVA_AdditionalInfo2));
											}
											
											var avaTaxDetails = ava_library.mainFunction('AVA_InitSignatureObject', avaConfigObjRec.AVA_ServiceUrl);
											
											if(imsRecDetails['imsitemid'] == '0'){
												var filterDetails = "?$filter=itemCode eq " + "'" + nRecord.getValue('itemid') + "'";
												var retrieveItemsForCompany = new avaTaxDetails.retrieveItemsForCompany(details, defCompanyId[1], filterDetails, '', avaConfigObjRec.AVA_AdditionalInfo3);
												
												var responseDetails = https.get({
													url: retrieveItemsForCompany.url,
													body: retrieveItemsForCompany.data,
													headers: retrieveItemsForCompany.headers
												});
												
												if(responseDetails.code == 200){
													var responseBodyDetails = JSON.parse(responseDetails.body);
													
													if(responseBodyDetails.value.length > 0){
														imsRecDetails['imsitemid'] = responseBodyDetails.value[0].id;
													}
												}
											}
											
											var itemDetails = AVA_ItemRecordToSyncBody(nRecord, avaTaxDetails, nRecord.type, imsRecDetails['imsitemid']);
											var itemToSyncCatalogue = itemDetails.itemToSyncCatalogue(details, defCompanyId[1], itemDetails, avaConfigObjRec.AVA_AdditionalInfo3);
											
											var response = https.post({
												url: itemToSyncCatalogue.url,
												body: itemToSyncCatalogue.data,
												headers: itemToSyncCatalogue.headers
											});
											
											var errorMessage = '';
											var responseBody = JSON.parse(response.body);
											
											if(response.code == 200){
												responseItemId = responseBody.result[0].itemId;
												
												if(responseBody.result[0].itemEvent == 'Error'){
													errorMessage = responseBody.result[0].errors[0];
												}
											}
											else{
												if(response.body){
													errorMessage = responseBody.error.message;
												}
											}
											
											AVA_CreateUpdateIMSRecord(nRecord, itemToSyncCatalogue.data, response.code, errorMessage, imsRecDetails['imsrecid'], responseItemId);
										}
										else{
											AVA_CreateUpdateIMSRecord(nRecord, '', '', 'Default company code not mapped on Avalara Configuration for this item subsidiary.', imsRecDetails['imsrecid'], responseItemId);
										}
									}
								}
							}
						}
						catch(e){
							log.error('itemToSyncCatalogue', 'ERROR NAME = ' + e.name + ', ERROR TYPE = ' + e.type + ', ERROR MESSAGE = ' + e.message);
						}
					}
					else if(context.type == context.UserEventType.DELETE){
						try{
							var details;
							var subsidiaryDetails = nRecord.getValue('subsidiary');
							subsidiaryDetails = subsidiaryDetails.toString();
							subsidiaryDetails = subsidiaryDetails.split(',');
							var defCompanyId = ava_library.mainFunction('AVA_GetDefaultCompanyCode', subsidiaryDetails[0]);
							
							if(avaConfigObjRec.AVA_AdditionalInfo3 != null && avaConfigObjRec.AVA_AdditionalInfo3.length > 0){
								details = avaConfigObjRec.AVA_AdditionalInfo3;
							}
							else if(avaConfigObjRec.AVA_AdditionalInfo != null && avaConfigObjRec.AVA_AdditionalInfo.length > 0){
								details = ava_commonFunction.mainFunction('AVA_General', (avaConfigObjRec.AVA_AccountValue + '+' + avaConfigObjRec.AVA_AdditionalInfo + '+' + avaConfigObjRec.AVA_AdditionalInfo1 + '+' + avaConfigObjRec.AVA_AdditionalInfo2));
							}
							
							var avaTaxDetails = ava_library.mainFunction('AVA_InitSignatureObject', avaConfigObjRec.AVA_ServiceUrl);
							var itemCode = encodeURIComponent(nRecord.getValue('itemid'));
							var deleteSingleItem = avaTaxDetails.deleteSingleItem(details, defCompanyId[1], itemCode, avaConfigObjRec.AVA_AdditionalInfo3);
							
							var response = https.delete({
								url: deleteSingleItem.url,
								body: deleteSingleItem.data,
								headers: deleteSingleItem.headers
							});
							log.debug('DeletedItem', 'itemCode = ' + nRecord.getValue('itemid') + ', responseCode = ' + response.code + ', responseBody = ' + response.body);
							
							var imsRecDetails = AVA_SearchIMSRecord(nRecord.id);
							if(imsRecDetails['imsrecid'] != 'null'){
								AVA_DeleteIMSRecord(imsRecDetails['imsrecid']);
							}
						}
						catch(e){
							log.error('DeletedItem', 'ERROR NAME = ' + e.name + ', ERROR TYPE = ' + e.type + ', ERROR MESSAGE = ' + e.message);
						}
					}
				}
			}
		}
				
		function AVA_ItemRecordToSyncBody(nRecord, avaTaxDetails, itemType, imsItemId){
			try{
				var itemDetails = new avaTaxDetails.itemsCatalog();
				
				itemDetails.itemId = imsItemId;
				
				var itemId = nRecord.getValue('itemid');
				itemDetails.itemCode = (itemId != null) ? itemId.substring(0, 50) : '';
				
				var itemDescription = '';
				if(itemType == 'assemblyitem' || itemType == 'kititem' || itemType == 'lotnumberedassemblyitem' || itemType == 'serializedassemblyitem'){
					itemDescription = nRecord.getValue('description');
				}
				else{
					itemDescription = nRecord.getValue('salesdescription');
				}
				itemDetails.description = (itemDescription != null) ? itemDescription.substring(0, 255) : '';
				
				var taxCodeValue = nRecord.getValue('custpage_ava_taxcodemapping');
				if(!taxCodeValue){
					taxCodeValue = nRecord.getValue('custitem_ava_taxcode');
				}
				
				itemDetails.taxCode = taxCodeValue != null ? taxCodeValue.substring(0, 50) : '';
				
				if(runtime.isFeatureInEffect('BARCODES') == true){
					var itemUPC = nRecord.getValue('upccode');
					itemDetails.upc = itemUPC != null ? itemUPC.substring(0, 50) : '';
				}
				
				itemDetails.source = 'NetSuite';
				return itemDetails;
			}
			catch(e){
				log.error('AVA_ItemRecordToSyncBody', 'ERROR NAME = ' + e.name + ', ERROR TYPE = ' + e.type + ', ERROR MESSAGE = ' + e.message);
			}
		}
		
		function AVA_CreateUpdateIMSRecord(nRecord, requestData, responseCode, errorMessage, imsRecId, responseItemId){
			try{
				if(imsRecId == 'null' || !imsRecId){
					var imsDetailsRecObj = record.create({
						type: 'customrecord_avaimsdetails'
					});
					imsDetailsRecObj.setValue({
						fieldId: 'custrecord_ava_imsitem',
						value: nRecord.id
					});
					imsDetailsRecObj.setValue({
						fieldId: 'custrecord_ava_imsiteminternalid',
						value: nRecord.id
					});
				}
				else{
					var imsDetailsRecObj = record.load({
						type: 'customrecord_avaimsdetails',
						id: imsRecId
					});
				}
				
				var todayDate = new Date();
				var userId = runtime.getCurrentUser().id;
				
				if(responseItemId != '0'){
					imsDetailsRecObj.setValue({
						fieldId: 'custrecord_ava_imsitemid',
						value: responseItemId
					});
				}
				
				imsDetailsRecObj.setValue({
					fieldId: 'custrecord_ava_imsdate',
					value: todayDate
				});
				
				if(userId != null && userId > 0){
					imsDetailsRecObj.setValue({
						fieldId: 'custrecord_ava_imsuser',
						value: userId
					});
				}
				
				imsDetailsRecObj.setValue({
					fieldId: 'custrecord_ava_imsrequestdata',
					value: requestData
				});
				imsDetailsRecObj.setValue({
					fieldId: 'custrecord_ava_imsresponsecode',
					value: responseCode
				});
				imsDetailsRecObj.setValue({
					fieldId: 'custrecord_ava_imsresponseerror',
					value: errorMessage
				});
				var imsDetailsRecId = imsDetailsRecObj.save({
					enableSourcing: true,
					ignoreMandatoryFields: true
				});
			}
			catch(e){
				log.error('AVA_CreateUpdateIMSRecord', 'ERROR NAME = ' + e.name + ', ERROR TYPE = ' + e.type + ', ERROR MESSAGE = ' + e.message);
			}
		}
		
		function AVA_DeleteIMSRecord(imsRecId){
			try{
				record.delete({
					type: 'customrecord_avaimsdetails',
					id: imsRecId
				});
			}
			catch(e){
				log.error('AVA_DeleteIMSRecord', 'ERROR NAME = ' + e.name + ', ERROR TYPE = ' + e.type + ', ERROR MESSAGE = ' + e.message);
			}
		}
		
		function AVA_SearchIMSRecord(itemInternalId){
			var imsRecDetails = [];
			imsRecDetails['imsrecid'] = 'null';
			imsRecDetails['imsitemid'] = '0';
			imsRecDetails['contexttype'] = '';
			
			try{
				var avaimsdetailsSearchObj = search.create({
				   type: "customrecord_avaimsdetails",
				   filters:
				   [
					  ["custrecord_ava_imsiteminternalid", "equalto", itemInternalId]
				   ],
				   columns:
				   [
					  search.createColumn({name: "custrecord_ava_imsitemid"}),
					  search.createColumn({name: "custrecord_ava_imscontexttype"})
				   ]
				});
				avaimsdetailsSearchObj.run().each(function(result){
				   imsRecDetails['imsrecid'] = result.id;
				   imsRecDetails['imsitemid'] = (result.getValue({name: "custrecord_ava_imsitemid"}) != '') ? result.getValue({name: "custrecord_ava_imsitemid"}) : '0';
				   imsRecDetails['contexttype'] = result.getValue({name: "custrecord_ava_imscontexttype"});
				   return true;
				});
			}
			catch(e){
				log.error('AVA_SearchIMSRecord', 'ERROR NAME = ' + e.name + ', ERROR TYPE = ' + e.type + ', ERROR MESSAGE = ' + e.message);
			}
			
			return imsRecDetails;
		}
		
		function AVA_AddLogsSubList(cForm, ui, nRecord){
			try{
				var logSubList = cForm.addSublist({
					id: 'custpage_avanotestab',
					label: 'Logs',
					tab: 'custpage_avatab',
					type: ui.SublistType.STATICLIST
				});
				logSubList.addField({
					id: 'custpage_avaimsdate',
					label: 'Date',
					type: ui.FieldType.TEXT
				});
				logSubList.addField({
					id: 'custpage_avaimsuser',
					label: 'User',
					type: ui.FieldType.TEXT
				});
				logSubList.addField({
					id: 'custpage_avaimsrequestdata',
					label: 'Request Data',
					type: ui.FieldType.TEXTAREA
				});
				logSubList.addField({
					id: 'custpage_avaimsresponsecode',
					label: 'Response Code',
					type: ui.FieldType.TEXT
				});
				logSubList.addField({
					id: 'custpage_avaimsresponseerror',
					label: 'Error',
					type: ui.FieldType.TEXT
				});
				
				var avaimsdetailsSearchObj = search.create({
				   type: "customrecord_avaimsdetails",
				   filters:
				   [
					  ["custrecord_ava_imsitem", "anyof", nRecord.id]
				   ],
				   columns:
				   [
					  "custrecord_ava_imsdate",
					  "custrecord_ava_imsuser",
					  "custrecord_ava_imsrequestdata",
					  "custrecord_ava_imsresponsecode",
					  "custrecord_ava_imsresponseerror"
				   ]
				});
				var searchResult = avaimsdetailsSearchObj.run();
				
				var i = 0;
				searchResult.each(function(result){
					if(result.getValue('custrecord_ava_imsdate')){
						logSubList.setSublistValue({
							id: 'custpage_avaimsdate',
							line: i,
							value: result.getValue('custrecord_ava_imsdate')
						});
					}
					if(result.getValue('custrecord_ava_imsuser')){
						logSubList.setSublistValue({
							id: 'custpage_avaimsuser',
							line: i,
							value: result.getText('custrecord_ava_imsuser')
						});
					}
					if(result.getValue('custrecord_ava_imsrequestdata')){
						logSubList.setSublistValue({
							id: 'custpage_avaimsrequestdata',
							line: i,
							value: result.getValue('custrecord_ava_imsrequestdata')
						});
					}
					if(result.getValue('custrecord_ava_imsresponsecode')){
						logSubList.setSublistValue({
							id: 'custpage_avaimsresponsecode',
							line: i,
							value: result.getValue('custrecord_ava_imsresponsecode')
						});
					}
					if(result.getValue('custrecord_ava_imsresponseerror')){
						logSubList.setSublistValue({
							id: 'custpage_avaimsresponseerror',
							line: i,
							value: result.getValue('custrecord_ava_imsresponseerror')
						});
					}
					
					i++;
					return true;
				});
			}
			catch(e){
				log.error('AVA_AddLogsSubList', 'ERROR NAME = ' + e.name + ', ERROR TYPE = ' + e.type + ', ERROR MESSAGE = ' + e.message);
			}
		}
		
		function AVA_RecordTypeValidation(recordType, subtype){
			try{
				var validationFlag = 'F';
				
				switch(recordType){
					case 'inventoryitem':
					case 'lotnumberedinventoryitem':
					case 'serializedinventoryitem':
					case 'assemblyitem':
					case 'lotnumberedassemblyitem':
					case 'serializedassemblyitem':
					case 'kititem':
					case 'downloaditem':
					case 'giftcertificateitem':
						validationFlag = 'T'; 
						break;
					
					case 'noninventoryitem':
					case 'noninventorysaleitem':
					case 'otherchargeitem':
					case 'otherchargesaleitem':
					case 'serviceitem':
					case 'servicesaleitem':
						if(subtype != 'Purchase'){
							validationFlag = 'T'; 
						}
						break;
							
					default:
						break;
				}
				return validationFlag;
			}
			catch(e){
				log.error('AVA_RecordTypeValidation', 'ERROR NAME = ' + e.name + ', ERROR TYPE = ' + e.type + ', ERROR MESSAGE = ' + e.message);
			}
		}
		
		return{
			beforeLoad: AVA_InventoryTabBeforeLoad,
			beforeSubmit: AVA_InventoryTabBeforeSubmit,
			afterSubmit: AVA_InventoryTabAfterSubmit
		};
	}
);