/**
 * @description Validation functions for SPL Item Fulfillments
 *
 * </br><b>Deployed On:</b> Item Fulfillment
 * </br><b>Excecution Context:</b> ALL
 * </br><b>Event Type/Mode:</b> All
 * </br><b>Entry Points:</b> saveRecord
 *
 * @NApiVersion 2.1
 * @NScriptType ClientScript
 * @NAmdConfig /SuiteScripts/config.json
 *
 * <AUTHOR>
 * @module spl_validate_item_fulfillment
 */

//@ts-ignore
define([
	"require",
	"GetTransactionAndItemObjsLib",
	"../../Classes/vlmd_custom_error_object",
], function (require, getTransactionItemObjsLib) {
	/** @type {import("../../Classes/vlmd_custom_error_object").CustomErrorObject} */

	const CustomErrorObject = require("../../Classes/vlmd_custom_error_object");
	const customErrorObject = new CustomErrorObject();

	var alertArr = [];
	var isValid = true;

	var validationFunctions = (function () {
		function _setInvalid(message, setHardError) {
			if (setHardError) {
				isValid = false;
			}

			alertArr.push(message);
		}

		function validateShipByTruck(transactionObj, itemObj) {
			if (transactionObj.customerIsShipByTruck && itemObj.itemIsShipByTruck) {
				_setInvalid(
					`Please truck glove orders with 30 cases or more for ${transactionObj.customerName}.`,
					false
				);
			}
		}

		function displayAlert() {
			if (alertArr.length > 0) {
				var alertText = alertArr[0]; //Set message up as a quick fix for now, displaying only the first alert. Will need to update to consolidate the message.
				// alertArr.forEach(function (message) {
				// 	alertText += message + "\n";
				// });
				alert(alertText);
			}
		}

		return {
			validateShipByTruck,
			displayAlert,
		};
	})();

	function pageInit(context) {}

	function saveRecord(context) {
		const transaction = context.currentRecord;
		const transactionType = transaction.type;
		const transactionIsIF = transactionType == "itemfulfillment";

		try {
			isValid = true;
			alertArr = [];
			const subsidiary = transaction.getValue("subsidiary");

			if (
				subsidiary != 1 || //Supplyline
				!transactionIsIF
			) {
				return isValid;
			}

			const transactionObj =
				getTransactionItemObjsLib.getIFTransactionObjForSave(transaction);

			if (!transactionObj) {
				throw customErrorObject.updateError({
					errorType: customErrorObject.ErrorTypes.NO_VALUE_RETURNED,
					summary: "TRANSACTION_OBJ_NOT_RETURNED",
					details: `No value returned`,
				});
			}

			const transactionLineCount = transaction.getLineCount({
				sublistId: "item",
			});

			for (let x = 0; x < transactionLineCount; x++) {
				const itemObj = getTransactionItemObjsLib.getIFItemObjForSave(
					transaction,
					x
				);

				if (!itemObj) {
					return true; //This is an empty line - no need to validate //todo might not be necessary
				}

				validationFunctions.validateShipByTruck(transactionObj, itemObj);
			}

			validationFunctions.displayAlert();

			return isValid; //Can set isValid to be false to block save if necessary in the future.
		} catch (err) {
			customErrorObject.throwError({
				summaryText: `ERROR_SAVING_ITEM_FULFILLMENT ${transaction.id}`,
				error:err,
			});
			throw "Save Record Error Getting Value for " + transaction.id + " : " + e;
		}
	}

	return {
		pageInit,
		saveRecord,
	};
});
