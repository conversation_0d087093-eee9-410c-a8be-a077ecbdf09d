/**
 * @description Set the Shipping Tax Code of the Credit Memo based on the Invoice applied to it
 *
 * </br><b>Deployed On:</b> Credit Memo
 * </br><b>Excecution Context:</b> USER EVENT
 * </br><b>Event Type/Mode:</b> CREATE/EDIT
 * </br><b>Entry Points:</b> AFTER SUBMIT
 *
 * @NApiVersion 2.1
 * @NScriptType UserEventScript
 * @NAmdConfig /SuiteScripts/config.json
 *
 * <AUTHOR>
 * @module spl_set_credit_memo_tax_code_ue
 */

define([
	"require",
	"N/record",
	"./../Classes/vlmd_custom_error_object",
	"N/query",
], (/** @type {any} */ require) => {
	const record = require("N/record");
	/** @type {import("./../Classes/vlmd_custom_error_object").CustomErrorObject} */
	const CustomErrorObject = require("./../Classes/vlmd_custom_error_object");
	const query = require("N/query");

	function afterSubmit(context) {
		if (
			context.type === context.UserEventType.CREATE ||
			context.type === context.UserEventType.EDIT
		) {
			let newRecord = context.newRecord;
			const customErrorObject = new CustomErrorObject();
			try {
				let creditMemoId = newRecord.id;

				let getShippingMethod = newRecord.getValue({ fieldId: "shipmethod" });
				let getShippingTaxCode = newRecord.getValue({
					fieldId: "shippingtaxcode",
				});

				if (!getShippingMethod || getShippingTaxCode) {
					return;
				}

				const sqlQuery = `SELECT DISTINCT
                currentcreditmemo.id AS creditmemoid,
                 currentrma.previousdoc AS rmaid,
                currentinv.previousdoc AS createdfromid,
                invoicetline.type AS createdfromtype
            
             FROM
                transaction AS currentcreditmemo
                LEFT JOIN
                   previoustransactionlinelink AS currentrma
                   ON currentrma.nextdoc = currentcreditmemo.id 
                LEFT JOIN
                    previoustransactionlinelink AS currentinv
                ON currentinv.nextdoc = currentrma.previousdoc
                LEFT JOIN
                    transactionline.transaction AS invoicetline
                ON invoicetline.id = currentinv.previousdoc
             WHERE
                currentcreditmemo.id = ? and invoicetline.type = 'CustInvc'`;

				let results = query
					.runSuiteQL({
						query: sqlQuery,
						params: [creditMemoId],
					})
					.asMappedResults();

				let getInvoiceShipTaxCode = getInvoiceTaxCode(
					results[0]["createdfromid"]
				);

				newRecord.setValue({
					fieldId: "shippingtaxcode",
					value: getInvoiceShipTaxCode,
				});
			} catch (error) {
				customErrorObject.throwError({
					summaryText: `ERROR_UPDATING_CREDIT_MEMO`,
					error: error,
					recordId: newRecord.id,
					recordType: `CREDIT_MEMO`,
					errorWillBeGrouped: true,
				});
			}
		}
	}

	function getInvoiceTaxCode(invoiceId) {
		let getInvoiceObj = record.load({
			type: record.Type.INVOICE,
			id: invoiceId,
			isDynamic: false,
		});

		let getInvoiceTaxCode = getInvoiceObj.getValue({
			fieldId: "shippingtaxcode",
		});

		return getInvoiceTaxCode;
	}

	return { afterSubmit };
});
