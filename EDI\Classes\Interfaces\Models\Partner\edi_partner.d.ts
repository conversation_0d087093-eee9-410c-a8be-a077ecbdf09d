/**
 * @description EDI Partner Class Interface
 *
 * <AUTHOR> <<EMAIL>>
 */

export type EDIParsingParams = {
    /** Field delimiter */
    fieldDelimiter: string;
    /** Segment delimeter */
    segmentDelimiter: string;
    /** File delimeter */
    fileDelimiter: string;
    /** EDI version number */
    ediVersion: string;
    /** Receiver qualifier */
    receiverQualifier: string;
    /** Receiver ID */
    receiverId: string;
};

export type EDIParsingInformation = {
    /** Field delimiter */
    fieldDelimiter: string;
    /** Segment delimeter */
    segmentDelimiter: string;
    /** Formatting Information */
    formattingInfo: {[key:string]: any}[];
    /** ISA GS Information */
    isaGsInfo: {[key:string]: any}[];
    /** Sender Information */
    senderInfo: {[key:string]: any}[];
    /** Receiver Information */
    receiverInfo: {[key:string]: any}[];
};

export type EDIPartnerConstructorParams = {
    /** in or out */
    direction: string;
    /** Transaction or Record Number ID */
    transactionType: string;
}

export type EDIPartnerOverrides = {
    /** Production Directory override */
    prodDirectory: string;
    /** Test Directory override */
    testDirectory: string;
    /** Reference Directory override */
    referenceDirectory: string;
    /** Username override */
    username: string;
    /** Username to access Reference folders */
    referenceUsername: string;
    /** Password override */
    passwordGuid: string;
    /** Password to access Reference folders*/
    referencePasswordGuid: string;
}

export type EDITemplate {
    /** ISA, GS, BIG, REF, N, ITD, DTM, FOB */
    header: string;
    /** REF */
    tracking: string;
    /** IT, PID, TDS */
    item: string;
    /** SAC */
    sac: string;
    /** CTT, SE, GE, IEA */
    summary: string;
}

export interface EDIBillingAddress {
    /** Addressee */
    addressee: string;
    /** Street */
    street: string;
    /** City */
    city: string;
    /** State */
    state: string;
    /** ZIP */
    zip: string;
    /** Country */
    country: string;
}

export interface EDIPartnerInterface {
    /** Internal ID */
    id: string;
    /** Purchasing Software ID */
    purchasingSoftwareId: string;
    /** Partner Name */
    name: string;
    /** Production Directory Path */
    prodDirectory: string;
    /** Test Directory Path */
    testDirectory: string;
    /** Reference Directory Path */
    referenceDirectory: string;
    /** Subsidiary */
    subsidiary: string;
    /** Parent */
    parent: string;
    /** Billing Address */
    billingAddress: EDIBillingAddress;
    /** Parsing Delimiters */
    delimiters: EDIParsingParams;
    /** Get information for parsing the file from the Partner */
    getParsingInformation({delimiters: EDIParsingParams, direction: string}): EDIParsingInformation;
}

export interface EDICustomerInterface extends EDIPartnerInterface {
    /** Include Is Replacement For */
    shouldIncludeIsReplacementFor: boolean;
    /** Include Deactivate Item */
    shouldIncludeDeactivateItem: boolean;
    /** Generate SuiteQL Query String to lookup customer record */
    generateQueryString(identifier: string): string;
}

export interface EDIVendorInterface extends EDIPartnerInterface {}