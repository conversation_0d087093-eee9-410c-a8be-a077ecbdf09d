/**
 * @description Updates all price tier on a customer based on the chosen tier
 *
 * </br><b>Deployed On:</b> Customers
 * </br><b>Execution Context:</b> ALL
 * </br><b>Event Type/Mode:</b> ALL
 * </br><b>Entry Points:</b> fieldChanged
 *
 * @NApiVersion 2.1
 * @NScriptType ClientScript
 * @NAmdConfig /SuiteScripts/config.json
 *
 * <AUTHOR>
 * @module spl_update_price_tier_cs
 */

define([
	"require",
	"N/ui/dialog",
	"N/ui/message",
	"N/runtime",
	"../../Classes/vlmd_custom_error_object",
	"../../Classes/spl_price_tier",
], (require) => {
	const dialog = require("N/ui/dialog");
	const message = require("N/ui/message");
	const runtime = require("N/runtime");

	/** @type {import("../../Classes/vlmd_custom_error_object").CustomErrorObject} */
	const CustomErrorObject = require("../../Classes/vlmd_custom_error_object");
	const customErrorObject = new CustomErrorObject();

	/** @type {import("../../Classes/spl_price_tier").SplPriceTier} */
	const SplPriceTier = require("../../Classes/spl_price_tier");

	/**
	 *
	 * @param {import("@hitc/netsuite-types/N/types").EntryPoints.Client.fieldChangedContext} context Field Changed script context
	 * @returns {void}
	 */
	function fieldChanged(context) {
		try {
			if (context.fieldId != "custentity_spl_price_tier") {
				return;
			}

			let currentUserID = runtime?.getCurrentUser()?.id;

			let authorizedUserIDs = runtime
				.getCurrentScript()
				.getParameter({ name: "custscript_spl_tier_change_authrzd_users" })
				.split(",");

			let isAuthorizedUser = authorizedUserIDs.find(
				(authorizedUserId) => authorizedUserId == currentUserID
			);

			if (!isAuthorizedUser) {
				dialog.alert({
					title: "Restricted Feature",
					message: `You do not have permission to change the price tier for customers. Please reach out to your manager for assistance. [Programming: add user ID to script deployment param].`,
				});

				return;
			}

			const customerRecord = context.currentRecord;

			if (customerRecord.getValue("subsidiary") != 1) {
				dialog.alert({
					title: "Restricted Feature",
					message: `The price tier feature is for Supplyline customers only.`,
				});

				return;
			}

			let chosenTierValue = Number(
				customerRecord.getValue("custentity_spl_price_tier")
			);

			if (!chosenTierValue) {
				dialog.alert({
					title: "ERROR",
					message: `Please enter a price tier for this customer.`,
				});

				return;
			}

			let tierObj = new SplPriceTier(chosenTierValue, customerRecord);

			if (!tierObj) {
				throw customErrorObject.updateError({
					errorType: customErrorObject.ErrorTypes.NO_VALUE_RETURNED,
					summary: "MISSING_TIER_OBJ",
					details: `No price tier obj gotten for customer ${customerRecord.id}`,
				});
			}

			var processingMessage = message.create({
				title: "Processing",
				message: "Loading tier levels...this may take a moment.",
				type: message.Type.INFORMATION,
			});

			processingMessage.show();

			setTimeout(() => {
				tierObj.setPriceLevels();
			}, 100);

			setTimeout(processingMessage.hide(), 5000);
		} catch (e) {
			customErrorObject.throwError({
				summaryText: `ERROR_UPDATING_PRICE_TIER`,
				error: e,
				recordId: context.currentRecord.id,
				recordType: `CUSTOMER`,
			});
		}
	}

	return {
		fieldChanged,
	};
});
