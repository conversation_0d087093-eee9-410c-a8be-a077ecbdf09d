/**
 * Primarily used to create Purchase Orders with Drop Ship items
 * from a Sales Order to address remaining items for LDEV-1162
 *
 * @NApiVersion 2.1
 * @module spl_create_purchase_order_lib
 * <AUTHOR>
 */

//@ts-ignore
define(["N/record"], function (record) {
	/**
	 * Create PO with drop ship items used on the parent SO
	 *
	 * @typedef ProcessingError
	 * @property {string} logMessage Error message
	 * @property {boolean} [programmingError] Defaults to false, true if programming error
	 *
	 * @param {object} purchaseOrderObj Contains data to create PO
	 * @param {number} salesOrderId ID of the SO to be set as parent
	 * @returns {{purchaseOrderId: number|void, processingErrorsArr: ProcessingError[]}} ID of PO and errors from processing
	 */
	function createPoWithDropShipItems(purchaseOrderObj, salesOrderId) {
		const SMXVenturesVendorId = 9109;
		const processingErrorsArr = [];

		if (
			!salesOrderId ||
			!purchaseOrderObj.customerInternalId ||
			!purchaseOrderObj.items ||
			!Array.isArray(purchaseOrderObj.items) ||
			purchaseOrderObj.items.length < 1
		) {
			processingErrorsArr.push({
				logMessage: "Cannot initialize the Drop-ship Purchase Order",
			});

			return {
				purchaseOrderId: -1,
				processingErrorsArr,
			};
		}

		const purchaseOrderRecord = createPurchaseOrderRecord();
		setPurchaseOrderItems(purchaseOrderRecord);
		const purchaseOrderId = savePurchaseOrder(purchaseOrderRecord);

		return {
			purchaseOrderId,
			processingErrorsArr,
		};

		/**
		 * Create and initialize a dropship PO record
		 *
		 * @returns {import("@hitc/netsuite-types/N/record").Record} NS PO transaction record
		 */
		function createPurchaseOrderRecord() {
			return record.create({
				type: record.Type.PURCHASE_ORDER,
				isDynamic: true,
				defaultValues: {
					recordmode: "dynamic",
					dropship: true,
					entity: SMXVenturesVendorId,
					custid: purchaseOrderObj.customerInternalId,
				},
			});
		}

		/**
		 * Set PO Record item lines
		 * We assume that only items to be added to the PO
		 * are present in the items array of purchaseOrderObj
		 *
		 * @param {import("@hitc/netsuite-types/N/record").Record} purchaseOrderRecord NS PO transaction record
		 * @returns {void}
		 */
		function setPurchaseOrderItems(purchaseOrderRecord) {
			const items = purchaseOrderObj.items;
			items.forEach((item) => {
				try {
					purchaseOrderRecord.selectNewLine({
						sublistId: "item",
					});
					purchaseOrderRecord.setCurrentSublistValue({
						sublistId: "item",
						fieldId: "item",
						value: item.internalId,
					});
					purchaseOrderRecord.setCurrentSublistValue({
						sublistId: "item",
						fieldId: "quantity",
						value: item.quantity,
					});

					purchaseOrderRecord.setCurrentSublistValue({
						sublistId: "item",
						fieldId: "units",
						value: item.uomId,
					});
					purchaseOrderRecord.setCurrentSublistValue({
						sublistId: "item",
						fieldId: "orderdoc",
						value: salesOrderId,
					});
					purchaseOrderRecord.setCurrentSublistValue({
						sublistId: "item",
						fieldId: "rate",
						value: item.vendorCost,
					});
					purchaseOrderRecord.setCurrentSublistValue({
						sublistId: "item",
						fieldId: "orderline",
						value: item.line + 1,
					});
					purchaseOrderRecord.commitLine({
						sublistId: "item",
					});
				} catch (error) {
					processingErrorsArr.push({
						logMessage: `${item.itemName} not set successfully: ${error.message}`,
						programmingError: true,
					});
				}
			});
		}

		/**
		 * Save the updated purchase order
		 *
		 * @param {import("@hitc/netsuite-types/N/record").Record} purchaseOrderRecord NS PO transaction record
		 * @returns {number|void} Internal ID of the created PO
		 */
		function savePurchaseOrder(purchaseOrderRecord) {
			try {
				purchaseOrderRecord.setValue("createdfrom", salesOrderId);
				purchaseOrderRecord.setValue(
					"shipaddress",
					purchaseOrderObj.shippingAddress
				);
				return purchaseOrderRecord.save({
					enableSourcing: true,
					ignoreMandatoryFields: true,
				});
			} catch (error) {
				processingErrorsArr.push({
					logMessage: `EDI File not saved: ${error.message}`,
				});
			}
		}
	}

	return {
		createPoWithDropShipItems,
	};
});
