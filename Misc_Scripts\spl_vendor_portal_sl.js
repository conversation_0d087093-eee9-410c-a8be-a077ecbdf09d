/**
* @NApiVersion 2.1
* @NScriptType Suitelet
* @NModuleScope Public

* @description a SL to generate price lists on demand
* <AUTHOR>
*/

/**Define global variables, needs to be var vs. let for scope */
var scriptURL, log, query, render, runtime, serverWidget, url;

define([
	"require",
	"N/log",
	"N/query",
	"N/render",
	"N/runtime",
	"N/ui/serverWidget",
	"N/url",
], main);

function main(require, logModule, queryModule) {
	log = logModule;
	query = queryModule;
	render = require("N/render");
	runtime = require("N/runtime");
	serverWidget = require("N/ui/serverWidget");
	url = require("N/url");

	return {
		onRequest: function (context) {
			/**Build a URL to redirect to this page*/
			scriptURL = url.resolveScript({
				scriptId: runtime.getCurrentScript().id,
				deploymentId: runtime.getCurrentScript().deploymentId,
				returnExternalURL: false,
			});

			if (context.request.method == "GET") {
				handleGetRequest(context);
			} else {
				handlePostRequest(context);
			}
		},
	};
}

function actionsView() {
	return /*html*/ `
		<button type="button" class="btn d-inline-block align-bottom btn-info"  onclick="viewBills()" accesskey="r">View Bills</button>	
`;
}

function billDatesSelect() {
	return /*html*/ `
	<div class="form-group mb-0 mr-2 d-inline-block" style="min-width: 300px;">
		<h3><label for="datePicker" class="text-left d-block">Bill Dates</label></h3>
		<div class="input-daterange input-group" id="datePicker">
			<input type="text" class="input form-control" name="billStartDate" placeholder="Start Date" id = 'billStartDate'>
			<input type="text" class="input form-control" name="billEndDate" placeholder="End Date" id = 'billEndDate'>
		</div>
	</div>  `;
}

function billStatusSelect() {
	return /*html*/ `
		<div class="form-group mb-0 mr-2 d-inline-block" style="min-width: 300px;">
			<h3><label for="billStatusSelect" class="text-left d-block">Bill Status</label></h3>
			<select class="form-control" id="billStatusSelect">
				<option selected value="all">-All-</option>
				<option value="open">Open</option>
				<option value="paid">Paid</option>
			</select>
		</div>`;
}

function chooseParametersView() {
	return /*html*/ `
		<div id="chooseParametersViewDiv" class="container d-flex align-items-end justify-content-center">
			<div class="row">
				<div class="col text-center">
				${billStatusSelect()}      
				${subsidiarySelect()}  				
				<!--${paymentMethodSelect()}-->		
				${billDatesSelect()}   				
				${actionsView()}
				</div>
			</div>
		</div>`;
}

function generatePdf(context) {
	try {
		let transactionId = parseInt(context.request.parameters["transactionId"]);
		let transactionType = context.request.parameters["transactionType"];

		let pdfFile = render.transaction({
			entityId: transactionId,
			formId: transactionType == "bill" ? 147 : 154,
		});

		context.response.writeFile({ file: pdfFile, isInline: true });
	} catch (e) {
		log.error({ title: "generateFile Error", details: e });

		context.response.write("Error: " + e);
	}
}

function generateHTML() {
	return /*html*/ `
	    <!--jQuery-->
		<script src="https://code.jquery.com/jquery-3.3.1.slim.min.js" integrity="sha384-q8i/X+965DzO0rT7abK41JStQIAqVgRVzpbzo5smXKp4YfRvH+8abtTE1Pi6jizo" crossorigin="anonymous"></script>
		<!--Popper--> 
		<script src="https://cdn.jsdelivr.net/npm/popper.js@1.14.7/dist/umd/popper.min.js" integrity="sha384-UO2eT0CpHqdSJQ6hJty5KVphtPhzWj9WO1clHTMGa3JDZwrnQq4sF86dIHNDz0W1" crossorigin="anonymous"></script>
		<!--Bootstrap--> 
		<script src="https://cdn.jsdelivr.net/npm/bootstrap@4.3.1/dist/js/bootstrap.min.js" integrity="sha384-JjSmVgyd0p3pXB1rRibZUAYoIIy6OrQ6VrjIEaFf/nJGzIxFDsf4x0xIM+B07jRM" crossorigin="anonymous"></script>
	  	<!--Bootstrap-DatePicker-->
		<script src="https://cdn.jsdelivr.net/npm/bootstrap-datepicker@1.9.0/dist/js/bootstrap-datepicker.min.js"></script>
		<!--DataTables-->
		<script type="text/javascript" charset="utf8" src="https://cdn.datatables.net/1.10.25/js/jquery.dataTables.js"></script>
		
		<!--Bootstrap CSS-->
		<link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@4.3.1/dist/css/bootstrap.min.css" integrity="sha384-ggOyR0iXCbMQv3Xipma34MD+dH/1fQ784/j6cY/iJTQUOhcWr7x9JvoRxT2MZw1T" crossorigin="anonymous">
		<!--Bootstrap-DatePicker CSS-->
		<link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-datepicker@1.9.0/dist/css/bootstrap-datepicker.min.css">
		<!--DataTables CSS-->
		<link rel="stylesheet" type="text/css" href="https://cdn.datatables.net/1.10.25/css/jquery.dataTables.css">
		<!--Icons CSS-->
		<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css">

		<style type="text/css">			
				input[type="text"],
				input[type="search"],
				textarea,
				button {
					outline: none;
					box-shadow: none !important;
					border: 1px solid #ccc !important;
				}
				p,
				pre {
					font-size: 10pt;
				}

				td,
				th {
					font-size: 10pt;
					border: 3px;
				}

				th {
					font-weight: bold;
				}
				.modal-content {
					height: 98vh; /* This makes the modal take up 90% of the viewport height */
				}

				.modal-body {
					max-height: calc(90vh - 120px); /* Adjust based on your header and footer height */
					overflow-y: auto;
				}

				/* This ensures the page takes up at least full viewport height */
				html, body {
					height: 100%;
					margin: 0;
				}

				/* Main page wrapper */
				.page-wrapper {
					min-height: 100%;
					display: flex;
					flex-direction: column;
				}

				/* Main content area */
				.main-content {
					flex: 1;  /* This makes the content area expand to fill available space */
				}
		</style>

		${htmlUI()}

		<script>
			var
				queryResponsePayload,
				fileLoadResponsePayload;

			window.jQuery = window.$ = jQuery;

			$(document).ready(function(){
				$('#datePicker').datepicker({
					format: 'mm/dd/yyyy',
					autoclose: true,
					todayHighlight: true,
					clearBtn: true,
					orientation: "auto"
				});
			});

			$('#portalUI').show();

			${jsFunctionGetBills()}
			${jsFunctionResponseGenerateTable()}
			${jsFunctionViewPDF()}
		</script>
		`;
}

function getBillsForVendorId(context, requestPayload) {
	try {
		var responsePayload;
		let records;

		var sessionScope = runtime.getCurrentSession();

		var paramsJsonObjInSession = sessionScope.get({ name: "paramsJsonObj" });

		if (requestPayload.paramsJsonObj === paramsJsonObjInSession) {
			records = JSON.parse(sessionScope.get({ name: "records" }));
		} else {
			records = new Array();
			var sqlQuery = getQueryForVendorId(requestPayload.paramsJsonObj);

			records = query
				.runSuiteQL({
					query: sqlQuery,
				})
				.asMappedResults();

			sessionScope.set({
				name: "records",
				value: JSON.stringify(records),
			});

			sessionScope.set({
				name: "paramsJsonObj",
				value: requestPayload.paramsJsonObj,
			});
		}

		responsePayload = { records };
	} catch (e) {
		log.error({ title: "generateHTMLPriceFile Error", details: e });
		responsePayload = { error: e };
	}

	context.response.write(JSON.stringify(responsePayload, null, 5));
}

function getQueryForVendorId(paramsJsonObj) {
	try {
		let {
			billStatusSelected,
			subsidiarySelected,
			paymentMethodSelected,
			billStartDateSelected,
			billEndDateSelected,
		} = JSON.parse(paramsJsonObj);

		let billStatusClause = {
			all: "",
			open: `AND BUILTIN.CF(b.status) IN ('VendBill:A', 'VendBill:D')`,
			paid: `AND BUILTIN.CF(b.status) = 'VendBill:B'`,
		};

		let paymentMethodDict = {
			ach: "3",
			check: "1",
			creditCard: "2",
		};

		let query = /*sql*/ `
			SELECT
				b.id bill_id,
				b.tranid bill_name,
				BUILTIN.DF(btl.subsidiary) company, 
				BUILTIN.DF(b.status) status,
				b.duedate due_date,
				btl.netamount * - 1 bill_total,
				p.id payment_id,
				p.transactionnumber payment_name,
				BUILTIN.DF(p.custbody_payment_method) payment_method,
				p.createddate payment_date,
				ptl.netamount * - 1 payment_total,
				b.memo memo
			FROM
				transaction b 
				JOIN
				transactionline btl 
				ON btl.transaction = b.id 
				AND btl.mainline = 'T' 
				LEFT JOIN
				NextTransactionLink ntl 
				ON b.id = ntl.previousdoc 
				LEFT JOIN
				transaction p 
				ON p.id = ntl.nextDoc 
				LEFT JOIN
				transactionline ptl 
				ON ptl.transaction = p.id 
				AND ptl.mainline = 'T' 
			WHERE
				b.entity = ${runtime.getCurrentUser().id}
				--When testing in SB: 742
				AND b.type IN 
				(
				'VendBill' 
				)
				${billStatusClause[billStatusSelected]}
				${subsidiarySelected ? "AND btl.subsidiary = " + subsidiarySelected : ""}
				${
					billStartDateSelected && billEndDateSelected
						? "AND TO_DATE(b.trandate, 'MM/DD/YYYY') BETWEEN TO_DATE('" +
						  billStartDateSelected +
						  "', 'MM/DD/YYYY' ) AND TO_DATE('" +
						  billEndDateSelected +
						  "', 'MM/DD/YYYY')"
						: ""
				}
				--//{paymentMethodSelected ? "AND p.custbody_payment_method  = " + paymentMethodDict[paymentMethodSelected]: ""}
			ORDER BY
				b.duedate DESC`;

		log.audit("Query (getQueryForVendorId) ", query);
		return query;
	} catch (e) {
		throw e;
	}
}

function handleGetRequest(context) {
	if (context.request.parameters.hasOwnProperty("function")) {
		/**If this is coming from a redirect, handle according to instructions sent */ if (
			context.request.parameters["function"] == "generatePdf"
		) {
			generatePdf(context);
		}
	} else {
		/**Coming to this page for the first time, load the start screen */

		var form = serverWidget.createForm({
			title: `Vendor Portal`,
			hideNavBar: true,
		});

		var htmlField = form.addField({
			id: "custpage_field_html",
			type: serverWidget.FieldType.INLINEHTML,
			label: "HTML",
		});

		htmlField.defaultValue = generateHTML();

		context.response.writePage(form);
	}
}

function handlePostRequest(context) {
	var requestPayload = JSON.parse(context.request.body);

	context.response.setHeader("Content-Type", "application/json");

	switch (requestPayload["function"]) {
		case "getBillsForVendorId":
			return getBillsForVendorId(context, requestPayload);
		case "generatePdf":
			return generatePdf(context, requestPayload);

		default:
			log.error({
				title: "Payload - Unsupported Function",
				details: requestPayload["function"],
			});
	}
}

function htmlUI() {
	return /*html*/ `
	<div class="page-wrapper">
		<!-- Your main content goes here -->
		<main class="main-content">
			<div class=".container-fluid" id="welcomeBannerUI" style="text-align: left;">	

					${welcomeBannerView()}
			</div>			

					<div class="container" id="portalUI" style="text-align: left;">	
					${chooseParametersView()}
					${resultsDivView()}			
					<div id="query" style="max-width: 100%; margin-top: 12px; display: none; overflow: auto; overflow-y: hidden;"></div>
									
			</div>			
		</main>		
	</div>
		`;
}

function jsFunctionResponseGenerateTable() {
	return `	
	function responseGenerateTable() {
		if ( queryResponsePayload.records.length > 0 ) {	
			let thead = '<thead class="thead-light">';                
            thead += '<tr>' +
				'<th class="th-sm">Company</th>' +
				'<th class="th-sm">Invoice</th>' +
				'<th class="th-sm">Status</th>' +
				'<th class="th-sm">Due Date</th>' +
				'<th class="th-sm">Invoice Total</th>' +
				'<th class="th-sm">View Bill</th>' +
				'<th class="th-sm">Payment</th>' +
				'<th class="th-sm">Payment Date</th>' +
				'<th class="th-sm">Payment Method</th>' +
				'<th class="th-sm">Payment Total</th>' +
				'<th class="th-sm">View Payment</th>' +
				'<th class="th-sm">Memo</th>' +
			'</tr>';

			let tbody = '<tbody>';
			
			queryResponsePayload.records.forEach((obj) => {
				let nullCell ='<td><span style="color: #ccc;">' + 'NULL' + '</span></td>';												
					
				tbody += '<tr>'+
					'<td class="td-sm">' + obj['company'] + '</td>' +
					'<td class="td-sm">' + obj['bill_name'] + '</td>' +
          		 	'<td class="td-sm">' + obj['status'] + '</td>' +
            		'<td class="td-sm">' + obj['due_date'] + '</td>' +
            		(obj['bill_total']? '<td class="td-sm">$' + obj['bill_total'].toFixed(2) + '</td>': nullCell) +
					'<td><button type="button" class="btn btn-sm btn-info" onclick="viewPDF(' + obj['bill_id'] + ','+"'bill'"+')" accesskey="r">View Bill</button></td>'+ 
            		(obj['payment_name']? '<td class="td-sm">' + obj['payment_name'] + '</td>' : nullCell) +
            		(obj['payment_date']? '<td class="td-sm">' + obj['payment_date'] + '</td>' : nullCell) +
            		(obj['payment_method']? '<td class="td-sm">' + obj['payment_method'] + '</td>' : nullCell) +
            		(obj['payment_total']? '<td class="td-sm">$' + obj['payment_total'].toFixed(2) + '</td>': nullCell)+
					(obj['payment_id']? '<td><button type="button" class="btn btn-sm btn-info" onclick="viewPDF(' + obj['payment_id'] + ','+"'payment'"+')" accesskey="r">View Payment</button></td>': nullCell)+
					(obj['memo']? '<td class="td-sm">' + obj['memo'] + '</td>' : nullCell) 
            		
					tbody += '</tr>';		
			}); 	

			tbody += '</tbody>';

			var content = ''; 
			content += 'Retrieved ' + queryResponsePayload.records.length + ' rows. <br/>';

			content += '<div class="table-responsive">';
			content += '<table class="table table-striped table-sm  table-hover table-responsive-sm" id="resultsTable">';
			content += thead;
			content += tbody;
			
			content += '</table>';
			content += '</div>';		

			document.getElementById('resultsDiv').innerHTML = content;
			$('#resultsTable').DataTable();
		} else {			
			document.getElementById('resultsDiv').innerHTML = '<h5 class="text-warning text-center d-block">No Bills Found</h5>';
		}
	}`;
}

function jsFunctionGetBills() {
	return `
	function viewBills() {	
		let paramsJsonObj = JSON.stringify({
			billStatusSelected: document.getElementById('billStatusSelect').value, 
			subsidiarySelected: document.getElementById('subsidiarySelect').value, 
			//paymentMethodSelected: document.getElementById('paymentMethodSelect').value, 
			billStartDateSelected: document.getElementById('billStartDate').value, 
			billEndDateSelected: document.getElementById('billEndDate').value 
		});

		document.getElementById('resultsDiv').style.display = "block";
		document.getElementById('resultsDiv').innerHTML = '<div  id="loadingDiv" class="row justify-content-md-center"><h5>'+
		'<div class="spinner-border text-success" role="status"><span class="visually-hidden"></span></div>'+
		"<div id='loadingStatusDiv'>Loading...</div></h5></div>"

		var requestPayload = { 
			'function': 'getBillsForVendorId', 
			'paramsJsonObj': paramsJsonObj,
		}

		var xhr = new XMLHttpRequest();
		xhr.open( 'POST', '${scriptURL}', true );
		JSON.stringify(xhr)

		xhr.setRequestHeader( 'Accept', 'application/json' );		

		xhr.send( JSON.stringify( requestPayload ) );

		xhr.onload = function() {
			if( xhr.status === 200 ) {	
				try {
					queryResponsePayload = JSON.parse( xhr.response );
				} catch( e ) {	
					alert( 'Unable to parse the response.' );
					return;					
				}
		
				if ( queryResponsePayload['error'] == undefined ) {	
					document.getElementById('loadingStatusDiv').value = 'Got the results! Getting the table ready.'
					responseGenerateTable(); 
				} else {		
					var content = '<h5 class="text-danger">Error</h5>';
					content += '<pre>';
					content += queryResponsePayload.error.message;
					content += '</pre>';		

					console.log(content)
				}																																	
			} else {		
				var content = '<h5 class="text-danger">Error</h5>';
				content += '<pre>';
				content += 'XHR Error: Status ' + xhr.status;
				content += '</pre>';		

				console.log(content)			
			}			
		}												
	}`;
}

function jsFunctionViewPDF() {
	return `function viewPDF(transactionId, transactionType) {
		if ( !transactionId) { 
			alert( 'No transaction id passed in!' );
			return; 
		}

		let params ='&function=generatePdf'+
		'&transactionId='+transactionId + 
		'&transactionType='+ transactionType; 

		const modalHtml = '<div class="modal fade" id="pdfModal" tabindex="-1" role="dialog" aria-labelledby="pdfModalLabel">'+
            '<div class="modal-dialog modal-lg" role="document">'+
                '<div class="modal-content">'+
                    '<div class="modal-header">'+
                      '<button type="button" class="btn-close justify-content-right" data-dismiss="modal"><span aria-hidden="true">&times;</span></button>'+
                    '</div>'+
                    '<div class="modal-body">'+
					'<embed src="${scriptURL}' + params + '" frameborder="0" width="100%" height="100%">' +
                    '</div>'+
                    '<div class="modal-footer">'+
						'<button type="button" class="btn btn-secondary" data-dismiss="modal">Close</button>'+
                    '</div>'+
                '</div>'+
            '</div>'+
        '</div>'; 

		// Remove existing modal if present
		const existingModal = document.getElementById('pdfModal');
		if (existingModal) {
			existingModal.remove();
		}

		// Add modal to document
		document.body.insertAdjacentHTML('beforeend', modalHtml);

		// Initialize and show modal
		const modal = new bootstrap.Modal(document.getElementById('pdfModal'));
		modal.show();
	}`;
}

function paymentMethodSelect() {
	return /*html*/ `
		<div class="form-group mb-0 mr-2 d-inline-block" style="min-width: 300px;">
			<h3><label for="paymentMethodSelect" class="text-left d-block">Payment Method</label></h3>
			<select class="form-control" id="paymentMethodSelect">
				<option selected value="">-All-</option>
				<option value="ach">ACH</option>
				<option value="check">Check</option>
				<option value="creditCard">Credit Card</option>
			</select>
		</div>`;
}

function resultsDivView() {
	return /*html*/ `
	<tr>
		<td colspan="3">	
			<div id="resultsDiv" style="max-width: 100%; margin-top: 12px; display: none; overflow: auto; overflow-y: hidden;">
			<!-- RESULTS -->								
			</div>
		</td>
	</tr>
	`;
}

function subsidiarySelect() {
	return /*html*/ `
		<div class="form-group mb-0 mr-2 d-inline-block" style="min-width: 300px;">
			<h3><label for="subsidiarySelect" class="text-left d-block">Company</label></h3>
			<select class="form-control" id="subsidiarySelect">
				<option selected value="">-All-</option>
				<option value="1">Supplyline</option>
				<option value="2">Valmar</option>
				<option value  ="40">Jay Medical</option>
				<option value ="42">TVGMB</option>
			</select>
		</div>`;
}

function welcomeBannerView() {
	return /*html*/ `
	<div id="welcomeBannerViewDiv">
		<div  class="row justify-content-md-left">
		<div class="col-9"></div>
		<div class="col-2">
			<h3>Questions? <a href="mailto:<EMAIL>"><EMAIL></a></h3>
			</div>
			<div class="col-1">
    		<img src="https://5802576.app.netsuite.com/core/media/media.nl?id=11172320&c=5802576&h=GyxFlIjPFEMduc87Hn4n2fnM4d0Lq2afXOEKyKfbXm03FiO-" width="90" height="90">
			</div>
		
			</div>

		<div   class="row justify-content-md-center">
			<span><h1>Welcome ${runtime.getCurrentUser().name}</h1><span>
		</div>
	</div>`;
}
