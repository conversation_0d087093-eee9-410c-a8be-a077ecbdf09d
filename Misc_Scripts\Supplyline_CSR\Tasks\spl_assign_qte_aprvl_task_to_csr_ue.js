/**
 *@NApiVersion 2.1
 *@NScriptType UserEventScript
 */

//@ts-ignore
define(["N/log", "N/search", "N/record"], function (log, search, record) {
	function afterSubmit(context) {
		if (context.type == "create" || context.type == "copy") {
			var quote = context.newRecord;

			var quoteStatusObj = search.lookupFields({
				type: search.Type.ESTIMATE,
				id: quote.id,
				columns: ["entitystatus"],
			});
			var quoteStatus = quoteStatusObj.entitystatus[0].value;
			log.debug("status " + quoteStatus);

			if (quoteStatus == 17) {
				//Waiting for Payment - in SandBox is 18
				var quoteNameObj = search.lookupFields({
					type: search.Type.ESTIMATE,
					id: quote.id,
					columns: ["tranid"],
				});

				var quoteName = quoteNameObj.tranid;
				var customer = quote.getValue("entity");
				var csrRep = getCsrRep();
				createTask();

				function getCsrRep() {
					var searchObj = search.create({
						type: "task",
						filters: [
							["assigned.role", "anyof", "1028"],
							"AND",
							["status", "noneof", "COMPLETE"], //UPDATE IF ADD STATUSES
						],
						columns: [
							search.createColumn({
								name: "assigned",
								summary: "GROUP",
								label: "Assigned To",
							}),
							search.createColumn({
								name: "title",
								summary: "COUNT",
								sort: search.Sort.ASC,
								label: "Task Title",
							}),
						],
					});

					var resultSet = searchObj.run();

					var results = resultSet.getRange({
						start: 0,
						end: 10, //PULLS RESULTS OF UP TO 10 REPS, CAN CHANGE
					});

					var resultsArr = [];

					results.forEach((result) => {
						var csrRep = result.getValue(resultSet.columns[0]);
						var taskCount = result.getValue(resultSet.columns[1]);
						resultsArr.push({
							csrRep: csrRep,
							taskCount: taskCount,
						});
					});

					resultsArr.sort((a, b) => a.taskCount - b.taskCount);

					var csrRep = resultsArr[0].csrRep;

					return csrRep;
				}

				function createTask() {
					var task = record.create({
						type: record.Type.TASK,
					});

					task.setValue({
						fieldId: "title",
						value: "Please approve " + quoteName,
					});

					task.setValue({
						fieldId: "assigned",
						value: csrRep,
					});

					task.setValue({
						fieldId: "company",
						value: customer,
					});

					task.setValue({
						fieldId: "transaction",
						value: quote.id,
					});

					task.save();
				}
			}
		}
	}

	return {
		afterSubmit: afterSubmit,
	};
});
