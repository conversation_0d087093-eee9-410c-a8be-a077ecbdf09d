/**
 * @description Updates the email field on invoices based on customer's custom email field
 *
 * </br><b>Deployed On:</b> Invoices
 * </br><b>Execution Context:</b> ALL
 * </br><b>Event Type/Mode:</b> Create or Edit
 * </br><b>Entry Points:</b> pageInit, fieldChanged
 *
 * @NApiVersion 2.x
 * @NScriptType ClientScript
 *
 * <AUTHOR> HuviBoxer
 * @module vlmd_invoice_multiple_email_addresses_ue
 */

define(["N/search", "N/log"], function (search, log) {
  var notificationFieldId = "custentity_2663_email_address_notif";
  var dontOverrideEmailFieldId = "custbody_vlmd_dont_override_invc_email";

  /**
   * Updates the email field with the customer's notification email
   * @param {Object} currentRecord - The current record
   * @returns {void}
   */
  function updateEmailField(currentRecord) {
    try {
      var dontOverrideEmail = currentRecord.getValue(dontOverrideEmailFieldId);
      if (dontOverrideEmail) return;

      var entityId = currentRecord.getValue("entity");
      if (!entityId) return;

      var notificationEmail = getCustomerNotificationEmail(
        entityId,
        notificationFieldId
      );
      if (!notificationEmail) return;

      var currentEmail = currentRecord.getValue("email");
      if (currentEmail !== notificationEmail) {
        currentRecord.setValue({
          fieldId: "email",
          value: notificationEmail,
        });

        log.debug(
          "Email updated",
          "From: " + (currentEmail || "(empty)") + " To: " + notificationEmail
        );
      }
    } catch (err) {
      log.error({
        title: "Error updating email field",
        details: err,
      });
    }
  }

  /**
   * Gets the notification email address for the customer
   * @param {string|number} customerId - The customer ID
   * @param {string} fieldId - The notification field ID
   * @returns {string|null} The notification email or null
   */
  function getCustomerNotificationEmail(customerId, fieldId) {
    try {
      var lookupResult = search.lookupFields({
        type: search.Type.CUSTOMER,
        id: customerId,
        columns: [fieldId],
      });

      var emailAddress = lookupResult && lookupResult[fieldId];
      if (!emailAddress) return null;

      // Clean up email format
      emailAddress = emailAddress
        .toString()
        .split(",")
        .join(";")
        .split(" ")
        .join("");

      return emailAddress;
    } catch (err) {
      log.error({
        title: "Error retrieving customer notification email",
        details: err,
      });
      return null;
    }
  }

  /**
   * Function that executes when the page completes loading - so the user can see the email address when they load the record
   * @param {Object} context - The context object
   * @param {Record} context.currentRecord - Current form record
   * @returns {void}
   */
  function pageInit(context) {
    updateEmailField(context.currentRecord);
  }

  /**
   * Function that executes when a field is changed - so the user can see the updated email address immediately after they make a change
   * @param {Object} context - The context object
   * @param {Record} context.currentRecord - Current form record
   * @param {string} context.fieldId - Field that was changed
   * @returns {void}
   */
  function fieldChanged(context) {
    if (
      context.fieldId === "entity" ||
      context.fieldId === dontOverrideEmailFieldId
    ) {
      updateEmailField(context.currentRecord);
    }
  }

  return {
    pageInit: pageInit,
    fieldChanged: fieldChanged,
  };
});
