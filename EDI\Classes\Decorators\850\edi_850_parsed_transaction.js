/**
 * @description Class containing functions to extract 850 column data through headers
 *
 * @NApiVersion 2.1
 * <AUTHOR> <<EMAIL>>
 */
define([
    "exports",
    "require",
    "N/log",
], function (
    /** @type {any} */ exports,
    /** @type {any} */ require,
) {
    const log = require("N/log");

    /**
     * EDI 850 Line class
     *
     * @class
     * @typedef {import("../../Interfaces/Decorators/850/edi_850").EDI850Transaction} EDI850Transaction
     * @typedef {import("../../Interfaces/Decorators/850/edi_850").EDI850Item} EDI850Item
     * @typedef {import("../../Interfaces/Decorators/850/edi_850").EDI850Entity} EDI850Entity
     * @typedef {import("../../Interfaces/Decorators/850/edi_850").EDI850SegmentData} EDI850SegmentData
     */
    class EDI850ParsedTransaction {
        /** @param {{[key:string]: any}} params */
        constructor(params){
            /** @type {string} */
            this.content = params.content;
            /** @type {string} */
            this.segmentDelimiter = params.segmentDelimiter || "~";
            /** @type {string} */
            this.fieldDelimiter = params.fieldDelimiter || "*";
            /** @type {EDI850SegmentData} */
            const parsed850 = this.parseSegments();
            /** @type {string} */
            this.controlNumber = parsed850.controlNumber;
            /** @type {string} */
            this.typeCode = parsed850.purchaseOrder.type;
            /** @type {string} */
            this.number = parsed850.purchaseOrder.number;
            /** @type {null | Date} */
            this.date = parsed850.purchaseOrder?.date
                ? new Date(
                    Number(parsed850.purchaseOrder.date.slice(0, 4)),
                    Number(parsed850.purchaseOrder.date.slice(4, 6)) - 1,
                    Number(parsed850.purchaseOrder.date.slice(6, 8))
                )
                : null;
            /** @type {null | Date} */
            this.mustArriveBy = parsed850.mustArriveBy
                ? new Date(
                    Number(parsed850.mustArriveBy.slice(0,4)),
                    Number(parsed850.mustArriveBy.slice(4,6)) - 1,
                    Number(parsed850.mustArriveBy.slice(6,8))
                )
                : null;
            /** @type {string} */
            this.department = parsed850.department;
            /** @type {string} */
            this.shipPoint = parsed850.shipPoint;
            /** @type {EDI850Entity} */
            this.customer = parsed850.customer;
            /** @type {EDI850Entity} */
            this.supplier = parsed850.supplier;
            /** @type {EDI850Item[]} */
            this.items = parsed850.items;
        }

        /**
         * Determine if the entity is a customer or a supplier
         *
         * @param {string} segment Segment from the EDI File
         * @returns {"customer"|"supplier"|undefined} Entity Key
         */
        getEntityKey(segment) {
            if (segment.includes(`${this.fieldDelimiter}BY${this.fieldDelimiter}`)
                || segment.includes(`${this.fieldDelimiter}ST${this.fieldDelimiter}`)
            ) {
                return "customer";
            } else if (segment.includes(`${this.fieldDelimiter}SU${this.fieldDelimiter}`)) {
                return "supplier";
            } else {
                return;
            }
        }

        /**
         * Retrieves the Purchase Order number and date
         * - LDEV-3960: BEG01 does not contain the PO Type anymore
         *
         * @param {string} segment EDI File BEG Segment
         * @returns {EDI850Transaction} BEG Segment fields mapped to values
         */
        getPurchaseOrderMetaData(segment) {
            const values = segment.split(this.fieldDelimiter);

            return {
                type: "",
                number: values[3],
                date: values[5]
            }
        }

        /**
         * Parse segments from the EDI File
         *
         * @returns {EDI850SegmentData} Parsed 850 file
         */
        parseSegments() {
            /** @type {"customer"|"supplier"|undefined} */
            let entity;
            return this.content.split(this.segmentDelimiter).reduce((acc, segment) => {
                const segmentTitle = segment?.substring(0,3);
                switch(segmentTitle){
                    case "ISA":
                        acc.controlNumber = segment.split(this.fieldDelimiter)?.[13].toString();
                        break;
                    case "BEG":
                        acc.purchaseOrder = this.getPurchaseOrderMetaData(segment);
                        break;
                    case "DTM":
                        const dtmFields = segment.split(this.fieldDelimiter);
                        if (dtmFields[1] && ["002", "037", "038", "063"].includes(dtmFields[1])) {
                            acc.mustArriveBy = dtmFields[2];
                        }
                    case "REF":
                        acc.department = segment.includes(`${this.fieldDelimiter}DP${this.fieldDelimiter}`)
                            ? segment.split(this.fieldDelimiter)[2]
                            : acc.department;

                        acc.purchaseOrder.type = segment.includes(`${this.fieldDelimiter}MR${this.fieldDelimiter}`)
                            ? segment.split(this.fieldDelimiter)[2]
                            : acc.purchaseOrder.type;
                        break;
                    case `N1${this.fieldDelimiter}`:
                        entity = this.getEntityKey(segment);
                        if (entity) {
                            const entityFields = segment.split(this.fieldDelimiter);
                            acc[entity].name = entityFields?.[2].toString();
                            acc[entity].identifier = entityFields?.[4]?.toString();

                        }
                        break;
                    case `N3${this.fieldDelimiter}`:
                        if (entity) {
                            acc[entity].address.street = segment.split(this.fieldDelimiter)?.[1].toString();
                        }
                        break;
                    case `N4${this.fieldDelimiter}`:
                        if (entity) {
                            const address = segment.split(this.fieldDelimiter);
                            acc[entity].address.city = address?.[1].toString();
                            acc[entity].address.state = address?.[2].toString();
                            acc[entity].address.zip = address?.[3].toString();
                        }
                        break;
                    case "PO1":
                        const item = segment.split(this.fieldDelimiter);
                        acc.items.push({
                            quantity: item?.[2].toString(),
                            units: item?.[3].toString(),
                            rate: item?.[4].toString(),
                            name: item?.[7].toString(),
                        });
                        break;
                    case "FOB":
                        const freeOnBoard = segment.split(this.fieldDelimiter);
                        acc.shipPoint = freeOnBoard?.[3]?.toString() || "08404701";
                        break;
                }

                return acc;
            }, /** @type {EDI850SegmentData} */ {
                controlNumber: "",
                customer: {
                    name: "",
                    identifier: "",
                    address: {
                        street: "",
                        city: "",
                        state: "",
                        zip: ""
                    }
                },
                department: "",
                purchaseOrder: {
                    type: "",
                    number: "",
                    date: ""
                },
                mustArriveBy: "",
                shipPoint: "",
                supplier: {
                    name: "",
                    identifier: "",
                    address: {
                        street: "",
                        city: "",
                        state: "",
                        zip: ""
                    }
                },
                /** @type {{quantity:string, units:string, rate:string, name:string}[]} */
                items: []
            });
        }
    }

    exports.EDI850ParsedTransaction = EDI850ParsedTransaction;
});