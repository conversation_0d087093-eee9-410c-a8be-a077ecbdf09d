/**
 * @NApiVersion 2.1
 */

define(["N/log", "N/record", "N/email", "N/search"], function (
	log,
	record,
	email,
	search
) {
	function sendItemFulfillmentEmail(itemFulfillment, recipientEmailAddresses) {
		//Passing in the IF record
		let processedSuccessfully = true;
		const errorLog = [];

		try {
			let itemFulfillmentName = itemFulfillment.getValue("tranid");
			const salesOrder = getSalesOrder();
			const itemFulfillmentObj = getItemFulfillmentObj();

			const {
				salesOrderId,
				customerName,
				shippingCarrier,
				trackingInfo,
				postedToAftership,
				purchaseOrderName,
				salesOrderName,
				itemLineCount,
			} = itemFulfillmentObj;

			sendEmail();

			return { processedSuccessfully, errorLog };

			/**********Item Fulfillment Email Helper Functions**********/

			function _processFail(errorMessage) {
				processedSuccessfully = false;
				errorLog.push(`${itemFulfillmentName} - ${recipientEmailAddresses} 
				 ${errorMessage}`);
			}

			function getItemFulfillmentObj() {
				return {
					itemFulfillmentName: itemFulfillmentName,
					shippingCarrier: itemFulfillment.getText(
						"custbody_spl_shipping_carrier"
					),
					trackingInfo: _getTrackingInfo(),
					postedToAftership: itemFulfillment.getValue(
						"custbody_spl_trckng_psted_to_aftership"
					),
					itemLineCount: itemFulfillment.getLineCount({ sublistId: "item" }),
					salesOrderName: itemFulfillment.getText("createdfrom"),
					salesOrderId: salesOrder.getValue("tranid"),
					purchaseOrderName: salesOrder.getValue("otherrefnum"),
					customerName: _getCustomerName(),
				};
			}

			function getSalesOrder() {
				return record.load({
					type: record.Type.SALES_ORDER,
					id: itemFulfillment.getValue("createdfrom"),
				});
			}
			function _getTrackingInfo() {
				let trackingInfo = itemFulfillment.getSublistValue({
					sublistId: "packageups",
					fieldId: "packagetrackingnumberups",
					line: 0,
				});

				if (!trackingInfo) {
					trackingInfo = itemFulfillment.getValue(
						"custbody_spl_tracking_information"
					);
				}

				return trackingInfo;
			}

			function _getCustomerName() {
				return search.lookupFields({
					type: search.Type.CUSTOMER,
					id: itemFulfillment.getValue("entity"),
					columns: ["companyname"],
				})["companyname"];
			}

			function _getEmailBody() {
				let body = `<div style="font-family: Open Sans, Verdana, Arial, Helvetica, sans-serif;>
					<p>
					<span style="font-size:14px;"><strong>SUPPLYLINE ITEM FULFILLMENT </strong></span>
					<br /><br />
					Dear ${customerName},
					<br /><br /> 
					Thank you for your recent order with Supplyline! We appreciate your business.
					<br /> <br /> 
					Good news! The following items in your order have shipped${
						shippingCarrier ? ` via ${shippingCarrier}` : ""
					}.`;

				if (trackingInfo) {
					let trackingLink;
					if (postedToAftership) {
						trackingLink = `<a data-auth="NotApplicable" href="https://splyln.aftership.com/
							${trackingInfo}" rel="noopener noreferrer" target="_blank">
							<span>${trackingInfo}</span>
							</a>`;
					}

					body += `<br /> 
						Your tracking number is ${postedToAftership ? trackingLink : trackingInfo}.`;

					if (postedToAftership) {
						body += `<br /> <br /> <br />
							<table style="width:540px" role="presentation" width="540" cellspacing="0" cellpadding="0" border="0" align="center">
							<tbody><tr><td style="padding:15px 0px 30px 0px">
							<table cellspacing="0" cellpadding="0" border="0" align="center" >
							<tbody>
							<tr>
							<td style="border-radius:3px" bgcolor="#44beae" align="center" >
							<a href="https://splyln.aftership.com/'${trackingInfo}" style="font-size:16px;font-family:Helvetica,Arial,sans-serif;color:#ffffff;text-decoration:none;display:inline-block;border:1px solid #44beae;padding:12px 50px 12px 50px;border-radius:3px" target="_blank" >
							Track status
							</a> 
							</td> 
							</tr> 
							</tbody> 
							</table> 
							</td> </tr> </tbody> 
							</table>`;
					}
				}

				body += `<br /> <br /> <br />
					<strong>Order Summary:</strong> 
					<br />
					Order # - ${salesOrderId}
					<br /> </p >
					<table border="0" cellpadding="5" cellspacing="0" style=" line-height: 16px; vertical-align: top; width: 100%; margin: 50px 0 px;">
					<tbody>
					<tr style="margin: 4px 0;">
					<th style="vertical-align: top;text-align: left;">Item</th>
					<th style="vertical-align: top;text-align: left;">Qty Shipped</th>
					<th style="vertical-align: top;text-align: left;">Brief Description</th>
					</tr>`;

				let groupedItems = new Map(); // key: isParentGroupItem, value: {quantity: number, descriptions: []}

				for (let i = 0; i < itemLineCount; i++) {
					const fulfill = itemFulfillment.getSublistValue({
						sublistId: "item",
						fieldId: "itemreceive",
						line: i,
					});

					const isKitMemberItem = itemFulfillment.getSublistValue({
						sublistId: "item",
						fieldId: "kitmemberof",
						line: i,
					});

					if (fulfill && !isKitMemberItem) {
						let item = itemFulfillment.getSublistValue({
							sublistId: "item",
							fieldId: "itemname",
							line: i,
						});

						const description = itemFulfillment.getSublistValue({
							sublistId: "item",
							fieldId: "itemdescription",
							line: i,
						});

						// Get the corresponding SO line number so we can get check if this item is part of an item group
						const soLine = itemFulfillment.getSublistValue({
							sublistId: "item",
							fieldId: "orderline",
							line: i
						}) - 1;

						const itemGroupTypeOnSo = salesOrder.getSublistValue({
							sublistId: "item",
							fieldId: "matrixtype",
							line: soLine
						});

						if (itemGroupTypeOnSo === "CHILD") {
							//Go up and search for its parent
							let currentLine = soLine;
							while (currentLine >= 0) {
								const itemGroupType = salesOrder.getSublistValue({
									sublistId: "item",
									fieldId: "itemtype",
									line: currentLine
								});
								
								if (itemGroupType === "Group") {
									const isParentGroupItem = salesOrder.getSublistValue({
										sublistId: "item",
										fieldId: "item_display",
										line: currentLine
									});

									const groupQty = salesOrder.getSublistValue({
										sublistId: "item",
										fieldId: "quantity",
										line: currentLine
									});

									if (!groupedItems.has(isParentGroupItem)) {
										groupedItems.set(isParentGroupItem, {
											quantity: groupQty,
											descriptions: []
										});
									}
									groupedItems.get(isParentGroupItem).descriptions.push(description);
									break;
								}
								currentLine--;
							}
						} else {
							const qty = itemFulfillment.getSublistValue({
								sublistId: "item",
								fieldId: "quantity",
								line: i,
							});

							body += `<tr style="margin: 4px 0;">
								<td style="vertical-align: top;">
								<span style="font-size: 14px; line-height: 16px;">
								${item}
								</span>
								</td>
								<td style="vertical-align: top;">
								<span style="font-size: 14px; line-height: 16px;">
								${qty}
								</span>
								</td>
								<td style="vertical-align: top;">
								<span style="font-size: 14px; line-height: 16px;">
								${description}
								</span>
								</td>
								</tr>`;
						}
					}
				}

				// Add grouped items to the body
				for (let [isParentGroupItem, data] of groupedItems) {
					const descriptions = [...new Set(data.descriptions)].join(', ');
					
					body += `<tr style="margin: 4px 0;">
						<td style="vertical-align: top;">
						<span style="font-size: 14px; line-height: 16px;">
						${isParentGroupItem}
						</span>
						</td>
						<td style="vertical-align: top;">
						<span style="font-size: 14px; line-height: 16px;">
						${data.quantity}
						</span>
						</td>
						<td style="vertical-align: top;">
						<span style="font-size: 14px; line-height: 16px;">
						${descriptions}
						</span>
						</td>
						</tr>`;
				}

				body += `</tbody >
					</table>
					<p>Should you need any further customer support, email us at 
					<a data-auth="NotApplicable" href="mailto:<EMAIL>" rel="noopener noreferrer" target="_blank">
					<EMAIL></a > or call us at 856-478-7759. 
					<br />
					</p>
					<p>Thanks!<br/></p>
					<p>
					<br/>The Supplyline Team<br/>
					</p>
					</div>`;

				return body;
			}

			function sendEmail() {
				try {
					email.send({
						author: 13267, //Supplyline Customer Service
						recipients: recipientEmailAddresses,
						subject: `${
							purchaseOrderName ? purchaseOrderName : salesOrderName
						} - Your Order for ${customerName} Has Shipped`,
						body: _getEmailBody(),
						relatedRecords: {
							transactionId: itemFulfillment.id,
						},
					});
				} catch (e) {
					_processFail(e);
				}
			}
		} catch (e) {
			_processFail(e);
		}
	}

	return {
		sendItemFulfillmentEmail,
	};
});
