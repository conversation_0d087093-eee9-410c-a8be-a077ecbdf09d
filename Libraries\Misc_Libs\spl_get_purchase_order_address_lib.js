/**
 * @NApiVersion 2.1
 */

//@ts-ignore
define(["N/log", "N/record", "N/search", "GetAddressObjLib"], function (
	log,
	record,
	search,
	getAddressObjLib
) {
	function getPurchaseOrderAddress(
		isDropShip,
		purchaseOrderInternalId,
		purchaseOrderNumber,
		customerInternalID
	) {
		var address = {};
		if (isDropShip) {
			var addressObj = search.lookupFields({
				type: record.Type.PURCHASE_ORDER,
				id: purchaseOrderInternalId,
				columns: [
					"shipaddress1",
					"shipaddress2",
					"shipstate",
					"shipzip",
					"shipcity",
				],
			});

			address = {
				address1: addressObj["shipaddress1"],
				address2: setAddress2(),
				city: addressObj["shipcity"],
				state: addressObj["shipstate"],
				zip: addressObj["shipzip"],
				deliveryDetails: getAddressDeliveryDetails(),
			};

			function setAddress2() {
				if (addressObj["shipaddress2"]) {
					return addressObj["shipaddress2"];
				} else {
					return "";
				}
			}

			function getAddressDeliveryDetails() {
				var deliveryDetails = { deliveryInstructions: "", liftgateFlag: "" };
				if (!customerInternalID) {
					return deliveryDetails;
				}
				var customerRecObj = record.load({
					type: record.Type.CUSTOMER,
					id: customerInternalID,
					isDynamic: true,
				});

				var addressIDValue = customerRecObj.getSublistValue({
					sublistId: "addressbook",
					fieldId: "addressbookaddress_key",
					line: 0,
				});

				if (addressIDValue) {
					var addressFieldvalues =
						getAddressObjLib.getDeliveryFields(addressIDValue);
					deliveryDetails.deliveryInstructions =
						addressFieldvalues.custrecord_adr_delivery_instructions;
					deliveryDetails.liftgateFlag =
						addressFieldvalues.custrecord_adr_liftgate_required;
					return deliveryDetails;
				} else {
					return deliveryDetails;
				}
			}
		} else {
			var addressObj = search.lookupFields({
				type: record.Type.PURCHASE_ORDER,
				id: purchaseOrderInternalId,
				columns: ["custbody_spl_warehouse_address"],
			});

			var addressToParse = addressObj["custbody_spl_warehouse_address"];

			if (!addressToParse) {
				throw (
					"No warehouse address entered for " +
					purchaseOrderNumber +
					" in NetSuite. Since this order is not a drop ship order it will be going to the warehouse. A 'Warehouse Address' needs to be entered on the purchase order."
				);
			}

			var addressArr = addressToParse.split(/\r\n/g);

			addressArr.shift();
			var address1 = addressArr[0];
			var address2 = "";

			var cityStateZip = "";

			if (addressArr.length === 3) {
				//There is an address2
				address2 = addressArr[1];
				cityStateZip = addressArr[2];
			} else if (addressArr.length === 2) {
				//No address2
				cityStateZip = addressArr[1];
			} else {
				//Format not accounted for
				throw `Please review the warehouse address entered for ${purchaseOrderNumber}: ${addressArr}`;
			}

			if (!cityStateZip) {
				throw `No cityStateZip gotten for address - ${addressArr.join(",")}`;
			}

			var cityStateZipArr = cityStateZip.split(",");
			var city = cityStateZipArr[0];
			var stateZip = cityStateZipArr[1].split(" ");
			var state = stateZip[1];
			var zip = stateZip[2];

			var obj = {
				address1: address1,
				address2: address2,
				city: city,
				state: state,
				zip: zip,
				deliveryDetails: { deliveryInstructions: "", liftgateFlag: "" },
			};

			address = obj;
		}
		return address;
	}

	return {
		getPurchaseOrderAddress: getPurchaseOrderAddress,
	};
});
