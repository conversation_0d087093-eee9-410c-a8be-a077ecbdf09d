/**
 * @NApiVersion 2.1
 */

define(["N/query"], function (query) {
	function setItemPricing(items, parentId) {
		var errorLog = [];

		var parentsPurchaserId = getParentsPurchaserId(parentId);

		if (!parentsPurchaserId) {
			_processFail(`This customer isn't connected to an EDI integrated purchaser or parent company.
				Please correct this and set all rates on the sales order to the correct rates.`);
		} else {
			items.forEach((item) => {
				if (!item.internalId) {
					return;
				}
				var itemPricing = getItemPricing(item, parentsPurchaserId);

				if (itemPricing) {
					item.itemPricing = itemPricing;
				} else {
					var priceLevelObj = getPriceLevel(item, parentsPurchaserId);

					if (priceLevelObj) {
						item.priceLevel = priceLevelObj.priceLevel;
						item.priceLevelName = priceLevelObj.priceLevelName;
					} else {
						item.missingPricingInNetSuite = true;
						_processFail(
							`${item.itemName} isn't assigned to any pricing group. 
						Please set on the item record and set the correct price on the sales order.`
						);
					}
				}
			});
		}

		return {
			errorLog,
			items,
		};

		function _processFail(logMessage, programmingError) {
			errorLog.push({ logMessage, programmingError });
		}

		function getParentsPurchaserId(parentsPurchaserId) {
			let sqlQuery = `SELECT
		integration.custrecord_spl_prchsng_fclty,
	 FROM
		customrecord_vlmd_edi_integration AS integration 
		JOIN
		   map_customrecord_vlmd_edi_integration_custrecord_edi_intgrtn_prnt_fclty map 
		   ON map.mapone = integration.id 
		JOIN
		   customer AS parent 
		   ON map.maptwo = parent.id 
	 WHERE
		parent.id  = ?`;

			let queryResults = query.runSuiteQL({
				query: sqlQuery,
				params: [parentsPurchaserId],
			});

			if (queryResults.results.length > 0) {
				return queryResults.results[0].values[0];
			}
		}

		function getItemPricing(item, parentsPurchaserId) {
			var itemPricingQuery = `SELECT
				IP.price 
				FROM
				customerItemPricing AS IP 
				WHERE
				IP.customer = ${parentsPurchaserId} 
				AND IP.item = ${item.internalId}`;

			var resultIterator = query.runSuiteQL({
				query: itemPricingQuery,
			});

			if (resultIterator.results.length <= 0) {
				return false;
			}

			return resultIterator.results[0].values[0];
		}

		function getPriceLevel(item, parentId) {
			var itemPricingQuery = `Select
				GP.group,
				BUILTIN.DF( GP.group) AS PriceGroup,
				GP.level,
				BUILTIN.DF( GP.level) AS PriceGroupLevel,
			from
				CustomerGroupPricing as gp 
				inner join
				item as i 
				on i.pricinggroup = gp.group 
			where
				gp.customer = ${parentId} 
				and i.id = ${item.internalId}`;

			var resultIterator = query.runSuiteQL({
				query: itemPricingQuery,
			});

			if (resultIterator.results.length <= 0) {
				return false;
			}
			var priceLevel = resultIterator.results[0].values[2];
			var priceLevelName = resultIterator.results[0].values[3];

			return {
				priceLevel,
				priceLevelName,
			};
		}
	}

	return {
		setItemPricing,
	};
});
