/******************************************************************************************************
	Script Name - AVA_CLI_TwoWayIMSCreateBatch.js
	Company -     Avalara Technologies Pvt Ltd.
******************************************************************************************************/

/**
 *@NApiVersion 2.0
 *@NScriptType ClientScript
*/

define(['N/currentRecord', 'N/url', 'N/https'],
	function(currentRecord, url, https){
		function saveRecord(context){
			var cRecord = context.currentRecord;
			var batchName = cRecord.getValue({
				fieldId: 'ava_batchname'
			});
			if(batchName != null && batchName.length <= 0){
				alert('Enter a batch name.');
				document.forms['main_form'].ava_batchname.focus();
				return false;
			}
			var startDate = cRecord.getValue({
				fieldId: 'ava_startddate'
			});
			if(startDate != null && startDate.length <= 0){
				alert('Select a start date.');
				document.forms['main_form'].ava_startddate.focus();
				return false;
			}
			var endDate = cRecord.getValue({
				fieldId: 'ava_enddate'
			});
			if(endDate != null && endDate.length <= 0){
				alert('Select an end date.');
				document.forms['main_form'].ava_enddate.focus();
				return false;
			}
			if(endDate < startDate){
				alert('End date should be greater than or equal to start date.');
				document.forms['main_form'].ava_enddate.focus();
				return false;
			}
			var response = https.request({
				method: https.Method.GET,
				url: url.resolveScript({
					scriptId: 'customscript_ava_recordload_suitelet',
					deploymentId: 'customdeploy_ava_recordload',
					params: {'type': 'customrecord_avatwowayimsbatch', 'batchname': batchName}
				})
			});
			if(response.body == '0'){
				alert('Batch name already exists. Enter a new batch name.');
				document.forms['main_form'].ava_batchname.focus();
				return false;
			}
			return true;
		}
		
		return{
			saveRecord: saveRecord
		};
	}
);