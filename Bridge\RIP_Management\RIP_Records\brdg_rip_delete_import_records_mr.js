/**
 * @description Deletes import records from parameter of months back
 *
 * </br><b>Schedule:</b> Runs on the first of every month @ 1:00 AM
 *
 * @NApiVersion 2.1
 * @NScriptType MapReduceScript
 * @NAmdConfig /SuiteScripts/config.json
 *
 * <AUTHOR>
 * @module brdg_rip_delete_records_mr
 */
define([
  "require",
  "N/runtime",
  "N/record",
  "../../../Classes/vlmd_custom_error_object",
  "../../../Classes/vlmd_mr_summary_handling",
], (require) => {
  const runtime = require("N/runtime");
  const record = require("N/record");

  /** @type {import("../../../Classes/vlmd_custom_error_object").CustomErrorObject} */

  const CustomErrorObject = require("../../../Classes/vlmd_custom_error_object");
  function getInputData(context) {
    const customErrorObject = new CustomErrorObject();
    try {
      const currentScript = runtime.getCurrentScript();
      const numMonthsToDelete =
        currentScript.getParameter({
          name: "custscript_months_to_delete",
        }) ?? 5;
      const sqlQuery = `SELECT id from customrecord_brdg_rip_import where created < ADD_MONTHS( SYSDATE, -${numMonthsToDelete} )`;
      return {
        type: "suiteql",
        query: sqlQuery,
      };
    } catch (err) {
      customErrorObject.throwError({
        summaryText: "GET_INPUT_DATA_ERROR",
        error: err,
      });
    }
  }
  function map(context) {
    const mapErrorObject = new CustomErrorObject();

    const parsedResult = JSON.parse(context.value);
    try {
      const recordId = parsedResult.values[0];
      record.delete({
        type: "customrecord_brdg_rip_import",
        id: recordId,
      });

      context.write("Record Deleted!", `${recordId}`);
    } catch (err) {
      mapErrorObject.throwError({
        summaryText: `MAP_ERROR`,
        error: err,
        recordType: "customrecord_brdg_rip_import",
        recordId: recordId,
      });
    }
  }
  function summarize(context) {
    /** @type {import("../../../Classes/vlmd_mr_summary_handling").MapReduceSummaryStageHandling} */
    const MapReduceSummaryStageHandling = require("../../../Classes/vlmd_mr_summary_handling");
    const stageHandling = new MapReduceSummaryStageHandling(context);
    stageHandling.printScriptProcessingSummary();
    stageHandling.printRecordsProcessed();
  }
  return {
    getInputData,
    map,
    summarize,
  };
});
