/**
 * @description Incoming EDI File Class
 *
 * @NApiVersion 2.1
 * <AUTHOR> <<EMAIL>>
 */

define([
    "exports",
    "require",
    "N/log",
    "./edi_file",
], (/** @type {any} */ exports, /** @type {any} */ require) => {
    const log = require("N/log");
    const { EDIFile } = require("./edi_file");

    /**
     * EDI Incoming class
     *
     * @class
     * @typedef {import("../../Interfaces/Models/File/edi_incoming").EDIIncomingInterface} EDIIncomingInterface
     * @extends {EDIFile}
     * @implements {EDIIncomingInterface}
     */
    class EDIIncoming extends EDIFile {
        constructor() {
            super();
            /** @type {string} */
            this.ediType = "EDI Incoming";
            /** @type {any} */
            this.transaction = null;
        }

        /**
         * Parse the incoming EDI File
         *
         * @returns {void}
         */
        parse() {
            log.error("EDI Incoming", "Error: Parse function should be implemented through a decorator.");
        }

        /**
         * Transform the EDI file to a NetSuite record
         *
         * @returns {void}
         */
        transform() {
            log.error("EDI Incoming", "Error: Transform function should be implemented through a decorator.");
        }

        /**
         * Move the EDI File to the Reference directory
         *
         * @param {object} params Archive params
         * @param {string} [params.filename] File name
         * @param {string} [params.source] Source directory
         * @param {string} [params.target] Target directory
         * @param {string} [params.content] File content to upload
         * @returns {void}
         */
        archive(params) {
            log.error("EDI Incoming", "Error: Archive function should be implemented through a decorator.");
        };

        /**
         * Retrieve the email object after processing the transaction
         *
         * @returns {void}
         */
        summarize() {
            log.error("EDI Incoming", "Error: Summarize function should be implemented through a decorator.");
        };
    }

    exports.EDIIncoming = EDIIncoming;
});