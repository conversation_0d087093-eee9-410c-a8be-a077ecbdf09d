/******************************************************************************************************
	Script Name - AVA_SUT_AdditionalInfo.js
	Company -     Avalara Technologies Pvt Ltd.
******************************************************************************************************/

/**
 *@NApiVersion 2.0
 *@NScriptType Suitelet
*/

define(['N/search', 'N/record', 'N/https', 'N/redirect', 'N/cache', './utility/AVA_Library'],
	function(search,record, https, redirect, cache, ava_library){
		function onRequest(context){
			try{
				var customrecord_avaconfigSearchObj = search.create({
					type: "customrecord_avaconfig",
					columns: ['custrecord_ava_url', 'custrecord_ava_additionalinfo4']
				});
				var searchresult = customrecord_avaconfigSearchObj.run();
				searchresult = searchresult.getRange({
					start: 0,
					end: 5
				});
				
				if(searchresult != null && searchresult.length > 0){
					var additional_Info4 = searchresult[0].getValue('custrecord_ava_additionalinfo4');
					
					if(additional_Info4 != null && additional_Info4.length > 0){
						var response = calladditionalInfo4(additional_Info4, searchresult[0].id, searchresult[0].getValue('custrecord_ava_url'));
						
						if(response == 200){
							log.debug('Tkn generated successfully');
							
							var avaConfigCache = cache.getCache({
								name: 'avaConfigCache',
								scope: cache.Scope.PROTECTED
							});
							avaConfigCache.put({
								key: 'avaConfigObjRec',
								value: ava_library.mainFunction('AVA_LoadValuesToGlobals', '')
							});
							
							redirect.toTaskLink({
								id: 'CARD_-29'
							});
						}
						else{
							log.debug({title: 'response Error', details: response});
						}
					}
					else{
						redirect.toSuitelet({
							scriptId: 'customscript_avaconfig_wizard',
							deploymentId: 'customdeploy_ava_configurewizard'
						});
					}
				}
			}
			catch(err){
				log.debug({title: 'onRequest Error', details: err.message});
			}
		}

		function calladditionalInfo4(additional_Info4, recordId, sUrl){
			try{
				var details;
				var authorizationUrl;
				
				var header = {};
				header["content-type"] = "application/x-www-form-urlencoded";
				
				if(sUrl == 1){
					details = 'client_id=sbx-netsuite-identity&client_secret=Netsbx123';
					authorizationUrl = 'https://ai-sbx.avlr.sh/connect/token';
				}
				else{
					details = 'client_id=prd-netsuite-identity&client_secret=yD7)o#Uk@!GH%1#PVahs5Lx0';
					authorizationUrl = 'https://identity.avalara.com/connect/token';
				}
				
				var body = 'refresh_token=' + additional_Info4 + '&grant_type=refresh_token&' + details + '&scope=offline_access openid profile avatax_api avatax email';
				
				var response = https.request({
					method: https.Method.POST,
					url: authorizationUrl,
					body: body,
					headers: header
				});
				
				if(response.code == 200){
					var myresponse_body = JSON.parse(response.body);
					
					record.submitFields({
						type: 'customrecord_avaconfig',
						id: recordId,
						values: {
							'custrecord_ava_additionalinfo3': myresponse_body.access_token,
							'custrecord_ava_additionalinfo4': myresponse_body.refresh_token
						}
					});
					
					return response.code;
				}
				else{
					var myresponse_body = JSON.parse(response.body);
					return myresponse_body.error;
				}
			}
			catch(err){
				log.debug({title: 'calladditionalInfo4 Error', details: err.message});
			}
		}
		
		return{
			onRequest: onRequest
		};
	}
)