/**
 * @description Partner class for DSSI
 *
 * @NApiVersion 2.1
 * <AUTHOR> <<EMAIL>>
 */

define([
    "exports",
    "require",
    "./edi_customer"
], (/** @type {any} */ exports, /** @type {any} */ require) => {
    const { EDICustomer } = require("./edi_customer");

    /**
     * DSSI Customer Class
     *
     * @class
     * @typedef {import("../../../Interfaces/Models/Partner/edi_partner").EDICustomerInterface} EDICustomerInterface
     * @extends {EDICustomer}
     * @implements {EDICustomerInterface}
     */
    class EDIDSSI extends EDICustomer {
        constructor() {
            super();
            /** @type {string} */
            this.name = "DSSI";
            /** @type {string} */
            this.prodDirectory = "/edi/prod/customer/dssi/out/832";
            /** @type {string} */
            this.testDirectory = "/edi/test/customer/dssi/out/832";
            /** @type {string} */
            this.referenceDirectory = "/edi/reference/customer/dssi/out/832";
            /** @type {boolean} */
            this.shouldIncludeIsReplacementFor = false;
            /** @type {boolean} */
            this.shouldIncludeDeactivateItem = false;
        }
    }

    exports.EDIDSSI = EDIDSSI;
});