/**
 * @NApiVersion 2.1
 * @NScriptType Suitelet
 * @NAmdConfig /SuiteScripts/config.json
 * @param {import ("N/types")}
 * <AUTHOR>
 * @description SL to allow GPO handler to create JE and update GPO info on invoices
 * @module spl_create_je_for_gpo_sl
 */

define([
	"require",
	"Moment",
	"N/log",
	"N/ui/serverWidget",
	"N/ui/message",
	"N/url",
	"N/task",
	"N/redirect",
], function (require, moment) {
	const log = require("N/log");
	const serverWidget = require("N/ui/serverWidget");
	const message = require("N/ui/message");
	const url = require("N/url");
	const task = require("N/task");
	const redirect = require("N/redirect");

	var helperFunctions = (function () {
		function createMapReduceTask(params) {
			const { gpoId, startDate, endDate, savedSearchId } = params;

			var mapReduceTask = task.create({ taskType: task.TaskType.MAP_REDUCE });

			mapReduceTask.scriptId = "customscript_spl_create_gpo_rfrl_fee_mr";
			mapReduceTask.deploymentId = "customdeploy_spl_create_gpo_rfrl_fees_mr";

			mapReduceTask.params = {
				custscript_gpo_id: gpoId,
				custscript_gpo_start_date: startDate,
				custscript_gpo_end_date: endDate,
				custscript_gpo_saved_search_id: savedSearchId,
			};

			return mapReduceTask.submit();
		}

		function redirectToSuitelet(taskId) {
			var suiteletURL = url.resolveScript({
				scriptId: "customscript_spl_create_je_for_gpo_sl",
				deploymentId: "customdeploy_spl_create_je_for_gpo_sl",
				params: {
					redirected_from_sl: true,
					task_id: taskId,
				},
			});

			redirect.redirect({ url: suiteletURL });
		}

		return {
			createMapReduceTask,
			redirectToSuitelet,
		};
	})();

	return {
		onRequest: function (context) {
			if (context.request.method === "GET") {
				const form = serverWidget.createForm({
					title: "Run GPO Script",
				});

				const redirectedFromSl =
					context.request.parameters["redirected_from_sl"];

				if (redirectedFromSl) {
					const mapReduceTaskId = context.request.parameters["task_id"];

					let taskStatus = task.checkStatus({
						taskId: mapReduceTaskId,
					});

					form.addPageInitMessage({
						type: message.Type.INFORMATION,
						title: "Task Results",
						message: `Task Status: ${taskStatus.status}`,
						duration: 10000,
					});

					form.addField({
						id: "custpage_results",
						label: "View Results",
						type: serverWidget.FieldType.INLINEHTML,
					});

					form.updateDefaultValues({
						custpage_results: `<a style='margin: 30px;padding: 20px 50px;font-size: 20px;'href="https://5802576.app.netsuite.com/app/common/search/searchresults.nl?searchid=4575" target=_blank>View GPOs Created This Month</a>`,
					});

					form.addField({
						id: "custpage_script_status",
						label: "View Script Status",
						type: serverWidget.FieldType.INLINEHTML,
					});

					form.updateDefaultValues({
						custpage_script_status: `<a style='margin: 30px;padding: 20px 50px;font-size: 20px;'href="https://5802576.app.netsuite.com/app/common/search/searchresults.nl?searchid=4576" target=_blank>View Script Status</a>`,
					});
				} else {
					var setValuesGroup = form.addFieldGroup({
						id: "setValuesGroup",
						label: "GPO and Date Parameters",
					});
					setValuesGroup.isCollapsible = true;

					form.addField({
						id: "custpage_gpo_help",
						label: "Help Label ",
						type: serverWidget.FieldType.INLINEHTML,
						container: "setValuesGroup",
					}).defaultValue = "Leave GPO selection empty to run on all GPOs.";

					const gpoField = form.addField({
						id: "custpage_gpo",
						label: "GPO",
						type: serverWidget.FieldType.SELECT,
						source: "customrecord_spl_gpo",
						container: "setValuesGroup",
					});
					gpoField.setHelpText({
						help: "Leave selection empty to run on all GPOs.",
					});

					form.addField({
						id: "custpage_start_date",
						label: "Start Date",
						type: serverWidget.FieldType.DATE,
						container: "setValuesGroup",
					}).defaultValue = moment()
						.subtract(1, "months")
						.startOf("month")
						.format("MM/DD/YYYY");

					form.addField({
						id: "custpage_end_date",
						label: "End Date",
						type: serverWidget.FieldType.DATE,
						container: "setValuesGroup",
					}).defaultValue = moment()
						.subtract(1, "months")
						.endOf("month")
						.format("MM/DD/YYYY");

					form.addFieldGroup({
						id: "chooseSearchGroup",
						label: "Use Saved Search",
					});

					form.addField({
						id: "custpage_saved_search_id",
						label: "Saved Search",
						type: serverWidget.FieldType.SELECT,
						source: -119,
						container: "chooseSearchGroup",
					});

					form.addSubmitButton({
						label: "Calculate GPOs",
					});

					form.addResetButton({});
				}

				context.response.writePage(form);
			} else {
				//POST Request
				const gpoId = context.request.parameters.custpage_gpo;
				const startDate = context.request.parameters.custpage_start_date;
				const endDate = context.request.parameters.custpage_end_date;
				const savedSearchId =
					context.request.parameters.custpage_saved_search_id;

				const taskId = helperFunctions.createMapReduceTask({
					gpoId,
					startDate,
					endDate,
					savedSearchId,
				});

				helperFunctions.redirectToSuitelet(taskId);

				return;
			}
		},
	};
});
