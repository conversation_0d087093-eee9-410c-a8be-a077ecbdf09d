/******************************************************************************************************
	Script Name - AVA_CLI_CustomersListForCertCapture.js
	Company -     Avalara Technologies Pvt Ltd.
******************************************************************************************************/

/**
 *@NApiVersion 2.0
 *@NScriptType ClientScript
 */

define(['N/currentRecord', 'N/url', 'N/search', 'N/https'],
	function(currentRecord, url, search, https){
		function fieldChanged(context){
			try{
				if(context.fieldId == 'custpage_selectaddresslabel'){
					var selectAddressLabel = context.currentRecord.getCurrentSublistText({
						sublistId: 'ava_bulkcustomersublist',
						fieldId: 'custpage_selectaddresslabel'
					});
					if(selectAddressLabel){
						setAddressOnAddressLabelChange(context, selectAddressLabel)
					}
				}
			}
			catch(e){
				log.error('fieldChanged', 'ERROR NAME = ' + e.name + ', ERROR TYPE = ' + e.type + ', ERROR MESSAGE = ' + e.message);
			}
		}

		function saveRecord(context){
			try{
				var currentRec = currentRecord.get();
				var batchName = currentRec.getValue({
					fieldId: 'ava_batchname'
				});
				var flagState = false;
				var customerLineCount = currentRec.getLineCount({
					sublistId: 'ava_bulkcustomersublist'
				});
				if(customerLineCount <= 0){
					alert('No records found.');
					return false;
				}
				for(var i = 0; i < customerLineCount; i++){
					var customerLineSelect = currentRec.getSublistValue({
						sublistId: 'ava_bulkcustomersublist',
						fieldId: 'selectmarkcheck',
						line: i
					});
					if(customerLineSelect == true){
						flagState = true
					}
				}
				if((!flagState) && (customerLineCount > 0)){
					alert('Select at least one customer.');
					return false;
				}
				
				var response = https.request({
					method: https.Method.GET,
					url: url.resolveScript({
						scriptId: 'customscript_ava_recordload_suitelet',
						deploymentId: 'customdeploy_ava_recordload',
						params: {'type': 'customrecord_avacertcapturebatch', 'batchname': batchName}
					})
				});
				if(response.body == '0'){
					alert('This batch is already submitted.');
					return false;
				}
				return true;
			}
			catch(e){
				log.error('saveRecord', 'ERROR NAME = ' + e.name + ', ERROR TYPE = ' + e.type + ', ERROR MESSAGE = ' + e.message);
			}
		}

		function pageInit(context){
			try{
				setAddressLabelValue(context.currentRecord)
			}
			catch(e){
				log.error('pageInit', 'ERROR NAME = ' + e.name + ', ERROR TYPE = ' + e.type + ', ERROR MESSAGE = ' + e.message);
			}
		}

		function AVA_CertCaptureBackToCriteria(context){
			try{
				var currentRecObj = currentRecord.get();
				var pageURL = window.location.href;
				var pageURLobj = new URL(pageURL);
				var criteriaSuiteURL = url.resolveScript({
					scriptId: 'customscript_avaaddcuststocert_suitelet',
					deploymentId: 'customdeploy_avaaddcuststocertcapture'
				});
				criteriaSuiteURL += "&batchname=" + encodeURI(pageURLobj.searchParams.get("batchname")) +
					"&subsidiary=" + encodeURI(pageURLobj.searchParams.get("subsidiary")) +
					"&namestartswith=" + encodeURI(pageURLobj.searchParams.get("namestartswith")) +
					"&namecontains=" + encodeURI(pageURLobj.searchParams.get("namecontains")) +
					"&customertype=" + encodeURI(pageURLobj.searchParams.get("customertype")) +
					"&startdate=" + encodeURI(pageURLobj.searchParams.get("startdate")) +
					"&enddate=" + encodeURI(pageURLobj.searchParams.get("enddate"));
				window.onbeforeunload = null;
				window.location.assign(criteriaSuiteURL);
			}
			catch(e){
				log.error('AVA_CertCaptureBackToCriteria', 'ERROR NAME = ' + e.name + ', ERROR TYPE = ' + e.type + ', ERROR MESSAGE = ' + e.message);
			}
		}

		function setAddressLabelValue(currentRecObj){
			try{
				var addressstoreInline = document.getElementById("custpage_ava_addressstore_fs").value;
				addressstoreInline = document.getElementById("custpage_ava_addressstore_val").innerHTML;
				document.getElementById("custpage_ava_addressstore_val").style.display = "none";
				addressstoreInline = addressstoreInline.replaceAll("cstid", '"cstid"');
				addressstoreInline = addressstoreInline.replaceAll("cstlb", '"cstlb"');
				addressstoreInline = addressstoreInline.replaceAll("=", ":");
				addressstoreInline = addressstoreInline.replaceAll("=", ":");
				addressstoreInline = addressstoreInline.replaceAll("\u0005", ",");
				addressstoreInline = '[' + addressstoreInline + ']';
				var parseObj = JSON.parse(addressstoreInline);
				var sublistLineCount = currentRecObj.getLineCount({
					sublistId: 'ava_bulkcustomersublist'
				});
				for(var i = 0; i < sublistLineCount; i++){
					var customerInternalId = currentRecObj.getSublistValue({
						sublistId: 'ava_bulkcustomersublist',
						fieldId: 'customerinternalid',
						line: i
					});
					var custIdIndex = parseObj.map(function(d){return d['cstid']}).indexOf(parseInt(customerInternalId));
					var addressLabelDetails = parseObj[custIdIndex].cstlb;
					if(addressLabelDetails){
						var objAddressStoreField = currentRecObj.getSublistField({
							sublistId: 'ava_bulkcustomersublist',
							fieldId: 'custpage_selectaddresslabel',
							line: i
						});
						objAddressStoreField.removeSelectOption({
							value: null,
						});
						var addressStoreArray = addressLabelDetails.split('~*');
						for(var ad = 0; ad < addressStoreArray.length; ad++){
							var fieldValue = addressStoreArray[ad];
							if(ad == 0){
								objAddressStoreField.insertSelectOption({
									value: ad + 1,
									text: fieldValue,
									isSelected: true
								});
							}
							else{
								objAddressStoreField.insertSelectOption({
									value: ad + 1,
									text: fieldValue
								});
							}
						}
					}
				}
			}
			catch(e){
				log.error('setAddressLabelValue', 'ERROR NAME = ' + e.name + ', ERROR TYPE = ' + e.type + ', ERROR MESSAGE = ' + e.message);
			}
		}
		
		function setAddressOnAddressLabelChange(context, selectAddressLabel){
			try{
				var customerInternalId = context.currentRecord.getCurrentSublistValue({
					sublistId: 'ava_bulkcustomersublist',
					fieldId: 'customerinternalid'
				});
				
				var customerSearchObj = search.create({
					type: "customer",
					filters: [
						["internalid", "anyof", customerInternalId],
						"AND",
						["addresslabel", "is", selectAddressLabel]
					],
					columns: [
						search.createColumn({
							name: "internalid",
							sort: search.Sort.ASC
						}),
						"attention",
						"address1",
						"address2",
						"city",
						"zipcode",
						"phone",
						"addressphone",
						"state",
						"country",
						"address"
					]
				});
				customerSearchObj.run().each(function(result){
					var customerPhone = (result.getValue({name: 'addressphone'}) != null && result.getValue({name: 'addressphone'}).length > 0) ? result.getValue({name: 'addressphone'}) : result.getValue({name: 'phone'});
					var completeAddress = result.getValue({
						name: 'address'
					});
					if(validateNull(result.getValue({name: 'attention'}))){
						context.currentRecord.setCurrentSublistValue({
							sublistId: 'ava_bulkcustomersublist',
							fieldId: 'attention',
							value: result.getValue({name: 'attention'})
						});
					}
					else{
						context.currentRecord.setCurrentSublistValue({
							sublistId: 'ava_bulkcustomersublist',
							fieldId: 'attention',
							value: ''
						});
					}
					if(validateNull(result.getValue({name: 'address1'}))){
						context.currentRecord.setCurrentSublistValue({
							sublistId: 'ava_bulkcustomersublist',
							fieldId: 'addressline1',
							value: result.getValue({name: 'address1'})
						});
					}
					else{
						context.currentRecord.setCurrentSublistValue({
							sublistId: 'ava_bulkcustomersublist',
							fieldId: 'addressline1',
							value: ''
						});
					}
					if(validateNull(result.getValue({name: 'address2'}))){
						context.currentRecord.setCurrentSublistValue({
							sublistId: 'ava_bulkcustomersublist',
							fieldId: 'addressline2',
							value: result.getValue({name: 'address2'})
						});
					}
					else{
						context.currentRecord.setCurrentSublistValue({
							sublistId: 'ava_bulkcustomersublist',
							fieldId: 'addressline2',
							value: ''
						});
					}
					if(validateNull(result.getValue({name: 'city'}))){
						context.currentRecord.setCurrentSublistValue({
							sublistId: 'ava_bulkcustomersublist',
							fieldId: 'city',
							value: result.getValue({name: 'city'})
						});
					}
					else{
						context.currentRecord.setCurrentSublistValue({
							sublistId: 'ava_bulkcustomersublist',
							fieldId: 'city',
							value: ''
						});
					}
					if(validateNull(result.getValue({name: 'state'}))){
						context.currentRecord.setCurrentSublistValue({
							sublistId: 'ava_bulkcustomersublist',
							fieldId: 'state',
							value: result.getValue({name: 'state'})
						});
					}
					else{
						context.currentRecord.setCurrentSublistValue({
							sublistId: 'ava_bulkcustomersublist',
							fieldId: 'state',
							value: ''
						});
					}
					if(validateNull(result.getValue({name: 'zipcode'}))){
						context.currentRecord.setCurrentSublistValue({
							sublistId: 'ava_bulkcustomersublist',
							fieldId: 'postalcode',
							value: result.getValue({name: 'zipcode'})
						});
					}
					else{
						context.currentRecord.setCurrentSublistValue({
							sublistId: 'ava_bulkcustomersublist',
							fieldId: 'postalcode',
							value: ''
						});
					}
					if(validateNull(result.getValue({name: 'country'}))){
						context.currentRecord.setCurrentSublistValue({
							sublistId: 'ava_bulkcustomersublist',
							fieldId: 'country',
							value: result.getValue({name: 'country'})
						});
					}
					else{
						context.currentRecord.setCurrentSublistValue({
							sublistId: 'ava_bulkcustomersublist',
							fieldId: 'country',
							value: ''
						});
					}
					if(validateNull(customerPhone)){
						context.currentRecord.setCurrentSublistValue({
							sublistId: 'ava_bulkcustomersublist',
							fieldId: 'phone',
							value: customerPhone
						});
					}
					else{
						context.currentRecord.setCurrentSublistValue({
							sublistId: 'ava_bulkcustomersublist',
							fieldId: 'phone',
							value: ''
						});
					}
					context.currentRecord.setCurrentSublistValue({
						sublistId: 'ava_bulkcustomersublist',
						fieldId: 'address',
						value: completeAddress
					});
					return true;
				});
			}
			catch(e){
				log.error('setCompleteAddress', 'ERROR NAME = ' + e.name + ', ERROR TYPE = ' + e.type + ', ERROR MESSAGE = ' + e.message);
			}
		}

		function validateNull(value){
			if(value == 'null' || value == null || value == '' || value == ' ' || value == undefined || value == 'undefined' || value == 'NaN' || value == NaN){
				return false;
			}
			else{
				return value;
			}
		}
		
		return{
			saveRecord: saveRecord,
			fieldChanged: fieldChanged,
			pageInit: pageInit,
			AVA_CertCaptureBackToCriteria: AVA_CertCaptureBackToCriteria
		};
	}
);