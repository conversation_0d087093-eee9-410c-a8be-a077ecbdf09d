/**
 * @NApiVersion 2.1
 * @NModuleScope SameAccount
 */

define(["N/render"], (render) => {
	const renderTransaction = (response) => {
		log.debug("here");
        const pdf = render.transaction({
            entityId: 1280, //internald id of transaciton
            formId: 71 //the transaction form id- not the id of the template - it pulls from what's chosen on the print template field on the transaction form
          })
        
          response.writeFile({ file: pdf, isInline: false })
	};

	return { renderTransaction };
});
