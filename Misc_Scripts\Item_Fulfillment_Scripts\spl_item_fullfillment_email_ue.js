/**
 * @NApiVersion 2.0
 * @NScriptType UserEventScript
 * @NAmdConfig /SuiteScripts/config.json
 */

//@ts-ignore
define([
	"N/log",
	"GetOrderEmailAddressLib",
	//"PostAfterShipTrackingLib",
	"ItemFulfillmentEmailLib",
], function (
	log,
	getOrderEmailAddress,
	/*postAftershipTrackingLib,*/ itmFlmntLib
) {
	function afterSubmit(context) {
		var itemFulfillment = context.newRecord;
		var trackingNumber = itemFulfillment.getValue(
			"custbody_spl_tracking_number"
		);
		//postAftershipTrackingLib.postTracking(trackingNumber, itemFulfillment.id);

		var sendEmail = itemFulfillment.getValue("custbody_spl_email_itm_flflmnt");
		if (sendEmail) {
			var customerId = itemFulfillment.getValue("entity");
			var emailAddresses =
				getOrderEmailAddress.getOrderEmailAddress(customerId);
			itmFlmntLib.sendItemFulfillmentEmail(itemFulfillment, emailAddresses);
		}

		return true;
	}

	return {
		afterSubmit: afterSubmit,
	};
});
