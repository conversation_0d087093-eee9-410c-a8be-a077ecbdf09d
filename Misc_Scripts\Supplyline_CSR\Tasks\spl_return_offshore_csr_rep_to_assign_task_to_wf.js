//Returns the offshore rep based on who the last task was assigned to.


/**
 *@NApiVersion 2.1
 *@NScriptType workflowactionscript
 * @NAmdConfig /SuiteScripts/config.json
 */

//@ts-ignore
define(["N/log", "N/record", "GetRepToAssignToLib"], function (
	log,
	record,
	getRepToAssignToLib
) {
	function onAction(context) {
		var scriptDeployment = record.load({
			type: record.Type.SCRIPT_DEPLOYMENT,
			id: 3177,
		});

		var repAssignedLastTask = scriptDeployment.getValue(
			"custscript_spl_last_tsk_assngd"
		);

		var repArr = getRepToAssignToLib.getTaskRepArr("9"); //Offshore CSR

		var nextRepToAssignTo = getRepToAssignToLib.getRepBasedOffOfLastAssigned(
			repArr,
			repAssignedLastTask
		);

		updateLastRepScripParam();

		return nextRepToAssignTo;
		
		function updateLastRepScripParam() {
			scriptDeployment.setValue({
				fieldId: "custscript_spl_last_tsk_assngd",
				value: nextRepToAssignTo,
			});

			scriptDeployment.save();
		}

	}

	return {
		onAction: onAction,
	};
});