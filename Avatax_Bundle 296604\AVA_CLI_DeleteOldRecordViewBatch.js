/******************************************************************************************************
	Script Name - AVA_CLI_DeleteOldRecordViewBatch.js
	Company -     Avalara Technologies Pvt Ltd.
******************************************************************************************************/

/**
 *@NApiVersion 2.0
 *@NScriptType ClientScript
 */

define(['N/url'],
	function(url){
		function saveRecord(context){
			var cRecord = context.currentRecord;
			var alertFlag = 'F', deleteFlag = 'F', alertMsg = '';

			var lineCount = cRecord.getLineCount({
				sublistId: 'custpage_deleteoldrecorbsublist'
			});

			for(var i = 0; i < lineCount; i++){
				var apply = cRecord.getSublistValue({
					sublistId: 'custpage_deleteoldrecorbsublist',
					fieldId: 'apply',
					line: i
				});

				if(apply == true){
					var batchStatus = cRecord.getSublistValue({
						sublistId: 'custpage_deleteoldrecorbsublist',
						fieldId: 'deleteoldrecordbatchstatus',
						line: i
					});

					if(batchStatus == 'In Progress' || batchStatus == 'In Queue'){
						var batchName = cRecord.getSublistValue({
							sublistId: 'custpage_deleteoldrecorbsublist',
							fieldId: 'deleteoldrecordbatchname',
							line: i
						});

						alertFlag = 'T';
						alertMsg += batchName + '\n';
					}

					deleteFlag = 'T'
				}
			}

			if(deleteFlag == 'F'){
				alert('Select line item for delete.');
				return false;
			}

			if(alertFlag == 'T'){
				alert('Following Batches are in Progress or in Queue and cannot be deleted: \n' + alertMsg);
				return false;
			}

			return true;
		}

		function AVA_DeleteOldRecordViewBatchRefresh(){
			try{
				var criteriaSuiteURL = url.resolveScript({
					scriptId: 'customscript_ava_deleteoldrecordvb_suit',
					deploymentId: 'customdeploy_ava_deleteoldrecordvb_suit'
				});
				window.onbeforeunload = null;
				window.location.assign(criteriaSuiteURL);
			}
			catch(e){
				log.error('AVA_DeleteOldRecordViewBatchRefresh', 'ERROR NAME = ' + e.name + ', ERROR TYPE = ' + e.type + ', ERROR MESSAGE = ' + e.message);
			}
		}

		return{
			saveRecord: saveRecord,
			AVA_DeleteOldRecordViewBatchRefresh: AVA_DeleteOldRecordViewBatchRefresh
		};
	}
);