/**
 * @NApiVersion 2.1
 * @NScriptType MapReduceScript
 * @NAmdConfig /SuiteScripts/config.json
 */

//@ts-ignore
define([
  "N/log",
  "N/runtime",
  "GetEdiIntegrationsLib",
  "GetEdiFileContents",
  "OncareParse850",
  "ProcessIncoming850Lib",
  "MoveFileLib",
  "MapReduceSummaryStageHandling",
], function (
  log,
  runtime,
  getEdiIntegrationsLib,
  getEdiFileContentsLib,
  getParsedPurchaseOrderLib,
  processIncoming850Lib,
  moveFileLib,
  MapReduceSummaryStageHandling
) {
  var dataObj = {
    prodGuidBool: true,
    prodDirectoryBool: true,
    decodeContent: true,
    prodGUID: "346a76b64f9e4ed4a16064dc0cacb459",
    sandboxGUID: "",
    prodDirectory: "", //Set individually per folder in script
    documentType: "Sales Order",
    documentTypeId: 1,
    purchasingSoftware: "Oncare",
    purchasingSoftwareId: 1,
    pushEmailToDB: true,
  };

  function getInputData(context) {
    //Get array of all folders for this purchasing software
    const integrationFoldersArr = getEdiIntegrationsLib.getIntegrationFolders(
      dataObj.purchasingSoftwareId
    );

    const filePathObjsArr = [];

    //Get file names for each folder
    for (const folder of integrationFoldersArr) {
      const remainingUsage = runtime.getCurrentScript().getRemainingUsage();

      /*Not enough usage to get the information for the next folder
				Stop iteration here and return the array with whatever was gotten so far*/
      if (remainingUsage < 10) {
        log.error(
          "SCRIPT_USAGE_MAX_REACHED",
          `Execution stopped at getting the files for ${folder.integrationName}.`
        );

        break;
      }

      dataObj.customerName = folder.integrationName;
      dataObj.prodDirectory = `/Receive from OnCare/${folder.accountNumber}/IN/850`;

      let fileNames = [];
      try {
        //Get file names for this folder
        const connection = getEdiFileContentsLib.createConnection(dataObj);

        if (!connection) {
          throw `No connection returned`;
        }

        fileNames = getEdiFileContentsLib.getFileNamesForCurrentIntegration(
          dataObj,
          connection
        );

        //Add fileName obj to array
        fileNames.forEach((fileObj) => {
          if (!fileObj || !fileObj.name) {
            throw `Missing value for fileObj: ${JSON.stringify(fileObj)}`;
          }

          filePathObjsArr.push({
            integrationAccountNumber: folder.accountNumber,
            integrationName: folder.integrationName,
            fileName: fileObj.name,
            prodDirectory: dataObj.prodDirectory,
          });
        });
      } catch (e) {
        log.error(
          `ERROR_GETTING_FILE_CONTENTS_FOR ${folder.integrationName} `,
          `FileNames: ${fileNames}\nError: ${e}`
        );
		
        log.error("filePathObjsArr", filePathObjsArr);
      }
    }

    return filePathObjsArr;
  }

  function map(context) {
    var parsedResult = JSON.parse(context.value);

    var { integrationAccountNumber, integrationName, fileName, prodDirectory } =
      parsedResult;

    dataObj.prodDirectory = prodDirectory;

    const connection = getEdiFileContentsLib.createConnection(dataObj);
    const fileContent = getEdiFileContentsLib.getContentForFile(
      connection,
      fileName,
      dataObj
    );

    try {
      var purchaseOrderObj = getParsedPurchaseOrderLib.parse850(
        fileContent.content
      );

      if (!purchaseOrderObj) {
        throw {
          name: "NO_PO_OBJ_RETURNED",
          message: "No further details provided.",
        };
      }

      var errorsProcessingEnd = processIncoming850Lib.processIncoming850(
        dataObj,
        purchaseOrderObj,
        fileName,
        integrationAccountNumber
      );

      if (errorsProcessingEnd && errorsProcessingEnd.length > 0) {
        log.error("ERROR_PROCESSING_END", errorsProcessingEnd);
      }
    } catch (err) {
      try {
        dataObj.referenceDirectory = `/EDI Reference Files/${dataObj.purchasingSoftware}/${integrationAccountNumber}/IN/850`;

        moveFileLib.moveFile(
          dataObj,
          fileName,
          false //Not processed successfully
        );
      } catch (moveError) {
        throw {
          name: "ERROR_MOVING_FILE_TO_REFERENCE_FOLDER",
          message: moveError,
        };
      }

      throw {
        name: `MAP_ERROR`,
        message: `${integrationAccountNumber}: ${err.name}					
${err.message}
EDI_FILE: ${JSON.stringify(fileContent)}`,
      };
    }

    context.write(integrationName, fileName);
  }

  function reduce(context) {
    context.write({
      key: context.key,
      value: context.values,
    });
  }

  function summarize(context) {
    const stageHandling = new MapReduceSummaryStageHandling(context);
    stageHandling.printRecordsProcessed();
    stageHandling.printErrors({
      groupErrors: true,
    });
  }

  return {
    getInputData,
    map,
    reduce,
    summarize,
  };
});
