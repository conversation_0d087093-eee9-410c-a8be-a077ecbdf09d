/**
 * @NApiVersion 2.1
 * @NScriptType MapReduceScript
 * @NAmdConfig /SuiteScripts/config.json
 */

//@ts-ignore
define(["N/error", "N/search", "N/record", "N/log"], function (
	error,
	search,
	record,
	log
) {
	function getInputData(context) {
		var searchInternalId = 2454; //Suitescript Search: GPO Referral Fees to Delete

		var searchObj = search.load({
			id: searchInternalId,
		});

		var recordsArr = searchObj
			.run()
			.getRange({
				start: 0,
				end: 999,
			})
			.map((row) => {
				return {
					referralId: row.id,
					journalEntryId: row.getValue(searchObj.columns[1]),
					createdForId: row.getValue(searchObj.columns[2]),
					createdForType: row.getValue(searchObj.columns[3]),
				};
			});

		return recordsArr;
	}

	function map(context) {
		var parsedResult = JSON.parse(context.value);

		var { referralId, journalEntryId, createdForId, createdForType } =
			parsedResult;

		// context.write(referralId, referralId); //Use when testing script results before running delete

		try {
			if (journalEntryId) {
				var deletedJournalEntry = record.delete({
					type: record.Type.JOURNAL_ENTRY,
					id: journalEntryId,
				});
			}

			if (createdForId) {
				const recordTypeForLoad =
					createdForType == "CustInvc" ? "INVOICE" : "CREDIT_MEMO";

				var createdForRecord = record.load({
					type: record.Type[recordTypeForLoad],
					id: createdForId,
				});

				createdForRecord.setValue("custbody_spl_referral_number", null);
				createdForRecord.setValue("custbody_spl_referral_percentage", null);
				createdForRecord.setValue("custbody_spl_referral_fee", null);
				createdForRecord.setValue("custbody_spl_referral_journal_entry", null);

				createdForRecord.save();
			}

			var deletedReferral = record.delete({
				type: "customrecord_spl_trnsctn_gpo_fee_record",
				id: referralId,
			});

			context.write(deletedReferral, deletedReferral, createdForId);
		} catch (e) {
			log.error("Error Deleting GPO Records", e);
		}
	}

	function summarize(context) {
		var recordsDeleted = "";

		var counter = 0;
		context.output.iterator().each(function (key, value) {
			counter++;
			recordsDeleted += "".concat(key, " ").concat(value, ", ");
			return true;
		});

		log.debug(
			`${counter} - The following records have been deleted ${recordsDeleted}`
		);
	}

	return {
		getInputData,
		map,
		summarize,
	};
});
