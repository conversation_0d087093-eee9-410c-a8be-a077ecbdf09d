/**
 * @description Represent an EDI Invoice, which is saved as an Invoice or Credit Memo in NetSuite
 *
 * @NApiVersion 2.1
 * <AUTHOR> <<EMAIL>>
 */

define([
  "require",
  "exports",
  "N/format",
  "N/log",
  "N/query",
  "N/record",
  "N/search",
  "./edi_sales_transaction",
], function (/** @type {any} */ require, /** @type {any} */ exports) {
  const format = require("N/format");
  const log = require("N/log");
  const query = require("N/query");
  const record = require("N/record");
  const search = require("N/search");
  const { EDISalesTransaction } = require("./edi_sales_transaction");

  /**
   * EDI Invoice Class
   *
   * @typedef {import("../../../../Classes/vlmd_custom_error_object").CustomErrorObject} CustomErrorObject
   * @typedef {import("../../Interfaces/Transactions/edi_transaction").EDIAddress} EDIAddress
   * @class
   */
  class EDIInvoice extends EDISalesTransaction {
    /** @param {{[key:string]: any}} params Constructor params */
    constructor(params) {
      super(params);
      /** @type {string} */
      this.supplierGLN = params.supplierGLN;
      /** @type {{quantity: number, units: string, rate: number, name: string, description: string, amount: number, gtin: string, upc: string}[]} */
      this.lineItems = [];
    }

    /**
     * Set and return the derived control number based on the latest number on the integration record
     *
     * @returns {number} Control number
     */
    getControlNumber() {
      try {
        const results = query
          .runSuiteQL({
            query: `
                            SELECT
                                custrecord_document_control_number as control_number
                            FROM
                                customrecord_vlmd_edi_integration 
                            WHERE
                                BUILTIN.MNFILTER(custrecord_edi_intgrtn_prnt_fclty, 'MN_INCLUDE', '', 'FALSE', NULL, '${this.customerParent}' ) = 'T' 
                                AND custrecord_document_control_number IS NOT NULL
                        `,
          })
          .asMappedResults();

        if (results.length <= 0 || !results[0]["control_number"]) {
          log.error(
            "EDI Invoice (getControlNumber)",
            `No control number was found for this parent customer's integration record: ${this.customerParent}`
          );
          throw this.customError.updateError({
            errorType: this.customError.ErrorTypes.NO_VALUE_RETURNED,
            summary: "NO_CONTROL_NUMBER_RETRIEVED",
            details: `No control number was found for this parent customer's integration record: ${this.customerParent}`,
          });
        }

        this.controlNumber = Number(results[0]["control_number"]);

        if (
          !this.controlNumber ||
          Number.isNaN(this.controlNumber) ||
          this.controlNumber < 100000001 ||
          this.controlNumber > 999999999
        ) {
          log.error(
            "EDI Invoice (getControlNumber)",
            `Control number, ${this.controlNumber}, is invalid. Please investigate and correct on the EDI integration record if needed.`
          );
          throw this.customError.updateError({
            errorType: this.customError.ErrorTypes.INVALID_DATA_TYPE,
            summary: "INVALID_CONTROL_NUMBER",
            details: `Control number, ${this.controlNumber}, is invalid. Please investigate and correct on the EDI integration record if needed.`,
          });
        }

        this.controlNumber += 1;

        return this.controlNumber;
      } catch (/** @type {any} */ err) {
        log.error("EDI Invoice (getControlNumber)", err);
        throw this.customError.updateError({
          errorType: this.customError.ErrorTypes.NO_VALUE_RETURNED,
          summary: "NO_CONTROL_NUMBER_RETRIEVED",
          details: `Error getting control number from EDI integration record: ${err}`,
        });
      }
    }

    /**
     * If Department Number is not available from the Invoice, retrieve it from the source Sales Order
     *
     * @returns {string} Department number
     */
    getDepartmentNumber() {
      try {
        if (
          !this.departmentNumber &&
          this.type === record.Type.INVOICE &&
          this.createdfrom
        ) {
          const departmentNumber = search.lookupFields({
            type: search.Type.SALES_ORDER,
            id: this.createdfrom,
            columns: ["custbody_crh_walmart_department"],
          })["custbody_crh_walmart_department"];
          if (Array.isArray(departmentNumber) && departmentNumber.length > 0) {
            this.departmentNumber = departmentNumber[0].text;
          }
        }

        return this.departmentNumber;
      } catch (/** @type {any} */ err) {
        log.error("EDI Invoice (getDepartmentNumber)", err);
        throw this.customError.updateError({
          errorType: this.customError.ErrorTypes.NO_VALUE_RETURNED,
          summary: "NO_DEPARTMENT_NUMBER_RETRIEVED",
          details: `Error getting department number from the Invoice: ${err}`,
        });
      }
    }

    /**
     * Attempt to retrieve the Transaction ID of the Credit Memo's parent transaction
     * If the transaction is not a Credit Memo, default to the supplied document number
     *
     * @returns {string|undefined} Parent's Transaction ID or Internal ID
     */
    getOriginalInvoice() {
      try {
        if (this.type === record.Type.CREDIT_MEMO) {
          const results = query
            .runSuiteQL({
              query: `
                                SELECT
                                    t.typebaseddocumentnumber AS document_number,
                                    BUILTIN.DF(ptll.previousdoc) AS invoice_number,
                                FROM
                                    Transaction as pt 
                                INNER JOIN
                                    PreviousTransactionLineLink AS ptll ON pt.id = ptll.nextdoc 
                                INNER JOIN
                                    Transaction AS t ON t.id = ptll.previousdoc 
                                    AND ptll.previoustype = 'CustInvc' 
                                WHERE PT.id = ?
                            `,
              params: [this.id],
            })
            .asMappedResults();

          //If there is a related invoice for this credit memo, return the invoice #
          if (results.length > 0 && results[0]["document_number"]) {
            return results[0]["invoice_number"]?.toString();
          }
        }

        return this.documentNumber;
      } catch (/** @type {any} */ err) {
        log.error("EDI Invoice (getOriginalInvoice)", err);
        throw this.customError.updateError({
          errorType: this.customError.ErrorTypes.NO_VALUE_RETURNED,
          summary: "NO_PARENT_INVOICE_RETRIEVED",
          details: `Error getting parent Invoice of the Credit Memo: ${err}`,
        });
      }
    }

    /**
     * Retrieve ship date from parent transaction
     *
     * @returns {string} Ship date string, defaults to date today
     */
    getShipDate() {
      try {
        if (this.createdfrom && this.type === record.Type.INVOICE) {
          const lookupObj = search.lookupFields({
            type: search.Type.SALES_ORDER,
            id: this.createdfrom,
            columns: ["actualshipdate", "shipdate"],
          });

          if (lookupObj.actualshipdate) {
            return lookupObj.actualshipdate.toString();
          } else if (lookupObj.shipdate) {
            return lookupObj.shipdate.toString();
          }
        }

        return this.formatToYYYYMMDD(new Date());
      } catch (/** @type {any} */ err) {
        log.error("EDI Invoice (getShipDate)", err);
        throw this.customError.updateError({
          errorType: this.customError.ErrorTypes.NO_VALUE_RETURNED,
          summary: "NO_SHIP_DATE_RETRIEVED",
          details: `Error getting ship date from the parent Sales Order: ${err}`,
        });
      }
    }

    /**
     * Sets the instance amount and line items properties
     *
     * @returns {void}
     */
    getLineItems() {
      try {
        const transaction = record.load({
          type: this.type,
          id: this.id,
        });
        this.subtotal = Number(transaction.getValue({ fieldId: "subtotal" }));
        this.lineCount = transaction.getLineCount({ sublistId: "item" });
        for (let i = 0; i < this.lineCount; i++) {
          const quantity = Number(
            transaction.getSublistValue({
              sublistId: "item",
              fieldId: "quantity",
              line: i,
            })
          );
          const units = transaction.getSublistText({
            sublistId: "item",
            fieldId: "units",
            line: i,
          });
          const rate = Number(
            transaction.getSublistValue({
              sublistId: "item",
              fieldId: "rate",
              line: i,
            })
          );
          const name = transaction.getSublistText({
            sublistId: "item",
            fieldId: "item",
            line: i,
          });
          const description = transaction.getSublistText({
            sublistId: "item",
            fieldId: "description",
            line: i,
          });
          const amount = Number(
            transaction.getSublistValue({
              sublistId: "item",
              fieldId: "amount",
              line: i,
            })
          );
          let gtin = transaction
            .getSublistValue({
              sublistId: "item",
              fieldId: "custcol_crh_gtin",
              line: i,
            })
            ?.toString();

          if (!gtin) {
            gtin = name === "IJS.7675" ? "00850052327675" : "0850052327668"; //Ice bath or Therapod
          }

          let upc = transaction
            .getSublistValue({
              sublistId: "item",
              fieldId: "custcol_crh_upc",
              line: i,
            })
            ?.toString();

          if (!upc) {
            upc = name === "IJS.7675" ? "850052327675" : "850052327668"; //Ice bath or Therapod
          }

          this.amount += amount;
          this.lineItems.push({
            quantity,
            units,
            rate,
            name,
            description,
            amount,
            gtin,
            upc,
          });
        }
      } catch (/** @type {any} */ err) {
        log.error("EDI Invoice (getLineItems)", err);
        throw this.customError.updateError({
          errorType: this.customError.ErrorTypes.NO_VALUE_RETURNED,
          summary: "NO_ITEMS_RETRIEVED",
          details: `Error getting line items from the transaction: ${err}`,
        });
      }
    }
  }

  exports.EDIInvoice = EDIInvoice;
});
