define(["require", "N/log", "N/record"], (
	/** @type {any} */ require
  ) => {
	const log = require("N/log");
	const record = require("N/record");
	function createRecord(recordType, isDynamic = true, defaultValues) {
		return record.create({
			type: record.Type[recordType],
			isDynamic: isDynamic,
			defaultValues: defaultValues,
		});
	}

	function loadRecord(transactionInternalId, recordType) {
		return record.load({
			type: record.Type[recordType],
			id: transactionInternalId,
		});
	}

	function getItemSublistObjsArr(netSuiteRecord, propsArr, inventoryItemsOnly) {
		const itemLineCount = netSuiteRecord.getLineCount({
			sublistId: "item",
		});

		const itemObjsArr = [];

		for (let x = 0; x < itemLineCount; x++) {
			let itemType = netSuiteRecord.getSublistValue({
				sublistId: "item",
				fieldId: "itemtype",
				line: x,
			});

			if (
				!inventoryItemsOnly ||
				(inventoryItemsOnly && itemType == "InvtPart")
			) {
				let itemObj = { lineNumber: x };

				propsArr.forEach((prop) => {
					itemObj[prop] = netSuiteRecord.getSublistValue({
						sublistId: "item",
						fieldId: prop.toLowerCase(),
						line: x,
					});
				});

				itemObjsArr.push(itemObj);
			}
		}

		return itemObjsArr;
	}

	function setBodyValues(valuesObj, netSuiteRecord) {
		//If need to use .setText - will need to build something out for that.
		for (const property in valuesObj) {
			try {
				netSuiteRecord.setValue(property, valuesObj[property]);
			} catch (e) {
				throw {
					name: `ERROR_SETTING_VALUES`,
					message: `${property} - ${valuesObj[property]}`,
				};
			}
		}

		return netSuiteRecord;
	}

	function setSublistValues(sublistArr, sublistIdParam, netSuiteRecord) {
		sublistArr.forEach((lineObj) => {
			netSuiteRecord.selectNewLine({
				sublistId: sublistIdParam,
			});

			for (const property in lineObj) {
				try {
					netSuiteRecord.setCurrentSublistValue({
						sublistId: sublistIdParam,
						fieldId: property,
						value: lineObj[property],
					});
				} catch (e) {
					log.error(
						`Error setting the values for ${property}`,
						`Value: ${lineObj[property]}, for line obj ${JSON.stringify(
							lineObj
						)}
						Error: ${e}`
					);
				}
			}

			netSuiteRecord.commitLine({
				sublistId: sublistIdParam,
			});

			return true;
		});
	}

	function saveRecord(netSuiteRecord) {
		return netSuiteRecord.save({
			ignoreMandatoryFields: true,
		});
	}

	function fulfillSO(salesOrderRecord) {
		try {
			const itemFulfillment = record.transform({
				fromType: record.Type.SALES_ORDER,
				fromId: salesOrderRecord.id,
				toType: record.Type.ITEM_FULFILLMENT,
			});

			itemFulfillment.save();
		} catch (e) {
			return e;
		}
	}

	return {
		createRecord,
		loadRecord,
		getItemSublistObjsArr,
		setBodyValues,
		setSublistValues,
		saveRecord,
		fulfillSO,
	};
});
