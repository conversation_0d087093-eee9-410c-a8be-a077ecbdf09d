/**
 * @description Represents the Partner entity that provide or receive EDI documents
 *
 * @NApiVersion 2.1
 * <AUTHOR> <<EMAIL>>
 */

define([
    "exports",
], (/** @type {any} */ exports) => {

    /**
     * EDI Partner Class
     *
     * @class
     * @typedef {import("../../Interfaces/Models/Partner/edi_partner").EDIPartnerInterface} EDIPartnerInterface
     * @typedef {import("../../Interfaces/Models/Partner/edi_partner").EDIParsingParams} EDIParsingParams
     * @typedef {import("../../Interfaces/Models/Partner/edi_partner").EDIParsingInformation} EDIParsingInformation
     * @typedef {import("../../Interfaces/Models/Partner/edi_partner").EDIBillingAddress} EDIBillingAddress
     * @implements {EDIPartnerInterface}
     */
    class EDIPartner {
        constructor() {
            /** @type {string} */
            this.id = "";
            /** @type {string} */
            this.purchasingSoftwareId = "";
            /** @type {string} */
            this.name = "";
            /** @type {string} */
            this.prodDirectory = "";
            /** @type {string} */
            this.testDirectory = "";
            /** @type {string} */
            this.referenceDirectory = "";
            /** @type {string} */
            this.subsidiary = "";
            /** @type {string} */
            this.parent = "";
            /** @type {EDIBillingAddress} */
            this.billingAddress = {
                addressee: "",
                street: "",
                city: "",
                state: "",
                zip: "",
                country: ""
            };
            /** @type {EDIParsingParams} */
            this.delimiters = {
                fieldDelimiter: "",
                segmentDelimiter: "",
                fileDelimiter: "",
                ediVersion: "",
                receiverQualifier: "",
                receiverId: "",
            }; // To be overridden by concrete Customer or Vendor classes
        }

        /**
         * Get information for parsing the file from the Partner
         *
         * @param {object} params Parameters
         * @param {EDIParsingParams} params.delimiters Parsing parameters
         * @param {"in" | "out"} params.direction Direction
         * @returns {EDIParsingInformation} Partner-specific values
         */
        getParsingInformation({
            delimiters: {
                fieldDelimiter,
                segmentDelimiter,
                fileDelimiter,
                ediVersion,
                receiverQualifier,
                receiverId
            },
            direction
        }) {
            const partnerValues = {
                fieldDelimiter: fieldDelimiter,
                segmentDelimiter: segmentDelimiter,
                formattingInfo: [
                    {
                        name: "element delimiter",
                        templateValue: "*",
                        partnerValue: fieldDelimiter,
                    },
                    {
                        name: "segmentDelimeiter",
                        templateValue: "~",
                        partnerValue: segmentDelimiter,
                    },
                    {
                        name: "fileDelimiter",
                        templateValue: ">",
                        partnerValue: fileDelimiter,
                    },
                ],
                isaGsInfo: [
                    {
                        name: "ISADate",
                        // moment().format("YYMMDD")
                        value: (() => {
                            const date = new Date();
                            const year = date.getFullYear().toString().slice(-2);
                            const month = (date.getMonth() + 1).toString().padStart(2, '0');
                            const day = date.getDate().toString().padStart(2, '0');

                            return `${year}${month}${day}`;
                        })(),
                    },
                    {
                        name: "GSDate",
                        //moment().format("YYYYMMDD")
                        value: (() => {
                            const date = new Date();
                            const year = date.getFullYear().toString();
                            const month = (date.getMonth() + 1).toString().padStart(2, '0');
                            const day = date.getDate().toString().padStart(2, '0');

                            return `${year}${month}${day}`;
                        })(),
                    },
                    {
                        name: "Time",
                        // moment().format("HHmm")
                        value: (() => {
                            const date = new Date();
                            const hours = date.getHours().toString().padStart(2, '0');
                            const minutes = date.getMinutes().toString().padStart(2, '0');

                            return `${hours}${minutes}`;
                        })(),
                    },
                    {
                        name: "EdiVersion",
                        value: ediVersion,
                    },
                ],
                senderInfo: (() => {
                    switch (direction) {
                        case "in":
                            return [
                                //Partner Info
                                {
                                    name: "SenderQualifier",
                                    value: receiverQualifier,
                                },
                                {
                                    name: "ISASenderId",
                                    //Needs to be 15 characters total, slice extracts up to but not including end
                                    value: (receiverId + "                 ").slice(0, 15),
                                },
                                {
                                    name: "GSSenderId",
                                    value: receiverId,
                                },
                            ];
                        case "out":
                            return [
                                //Valmed Info
                                {
                                    name: "SenderQualifier",
                                    value: "12",
                                },
                                {
                                    name: "ISASenderId",
                                    value: "7328137750" + "     ",
                                },
                                {
                                    name: "GSSenderId",
                                    value: "7328137750",
                                },
                            ];
                        default:
                            return [];
                    }
                })(),
                receiverInfo: (() => {
                    switch (direction) {
                        case "in":
                            return [
                                //Valmed Info
                                {
                                    name: "ReceiverQualifier",
                                    value: "12",
                                },
                                {
                                    name: "ISAReceiverId",
                                    value: "7328137750" + "     ",
                                },
                                {
                                    name: "GSReceiverId",
                                    value: "7328137750",
                                },
                            ];
                        case "out":
                            return [
                                //Partner Info
                                {
                                    name: "ReceiverQualifier",
                                    value: receiverQualifier,
                                },
                                {
                                    name: "ISAReceiverId",
                                    //Needs to be 15 characters total, slice extracts up to but not including end
                                    value: (receiverId + "                 ").slice(0, 15),
                                },
                                {
                                    name: "GSReceiverId",
                                    value: receiverId,
                                },
                            ];
                        default:
                            return [];
                    }
                })(),
            };
    
            return partnerValues;
        }
    }

    exports.EDIPartner = EDIPartner;
});